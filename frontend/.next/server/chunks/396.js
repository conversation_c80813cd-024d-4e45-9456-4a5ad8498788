"use strict";exports.id=396,exports.ids=[396],exports.modules={13396:(e,r,l)=>{l.r(r),l.d(r,{clearMarkdownCache:()=>o,getCacheStats:()=>i,processMarkdownFast:()=>c});let t={},a={htmlTags:/<[^>]+>/g,codeBlocks:/```(\w+)?\n([\s\S]*?)```/g,inlineCode:/`([^`]+)`/g,h4:/^#### (.*$)/gim,h3:/^### (.*$)/gim,h2:/^## (.*$)/gim,h1:/^# (.*$)/gim,bold:/\*\*(.*?)\*\*/g,italic:/\*(.*?)\*/g,strikethrough:/~~(.*?)~~/g,links:/\[([^\]]+)\]\(([^)]+)\)/g,icons:/!\[([^\]]*)\]\((\/api\/icons\/render\/[^)]+)\)/g,images:/!\[([^\]]*)\]\(([^)]+)\)/g,blockquotes:/^> (.+)$/gm,horizontalRule:/^---$/gm,unorderedList:/^[\s]*[-*+]\s+(.+)$/gm,orderedList:/^[\s]*\d+\.\s+(.+)$/gm,extraBr:/<br><\/p>/g,emptyP:/<p><\/p>/g};function c(e){let r=function(e){let r=0;for(let l=0;l<e.length;l++)r=(r<<5)-r+e.charCodeAt(l),r&=r;return r.toString()}(e);if(t[r])return t[r];try{if(!e||e.length<10)return e||"";let l=e,c=[],o=0,i=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replace(a.htmlTags,e=>{let r=`__HTML_TAG_${o}__`;return c[o]=e,o++,r})).replace(a.codeBlocks,(e,r,l)=>{let t=l.trim();return`<div data-code-block="true" data-language="${r||"text"}">${t}</div>`})).replace(a.inlineCode,'<code class="inline-code">$1</code>')).replace(a.h4,"<h4>$1</h4>")).replace(a.h3,"<h3>$1</h3>")).replace(a.h2,"<h2>$1</h2>")).replace(a.h1,"<h1>$1</h1>")).replace(a.bold,"<strong>$1</strong>")).replace(a.italic,"<em>$1</em>")).replace(a.strikethrough,"<del>$1</del>")).replace(a.links,'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')).replace(a.icons,'<span data-icon-placeholder data-src="$2" data-alt="$1"></span>')).replace(a.images,'<img src="$2" alt="$1" loading="lazy" style="max-width: 100%; height: auto;" />')).replace(a.blockquotes,"<blockquote>$1</blockquote>")).replace(a.horizontalRule,"<hr>")).replace(a.unorderedList,"<li>$1</li>")).replace(a.orderedList,"<li>$1</li>")).replace(/(<li>[\s\S]*?<\/li>)/g,e=>e.includes("<ul>")||e.includes("<ol>")?e:`<ul>${e}</ul>`)).split("\n"),n=[],s=!1;for(let e of i){let r=e.trim();if(!r){s&&(n.push("</p>"),s=!1);continue}r.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)?(s&&(n.push("</p>"),s=!1),n.push(r)):(s||(n.push("<p>"),s=!0),n.push(r+"<br>"))}s&&n.push("</p>"),l=n.join("\n");for(let e=0;e<c.length;e++)l=l.replace(`__HTML_TAG_${e}__`,c[e]);return l=(l=l.replace(a.extraBr,"</p>")).replace(a.emptyP,""),t[r]=l,l}catch(a){let l=e.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>");return t[r]=l,l}}function o(){Object.keys(t).forEach(e=>delete t[e])}function i(){return{size:Object.keys(t).length,keys:Object.keys(t)}}}};