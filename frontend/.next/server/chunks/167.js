"use strict";exports.id=167,exports.ids=[167],exports.modules={37358:(t,e,a)=>{a.d(e,{Z:()=>i});let i=(0,a(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51896:(t,e,a)=>{a.d(e,{Z:()=>i});let i=(0,a(62881).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},77636:(t,e,a)=>{a.d(e,{Z:()=>i});let i=(0,a(62881).Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},71754:(t,e,a)=>{a.d(e,{O1:()=>s,Uf:()=>g,Vj:()=>u,ay:()=>o,dA:()=>c,oX:()=>d});var i=a(10326),r=a(17577),n=a(51223),l=a(23201);function o({children:t,delay:e=0,className:a}){let[l,o]=(0,r.useState)(!1),s=(0,r.useRef)(null);return i.jsx("div",{ref:s,className:(0,n.cn)("transition-all duration-700 ease-out",l?"opacity-100 translate-y-0":"opacity-0 translate-y-8",a),children:t})}function s({children:t,delay:e=0,className:a}){let[l,o]=(0,r.useState)(!1),s=(0,r.useRef)(null);return i.jsx("div",{ref:s,className:(0,n.cn)("transition-all duration-500 ease-out",l?"opacity-100 scale-100":"opacity-0 scale-95",a),children:t})}function d({children:t,direction:e="left",delay:a=0,className:l}){let[o,s]=(0,r.useState)(!1),d=(0,r.useRef)(null);return i.jsx("div",{ref:d,className:(0,n.cn)("transition-all duration-600 ease-out",o?"opacity-100":"opacity-0",(()=>{if(o)return"translate-x-0 translate-y-0";switch(e){case"left":default:return"-translate-x-8";case"right":return"translate-x-8";case"up":return"-translate-y-8";case"down":return"translate-y-8"}})(),l),children:t})}function c({children:t,delay:e=0,staggerDelay:a=100,className:r}){return i.jsx("div",{className:r,children:t.map((t,r)=>i.jsx(o,{delay:e+r*a,children:t},r))})}function u({children:t,color:e=l.AS.primary.main,intensity:a="normal",className:r}){return i.jsx("div",{className:(0,n.cn)("transition-all duration-300",r),style:{filter:`drop-shadow(0 0 10px ${e}${Math.round(255*({subtle:.2,normal:.3,intense:.5})[a]).toString(16)})`},children:t})}function g({children:t,color:e=l.AS.primary.main,className:a}){let[o,s]=(0,r.useState)([]);return(0,i.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",a),onClick:t=>{let e=t.currentTarget.getBoundingClientRect(),a=t.clientX-e.left,i=t.clientY-e.top,r=Date.now();s(t=>[...t,{x:a,y:i,id:r}]),setTimeout(()=>{s(t=>t.filter(t=>t.id!==r))},600)},children:[t,o.map(t=>i.jsx("span",{className:"absolute animate-ping rounded-full opacity-75",style:{left:t.x-10,top:t.y-10,width:20,height:20,backgroundColor:e,animationDuration:"600ms"}},t.id))]})}},23201:(t,e,a)=>{a.d(e,{AS:()=>l,K$:()=>n,WD:()=>o});let i=["#10B981","#059669","#22C55E","#16A34A","#84CC16","#60A5FA","#3B82F6","#0EA5E9","#38BDF8","#06B6D4","#A78BFA","#8B5CF6","#A855F7","#C084FC","#DDD6FE","#FB923C","#F97316","#FDBA74","#FED7AA","#FEF3C7","#F472B6","#EC4899","#F9A8D4","#FBCFE8","#FCE7F3","#22D3EE","#06B6D4","#67E8F9","#A5F3FC","#CFFAFE","#FDE047","#EAB308","#FEF08A","#FEFCE8","#FFFBEB","#94A3B8","#64748B","#CBD5E1","#E2E8F0","#F1F5F9"],r={getLuminance(t){let e=this.hexToRgb(t);if(!e)return 0;let{r:a,g:i,b:r}=e;return .2126*a+.7152*i+.0722*r},hexToRgb(t){let e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null},getContrastColor(t){return this.getLuminance(t)>128?"#000000":"#FFFFFF"},withOpacity(t,e){let a=this.hexToRgb(t);return a?`rgba(${a.r}, ${a.g}, ${a.b}, ${e})`:t},getShadow(t,e=.25){return`0 4px 12px ${this.withOpacity(t,e)}`},getGradient(t,e="to right"){let a=this.withOpacity(t,.8),i=this.withOpacity(t,.6);return`linear-gradient(${e}, ${a}, ${i})`}},n={getTagStyle(t,e="default"){let a=t&&t.startsWith("#")?t:i[0];switch(e){case"minimal":return{backgroundColor:r.withOpacity(a,.08),color:r.withOpacity(a,.8),border:`1px solid ${r.withOpacity(a,.15)}`,boxShadow:"none"};case"soft":return{backgroundColor:r.withOpacity(a,.12),color:r.withOpacity(a,.9),border:`1px solid ${r.withOpacity(a,.2)}`,boxShadow:`0 1px 3px ${r.withOpacity(a,.1)}`};case"outline":return{backgroundColor:"transparent",color:r.withOpacity(a,.8),border:`1px solid ${r.withOpacity(a,.3)}`,boxShadow:`0 1px 2px ${r.withOpacity(a,.05)}`};default:return{backgroundColor:r.withOpacity(a,.15),color:r.withOpacity(a,.9),border:`1px solid ${r.withOpacity(a,.25)}`,boxShadow:`0 2px 4px ${r.withOpacity(a,.1)}`}}},getTagClasses(t,e="default"){let a="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 cursor-pointer relative overflow-hidden";return t&&t.startsWith("#")?a:`${a} bg-muted/50 text-muted-foreground border border-border/50`}},l={primary:{main:"#10B981",dark:"#059669",light:"#6EE7B7",deeper:"#047857",lighter:"#A7F3D0",subtle:"#D1FAE5"},gradients:{primary:"linear-gradient(135deg, #10B981 0%, #059669 100%)",subtle:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",card:"linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(5, 150, 105, 0.08) 100%)",glow:"radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%)",overlay:"linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%)"},shadows:{card:"0 4px 12px rgba(16, 185, 129, 0.15)",cardHover:"0 8px 25px rgba(16, 185, 129, 0.25)",glow:"0 0 20px rgba(16, 185, 129, 0.3)",glowIntense:"0 0 40px rgba(16, 185, 129, 0.4)"},animations:{duration:{fast:"200ms",normal:"300ms",slow:"500ms",slower:"700ms"},easing:{smooth:"cubic-bezier(0.4, 0, 0.2, 1)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)",elastic:"cubic-bezier(0.175, 0.885, 0.32, 1.275)"}}},o={getTimelineCardStyle:()=>({background:l.gradients.card,border:`1px solid ${r.withOpacity(l.primary.main,.2)}`,borderRadius:"1rem",boxShadow:l.shadows.card,backdropFilter:"blur(10px)",transition:`all ${l.animations.duration.normal} ${l.animations.easing.smooth}`}),getAlbumCardStyle:()=>({background:l.gradients.subtle,border:`1px solid ${r.withOpacity(l.primary.main,.15)}`,borderRadius:"1.5rem",boxShadow:l.shadows.card,backdropFilter:"blur(8px)",transition:`all ${l.animations.duration.slow} ${l.animations.easing.smooth}`}),getGridCardStyle:()=>({borderRadius:"0.75rem",overflow:"hidden",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:`all ${l.animations.duration.normal} ${l.animations.easing.smooth}`}),getHoverStyle:()=>({transform:"translateY(-8px) scale(1.02)",boxShadow:l.shadows.cardHover,borderColor:r.withOpacity(l.primary.main,.4)})};n.getTagStyle(),n.getTagClasses()},27524:(t,e,a)=>{a.d(e,{cn:()=>n});var i=a(41135),r=a(31009);function n(...t){return(0,r.m6)((0,i.W)(t))}},63979:(t,e,a)=>{a.d(e,{Eh:()=>g,RQ:()=>s,gn:()=>l});var i=a(29712),r=a(53722);async function n(){try{return(await i.Z.get(`${r.CT}/gallery/public`)).data}catch(t){return{categories:[],featured_items:[],total_items:0}}}async function l(t){try{return(await i.Z.get(`${r.CT}/gallery/public/category/${t}`)).data}catch(t){return{category:{},items:[],total:0}}}async function o(){try{return(await i.Z.get(`${r.CT}/gallery/public/timeline`)).data}catch(t){return[]}}async function s(t){if(t.startsWith("category-"))try{let e=(await g()).albums.find(e=>e.id===t);if(e)return e;throw Error(`Gallery category album not found: ${t}`)}catch(t){throw t}try{let e=(await i.Z.get(`${r.CT}/images/albums/${t}`)).data;return{id:e.id.toString(),title:e.title,description:e.description,coverImage:e.cover_image?{id:e.cover_image.id.toString(),url:e.cover_image.url,thumbnail_url:e.cover_image.thumbnail_url,alt:e.cover_image.display_name||e.title,width:e.cover_image.width||400,height:e.cover_image.height||300,date:e.created_at||new Date().toISOString(),tags:[]}:{id:"0",url:"",thumbnail_url:"",alt:e.title,width:400,height:300,date:e.created_at||new Date().toISOString(),tags:[]},images:e.images?.map(t=>({id:t.id.toString(),url:t.url,thumbnail_url:t.thumbnail_url,alt:t.display_name||t.original_filename,width:t.width||400,height:t.height||300,placeholderUrl:t.thumbnail_url||t.url,fullsizeUrl:t.url,display_name:t.display_name,original_filename:t.original_filename}))||[],date:e.date,location:e.location,tags:e.images?.flatMap(t=>t.tags?.map(t=>t.name)||[])||[]}}catch(t){throw t}}function d(t){return t.map(t=>{let e=t.image,a=r.CT.replace("/api",""),i=t=>t.startsWith("http://")||t.startsWith("https://")?t:`${a}${t}`;return{id:t.id.toString(),url:i(e.url),thumbnail_url:i(e.thumbnail_url?e.thumbnail_url:e.url),alt:t.title||e.display_name||e.original_filename||"Gallery Image",width:e.width||1200,height:e.height||800,date:new Date().toISOString().split("T")[0],location:void 0,tags:[],caption:t.description||void 0}})}function c(t){let e={};return t.forEach(t=>{let a=t.date;e[a]||(e[a]=[]),e[a].push(t)}),Object.entries(e).map(([t,e])=>{let a=[...new Set(e.map(t=>t.location).filter(t=>!!t))],i=1===a.length?a[0]:void 0,r=[...new Set(e.flatMap(t=>t.tags||[]))];return{id:`timeline-${t}`,content:new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),images:e,date:t,location:i,tags:r}}).sort((t,e)=>new Date(e.date).getTime()-new Date(t.date).getTime())}async function u(){try{return(await i.Z.get(`${r.CT}/images/albums`)).data.map(t=>({id:t.id.toString(),title:t.title,description:t.description,coverImage:t.cover_image?{id:t.cover_image.id.toString(),url:t.cover_image.url,thumbnail_url:t.cover_image.thumbnail_url,alt:t.cover_image.display_name||t.title,width:t.cover_image.width||400,height:t.cover_image.height||300,placeholderUrl:t.cover_image.thumbnail_url||t.cover_image.url,fullsizeUrl:t.cover_image.url,display_name:t.cover_image.display_name,original_filename:t.cover_image.original_filename}:{id:"0",url:"",thumbnail_url:"",alt:t.title,width:400,height:300,placeholderUrl:"",fullsizeUrl:"",display_name:"",original_filename:""},images:[],date:t.date,location:t.location,tags:[]}))}catch(t){return[]}}async function g(){let t=await n(),e=await o(),a=[...await u()];for(let e of t.categories)if(e.item_count>0)try{let t=await l(e.slug),i=d(t.items);i.length>0&&a.push({id:`category-${e.id}`,title:e.name,description:e.description,coverImage:i[0],images:i,date:new Date().toISOString().split("T")[0],tags:[e.name],slug:e.slug})}catch(t){}let i=e;return 0===e.length&&t.featured_items.length>0&&(i=c(d(t.featured_items))),0===i.length&&a.length>0&&(i=c(a.flatMap(t=>t.images))),{albums:a,timelineEntries:i,allImages:t.featured_items.length>0?d(t.featured_items):a.flatMap(t=>t.images),galleryData:t}}}};