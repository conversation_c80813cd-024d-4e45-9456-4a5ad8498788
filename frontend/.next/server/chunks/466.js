exports.id=466,exports.ids=[466],exports.modules={66922:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},50144:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79404,23))},90984:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,44064,23)),Promise.resolve().then(r.bind(r,12315)),Promise.resolve().then(r.bind(r,16040)),Promise.resolve().then(r.bind(r,75734)),Promise.resolve().then(r.bind(r,68776)),Promise.resolve().then(r.bind(r,95008)),Promise.resolve().then(r.bind(r,10085)),Promise.resolve().then(r.bind(r,22985)),Promise.resolve().then(r.bind(r,68342)),Promise.resolve().then(r.bind(r,99117)),Promise.resolve().then(r.bind(r,96050)),Promise.resolve().then(r.bind(r,15762))},12315:(e,t,r)=>{"use strict";r.d(t,{I:()=>h,Providers:()=>x});var a=r(10326),n=r(17577),s=r.n(n),i=r(35047),o=r(3574);function l({children:e}){return a.jsx(a.Fragment,{children:e})}let c=(0,n.createContext)(null);function d({children:e}){let t=(0,n.useRef)(new Map),r=(0,n.useRef)(new Map),i=(0,n.useCallback)((e,r)=>{let a=e.toString();return(...n)=>{let s=t.current.get(a);s&&clearTimeout(s);let i=setTimeout(()=>{e(...n),t.current.delete(a)},r);t.current.set(a,i)}},[]),o=(0,n.useCallback)((e,t)=>{let a=e.toString();return(...n)=>{let s=Date.now(),i=r.current.get(a);(!i||s-i.lastRun>=t)&&(e(...n),r.current.set(a,{timer:setTimeout(()=>{r.current.delete(a)},t),lastRun:s}))}},[]),l=(0,n.useCallback)(e=>{s().startTransition(()=>{e.forEach(e=>e())})},[]),d=(0,n.useCallback)((e,t)=>{performance.now(),t(),performance.now()},[]),m=(0,n.useMemo)(()=>({debounce:i,throttle:o,batchUpdate:l,measurePerformance:d}),[i,o,l,d]);return a.jsx(c.Provider,{value:m,children:e})}var m=r(24173);function u(){let{resolvedTheme:e,setTheme:t}=(0,o.F)();return null}let h=(0,n.createContext)({});function x({children:e}){(0,i.usePathname)();let t=(0,n.useRef)().current;return a.jsx(h.Provider,{value:{previousPathname:t},children:a.jsx(m.PK,{children:a.jsx(d,{children:a.jsx(o.f,{attribute:"class",disableTransitionOnChange:!0,children:(0,a.jsxs)(l,{children:[a.jsx(u,{}),e]})})})})})}},68776:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(10326),n=r(17577);class s extends n.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){}render(){return this.state.hasError?this.props.fallback?this.props.fallback:a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center",children:[a.jsx("div",{className:"w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-red-100 dark:bg-red-900/20 rounded-full",children:a.jsx("svg",{className:"w-8 h-8 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),a.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"出现了一些问题"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"页面遇到了意外错误，请尝试刷新页面或稍后再试。"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200",children:"刷新页面"}),a.jsx("button",{onClick:()=>window.history.back(),className:"w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-md transition-colors duration-200",children:"返回上一页"})]}),!1]})}):this.props.children}}let i=s},16040:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(10326),n=r(29380);function s(){let e=process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;return e?a.jsx(n.GoogleAnalytics,{gaId:e}):null}},75734:(e,t,r)=>{"use strict";r.d(t,{PlausibleAnalytics:()=>o});var a=r(10326),n=r(96931);let s=process.env.NEXT_PUBLIC_PLAUSIBLE_URL,i=process.env.NEXT_PUBLIC_PLAUSIBLE_SRC;function o(){return a.jsx(n.default,{defer:!0,type:"text/javascript","data-domain":s,src:i})}},62876:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(10326),n=r(95470),s=r(90434),i=r(78357),o=r(51223);function l({className:e,style:t,socialLinks:r}){return r&&0!==r.length?a.jsx("div",{className:(0,o.cn)("mt-6 flex items-center gap-2",e),style:t,children:r.map((e,t)=>(0,a.jsxs)("div",{className:"group relative",style:{animationDelay:`${100*t}ms`},children:[a.jsx("div",{className:"absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300 animate-glow-pulse"}),(0,o.xf)(e.href)?(0,a.jsxs)("a",{href:(0,o.h1)(e.href,n.fI),target:"_blank",rel:"noreferrer","aria-label":`Follow on ${e.name}`,className:"relative inline-flex h-10 w-10 items-center justify-center rounded-lg border border-muted-foreground/20 bg-background/50 backdrop-blur-sm transition-all duration-300 ease-smooth hover:bg-primary/10 hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg group-hover:text-primary animate-float-gentle",children:[a.jsx(i.CustomIcon,{name:e.icon}),a.jsx("span",{className:"sr-only",children:e.name})]}):(0,a.jsxs)(s.default,{href:(0,o.h1)(e.href,n.fI),target:"_blank",rel:"noreferrer","aria-label":`Follow on ${e.name}`,className:"relative inline-flex h-10 w-10 items-center justify-center rounded-lg border border-muted-foreground/20 bg-background/50 backdrop-blur-sm transition-all duration-300 ease-smooth hover:bg-primary/10 hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg group-hover:text-primary animate-float-gentle",children:[a.jsx(i.CustomIcon,{name:e.icon}),a.jsx("span",{className:"sr-only",children:e.name})]})]},e.name))}):a.jsx("div",{className:(0,o.cn)("mt-6 flex items-center",e),style:t})}},30131:(e,t,r)=>{"use strict";r.d(t,{W2:()=>l,Zb:()=>i,le:()=>o});var a=r(10326),n=r(17577),s=r(41135);let i=(0,n.forwardRef)(function({className:e,children:t,...r},n){return a.jsx("div",{ref:n,className:(0,s.Z)("sm:px-8",e),...r,children:a.jsx("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:t})})}),o=(0,n.forwardRef)(function({className:e,children:t,...r},n){return a.jsx("div",{ref:n,className:(0,s.Z)("relative px-4 sm:px-8 lg:px-12",e),...r,children:a.jsx("div",{className:"mx-auto max-w-2xl lg:max-w-5xl",children:t})})}),l=(0,n.forwardRef)(function({children:e,...t},r){return a.jsx(i,{ref:r,...t,children:a.jsx(o,{children:e})})})},95008:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>h});var a=r(10326),n=r(90434),s=r(17577),i=r(30131),o=r(11476),l=r(62876);function c({className:e,style:t}){let[r,n]=(0,s.useState)([]),[i,o]=(0,s.useState)(!0);return i?a.jsx("div",{className:e,style:t,children:"Loading..."}):a.jsx(l.default,{className:e,style:t,socialLinks:r})}var d=r(52879);function m(){let[e,t]=(0,s.useState)({totalUV:"-",dailyUV:"-"});return(0,a.jsxs)("div",{className:"flex flex-row items-center justify-center gap-2 text-sm text-gray-500 mt-2",children:[a.jsx(d.b,{size:16,weight:"duotone"}),"Total Visits: ",e.totalUV," / Today Visits: ",e.dailyUV]})}function u({href:e,children:t}){return a.jsx(n.default,{href:e,className:"transition hover:text-primary",children:t})}function h(){let[e,t]=(0,s.useState)([{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Blogs",href:"/blogs"},{name:"Gallery",href:"/gallery"}]);return a.jsx("footer",{className:"mt-16 flex-none",children:a.jsx(i.Zb,{children:a.jsx("div",{className:"border-t border-muted pb-16 pt-10",children:a.jsx(i.le,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-between gap-6 sm:flex-row sm:items-start",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[a.jsx("div",{className:"flex flex-wrap justify-center gap-x-6 gap-y-1 text-sm font-medium",children:e.map(e=>a.jsx(u,{href:e.href,children:e.name},e.name))}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground text-center",children:["Frontend template from ",a.jsx("a",{href:"https://github.com/iAmCorey/coreychiu-portfolio-template",target:"_blank",rel:"noopener noreferrer",className:"underline hover:text-primary",children:"coreychiu-portfolio-template"}),a.jsx("br",{}),"Full-stack personal website based on Next.js + MySQL + FastAPI"]})]}),(0,a.jsxs)("div",{className:"flex flex-col justify-center items-start",children:[(0,a.jsxs)("div",{className:"flex flex-row justify-end items-center gap-2",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," ","Jay-Yao",". All rights reserved."]}),a.jsx(n.default,{href:"/version-history",className:"text-xs text-muted-foreground hover:text-primary transition-colors duration-200 underline",title:"查看网站版本历史",children:"Version"}),a.jsx(o.T,{})]}),a.jsx(c,{className:"mt-0"}),a.jsx(m,{})]})]})})})})})}},10085:(e,t,r)=>{"use strict";r.d(t,{Header:()=>k});var a=r(10326),n=r(17577),s=r(46226),i=r(90434),o=r(35047),l=r(69920),c=r(29441),d=r(41135),m=r(30131);let u={src:"/_next/static/media/avatar.700ad1ea.jpg",height:902,width:902,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAP/xAAVAQEBAAAAAAAAAAAAAAAAAAADBP/aAAwDAQACEAMQAAABqJ3/AP/EABcQAQADAAAAAAAAAAAAAAAAAAEAAgP/2gAIAQEAAQUCcm8//8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAS/9oACAEDAQE/AdJf/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAgEBPwF//8QAFxAAAwEAAAAAAAAAAAAAAAAAAAECEv/aAAgBAQAGPwJ6cuj/xAAYEAACAwAAAAAAAAAAAAAAAAABEQAhof/aAAgBAQABPyGv5QBeT//aAAwDAQACAAMAAAAQB//EABgRAQADAQAAAAAAAAAAAAAAAAEAESGB/9oACAEDAQE/EAWNcJ//xAAWEQADAAAAAAAAAAAAAAAAAAAAARH/2gAIAQIBAT8QcP/EABgQAQEBAQEAAAAAAAAAAAAAAAERIQBh/9oACAEBAAE/EF25JU0tPumvf//Z",blurWidth:8,blurHeight:8};var h=r(11476),x=r(941),g=r(94019),f=r(51223);function p({text:e,duration:t=200,className:r}){let[s,i]=(0,n.useState)(""),[o,l]=(0,n.useState)(0);return a.jsx("h1",{className:(0,f.cn)("font-display text-center text-4xl font-bold leading-[5rem] tracking-[-0.02em] drop-shadow-sm",r),children:s||e})}function b({href:e,children:t}){return a.jsx("li",{children:a.jsx(l.J.Button,{as:i.default,href:e,prefetch:!0,className:"block py-2",children:t})})}function y({navItems:e,...t}){return(0,a.jsxs)(l.J,{...t,children:[(0,a.jsxs)(l.J.Button,{className:"group flex items-center rounded-full px-4 py-2 text-sm font-medium shadow-lg ring-1 ring-border backdrop-blur-md bg-card/95 transition-all duration-300 hover:shadow-xl hover:ring-primary/20 hover:bg-primary/5",children:["Menu",a.jsx(x.Z,{className:"ml-3 h-auto w-2 transition-transform duration-200 group-hover:rotate-180"})]}),(0,a.jsxs)(c.u.Root,{children:[a.jsx(c.u.Child,{as:n.Fragment,enter:"duration-150 ease-out",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"duration-150 ease-in",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx(l.J.Overlay,{className:"fixed inset-0 z-50 backdrop-blur-sm dark:bg-background/80"})}),a.jsx(c.u.Child,{as:n.Fragment,enter:"duration-150 ease-out",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"duration-150 ease-in",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.J.Panel,{focus:!0,className:"fixed inset-x-4 top-8 z-50 origin-top rounded-3xl p-8 ring-1 ring-muted bg-card",children:[(0,a.jsxs)("div",{className:"flex flex-row-reverse items-center justify-between",children:[a.jsx(l.J.Button,{"aria-label":"Close menu",className:"-m-1 p-1",children:a.jsx(g.Z,{className:"h-6 w-6 text-muted-foreground"})}),a.jsx("h2",{className:"text-sm font-medium text-muted-foreground",children:"Navigation"})]}),a.jsx("nav",{className:"mt-6",children:a.jsx("ul",{className:"-my-2 divide-y divide-zinc-100 text-base dark:divide-zinc-100/5",children:e.map(e=>a.jsx(b,{href:e.href,children:e.name},e.name))})})]})})]})]})}function A({href:e,children:t}){let r=(0,o.usePathname)()===e;return a.jsx("li",{children:(0,a.jsxs)(i.default,{href:e,prefetch:!0,className:(0,d.Z)("relative block px-3 py-2 transition-all duration-300 rounded-md",r?"text-primary bg-primary/10":"opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5"),children:[t,r&&a.jsx("span",{className:"absolute inset-x-1 -bottom-px h-[2px] bg-gradient-to-r from-primary/0 via-primary/60 to-primary/0 rounded-full animate-pulse-soft"})]})})}function v({navItems:e,...t}){return a.jsx("nav",{...t,children:a.jsx("ul",{className:"flex rounded-full px-3 text-sm font-medium bg-card/95 ring-1 ring-border shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-xl hover:ring-primary/20",children:e.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:[t>0&&a.jsx("li",{className:"flex items-center",children:a.jsx("div",{className:"h-4 w-px bg-muted-foreground/30"})}),a.jsx(A,{href:e.href,children:e.name})]},e.name))})})}function j({showName:e=!1,personalName:t="Jay-Yao",className:r,...n}){return(0,a.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[a.jsx("div",{className:(0,d.Z)(r,"h-10 w-10 rounded-full bg-white/90 p-0.5 shadow-lg shadow-zinc-800/5 ring-1 ring-zinc-900/5 backdrop-blur dark:bg-zinc-800/90 dark:ring-white/10"),...n}),e&&a.jsx(i.default,{href:"/","aria-label":"Home",className:"pointer-events-auto",children:a.jsx("div",{className:"text-md font-semibold capitalize",children:t})})]})}function w({large:e=!1,className:t,...r}){return a.jsx(i.default,{href:"/","aria-label":"Home",className:(0,d.Z)(t,"pointer-events-auto"),...r,children:a.jsx(s.default,{src:u,alt:"",sizes:e?"4rem":"2.25rem",className:(0,d.Z)("rounded-full bg-zinc-100 object-cover dark:bg-zinc-800",e?"h-16 w-16":"h-9 w-9"),priority:!0})})}function k(){let e="/"===(0,o.usePathname)(),[t,r]=(0,n.useState)([{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Blogs",href:"/blogs"},{name:"Gallery",href:"/gallery"}]),[s,i]=(0,n.useState)("Jay-Yao"),l=(0,n.useRef)(null),c=(0,n.useRef)(null);return(0,n.useRef)(!0),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:"pointer-events-none relative z-50 flex flex-none flex-col",style:{height:"var(--header-height)",marginBottom:"var(--header-mb)"},children:[e&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{ref:c,className:"order-last mt-[calc(theme(spacing.16)-theme(spacing.3))]"}),a.jsx(m.W2,{className:"top-0 order-last -mb-3 pt-3",style:{position:"var(--header-position)"},children:a.jsx("div",{className:"top-[var(--avatar-top,theme(spacing.3))] w-full",style:{position:"var(--header-inner-position)"},children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(j,{className:"absolute left-0 top-3 origin-left transition-opacity",style:{opacity:"var(--avatar-border-opacity, 0)",transform:"var(--avatar-border-transform)"}}),(0,a.jsxs)("div",{className:"flex flex-row items-center gap-4",children:[a.jsx(w,{large:!0,className:"block h-16 w-16 origin-left",style:{transform:"var(--avatar-image-transform)"}}),(0,a.jsxs)("div",{className:"text-3xl md:text-6xl font-bold tracking-tight flex flex-row",style:{opacity:"var(--avatar-hi-opacity, 0)",transform:"var(--avatar-hi-transform)"},children:["Hi,"," ",a.jsx(p,{className:"text-3xl md:text-6xl font-bold tracking-tight",text:`I'm ${s} `,duration:150}),"\uD83D\uDC4B"]})]})]})})})]}),a.jsx("div",{ref:l,className:"top-0 z-10 h-16 pt-6",style:{position:"var(--header-position)"},children:a.jsx(m.W2,{className:"top-[var(--header-top,theme(spacing.6))] w-full",style:{position:"var(--header-inner-position)"},children:(0,a.jsxs)("div",{className:"relative flex gap-4",children:[a.jsx("div",{className:"flex flex-1",children:!e&&a.jsx(j,{showName:!0,personalName:s,children:a.jsx(w,{})})}),(0,a.jsxs)("div",{className:"flex flex-1 justify-end md:justify-center",children:[a.jsx(y,{navItems:t,className:"pointer-events-auto md:hidden"}),a.jsx(v,{navItems:t,className:"pointer-events-auto hidden md:block"})]}),a.jsx("div",{className:"flex justify-end md:flex-1",children:a.jsx("div",{className:"pointer-events-auto flex flex-row items-center gap-2 md:mr-2",children:a.jsx(h.T,{})})})]})})})]}),e&&a.jsx("div",{className:"flex-none",style:{height:"var(--content-offset)"}})]})}},22985:(e,t,r)=>{"use strict";function a(){return null}function n(){return null}r.d(t,{PerformanceDebugger:()=>n,PerformanceMonitor:()=>a}),r(17577)},78357:(e,t,r)=>{"use strict";r.d(t,{CustomIcon:()=>g});var a=r(10326),n=r(17640),s=r(10629),i=r(16462),o=r(90917),l=r(30140),c=r(77895),d=r(77328),m=r(32098),u=r(62249),h=r(4927),x=r(4577);function g({name:e,size:t=20}){if(!e)return a.jsx("span",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t,fontSize:`${.6*t}px`,color:"#999",background:"#f5f5f5",borderRadius:"2px",border:"1px solid #ddd"},children:"?"});if(e.includes(":")){let[r,n]=e.split(":");if(!r||!n)return a.jsx("span",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t,fontSize:`${.6*t}px`,color:"#f44336",background:"#ffebee",borderRadius:"2px",border:"1px solid #f44336"},children:"!"});if("custom"===r||"iconic"===r&&["cursor","claude","anthropic","deepseek","doubao","gemini","github","google","huggingface","hunyuan","langchain","meta","mistral","modelscope","ollama","openai","openrouter","qwen","zhipu"].includes(n)){let e=`/images/icons/${n}.svg`;return a.jsx("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t},children:a.jsx("img",{src:e,alt:`${n} icon`,width:t,height:t,style:{display:"block",maxWidth:"100%",maxHeight:"100%"},onError:e=>{let r=e.target.parentElement;r&&(r.innerHTML=`
                  <span style="
                    font-size: ${.6*t}px;
                    color: #f44336;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: ${t}px;
                    height: ${t}px;
                    background: #ffebee;
                    border-radius: 2px;
                    border: 1px solid #f44336;
                  ">!</span>
                `)},onLoad:()=>{}})})}let s=`http://**************:8000/api/icons/render/${r}/${n}?size=${t}&theme=light`;return a.jsx("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t,verticalAlign:"middle"},children:a.jsx("img",{src:s,alt:`${r} ${n} icon`,width:t,height:t,style:{display:"block",maxWidth:"100%",maxHeight:"100%"},onError:e=>{let r=e.target.parentElement;r&&(r.innerHTML=`
                <span style="
                  font-size: ${.6*t}px;
                  color: #f44336;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: ${t}px;
                  height: ${t}px;
                  background: #ffebee;
                  border-radius: 2px;
                  border: 1px solid #f44336;
                ">!</span>
              `)},onLoad:()=>{}})})}switch(e){case"bank":return a.jsx(n.B,{size:t,weight:"duotone"});case"github":return a.jsx(s.b,{size:t,weight:"duotone"});case"x":return a.jsx(i.S,{size:t,weight:"duotone"});case"instagram":return a.jsx(o.C,{size:t,weight:"duotone"});case"bsky":return a.jsx(l.C,{size:t,weight:"duotone"});case"email":return a.jsx(c.w,{size:t,weight:"duotone"});case"college":return a.jsx(d.X,{size:t,weight:"duotone"});case"coffee":return a.jsx(m.n,{size:t,weight:"duotone"});case"pill":return a.jsx(u.D,{size:t,weight:"duotone"});case"wechat":return a.jsx(h.F,{size:t,weight:"duotone"});case"discord":return a.jsx(x.$,{size:t,weight:"duotone"});case"huggingface":return a.jsx("div",{className:"flex items-center justify-center",style:{width:`${t}px`,height:`${t}px`},children:a.jsx("span",{style:{fontSize:`${.85*t}px`},children:"\uD83E\uDD17"})});default:return null}}},11476:(e,t,r)=>{"use strict";r.d(t,{T:()=>h});var a=r(10326),n=r(17577),s=r(60850),i=r(72607),o=r(3574),l=r(34214),c=r(79360),d=r(51223);let m=(0,c.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...s},i)=>{let o=n?l.g7:"button";return a.jsx(o,{className:(0,d.cn)(m({variant:t,size:r,className:e})),ref:i,...s})});function h(){let{setTheme:e,theme:t,resolvedTheme:r}=(0,o.F)();return(0,a.jsxs)(u,{variant:"ghost",size:"icon",onClick:()=>{e("dark"===r?"light":"dark")},className:"relative",children:[a.jsx(s.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),a.jsx(i.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),a.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}u.displayName="Button"},68342:(e,t,r)=>{"use strict";r.d(t,{ThemeInitializer:()=>s});var a=r(17577);let n=()=>{let e=document.documentElement;["--theme-manager-primary","--theme-manager-primary-dark","--theme-manager-secondary","--theme-manager-accent","--theme-manager-bg-primary","--theme-manager-bg-secondary","--theme-manager-bg-tertiary","--theme-manager-text-primary","--theme-manager-text-secondary","--theme-manager-text-muted","--theme-manager-border-primary","--theme-manager-border-secondary","--theme-primary-hue","--theme-primary-saturation","--theme-primary-lightness","--theme-accent-hue","--theme-accent-saturation","--theme-accent-lightness"].forEach(t=>{e.style.removeProperty(t)})},s=()=>((0,a.useEffect)(()=>{n()},[]),null)},99117:(e,t,r)=>{"use strict";r.d(t,{GlobalLoadingManager:()=>l});var a=r(10326),n=r(24173),s=r(17577),i=r(35047);function o({isLoading:e=!1,message:t="Loading...",showOverlay:r=!0}){let[n,o]=(0,s.useState)(!1),[l,c]=(0,s.useState)(t);return((0,i.usePathname)(),n&&r)?a.jsx("div",{className:"fixed inset-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm z-50 flex items-center justify-center transition-all duration-300",children:(0,a.jsxs)("div",{className:"text-center max-w-sm mx-auto px-6",children:[(0,a.jsxs)("div",{className:"relative mb-6",children:[a.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-4 border-gray-200 dark:border-gray-700 border-t-blue-600 dark:border-t-blue-400 mx-auto"}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full animate-pulse"})}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"w-20 h-20 border-2 border-blue-200 dark:border-blue-800 rounded-full animate-ping opacity-20"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:l}),(0,a.jsxs)("div",{className:"flex justify-center space-x-1",children:[a.jsx("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"}),a.jsx("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),a.jsx("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-4",children:"Please wait, preparing amazing content for you"})]})}):null}function l(){let{loadingState:e}=(0,n.r$)();return"page"===e.type&&e.isLoading?a.jsx(o,{isLoading:e.isLoading,message:e.message,showOverlay:!0}):null}},96050:(e,t,r)=>{"use strict";r.d(t,{RouteProgressBar:()=>d});var a=r(10326),n=r(77626),s=r.n(n),i=r(17577),o=r(35047),l=r(8063);function c(){(0,o.usePathname)(),(0,o.useSearchParams)();let[e,t]=(0,i.useState)(!1);return a.jsx(a.Fragment,{children:a.jsx(s(),{id:"da1a53aeab4aec9c",children:"#nprogress{pointer-events:none}#nprogress .bar{background:-webkit-linear-gradient(left,#3b82f6,#8b5cf6)!important;background:-moz-linear-gradient(left,#3b82f6,#8b5cf6)!important;background:-o-linear-gradient(left,#3b82f6,#8b5cf6)!important;background:linear-gradient(90deg,#3b82f6,#8b5cf6)!important;position:fixed;z-index:1031;top:0;left:0;width:100%;height:3px;-webkit-border-radius:0 0 2px 2px;-moz-border-radius:0 0 2px 2px;border-radius:0 0 2px 2px}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;-webkit-box-shadow:0 0 10px#3b82f6,0 0 5px#3b82f6;-moz-box-shadow:0 0 10px#3b82f6,0 0 5px#3b82f6;box-shadow:0 0 10px#3b82f6,0 0 5px#3b82f6;opacity:1;-webkit-transform:rotate(3deg)translate(0px,-4px);-moz-transform:rotate(3deg)translate(0px,-4px);-ms-transform:rotate(3deg)translate(0px,-4px);-o-transform:rotate(3deg)translate(0px,-4px);transform:rotate(3deg)translate(0px,-4px)}.dark #nprogress .bar{background:-webkit-linear-gradient(left,#60a5fa,#a78bfa)!important;background:-moz-linear-gradient(left,#60a5fa,#a78bfa)!important;background:-o-linear-gradient(left,#60a5fa,#a78bfa)!important;background:linear-gradient(90deg,#60a5fa,#a78bfa)!important}.dark #nprogress .peg{-webkit-box-shadow:0 0 10px#60a5fa,0 0 5px#60a5fa;-moz-box-shadow:0 0 10px#60a5fa,0 0 5px#60a5fa;box-shadow:0 0 10px#60a5fa,0 0 5px#60a5fa}@media(max-width:768px){#nprogress .bar{height:2px}}"})})}function d(){return a.jsx(i.Suspense,{fallback:null,children:a.jsx(c,{})})}r.n(l)().configure({minimum:.3,easing:"ease",speed:500,showSpinner:!1})},95470:(e,t,r)=>{"use strict";r.d(t,{fI:()=>a});let a=process.env.NEXT_PUBLIC_UTM_SOURCE},24173:(e,t,r)=>{"use strict";r.d(t,{PK:()=>i,r$:()=>o});var a=r(10326),n=r(17577);let s=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)({isLoading:!1,message:"Loading...",type:"page"}),i=(0,n.useCallback)((e,t="Loading...",a="page")=>{r({isLoading:e,message:t,type:a})},[]),o=(0,n.useCallback)((e="Loading page...")=>{i(!0,e,"page")},[i]),l=(0,n.useCallback)((e="Loading content...")=>{i(!0,e,"content")},[i]),c=(0,n.useCallback)((e="Loading data...")=>{i(!0,e,"api")},[i]),d=(0,n.useCallback)(()=>{i(!1)},[i]);return a.jsx(s.Provider,{value:{loadingState:t,setLoading:i,showPageLoading:o,showContentLoading:l,showApiLoading:c,hideLoading:d},children:e})}function o(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useLoading must be used within a LoadingProvider");return e}},15762:(e,t,r)=>{"use strict";r.d(t,{SmartPrefetch:()=>s});var a=r(17577),n=r(35047);function s({routes:e,priority:t="low",delay:r=2e3,checkOnline:s=!0}){return function(e,t={}){(0,n.useRouter)(),(0,a.useRef)(new Set);let{priority:r="low",delay:s=2e3,condition:i}=t}(0,{priority:t,delay:r,condition:s?()=>!1:void 0}),null}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s,h1:()=>o,xf:()=>l});var a=r(41135),n=r(31009);function s(...e){return(0,n.m6)((0,a.W)(e))}function i(e){return!e||e.startsWith("mailto:")||e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/")?e:`https://${e}`}function o(e,t){if(!e||!t||e.startsWith("mailto:"))return e;try{let r=i(e),a=new URL(r);return a.searchParams.set("utm_source",t),a.toString()}catch(t){return i(e)}}function l(e){return!!e&&(!!e.startsWith("mailto:")||!e.startsWith("/")&&(!!(e.startsWith("http://")||e.startsWith("https://"))||/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)))}},48109:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k,generateMetadata:()=>w});var a=r(19510),n=r(68570);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/app/providers.tsx#AppContext`);let s=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/app/providers.tsx#Providers`),i=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/layout/Footer.tsx#Footer`),o=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/layout/Header.tsx#Header`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/hooks/useSmartPrefetch.ts#useSmartPrefetch`);let l=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/hooks/useSmartPrefetch.ts#SmartPrefetch`);function c({children:e}){return(0,a.jsxs)(a.Fragment,{children:[a.jsx(l,{routes:["/","/about","/projects","/blogs","/gallery"],priority:"low",delay:1500}),a.jsx("div",{className:"fixed inset-0 flex justify-center sm:px-8",children:a.jsx("div",{className:"flex w-full max-w-7xl lg:px-8",children:a.jsx("div",{className:"w-full shadow-xl dark:bg-muted"})})}),(0,a.jsxs)("div",{className:"relative flex w-full flex-col px-4 sm:px-0",children:[a.jsx(o,{}),a.jsx("main",{className:"flex-auto",children:e}),a.jsx(i,{})]})]})}(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/hooks/useSmartPrefetch.ts#useHoverPrefetch`),(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/hooks/useSmartPrefetch.ts#useNetworkAwarePrefetch`);let d=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/analytics/google-analytics.tsx#default`);var m=r(76934);function u(){let e=process.env.NEXT_PUBLIC_OPENPANEL_CLIENT_ID;return e?a.jsx(m.ic,{clientId:e,trackScreenViews:!0,trackAttributes:!0,trackOutgoingLinks:!0}):null}let h=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/analytics/plausible-analytics.tsx#PlausibleAnalytics`);function x(){return(0,a.jsxs)(a.Fragment,{children:[a.jsx(u,{}),a.jsx(d,{}),a.jsx(h,{})]})}(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/analytics/plausible-analytics.tsx#usePlausibleAnalytics`);let g=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/theme/ThemeInitializer.tsx#ThemeInitializer`),f=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/performance/PerformanceMonitor.tsx#PerformanceMonitor`),p=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/performance/PerformanceMonitor.tsx#PerformanceDebugger`),b=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/RouteProgressBar.tsx#RouteProgressBar`),y=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/GlobalLoadingManager.tsx#GlobalLoadingManager`),A=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ErrorBoundary.tsx#default`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ErrorBoundary.tsx#withErrorBoundary`);var v=r(14927),j=r(59979);async function w(){try{let e=await (0,j.ve)("homepage");return{...e,title:{template:"%s",default:e.title||"Jingyao Chen - International Red Dot Award Designer & Full-Stack Developer"},alternates:{...e.alternates,types:{"application/rss+xml":"http://**************:3000/feed"}}}}catch(e){return{title:{template:"%s - Jay-Yao",default:"Jay-Yao - Master's candidate in Information Science"},description:v.eF,alternates:{types:{"application/rss+xml":"http://**************:3000/feed"}}}}}function k({children:e}){return a.jsx("html",{lang:"en",className:"h-full antialiased",suppressHydrationWarning:!0,children:a.jsx("body",{className:"flex h-full",children:a.jsx(A,{children:(0,a.jsxs)(s,{children:[a.jsx(g,{}),a.jsx(b,{}),a.jsx(y,{}),a.jsx("div",{className:"flex w-full",children:a.jsx(c,{children:e})}),a.jsx(x,{}),a.jsx(f,{}),a.jsx(p,{})]})})})})}r(66092)},27683:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(19510),n=r(57371),s=r(55761);let i={primary:"bg-zinc-800 font-semibold text-zinc-100 hover:bg-zinc-700 active:bg-zinc-800 active:text-zinc-100/70 dark:bg-zinc-700 dark:hover:bg-zinc-600 dark:active:bg-zinc-700 dark:active:text-zinc-100/70",secondary:"bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70"};function o({variant:e="primary",className:t,...r}){return t=(0,s.Z)("inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none",i[e],t),void 0===r.href?a.jsx("button",{className:t,...r}):a.jsx(n.default,{className:t,...r})}var l=r(91925);function c(){return a.jsx(l.W2,{className:"flex h-full items-center pt-16 sm:pt-32",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[a.jsx("p",{className:"text-base font-semibold text-zinc-400 dark:text-zinc-500",children:"404"}),a.jsx("h1",{className:"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100",children:"Page not found"}),a.jsx("p",{className:"mt-4 text-base text-zinc-600 dark:text-zinc-400",children:"Sorry, we couldn’t find the page you’re looking for."}),a.jsx(o,{href:"/",variant:"secondary",className:"mt-4",children:"Go back home"})]})})}},91925:(e,t,r)=>{"use strict";r.d(t,{W2:()=>l});var a=r(19510),n=r(71159),s=r(55761);let i=(0,n.forwardRef)(function({className:e,children:t,...r},n){return a.jsx("div",{ref:n,className:(0,s.Z)("sm:px-8",e),...r,children:a.jsx("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:t})})}),o=(0,n.forwardRef)(function({className:e,children:t,...r},n){return a.jsx("div",{ref:n,className:(0,s.Z)("relative px-4 sm:px-8 lg:px-12",e),...r,children:a.jsx("div",{className:"mx-auto max-w-2xl lg:max-w-5xl",children:t})})}),l=(0,n.forwardRef)(function({children:e,...t},r){return a.jsx(i,{ref:r,...t,children:a.jsx(o,{children:e})})})},14927:(e,t,r)=>{"use strict";r.d(t,{Do:()=>s,eF:()=>n,u2:()=>a});let a="Jay-Yao",n="I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support.",s="<EMAIL>"},59979:(e,t,r)=>{"use strict";async function a(e){try{let t=await fetch(`http://**************:8000/api/seo/meta-tags/page/${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:3600}});if(!t.ok)return null;return await t.json()}catch(e){return null}}r.d(t,{Cl:()=>o,ve:()=>s});let n={title:"Jingyao Chen - International Red Dot Award Designer & Developer",description:"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative projects, insightful blogs, and creative journey.",keywords:"designer,developer,red dot award,portfolio,projects,blogs,web development,UI/UX design",canonical_url:"http://**************:3000",robots:"index,follow",og_title:"Jingyao Chen - Award-Winning Designer & Developer",og_description:"Discover the portfolio of an international Red Dot Award designer and full-stack developer. Innovation meets functionality.",og_image:"/images/og-default.jpg",og_url:"http://**************:3000",og_type:"website",og_site_name:"Jingyao Chen Portfolio",og_locale:"en_US",twitter_card:"summary_large_image",twitter_site:"@JingyaoC",twitter_creator:"@JingyaoC"};async function s(e){try{let t=await a(e);if(!t)return i(n);return i(t)}catch(e){return i(n)}}function i(e){return{title:e.title,description:e.description,keywords:e.keywords.split(",").map(e=>e.trim()),robots:{index:e.robots.includes("index"),follow:e.robots.includes("follow"),noarchive:e.robots.includes("noarchive"),nosnippet:e.robots.includes("nosnippet"),noimageindex:e.robots.includes("noimageindex"),nocache:e.robots.includes("nocache")},alternates:{canonical:e.canonical_url},openGraph:{title:e.og_title,description:e.og_description,url:e.og_url,siteName:e.og_site_name,images:[{url:e.og_image,width:1200,height:630,alt:e.og_title}],locale:e.og_locale,type:e.og_type},twitter:{card:e.twitter_card,site:e.twitter_site,creator:e.twitter_creator,title:e.og_title,description:e.og_description,images:[e.og_image]},authors:[{name:"Jingyao Chen"}],creator:"Jingyao Chen",publisher:"Jingyao Chen",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://**************:3000"),other:{"theme-color":"#171717","msapplication-TileColor":"#171717"}}}async function o(e,t){try{if(t){let r={...n,title:t.title?`${t.title} - Jingyao Chen Blog`:n.title,description:t.description||n.description,keywords:t.tags?t.tags.join(",")+",blog,Jingyao Chen":n.keywords,canonical_url:`http://**************:3000/blogs/${e}`,og_title:t.title?`${t.title} | Jingyao Chen Blog`:n.og_title,og_description:t.description||n.og_description,og_url:`http://**************:3000/blogs/${e}`,og_type:"article"};return i(r)}return i(n)}catch(e){return i(n)}}},9873:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(66621);let n=e=>[{type:"image/x-icon",sizes:"902x902",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},66092:()=>{}};