"use strict";exports.id=722,exports.ids=[722],exports.modules={53722:(e,t,a)=>{a.d(t,{$2:()=>l,CT:()=>o,HG:()=>r,QF:()=>c,RU:()=>h,dv:()=>s,g0:()=>i,rW:()=>n});let o="http://**************:8000/api";function i(){return{"Content-Type":"application/json"}}let n=async e=>{try{let t=await fetch(`${o}/layout-manager/manager/${e}`,{next:{revalidate:60,tags:[`layout-${e}`]},headers:{"Cache-Control":"public, s-maxage=60, stale-while-revalidate=120"}});if(!t.ok)throw Error(`Failed to fetch layout data: ${t.status} ${t.statusText}`);return await t.json()}catch(t){return{page_layout:{page_type:e,layout_name:"默认布局",description:"默认页面布局",layout_config:{},block_order:[],is_active:!0,version:"1.0"},blocks:[]}}},r=async()=>{try{let e=await fetch(`${o}/site-settings/personal-info/config`,{next:{revalidate:30,tags:["personal-info"]},headers:{"Cache-Control":"public, s-maxage=30, stale-while-revalidate=60"}});if(!e.ok)throw Error(`Failed to fetch personal info: ${e.status} ${e.statusText}`);return(await e.json()).personal_info}catch(e){throw Error(`Failed to fetch personal info: ${e}`)}},s=async()=>{try{let e=await fetch(`${o}/site-settings/social-links/config`,{next:{revalidate:300,tags:["social-links"]},headers:{"Cache-Control":"public, s-maxage=300, stale-while-revalidate=600"}});if(!e.ok)throw Error(`Failed to fetch social links: ${e.status} ${e.statusText}`);return(await e.json()).social_links}catch(e){return[{name:"Github",icon:"github",href:"https://github.com/JYao-Chen"},{name:"HuggingFace",icon:"huggingface",href:"https://huggingface.co/JYaooo"},{name:"Email",icon:"email",href:"mailto:<EMAIL>"}]}},c=async()=>{try{let e=await fetch(`${o}/site-settings/homepage-sections/config`,{next:{revalidate:30,tags:["homepage-sections"]},headers:{"Cache-Control":"public, s-maxage=30, stale-while-revalidate=60"}});if(!e.ok)throw Error("Failed to fetch homepage sections");return(await e.json()).homepage_sections}catch(e){return{projectHeadLine:"What I've been working on.",projectIntro:"I've worked on tons of little projects over the years but these are the ones that I'm most proud of. Many of them are open-source, so if you see something that piques your interest, check out the code and contribute if you have ideas for how it can be improved.",blogHeadLine:"What I've been thinking about.",blogIntro:"I've written something about programming and life.",activityHeadLine:"What I've been up to.",activityIntro:"I've been sharing my thoughts and experiences on social media."}}},l=async()=>{try{let e=await fetch(`${o}/site-settings/pages-config`,{next:{revalidate:30,tags:["pages-config"]},headers:{"Cache-Control":"public, s-maxage=30, stale-while-revalidate=60"}});if(!e.ok)throw Error("Failed to fetch pages config");return(await e.json()).pages_config}catch(e){return{blogs:{title:"Thoughts & Insights",description:"Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation."},projects:{title:"Featured Work",description:"A showcase of my research projects and applications in Large Language Models and Traditional Chinese Medicine."},gallery:{title:"Visual Gallery",description:"A collection of moments, designs, and visual stories from my research and creative journey."}}}},h=async()=>{try{let e=await fetch(`${o}/about/`);if(!e.ok)throw Error("Failed to fetch about page");return await e.json()}catch(e){return{id:0,title:"About Me",content:"# About Me\n\nWelcome to my portfolio! This page is currently being set up.",is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}}}}};