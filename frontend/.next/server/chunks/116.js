"use strict";exports.id=116,exports.ids=[116],exports.modules={6343:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},37358:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},96633:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},88307:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},33734:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},79635:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},70331:(e,t,r)=>{r(10326),r(17577),r(3574)},94046:(e,t,r)=>{r.d(t,{r:()=>c});var a=r(10326),o=r(17577),n=r(51223),s=r(6343),i=r(96633),l=r(88307);function c({content:e,className:t}){let[r,c]=(0,o.useState)([]),[d,p]=(0,o.useState)(""),[u,h]=(0,o.useState)(!1),[g,m]=(0,o.useState)(""),[f,b]=(0,o.useState)(!1),[x,y]=(0,o.useState)(!1),[v,w]=(0,o.useState)(!1),[j,_]=(0,o.useState)(0),[k,C]=(0,o.useState)(null),[$,N]=(0,o.useState)(""),[F,S]=(0,o.useState)({right:0,left:0}),E=(0,o.useRef)(null),O=(0,o.useCallback)(e=>{let t=document.getElementById(e);if(t){let e=t.getBoundingClientRect().top+window.pageYOffset+-180,r=document.documentElement.scrollHeight-window.innerHeight;e>r&&(e=Math.max(r,0)),e=Math.max(e,0),window.scrollTo({top:e,behavior:"smooth"}),y(!1)}},[]),R=r.filter(e=>e.text.toLowerCase().includes(g.toLowerCase())),T=({progress:e})=>{let t=2*Math.PI*20;return(0,a.jsxs)("div",{className:"relative w-12 h-12 group/progress",children:[(0,a.jsxs)("svg",{className:"w-12 h-12 transform -rotate-90",viewBox:"0 0 48 48",children:[a.jsx("circle",{cx:"24",cy:"24",r:20,stroke:"hsl(var(--muted))",strokeWidth:"3",fill:"none",className:"opacity-20"}),a.jsx("circle",{cx:"24",cy:"24",r:20,stroke:"hsl(var(--primary))",strokeWidth:"3",fill:"none",strokeDasharray:t,strokeDashoffset:t-e/100*t,className:"transition-all duration-300 ease-out drop-shadow-sm",strokeLinecap:"round"})]}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-semibold text-primary group-hover/progress:scale-110 transition-transform duration-200",children:[Math.round(e),"%"]})}),a.jsx("div",{className:"absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover/progress:opacity-100 transition-opacity duration-300 scale-110"})]})},M=e=>{let t=d===e.id,r=k===e.id,o=(e.level-1)*12+8;return(0,a.jsxs)("div",{className:"relative group/item",children:[(0,a.jsxs)("button",{onClick:()=>O(e.id),onMouseEnter:()=>{C(e.id),N(e.preview||"")},onMouseLeave:()=>{C(null),N("")},className:(0,n.cn)("w-full text-left text-sm transition-all duration-300 py-3 px-3 rounded-lg hover:bg-muted/50 group relative overflow-hidden hover:scale-105 hover:-translate-y-0.5 hover:shadow-lg",{"text-primary font-medium bg-gradient-to-r from-primary/15 to-primary/5 border-l-4 border-primary shadow-md scale-105":t,"text-muted-foreground hover:text-foreground":!t}),style:{paddingLeft:`${o}px`,transform:"perspective(200px) translateZ(0)",transformStyle:"preserve-3d"},children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),t&&a.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary via-primary/80 to-primary/60 rounded-r-sm shadow-sm"}),a.jsx("div",{className:"relative z-10 space-y-1",children:a.jsx("span",{className:"block group-hover:text-foreground transition-colors font-medium",children:e.text})}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"})]}),r&&e.preview&&(0,a.jsxs)("div",{className:"absolute left-full top-0 ml-4 w-80 p-4 bg-card border border-border/50 rounded-xl shadow-xl backdrop-blur-sm z-50 animate-fade-in-up",style:{transform:"perspective(300px) translateZ(20px)",transformStyle:"preserve-3d"},children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-primary",children:[a.jsx(s.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"章节预览"})]}),a.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed",children:e.preview})]}),a.jsx("div",{className:"absolute left-0 top-4 transform -translate-x-1 w-2 h-2 bg-card border-l border-t border-border/50 rotate-45"})]})]},e.id)};return u?v?a.jsx("div",{className:(0,n.cn)("transition-all duration-700 ease-in-out transform-gpu",f?"w-16":"w-full max-h-[80vh]"),style:{position:"relative"},children:(0,a.jsxs)("div",{className:(0,n.cn)("bg-card/95 backdrop-blur-md border border-border/50 shadow-xl hover:shadow-2xl group/toc overflow-hidden flex flex-col transform-gpu",f?"rounded-full w-16 h-16 items-center justify-center cursor-pointer hover:scale-110 hover:shadow-primary/20 absolute transition-all duration-500 ease-out":"rounded-2xl max-h-[80vh] relative transition-all duration-500 ease-out"),style:f?{transform:"perspective(1000px) translateZ(0) translateX(calc(100% - 2rem))",transformStyle:"preserve-3d",willChange:"transform",right:"-2rem",width:"4rem",height:"4rem"}:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d",willChange:"transform",width:"100%",height:"auto"},ref:E,onClick:f?()=>b(!1):void 0,children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover/toc:opacity-100 transition-opacity duration-500 rounded-inherit"}),(0,a.jsxs)("div",{className:(0,n.cn)("absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300",f&&"opacity-100"),children:[a.jsx("div",{className:"transform transition-all duration-500 ease-out hover:scale-105",children:a.jsx(T,{progress:j})}),(0,a.jsxs)("div",{className:"absolute -top-12 -left-16 bg-card border rounded-lg px-3 py-1 text-xs whitespace-nowrap opacity-0 group-hover/toc:opacity-100 transition-all duration-300 pointer-events-none z-50 shadow-lg",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"font-medium",children:"目录导航"}),(0,a.jsxs)("div",{className:"text-muted-foreground",children:[Math.round(j),"% 已读"]})]}),a.jsx("div",{className:"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45"})]})]}),a.jsx("div",{className:(0,n.cn)("opacity-0 transition-opacity duration-300",!f&&"opacity-100"),children:!f&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"relative flex items-center justify-between p-4 border-b border-border/30",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(T,{progress:j}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-semibold text-foreground",children:"目录导航"}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[R.length," 个章节"]})]})]}),a.jsx("button",{onClick:()=>b(!f),className:"p-2 rounded-lg hover:bg-muted/50 transition-all duration-300 hover:scale-110 group/btn",style:{transform:"perspective(100px) translateZ(5px)",transformStyle:"preserve-3d"},children:a.jsx(i.Z,{className:"w-4 h-4 group-hover/btn:scale-110 transition-transform duration-300"})})]}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col flex-1 min-h-0",children:[a.jsx("div",{className:"p-4 pb-2 flex-shrink-0",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"}),a.jsx("input",{type:"text",placeholder:"搜索章节...",value:g,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-2 text-sm border border-border/50 rounded-lg bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 transition-all duration-200"})]})}),a.jsx("div",{className:"flex-1 overflow-y-auto space-y-2 px-4 pb-4 custom-scrollbar min-h-0",children:R.length>0?R.map(M):(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[a.jsx(l.Z,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),a.jsx("p",{className:"text-sm",children:"未找到匹配的章节"})]})})]})]})})]})}):(0,a.jsxs)("div",{className:"md:hidden",children:[a.jsx("button",{onClick:()=>y(!0),className:"fixed bottom-6 right-6 z-40 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110",children:"\uD83D\uDCCB"}),x&&a.jsx("div",{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:()=>y(!1)}),a.jsx("div",{className:(0,n.cn)("fixed bottom-0 left-0 right-0 z-50 bg-card border-t rounded-t-xl transition-transform duration-300",x?"translate-y-0":"translate-y-full"),children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"目录导航"}),a.jsx("button",{onClick:()=>y(!1),className:"p-2 rounded-md hover:bg-muted/50",children:"✕"})]}),a.jsx("div",{className:"mb-3",children:a.jsx("input",{type:"text",placeholder:"搜索章节...",value:g,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 text-sm border border-muted rounded-md bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20"})}),a.jsx("div",{className:"max-h-80 overflow-y-auto space-y-2 custom-scrollbar",children:R.map(M)})]})})]}):null}},4205:(e,t,r)=>{r.d(t,{M:()=>n});var a=r(10326),o=r(41135);function n({className:e,...t}){return a.jsx("div",{className:(0,o.Z)(e,"prose prose-lg dark:prose-invert rich-text-content","prose-headings:font-semibold prose-h1:text-4xl prose-h2:text-3xl prose-h3:text-2xl prose-h4:text-xl prose-h5:text-lg prose-h6:text-base","prose-p:my-3 prose-p:leading-relaxed prose-p:text-lg sm:prose-p:text-xl","prose-a:text-primary prose-a:font-semibold prose-a:decoration-1 prose-a:underline-offset-2 hover:prose-a:decoration-2","prose-strong:font-semibold prose-strong:text-gray-900 dark:prose-strong:text-gray-100","prose-ul:list-disc prose-ul:pl-6 prose-ol:list-decimal prose-ol:pl-6","prose-li:my-1.5 prose-li:pl-2 prose-li:text-lg sm:prose-li:text-xl prose-li:leading-relaxed","prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-700 prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:text-lg prose-blockquote:my-4","prose-img:rounded-lg prose-img:mx-auto prose-img:shadow-lg","prose-code:px-2 prose-code:py-1 prose-code:rounded-md prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:text-base prose-code:font-medium","prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:rounded-lg prose-pre:p-6 prose-pre:overflow-auto prose-pre:text-base prose-pre:leading-relaxed","prose-table:border-collapse prose-table:w-full prose-table:text-base prose-table:my-6","prose-thead:bg-gray-50 dark:prose-thead:bg-gray-800","prose-th:p-3 prose-th:border prose-th:border-gray-200 dark:prose-th:border-gray-700 prose-th:font-semibold prose-th:text-left","prose-td:p-3 prose-td:border prose-td:border-gray-200 dark:prose-td:border-gray-700 prose-td:text-base","[&_.project-card]:my-4 [&_.project-card]:bg-white [&_.project-card]:dark:bg-zinc-800 [&_.project-card]:border [&_.project-card]:border-zinc-200 [&_.project-card]:dark:border-zinc-700 [&_.project-card]:rounded-lg [&_.project-card]:overflow-hidden [&_.project-card]:transition-all [&_.project-card]:duration-300","[&_.project-card:hover]:transform [&_.project-card:hover]:-translate-y-1 [&_.project-card:hover]:shadow-lg","[&_.project-card-content]:p-6","[&_.project-card-title]:text-xl [&_.project-card-title]:font-semibold [&_.project-card-title]:text-zinc-900 [&_.project-card-title]:dark:text-zinc-100 [&_.project-card-title]:mb-2","[&_.project-card-desc]:text-sm [&_.project-card-desc]:text-zinc-600 [&_.project-card-desc]:dark:text-zinc-400","[&_.project-card-link]:inline-flex [&_.project-card-link]:items-center [&_.project-card-link]:text-primary [&_.project-card-link]:text-sm [&_.project-card-link]:font-medium [&_.project-card-link]:no-underline [&_.project-card-link:hover]:text-primary-dark","[&_.project-card-link-text]:mr-2","[&_.project-card-link-icon]:w-4 [&_.project-card-link-icon]:h-4","max-w-none"),style:{columnCount:"unset",columns:"unset",columnFill:"unset",columnGap:"unset"},...t})}},23201:(e,t,r)=>{r.d(t,{AS:()=>s,K$:()=>n,WD:()=>i});let a=["#10B981","#059669","#22C55E","#16A34A","#84CC16","#60A5FA","#3B82F6","#0EA5E9","#38BDF8","#06B6D4","#A78BFA","#8B5CF6","#A855F7","#C084FC","#DDD6FE","#FB923C","#F97316","#FDBA74","#FED7AA","#FEF3C7","#F472B6","#EC4899","#F9A8D4","#FBCFE8","#FCE7F3","#22D3EE","#06B6D4","#67E8F9","#A5F3FC","#CFFAFE","#FDE047","#EAB308","#FEF08A","#FEFCE8","#FFFBEB","#94A3B8","#64748B","#CBD5E1","#E2E8F0","#F1F5F9"],o={getLuminance(e){let t=this.hexToRgb(e);if(!t)return 0;let{r,g:a,b:o}=t;return .2126*r+.7152*a+.0722*o},hexToRgb(e){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},getContrastColor(e){return this.getLuminance(e)>128?"#000000":"#FFFFFF"},withOpacity(e,t){let r=this.hexToRgb(e);return r?`rgba(${r.r}, ${r.g}, ${r.b}, ${t})`:e},getShadow(e,t=.25){return`0 4px 12px ${this.withOpacity(e,t)}`},getGradient(e,t="to right"){let r=this.withOpacity(e,.8),a=this.withOpacity(e,.6);return`linear-gradient(${t}, ${r}, ${a})`}},n={getTagStyle(e,t="default"){let r=e&&e.startsWith("#")?e:a[0];switch(t){case"minimal":return{backgroundColor:o.withOpacity(r,.08),color:o.withOpacity(r,.8),border:`1px solid ${o.withOpacity(r,.15)}`,boxShadow:"none"};case"soft":return{backgroundColor:o.withOpacity(r,.12),color:o.withOpacity(r,.9),border:`1px solid ${o.withOpacity(r,.2)}`,boxShadow:`0 1px 3px ${o.withOpacity(r,.1)}`};case"outline":return{backgroundColor:"transparent",color:o.withOpacity(r,.8),border:`1px solid ${o.withOpacity(r,.3)}`,boxShadow:`0 1px 2px ${o.withOpacity(r,.05)}`};default:return{backgroundColor:o.withOpacity(r,.15),color:o.withOpacity(r,.9),border:`1px solid ${o.withOpacity(r,.25)}`,boxShadow:`0 2px 4px ${o.withOpacity(r,.1)}`}}},getTagClasses(e,t="default"){let r="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 cursor-pointer relative overflow-hidden";return e&&e.startsWith("#")?r:`${r} bg-muted/50 text-muted-foreground border border-border/50`}},s={primary:{main:"#10B981",dark:"#059669",light:"#6EE7B7",deeper:"#047857",lighter:"#A7F3D0",subtle:"#D1FAE5"},gradients:{primary:"linear-gradient(135deg, #10B981 0%, #059669 100%)",subtle:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",card:"linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(5, 150, 105, 0.08) 100%)",glow:"radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%)",overlay:"linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%)"},shadows:{card:"0 4px 12px rgba(16, 185, 129, 0.15)",cardHover:"0 8px 25px rgba(16, 185, 129, 0.25)",glow:"0 0 20px rgba(16, 185, 129, 0.3)",glowIntense:"0 0 40px rgba(16, 185, 129, 0.4)"},animations:{duration:{fast:"200ms",normal:"300ms",slow:"500ms",slower:"700ms"},easing:{smooth:"cubic-bezier(0.4, 0, 0.2, 1)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)",elastic:"cubic-bezier(0.175, 0.885, 0.32, 1.275)"}}},i={getTimelineCardStyle:()=>({background:s.gradients.card,border:`1px solid ${o.withOpacity(s.primary.main,.2)}`,borderRadius:"1rem",boxShadow:s.shadows.card,backdropFilter:"blur(10px)",transition:`all ${s.animations.duration.normal} ${s.animations.easing.smooth}`}),getAlbumCardStyle:()=>({background:s.gradients.subtle,border:`1px solid ${o.withOpacity(s.primary.main,.15)}`,borderRadius:"1.5rem",boxShadow:s.shadows.card,backdropFilter:"blur(8px)",transition:`all ${s.animations.duration.slow} ${s.animations.easing.smooth}`}),getGridCardStyle:()=>({borderRadius:"0.75rem",overflow:"hidden",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:`all ${s.animations.duration.normal} ${s.animations.easing.smooth}`}),getHoverStyle:()=>({transform:"translateY(-8px) scale(1.02)",boxShadow:s.shadows.cardHover,borderColor:o.withOpacity(s.primary.main,.4)})};n.getTagStyle(),n.getTagClasses()},22428:(e,t,r)=>{r.d(t,{p:()=>a});function a(e){if(!e)return"Unknown date";let t=new Date(e);return isNaN(t.getTime())?"Unknown date":t.toLocaleDateString("en-US",{day:"numeric",month:"long",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1})}},58585:(e,t,r)=>{var a=r(61085);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}})},61085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return a.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return a.permanentRedirect},redirect:function(){return a.redirect}});let a=r(83953),o=r(16399);class n extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new n}delete(){throw new n}set(){throw new n}sort(){throw new n}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return a}});let r="NEXT_NOT_FOUND";function a(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{var a;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return a},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return g},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return u},isRedirectError:function(){return p},permanentRedirect:function(){return d},redirect:function(){return c}});let o=r(54580),n=r(72934),s=r(8586),i="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let a=Error(i);a.digest=i+";"+t+";"+e+";"+r+";";let n=o.requestAsyncStorage.getStore();return n&&(a.mutableCookies=n.mutableCookies),a}function c(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.PermanentRedirect)}function p(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,a,o]=e.digest.split(";",4),n=Number(o);return t===i&&("replace"===r||"push"===r)&&"string"==typeof a&&!isNaN(n)&&n in s.RedirectStatusCode}function u(e){return p(e)?e.digest.split(";",3)[2]:null}function h(e){if(!p(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function g(e){if(!p(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(a||(a={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70687:(e,t,r)=>{r.d(t,{L:()=>n,Ul:()=>o,hT:()=>l,rR:()=>i,v3:()=>s});var a=r(53722);let o="Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation.",n=async function(e,t,r){try{let o=new URLSearchParams;o.append("published_only","true"),o.append("limit","100"),r||o.append("article_type","blog"),e&&o.append("search",e),t&&o.append("tag",t);let n=`${a.CT}/blogs?${o.toString()}`,s=!e&&!t,i=await fetch(n,{...s?{next:{revalidate:60,tags:["blogs"]}}:{cache:"no-store"},headers:{"Cache-Control":s?"public, s-maxage=60, stale-while-revalidate=120":"no-cache"}});if(!i.ok)throw Error(`Failed to fetch blogs: ${i.status}`);return await i.json()||[]}catch(e){return[]}};async function s(){try{let e=`${a.CT}/blogs/homepage-content`,t=await fetch(e,{next:{revalidate:180,tags:["homepage-content","blogs","projects"]},headers:{"Cache-Control":"public, s-maxage=180, stale-while-revalidate=360"}});if(!t.ok)throw Error(`Failed to fetch homepage content: ${t.status}`);return await t.json()}catch(e){return{blogs:[],projects:[],total_blogs:0,total_projects:0}}}async function i(e){try{let t=`${a.CT}/blogs/${e}`,r=await fetch(t,{next:{revalidate:300,tags:[`blog-${e}`,"blogs"]},headers:{"Cache-Control":"public, s-maxage=300, stale-while-revalidate=600"}});if(!r.ok)throw Error(`Failed to fetch blog: ${r.status}`);let o=await r.json();if(o&&"blog"===o.article_type)return o;return null}catch(e){return null}}async function l(){try{let e=`${a.CT}/tags/with-blogs/`,t=await fetch(e,{next:{revalidate:240,tags:["tags","blogs"]},headers:{"Cache-Control":"public, s-maxage=240, stale-while-revalidate=480"}});if(!t.ok)throw Error(`Failed to fetch tags with blogs: ${t.status}`);return await t.json()||[]}catch(e){return[]}}},69619:(e,t,r)=>{r.r(t),r.d(t,{clearMarkdownCache:()=>s,getCacheStats:()=>i,processMarkdownFast:()=>n});let a={},o={htmlTags:/<[^>]+>/g,codeBlocks:/```(\w+)?\n([\s\S]*?)```/g,inlineCode:/`([^`]+)`/g,h4:/^#### (.*$)/gim,h3:/^### (.*$)/gim,h2:/^## (.*$)/gim,h1:/^# (.*$)/gim,bold:/\*\*(.*?)\*\*/g,italic:/\*(.*?)\*/g,strikethrough:/~~(.*?)~~/g,links:/\[([^\]]+)\]\(([^)]+)\)/g,icons:/!\[([^\]]*)\]\((\/api\/icons\/render\/[^)]+)\)/g,images:/!\[([^\]]*)\]\(([^)]+)\)/g,blockquotes:/^> (.+)$/gm,horizontalRule:/^---$/gm,unorderedList:/^[\s]*[-*+]\s+(.+)$/gm,orderedList:/^[\s]*\d+\.\s+(.+)$/gm,extraBr:/<br><\/p>/g,emptyP:/<p><\/p>/g};function n(e){let t=function(e){let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return t.toString()}(e);if(a[t])return a[t];try{if(!e||e.length<10)return e||"";let r=e,n=[],s=0,i=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=r.replace(o.htmlTags,e=>{let t=`__HTML_TAG_${s}__`;return n[s]=e,s++,t})).replace(o.codeBlocks,(e,t,r)=>{let a=r.trim();return`<div data-code-block="true" data-language="${t||"text"}">${a}</div>`})).replace(o.inlineCode,'<code class="inline-code">$1</code>')).replace(o.h4,"<h4>$1</h4>")).replace(o.h3,"<h3>$1</h3>")).replace(o.h2,"<h2>$1</h2>")).replace(o.h1,"<h1>$1</h1>")).replace(o.bold,"<strong>$1</strong>")).replace(o.italic,"<em>$1</em>")).replace(o.strikethrough,"<del>$1</del>")).replace(o.links,'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')).replace(o.icons,'<span data-icon-placeholder data-src="$2" data-alt="$1"></span>')).replace(o.images,'<img src="$2" alt="$1" loading="lazy" style="max-width: 100%; height: auto;" />')).replace(o.blockquotes,"<blockquote>$1</blockquote>")).replace(o.horizontalRule,"<hr>")).replace(o.unorderedList,"<li>$1</li>")).replace(o.orderedList,"<li>$1</li>")).replace(/(<li>[\s\S]*?<\/li>)/g,e=>e.includes("<ul>")||e.includes("<ol>")?e:`<ul>${e}</ul>`)).split("\n"),l=[],c=!1;for(let e of i){let t=e.trim();if(!t){c&&(l.push("</p>"),c=!1);continue}t.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)?(c&&(l.push("</p>"),c=!1),l.push(t)):(c||(l.push("<p>"),c=!0),l.push(t+"<br>"))}c&&l.push("</p>"),r=l.join("\n");for(let e=0;e<n.length;e++)r=r.replace(`__HTML_TAG_${e}__`,n[e]);return r=(r=r.replace(o.extraBr,"</p>")).replace(o.emptyP,""),a[t]=r,r}catch(o){let r=e.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>");return a[t]=r,r}}function s(){Object.keys(a).forEach(e=>delete a[e])}function i(){return{size:Object.keys(a).length,keys:Object.keys(a)}}},96421:(e,t,r)=>{r.d(t,{Q:()=>d});var a=r(70687),o=r(6879),n=r(71159),s=r(69619);let i=(0,n.cache)(async(e,t)=>{performance.now();try{if(!e||e.length<10)return e||"";let t=(0,s.processMarkdownFast)(e);return performance.now(),t}catch(t){return e.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>")}});async function l(e,t,r){let a=performance.now(),o=r?.includeStats||!1;try{let n;let l=!1;if(r?.skipCache)n=(0,s.processMarkdownFast)(e);else{let r=(0,s.getCacheStats)().size;n=await i(e,t),l=(0,s.getCacheStats)().size===r}let c=performance.now(),d={content:n};return o&&(d.stats={processingTime:c-a,cacheHit:l,contentLength:e.length}),d}catch(t){return{content:e.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>"),...o&&{stats:{processingTime:performance.now()-a,cacheHit:!1,contentLength:e.length}}}}}class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}async warmupCache(e="medium"){this.isWarming||(this.isWarming=!0,this.isWarming=!1)}getCacheInfo(){return{...(0,s.getCacheStats)(),isWarming:this.isWarming}}constructor(){this.isWarming=!1}}async function d(e,t,r){let n=r;try{if(!n){if("project"===t){let t=await (0,o.IQ)(e);n=t?.content}else{let t=await (0,a.rR)(e);if(!(n=t?.content)){let t=await (0,o.IQ)(e);n=t?.content}}}if(!n)throw Error(`Failed to load content for slug: ${e}`);return(await l(n,e,{includeStats:!1})).content}catch(t){throw Error(`Failed to load content for slug: ${e}`)}}c.getInstance()},6879:(e,t,r)=>{r.d(t,{IQ:()=>s,V$:()=>n});var a=r(53722);function o(e){let t=e.project_url?{href:e.project_url,label:e.title}:void 0,r=e.tags?.filter(e=>"tech"===e.category)||[],a=r.length>0?r[0]:null,o=!!(e.content&&e.content.trim().length>0);return{id:e.id,slug:e.slug,name:e.title,description:e.description||"",link:t,github_link:e.github_url,logo:e.logo_url,icon:e.icon||null,featured:e.featured,type:"work",is_github_project:e.is_github_project,has_detail_page:o,content:e.content,detail_content:e.content,project_order:e.display_order,date:e.display_date||e.date||e.created_at,display_date:e.display_date||e.date,created_at:e.created_at,updated_at:e.updated_at,category:a?.name,category_icon:a?.icon||null,tags:e.tags?.map(e=>e.name)||[],tag_objects:e.tags||[],tech_stack:e.tags?.filter(e=>"tech"===e.category).map(e=>e.name)||[],categories:[],module_categories:[]}}async function n(){try{let e=`${a.CT}/blogs/?article_type=project&published_only=true`,t=await fetch(e,{next:{revalidate:10,tags:["projects"]},headers:{"Cache-Control":"public, s-maxage=10, stale-while-revalidate=30",...(0,a.g0)()}});if(!t.ok)throw Error(`Failed to fetch projects: ${t.status}`);let r=(await t.json()).map(o),n={};r.forEach(e=>{let t="其他",r=null,a=null,o=999,s=0,i="other";if(e.module_categories&&e.module_categories.length>0){let n=e.module_categories[0];t=n.name,r=n.icon,a=n.description||null,o=n.display_order||999,s=n.id,i=n.slug}else if(e.tag_objects&&e.tag_objects.length>0){let n=e.tag_objects[0];t=n.name,r=n.icon||null,a=n.description||null,o=500,s=n.id,i=n.slug||n.name.toLowerCase().replace(/\s+/g,"-")}else e.tags&&e.tags.length>0&&(t=e.tags[0],r=null,a=null,o=500,s=0,i=e.tags[0].toLowerCase().replace(/\s+/g,"-"));n[t]||(n[t]={projects:[],icon:r,description:a,display_order:o,id:s,slug:i}),n[t].projects.push(e)}),Object.keys(n).forEach(e=>{n[e].projects.sort((e,t)=>{let r=e.project_order||999,a=t.project_order||999;return r!==a?r-a:e.name.localeCompare(t.name)})});let s=Object.entries(n).map(([e,t])=>({categoryName:e,...t}));return s.sort((e,t)=>{let r=e.display_order,a=t.display_order;return r!==a?r-a:e.categoryName.localeCompare(t.categoryName)}),s}catch(e){return[]}}async function s(e){try{let t=`${a.CT}/blogs/${e}`,r=await fetch(t,{next:{revalidate:10,tags:[`project-${e}`,"projects"]},headers:{"Cache-Control":"public, s-maxage=10, stale-while-revalidate=30",...(0,a.g0)()}});if(!r.ok)throw Error(`Failed to fetch project: ${r.status}`);let n=await r.json();if("project"===n.article_type)return o(n);return null}catch(e){return null}}}};