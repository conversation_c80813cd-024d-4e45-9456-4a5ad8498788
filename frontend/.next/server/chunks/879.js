"use strict";exports.id=879,exports.ids=[879],exports.modules={24230:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},48998:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},36283:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},94893:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},1572:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},93879:(e,t,a)=>{a.d(t,{s:()=>D});var r=a(10326),i=a(17577),n=a(36283),l=a(1572),s=a(24230),o=a(12714),d=a(78357),c=a(61711);function u({isActive:e,color:t="#3B82F6",particleCount:a=20,className:n=""}){let l=(0,i.useRef)(null),s=(0,i.useRef)(),o=(0,i.useRef)([]),[d,c]=(0,i.useState)({width:0,height:0}),u=(e,a)=>({x:e,y:a,vx:(Math.random()-.5)*2,vy:(Math.random()-.5)*2,life:0,maxLife:60+60*Math.random(),size:1+3*Math.random(),color:t,opacity:.8}),g=e=>(e.x+=e.vx,e.y+=e.vy,e.life++,e.opacity=.8*(1-e.life/e.maxLife),e.life<e.maxLife&&e.x>0&&e.x<d.width&&e.y>0&&e.y<d.height),p=()=>{let e=l.current;if(!e)return;let t=e.getContext("2d");t&&(t.clearRect(0,0,d.width,d.height),o.current.forEach(e=>{t.save(),t.globalAlpha=e.opacity,t.fillStyle=e.color,t.beginPath(),t.arc(e.x,e.y,e.size,0,2*Math.PI),t.fill(),t.restore()}))},h=()=>{o.current=o.current.filter(g),p(),(e||o.current.length>0)&&(s.current=requestAnimationFrame(h))},x=(e,t,a=5)=>{for(let r=0;r<a;r++)o.current.push(u(e,t))};return r.jsx("canvas",{ref:l,className:`absolute inset-0 pointer-events-none ${n}`,onMouseMove:t=>{if(!e)return;let a=l.current?.getBoundingClientRect();if(!a)return;let r=t.clientX-a.left,i=t.clientY-a.top;.3>Math.random()&&x(r,i,2)},onClick:e=>{let t=l.current?.getBoundingClientRect();t&&x(e.clientX-t.left,e.clientY-t.top,15)},style:{width:"100%",height:"100%",pointerEvents:e?"auto":"none"}})}function g({children:e,className:t="",color:a="rgba(255, 255, 255, 0.3)"}){let[n,l]=(0,i.useState)([]);return(0,r.jsxs)("div",{className:`relative overflow-hidden ${t}`,onClick:e=>{let t=e.currentTarget.getBoundingClientRect(),a=e.clientX-t.left,r=e.clientY-t.top,i=Date.now();l(e=>[...e,{x:a,y:r,id:i}]),setTimeout(()=>{l(e=>e.filter(e=>e.id!==i))},600)},children:[e,n.map(e=>r.jsx("span",{className:"absolute rounded-full animate-ping",style:{left:e.x-10,top:e.y-10,width:20,height:20,backgroundColor:a,animationDuration:"0.6s"}},e.id))]})}var p=a(94893),h=a(62881);let x=(0,h.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var y=a(48998);let m=(0,h.Z)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),b=(0,h.Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),f={active:{icon:p.Z,color:"#10B981",bgColor:"#10B98120",label:"Active",description:"Currently active and maintained"},completed:{icon:x,color:"#059669",bgColor:"#05966920",label:"Completed",description:"Project completed successfully"},"in-progress":{icon:y.Z,color:"#F59E0B",bgColor:"#F59E0B20",label:"In Progress",description:"Currently under development"},paused:{icon:m,color:"#6B7280",bgColor:"#6B728020",label:"Paused",description:"Development temporarily paused"},archived:{icon:b,color:"#EF4444",bgColor:"#EF444420",label:"Archived",description:"No longer maintained"}};function v({status:e,className:t="",showLabel:a=!1}){let i=f[e],n=i.icon;return(0,r.jsxs)("div",{className:`inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 ${t}`,style:{backgroundColor:i.bgColor,color:i.color,border:`1px solid ${i.color}40`},title:i.description,children:[r.jsx(n,{size:12}),a&&r.jsx("span",{children:i.label}),r.jsx("div",{className:"w-2 h-2 rounded-full animate-pulse",style:{backgroundColor:i.color}})]})}let k={React:"#61DAFB",Vue:"#4FC08D",Angular:"#DD0031","Next.js":"#000000","Nuxt.js":"#00DC82",TypeScript:"#3178C6",JavaScript:"#F7DF1E",HTML:"#E34F26",CSS:"#1572B6",Tailwind:"#06B6D4",SCSS:"#CC6699","Node.js":"#339933",Python:"#3776AB",Java:"#007396",Go:"#00ADD8",Rust:"#000000",PHP:"#777BB4","C#":"#239120",Ruby:"#CC342D",MySQL:"#4479A1",PostgreSQL:"#336791",MongoDB:"#47A248",Redis:"#DC382D",SQLite:"#003B57",AWS:"#FF9900",Azure:"#0078D4",GCP:"#4285F4",Docker:"#2496ED",Kubernetes:"#326CE5",Vercel:"#000000",Netlify:"#00C7B7",Git:"#F05032",GitHub:"#181717",GitLab:"#FC6D26",Figma:"#F24E1E",Sketch:"#F7B500"};function w({techStack:e,maxVisible:t=5,className:a=""}){let i=e.slice(0,t),n=e.length-t;return(0,r.jsxs)("div",{className:`flex items-center gap-2 ${a}`,children:[(0,r.jsxs)("div",{className:"flex -space-x-2",children:[i.map((e,t)=>r.jsx("div",{className:"relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10",style:{backgroundColor:k[e]||"#6B7280",zIndex:i.length-t},title:e,children:e.charAt(0).toUpperCase()},e)),n>0&&(0,r.jsxs)("div",{className:"relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-500 flex items-center justify-center text-xs font-bold text-white shadow-lg",title:`+${n} more technologies`,children:["+",n]})]}),(0,r.jsxs)("div",{className:"flex-1 max-w-20",children:[r.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:r.jsx("div",{className:"h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500",style:{width:`${Math.min(e.length/10*100,100)}%`}})}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.length," tech",1!==e.length?"s":""]})]})]})}function j({stats:e,className:t=""}){let a=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),i=[{key:"stars",label:"Stars",icon:"⭐",value:e.stars},{key:"forks",label:"Forks",icon:"\uD83C\uDF74",value:e.forks},{key:"views",label:"Views",icon:"\uD83D\uDC41️",value:e.views},{key:"downloads",label:"Downloads",icon:"⬇️",value:e.downloads},{key:"commits",label:"Commits",icon:"\uD83D\uDCDD",value:e.commits},{key:"contributors",label:"Contributors",icon:"\uD83D\uDC65",value:e.contributors}].filter(e=>void 0!==e.value&&e.value>0);return 0===i.length?null:r.jsx("div",{className:`grid grid-cols-2 gap-2 ${t}`,children:i.slice(0,4).map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105",children:[r.jsx("span",{className:"text-xs",children:e.icon}),r.jsx("span",{className:"font-medium",children:a(e.value)}),r.jsx("span",{className:"text-gray-500 dark:text-gray-400 truncate",children:e.label})]},e.key))})}function C({priority:e,className:t=""}){let a={high:{color:"#EF4444",label:"High Priority",intensity:3},medium:{color:"#F59E0B",label:"Medium Priority",intensity:2},low:{color:"#10B981",label:"Low Priority",intensity:1}}[e];return r.jsx("div",{className:`flex items-center gap-1 ${t}`,title:a.label,children:Array.from({length:3}).map((e,t)=>r.jsx("div",{className:`w-1 h-3 rounded-full transition-all duration-300 ${t<a.intensity?"opacity-100":"opacity-30"}`,style:{backgroundColor:a.color}},t))})}let N=()=>({measureText:(0,i.useCallback)((e,t,a=14,r=1.5)=>{if("undefined"==typeof document)return{lines:1,height:a*r};let i=document.createElement("canvas").getContext("2d");if(!i)return{lines:1,height:a*r};i.font=`${a}px system-ui, -apple-system, sans-serif`;let n=e.split(" "),l=[],s="";for(let e of n){let a=s?`${s} ${e}`:e;i.measureText(a).width>t&&s?(l.push(s),s=e):s=a}return s&&l.push(s),{lines:l.length,height:l.length*a*r,wrappedText:l}},[])}),$=e=>({web:{primary:"#10B981",secondary:"#059669",accent:"#6EE7B7",background:"linear-gradient(135deg, #10B981 0%, #059669 100%)",cardBg:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",shadow:"rgba(16, 185, 129, 0.25)",glow:"rgba(16, 185, 129, 0.4)"},mobile:{primary:"#14B8A6",secondary:"#0D9488",accent:"#5EEAD4",background:"linear-gradient(135deg, #14B8A6 0%, #0D9488 100%)",cardBg:"linear-gradient(135deg, rgba(20, 184, 166, 0.08) 0%, rgba(13, 148, 136, 0.05) 100%)",shadow:"rgba(20, 184, 166, 0.25)",glow:"rgba(20, 184, 166, 0.4)"},ai:{primary:"#84CC16",secondary:"#65A30D",accent:"#BEF264",background:"linear-gradient(135deg, #84CC16 0%, #65A30D 100%)",cardBg:"linear-gradient(135deg, rgba(132, 204, 22, 0.08) 0%, rgba(101, 163, 13, 0.05) 100%)",shadow:"rgba(132, 204, 22, 0.25)",glow:"rgba(132, 204, 22, 0.4)"},opensource:{primary:"#16A34A",secondary:"#15803D",accent:"#86EFAC",background:"linear-gradient(135deg, #16A34A 0%, #15803D 100%)",cardBg:"linear-gradient(135deg, rgba(22, 163, 74, 0.08) 0%, rgba(21, 128, 61, 0.05) 100%)",shadow:"rgba(22, 163, 74, 0.25)",glow:"rgba(22, 163, 74, 0.4)"},backend:{primary:"#047857",secondary:"#065F46",accent:"#A7F3D0",background:"linear-gradient(135deg, #047857 0%, #065F46 100%)",cardBg:"linear-gradient(135deg, rgba(4, 120, 87, 0.08) 0%, rgba(6, 95, 70, 0.05) 100%)",shadow:"rgba(4, 120, 87, 0.25)",glow:"rgba(4, 120, 87, 0.4)"},design:{primary:"#06B6D4",secondary:"#0891B2",accent:"#67E8F9",background:"linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)",cardBg:"linear-gradient(135deg, rgba(6, 182, 212, 0.08) 0%, rgba(8, 145, 178, 0.05) 100%)",shadow:"rgba(6, 182, 212, 0.25)",glow:"rgba(6, 182, 212, 0.4)"},game:{primary:"#22C55E",secondary:"#16A34A",accent:"#BBF7D0",background:"linear-gradient(135deg, #22C55E 0%, #16A34A 100%)",cardBg:"linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(22, 163, 74, 0.05) 100%)",shadow:"rgba(34, 197, 94, 0.25)",glow:"rgba(34, 197, 94, 0.4)"},default:{primary:"#10B981",secondary:"#059669",accent:"#6EE7B7",background:"linear-gradient(135deg, #10B981 0%, #059669 100%)",cardBg:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",shadow:"rgba(16, 185, 129, 0.25)",glow:"rgba(16, 185, 129, 0.4)"}})[(()=>{let t=e.categories?.[0]?.name?.toLowerCase()||"",a=e.name.toLowerCase(),r=e.description?.toLowerCase()||"";return t.includes("web")||a.includes("web")||r.includes("website")?"web":t.includes("mobile")||t.includes("app")||a.includes("app")?"mobile":t.includes("ai")||t.includes("ml")||r.includes("machine learning")?"ai":t.includes("open")||"opensource"===e.type?"opensource":t.includes("backend")||t.includes("api")||r.includes("server")?"backend":t.includes("design")||t.includes("ui")||r.includes("design")?"design":t.includes("game")||r.includes("game")?"game":"default"})()];function D({project:e,index:t,categoryIndex:a,onPreview:p}){let[h,x]=(0,i.useState)(!1),[y,m]=(0,i.useState)(320),[b,f]=(0,i.useState)({x:0,y:0}),[k,D]=(0,i.useState)(!1),B=(0,i.useRef)(null),A=(0,i.useRef)(null),{measureText:S}=N();(function(e,t=.3){})(0,.2),function(e,t=15){}(0,8);let F=(0,i.useCallback)(()=>!!e.description&&!!y&&S(e.description,y-48,14,1.5).lines>4,[e.description,y,S])(),z=$(e);return(0,r.jsxs)("li",{ref:B,className:"group relative flex flex-col items-start animate-fade-in-up focus-within:outline-none w-full",style:{animationDelay:`${200*a+100*t}ms`,height:"480px",minWidth:"320px",maxWidth:"100%"},onMouseEnter:()=>{x(!0),D(!0)},onMouseLeave:()=>{x(!1),D(!1)},onMouseMove:e=>{if(B.current){let t=B.current.getBoundingClientRect();f({x:e.clientX-t.left,y:e.clientY-t.top})}},role:"article","aria-label":`Project: ${e.name}`,tabIndex:0,children:[r.jsx("div",{className:"absolute -inset-2 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse-slow",style:{background:`radial-gradient(circle at ${b.x}px ${b.y}px, ${z.glow}, transparent 70%)`}}),r.jsx("div",{className:"absolute -inset-1 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500",style:{background:z.cardBg}}),(0,r.jsxs)("div",{className:"relative w-full h-full backdrop-blur-xl rounded-2xl border shadow-2xl overflow-hidden project-card-hover focus-within:outline-none",style:{background:`linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(255, 255, 255, 0.90) 50%,
            rgba(255, 255, 255, 0.85) 100%)`,borderColor:`${z.primary}40`,boxShadow:`0 25px 50px -12px ${z.shadow}, 0 0 0 1px ${z.primary}20`},children:[r.jsx("div",{className:"absolute inset-0 rounded-2xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none",style:{boxShadow:`0 0 0 3px ${z.primary}60, 0 0 0 6px ${z.primary}20`}}),r.jsx("div",{className:"absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300",style:{background:`linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.90) 50%,
              rgba(51, 65, 85, 0.85) 100%)`}}),r.jsx("div",{className:"absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300",style:{background:`linear-gradient(135deg,
                 rgba(15, 23, 42, 0.95) 0%,
                 rgba(30, 41, 59, 0.90) 50%,
                 rgba(51, 65, 85, 0.85) 100%)`}}),r.jsx(u,{isActive:k,color:z.primary,particleCount:15,className:"z-0"}),r.jsx("div",{className:"absolute top-0 left-0 right-0 h-1 opacity-80 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none",style:{background:z.background}}),r.jsx("div",{className:"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none",style:{background:`radial-gradient(circle at 50% 0%, ${z.primary}20, transparent 50%)`}}),(0,r.jsxs)("div",{className:"relative z-10 h-full p-3 sm:p-4 lg:p-6 flex flex-col justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-3",children:[(0,r.jsxs)("div",{ref:A,className:"relative flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 flex-shrink-0",style:{background:z.background,boxShadow:`0 6px 24px ${z.shadow}, inset 0 1px 0 rgba(255, 255, 255, 0.2)`},role:"img","aria-label":`${e.name} project icon`,children:[e.icon?e.icon.includes(":")?r.jsx(d.CustomIcon,{name:e.icon,size:24}):r.jsx(c.AntdIcon,{iconName:e.icon,size:24,className:"text-white"}):e.has_detail_page?r.jsx(n.Z,{size:24,className:"text-white"}):r.jsx(l.Z,{size:24,className:"text-white"}),r.jsx("div",{className:"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur-md",style:{background:`radial-gradient(circle, ${z.glow}, transparent 70%)`}}),r.jsx("div",{className:"absolute inset-0 rounded-xl border-2 opacity-0 group-hover:opacity-40 transition-opacity duration-300",style:{borderImage:`linear-gradient(45deg, ${z.primary}, ${z.secondary}, ${z.accent}) 1`,animation:h?"spin 3s linear infinite":"none"}}),r.jsx("div",{className:"absolute inset-1 rounded-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300",style:{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent 50%)"}})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[r.jsx(v,{status:e.status||"active",showLabel:!1}),e.priority&&r.jsx(C,{priority:e.priority})]})]}),(0,r.jsxs)("div",{className:"mb-2",children:[r.jsx("h3",{className:"text-sm sm:text-base font-bold text-gray-900 dark:text-white leading-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300 line-clamp-2",style:{backgroundImage:h?`linear-gradient(135deg, ${z.primary}, ${z.secondary})`:void 0},id:`project-title-${e.name.replace(/\s+/g,"-").toLowerCase()}`,title:e.name,children:e.name}),r.jsx("div",{className:"h-0.5 transition-all duration-500 group-hover:w-full rounded-full",style:{width:h?"100%":"0%",background:z.background,boxShadow:`0 0 8px ${z.glow}`}})]}),(0,r.jsxs)("div",{className:"flex-1 mb-3 min-h-[80px] flex flex-col",children:[r.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-300 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 line-clamp-4","aria-describedby":`project-title-${e.name.replace(/\s+/g,"-").toLowerCase()}`,title:e.description,children:e.description}),F&&(0,r.jsxs)("div",{className:"mt-2 text-xs opacity-60 group-hover:opacity-100 transition-opacity duration-300 flex items-center gap-1",style:{color:z.primary},children:[r.jsx("span",{className:"inline-block animate-pulse",children:"\uD83D\uDCD6"}),r.jsx("span",{children:"Click to read more"})]}),r.jsx("div",{className:"flex-1"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[e.tech_stack&&e.tech_stack.length>0&&r.jsx(w,{techStack:e.tech_stack,maxVisible:3}),e.stats&&r.jsx(j,{stats:e.stats})]}),e.categories&&e.categories.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-1.5",children:[e.categories.slice(0,2).map((e,t)=>r.jsx("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105",style:{backgroundColor:`${e.color||z.primary}15`,borderColor:`${e.color||z.primary}30`,color:e.color||z.primary},children:e.name},t)),e.categories.length>2&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full border border-gray-200 dark:border-gray-700",children:["+",e.categories.length-2]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between gap-2 relative z-30 mt-auto",children:[r.jsx(g,{className:"rounded-lg flex-1",children:(0,r.jsxs)("a",{href:e.has_detail_page&&e.slug?`/projects/${e.slug}`:"string"==typeof e.link?e.link:e.link?.href?e.link.href:"#",className:"inline-flex items-center justify-center gap-1.5 px-3 py-2 text-xs font-medium text-white rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 magnetic-effect w-full relative overflow-hidden z-30",style:{background:z.background,boxShadow:`0 4px 20px ${z.shadow}`,minHeight:"36px"},target:e.has_detail_page?"_self":"_blank",rel:e.has_detail_page?void 0:"noopener noreferrer","aria-label":`${e.has_detail_page?"View details for":"Visit"} ${e.name}`,children:[r.jsx("div",{className:"absolute inset-0 opacity-0 hover:opacity-20 transition-opacity duration-300 pointer-events-none",style:{background:"linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent)"}}),r.jsx("span",{className:"relative z-10 truncate",children:e.has_detail_page?"Details":"Visit"}),r.jsx(s.Z,{size:12,className:"relative z-10 transition-transform duration-300 group-hover:translate-x-1 flex-shrink-0"})]})}),p&&r.jsx(g,{className:"rounded-full flex-shrink-0",children:r.jsx("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),p(e)},className:"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 hover:scale-110 magnetic-effect relative z-30 rounded-full",style:{minWidth:"36px",minHeight:"36px"},"aria-label":`Preview ${e.name}`,children:r.jsx(o.Z,{size:14})})})]})]})]}),r.jsx("div",{className:"absolute pointer-events-none opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-full blur-xl",style:{width:"120px",height:"120px",background:`radial-gradient(circle, ${z.glow}, ${z.primary}30, transparent)`,left:b.x-60,top:b.y-60,transform:"translate3d(0, 0, 0)"}}),r.jsx("div",{className:"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none",style:{background:`linear-gradient(135deg, transparent, ${z.primary}20, transparent)`,filter:"blur(1px)"}})]})]})}},61711:(e,t,a)=>{a.d(t,{AntdIcon:()=>n});var r=a(10326),i=a(17577);let n=({iconName:e,size:t=20,className:n=""})=>{let[l,s]=(0,i.useState)(null),[o,d]=(0,i.useState)(!0),[c,u]=(0,i.useState)(!1),[g,p]=(0,i.useState)(!1),[h,x]=(0,i.useState)("");(0,i.useEffect)(()=>{if(!e){d(!1),u(!0);return}if(e.startsWith("emoji:")){p(!0),x(e.split(":")[1]),d(!1),u(!1);return}let t=!0;return d(!0),p(!1),a.e(990).then(a.bind(a,24990)).then(a=>{if(!t)return;let r=a[e];if(!r)throw Error(`Icon "${e}" not found in @ant-design/icons`);s(()=>r),u(!1)}).catch(e=>{t&&u(!0)}).finally(()=>{t&&d(!1)}),()=>{t=!1}},[e]);let y={fontSize:`${t}px`},m={display:"inline-flex",alignItems:"center",justifyContent:"center",fontSize:`${t}px`,width:`${t}px`,height:`${t}px`},b={display:"inline-flex",alignItems:"center",justifyContent:"center",width:`${t}px`,height:`${t}px`,fontSize:`${t/2}px`,fontWeight:"bold",color:"#ffffff",backgroundColor:c?"#ff4d4f":"#1890ff",borderRadius:"50%"};if(g)return r.jsx("span",{className:`antd-icon-emoji ${n}`,style:m,children:h});if(o)return r.jsx("span",{className:`antd-icon-loading ${n}`,style:b,children:"•••"});if(c||!l){let t=e?e.charAt(0).toUpperCase():"?";return r.jsx("span",{className:`antd-icon-error ${n}`,style:b,children:t})}return r.jsx(l,{className:n,style:y})}}};