(()=>{var e={};e.id=675,e.ids=[675],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},43342:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>m,routeModule:()=>x,tree:()=>d}),t(94473),t(48109),t(27683);var a=t(23191),s=t(88716),n=t(37922),i=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["version-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94473)),"/home/<USER>/Code/me/My-web/frontend/src/app/version-history/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],m=["/home/<USER>/Code/me/My-web/frontend/src/app/version-history/page.tsx"],c="/version-history/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/version-history/page",pathname:"/version-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74622:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,79404,23)),Promise.resolve().then(t.bind(t,39414)),Promise.resolve().then(t.bind(t,56006))},27162:(e,r,t)=>{"use strict";t.d(r,{Z:()=>l});var a=t(71159);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:o="",children:l,iconNode:d,...m},c)=>(0,a.createElement)("svg",{ref:c,...i,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:n("lucide",o),...m},[...d.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(l)?l:[l]])),l=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...i},l)=>(0,a.createElement)(o,{ref:l,iconNode:r,className:n(`lucide-${s(e)}`,t),...i}));return t.displayName=`${e}`,t}},82688:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(27162).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},94473:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b,metadata:()=>v,revalidate:()=>y,runtime:()=>g});var a=t(19510),s=t(91925),n=t(68570);let i=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx#WalineComment`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx#default`);let o=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/comment/CommentStats.tsx#CommentStats`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/comment/CommentStats.tsx#default`);var l=t(57371),d=t(27162);let m=(0,d.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var c=t(82688);let p=(0,d.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),x=(0,d.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),h=(0,d.Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),u=(0,d.Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),g="nodejs",y=3600,v={title:"Version History",description:"Website development history and version updates"};async function f(){try{let e=await fetch("http://**************:8000/api/website-versions/?published_only=true&size=50",{next:{revalidate:3600},headers:{"Content-Type":"application/json"}});if(!e.ok)return[];return(await e.json()).versions||[]}catch(e){return[]}}async function b(){let e=await f();return(0,a.jsxs)(s.W2,{className:"mt-16 sm:mt-32",children:[(0,a.jsxs)("header",{className:"max-w-2xl",children:[a.jsx("h1",{className:"text-4xl font-bold tracking-tight text-foreground sm:text-5xl",children:"Version History"}),a.jsx("p",{className:"mt-6 text-base text-muted-foreground",children:"Explore the development journey of this website. Each version represents a milestone in our continuous improvement and feature enhancement."}),a.jsx("div",{className:"mt-4",children:a.jsx(o,{path:"/version-history",showIcons:!0})})]}),a.jsx("div",{className:"mt-16 sm:mt-20",children:0===e.length?a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"No version history available yet."})}):a.jsx(j,{versions:e})}),a.jsx("section",{className:"mt-24 mb-12",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"}),a.jsx("div",{className:"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent"}),(0,a.jsxs)("div",{className:"pt-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm",children:[a.jsx("div",{className:"p-2 rounded-xl bg-primary/10 text-primary",children:a.jsx(m,{className:"w-5 h-5"})}),a.jsx("span",{className:"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase",children:"Development Journey"})]}),a.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3",children:"What's Your Take?"}),a.jsx("p",{className:"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed",children:"Every version tells a story of growth and innovation. Share your thoughts on this development journey."})]}),a.jsx(i,{path:"/version-history",title:"Version History",className:"max-w-5xl mx-auto"})]})]})})]})}function j({versions:e}){let r=(e=>{let r={};return e.forEach(e=>{try{let t=new Date(e.release_date),a=isNaN(t.getTime())?"2024":t.getUTCFullYear().toString();r[a]||(r[a]=[]),r[a].push(e)}catch(t){r["2024"]||(r["2024"]=[]),r["2024"].push(e)}}),Object.entries(r).map(([e,r])=>({year:e,versions:r.sort((e,r)=>{try{let t=new Date(e.release_date).getTime();return new Date(r.release_date).getTime()-t}catch(e){return 0}})})).sort((e,r)=>parseInt(r.year)-parseInt(e.year))})(e);return(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/70 via-primary/40 via-border to-transparent shadow-sm"}),a.jsx("div",{className:"absolute left-[31px] top-0 bottom-0 w-px bg-gradient-to-b from-primary/20 via-transparent to-transparent"}),a.jsx("div",{className:"space-y-20",children:r.map((e,t)=>(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"relative mb-16",children:a.jsx("div",{className:"ml-24 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-xl p-6 border border-primary/20 shadow-lg backdrop-blur-sm",children:a.jsx("div",{className:"flex items-center justify-between flex-wrap gap-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)("h2",{className:"text-4xl font-bold text-foreground flex items-center gap-4 group",children:[a.jsx("div",{className:"p-2 rounded-xl bg-primary/10 text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300 shadow-lg",children:a.jsx(c.Z,{className:"w-8 h-8"})}),a.jsx("span",{className:"bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent drop-shadow-sm",children:e.year})]}),(0,a.jsxs)("div",{className:"px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium border border-primary/20 hover:bg-primary/20 hover:scale-105 transition-all duration-300 shadow-md",children:[e.versions.length," version",1!==e.versions.length?"s":""]})]})})})}),a.jsx("div",{className:"space-y-16",children:e.versions.map((e,s)=>{let n=r.slice(0,t).reduce((e,r)=>e+r.versions.length,0)+s;return a.jsx(w,{version:e,isLatest:0===n,index:n,showDateOnNode:!0},e.id)})})]},e.year))}),(0,a.jsxs)("div",{className:"relative mt-20",children:[a.jsx("div",{className:"absolute left-4 w-8 h-8 rounded-full bg-gradient-to-br from-primary via-blue-500 to-secondary shadow-xl shadow-primary/20 flex items-center justify-center animate-pulse",children:a.jsx("div",{className:"w-3 h-3 rounded-full bg-white shadow-sm"})}),(0,a.jsxs)("div",{className:"ml-24 p-4 rounded-xl bg-gradient-to-r from-muted/50 to-transparent border border-border/30 backdrop-blur-sm",children:[a.jsx("div",{className:"text-sm text-muted-foreground italic font-medium",children:"✨ End of timeline"}),a.jsx("div",{className:"text-xs text-muted-foreground/70 mt-1",children:"Thanks for following our development journey"})]})]})]})}function w({version:e,isLatest:r,index:t,showDateOnNode:s=!1}){let n=e=>e?e.split(",").map(e=>e.trim()).filter(Boolean):[];return(0,a.jsxs)("article",{className:"relative group/version",children:[(0,a.jsxs)("div",{className:"absolute left-2 top-8 flex items-center gap-3 z-10",children:[(0,a.jsxs)("div",{className:"w-6 h-6 rounded-full bg-background border-3 border-primary shadow-xl shadow-primary/20 group-hover/version:scale-125 group-hover/version:shadow-2xl group-hover/version:shadow-primary/30 transition-all duration-500",style:{transform:"perspective(500px) translateZ(0)",transformStyle:"preserve-3d"},children:[a.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-primary/5 group-hover/version:from-primary/40 group-hover/version:to-primary/10 transition-all duration-500"}),a.jsx("div",{className:"absolute inset-1 rounded-full bg-primary/80 group-hover/version:bg-primary transition-all duration-500"})]}),s&&a.jsx("div",{className:"px-3 py-1 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 text-primary text-xs font-medium border border-primary/20 shadow-md backdrop-blur-sm whitespace-nowrap",children:(e=>{try{let r=new Date(e);if(isNaN(r.getTime()))return e;return r.toLocaleDateString("en-US",{month:"short",day:"numeric",timeZone:"UTC"})}catch(r){return e}})(e.release_date)})]}),a.jsx("div",{className:"ml-24 group-hover/version:translate-x-2 transition-transform duration-500",children:(0,a.jsxs)("div",{className:"bg-card border rounded-xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border-l-4 border-l-primary/30 hover:border-l-primary group/card",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 flex-wrap",children:[a.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${r?"bg-primary/10 text-primary ring-2 ring-primary/20":e.is_major?"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 ring-2 ring-red-500/20":"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 ring-2 ring-blue-500/20"}`,children:e.version}),r&&a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 animate-pulse",children:"Latest"}),e.is_major&&a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",children:"Major Release"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[a.jsx(c.Z,{size:14}),a.jsx("span",{children:(e=>{try{let r=new Date(e);if(isNaN(r.getTime()))return e;return r.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",timeZone:"UTC"})}catch(r){return e}})(e.release_date)}),e.author&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{children:"•"}),a.jsx(p,{size:14}),a.jsx("span",{children:e.author})]})]})]}),a.jsx(l.default,{href:`/version-history/${e.id}`,className:"group/title",children:(0,a.jsxs)("h2",{className:"text-xl font-bold text-foreground mb-4 group-hover/title:text-primary transition-colors duration-200 flex items-center gap-2",children:[e.title,a.jsx(x,{size:16,className:"opacity-0 group-hover/title:opacity-100 transition-opacity duration-200"})]})}),n(e.tags).length>0&&a.jsx("div",{className:"flex flex-wrap gap-2 mb-5",children:n(e.tags).map((e,r)=>(0,a.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-muted/60 text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors duration-200",children:[a.jsx(h,{size:10}),e]},r))}),a.jsx("div",{className:"text-sm text-muted-foreground mb-4 leading-relaxed",children:((e,r=200)=>{let t=e.replace(/#{1,6}\s+/g,"").replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/`(.*?)`/g,"$1").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/\n+/g," ").replace(/\s+/g," ").trim();if(t.length<=r)return t;let a=t.slice(0,r),s=a.lastIndexOf(" ");return s>.8*r&&(a=a.slice(0,s)),a+"..."})(e.content)}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l.default,{href:`/version-history/${e.id}`,className:"inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors duration-200",children:["Read full details",a.jsx(u,{size:14})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[a.jsx(m,{size:12}),(0,a.jsxs)("span",{children:[Math.ceil(e.content.length/1e3)," min read"]})]})]})]})})]})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[948,499,469,466,26],()=>t(43342));module.exports=a})();