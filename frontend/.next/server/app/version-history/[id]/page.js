(()=>{var e={};e.id=46,e.ids=[46],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},36152:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(69926),t(48109),t(27683);var a=t(23191),s=t(88716),o=t(37922),i=t.n(o),n=t(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d=["",{children:["version-history",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69926)),"/home/<USER>/Code/me/My-web/frontend/src/app/version-history/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/version-history/[id]/page.tsx"],m="/version-history/[id]/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/version-history/[id]/page",pathname:"/version-history/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33533:(e,r,t)=>{Promise.resolve().then(t.bind(t,17261)),Promise.resolve().then(t.bind(t,70881))},12714:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},67427:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},39730:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},17261:(e,r,t)=>{"use strict";t.d(r,{OptimizedBlogContent:()=>l});var a=t(10326),s=t(17577);t(70331);var o=t(51223);function i({src:e,alt:r,className:t,priority:i=!1,placeholder:n,onLoad:l,onError:d,onClick:c}){let[m,u]=(0,s.useState)(!0),[p,x]=(0,s.useState)(!1),[g,h]=(0,s.useState)(i),v=(0,s.useRef)(null),f=(0,s.useRef)(null);return(0,a.jsxs)("div",{ref:f,className:(0,o.cn)("relative overflow-hidden bg-muted rounded-lg",t),onClick:c,style:{cursor:c?"pointer":"default"},children:[m&&(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[a.jsx("img",{src:n||`data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)" />
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="system-ui" font-size="14">Loading...</text>
      </svg>
    `)}`,alt:"",className:"w-full h-full object-cover opacity-50"}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"})})]}),p&&(0,a.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-muted text-muted-foreground",children:[a.jsx("svg",{className:"w-12 h-12 mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"})}),a.jsx("span",{className:"text-sm",children:"Failed to load image"})]}),g&&!p&&a.jsx("img",{ref:v,src:e,alt:r,onLoad:()=>{u(!1),x(!1),l?.()},onError:()=>{u(!1),x(!0),d?.(Error(`Failed to load image: ${e}`))},className:(0,o.cn)("w-full h-full object-cover transition-opacity duration-500",m?"opacity-0":"opacity-100"),loading:i?"eager":"lazy",decoding:"async"}),!m&&!p&&a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"})]})}function n({src:e,alt:r,onClose:t}){return a.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",onClick:t,children:(0,a.jsxs)("div",{className:"relative max-w-[90vw] max-h-[90vh] p-4",children:[a.jsx("button",{onClick:t,className:"absolute -top-2 -right-2 z-10 w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors",children:a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),a.jsx(i,{src:e,alt:r,priority:!0,className:"max-w-full max-h-full rounded-lg shadow-2xl"})]})})}function l({content:e,className:r=""}){let t=(0,s.useRef)(null),[o,i]=(0,s.useState)(null),[l,d]=(0,s.useState)(!1);!function({enabled:e=!0,onMetricsCollected:r,debug:t=!1}={}){(0,s.useRef)(null),(0,s.useRef)([])}({enabled:!1,debug:!1,onMetricsCollected:e=>{}});let c=(0,s.useMemo)(()=>e.replace(/<h([1-6])>(.*?)<\/h[1-6]>/g,(e,r,t)=>{let a=t.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g,"").replace(/\s+/g,"-").trim()||`heading-${Math.random().toString(36).substr(2,9)}`;return`<h${r} id="${a}" class="${({1:"text-4xl font-bold mb-6 mt-12 text-foreground tracking-tight leading-tight scroll-mt-20 relative group",2:"text-3xl font-bold mb-4 mt-10 text-foreground tracking-tight leading-tight scroll-mt-20 relative group border-l-4 border-primary/30 pl-4 hover:border-primary/60 transition-colors duration-300",3:"text-2xl font-semibold mb-3 mt-8 text-foreground/90 tracking-tight leading-tight scroll-mt-20 relative group",4:"text-xl font-semibold mb-2 mt-6 text-foreground/85 tracking-tight leading-tight scroll-mt-20 relative group",5:"text-lg font-medium mb-2 mt-4 text-foreground/80 tracking-tight leading-tight scroll-mt-20 relative group",6:"text-base font-medium mb-2 mt-4 text-foreground/75 tracking-tight leading-tight scroll-mt-20 relative group"})[r]||""}">${t}</h${r}>`}).replace(/<p>/g,'<p class="mb-4 leading-8 text-foreground/85 text-lg tracking-wide hover:text-foreground transition-colors duration-300 relative group px-2 py-1 rounded-lg hover:bg-primary/5">').replace(/<blockquote>/g,'<blockquote class="relative pl-8 pr-6 py-6 my-8 bg-gradient-to-r from-primary/8 via-primary/5 to-transparent border-l-4 border-primary rounded-r-2xl italic text-foreground/80 text-lg leading-8 hover:from-primary/12 hover:via-primary/8 hover:to-primary/5 transition-all duration-500 group overflow-hidden">').replace(/<ul>/g,'<ul class="mb-8 space-y-3 text-foreground/85">').replace(/<ol>/g,'<ol class="mb-8 space-y-3 text-foreground/85 list-decimal list-inside">').replace(/<li>/g,'<li class="flex items-start gap-3 leading-7 hover:text-foreground transition-colors duration-300">').replace(/<table>/g,'<div class="overflow-x-auto my-8 rounded-lg border border-border"><table class="w-full border-collapse bg-background">').replace(/<\/table>/g,"</table></div>").replace(/<code class="inline-code">/g,'<code class="px-1.5 py-0.5 bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 rounded text-sm font-mono">'),[e]);return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{ref:t,className:`prose prose-zinc dark:prose-invert max-w-none ${r}`,style:{columnCount:"unset",columns:"unset",columnFill:"unset",columnGap:"unset"},dangerouslySetInnerHTML:{__html:c}}),o&&a.jsx(n,{src:o.src,alt:o.alt,onClose:()=>i(null)})]})}},39414:(e,r,t)=>{"use strict";t.d(r,{CommentStats:()=>l});var a=t(10326),s=t(17577),o=t(39730),i=t(12714),n=t(67427);function l({path:e,className:r="",showIcons:t=!0}){let[l,d]=(0,s.useState)({commentCount:0,pageViews:0,likes:0}),[c,m]=(0,s.useState)(!0);return c?(0,a.jsxs)("div",{className:`flex flex-row items-center justify-center gap-6 ${r}`,children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground",children:[t&&a.jsx(o.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"animate-pulse",children:"-"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground",children:[t&&a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"animate-pulse",children:"-"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground",children:[t&&a.jsx(n.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"animate-pulse",children:"-"})]})]}):(0,a.jsxs)("div",{className:`flex flex-row items-center justify-center gap-6 ${r}`,children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[t&&a.jsx(o.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[l.commentCount," comments"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[t&&a.jsx(i.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[l.pageViews," views"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[t&&a.jsx(n.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[l.likes," likes"]})]})]})}},70881:(e,r,t)=>{"use strict";t.d(r,{VersionLayout:()=>b});var a=t(10326),s=t(17577),o=t.n(s),i=t(35047),n=t(12315),l=t(30131),d=t(4205),c=t(22428),m=t(94046),u=t(39414),p=t(23201),x=t(33734),g=t(37358),h=t(79635),v=t(6343),f=t(51223);function b({version:e,children:r}){var t;let b=(0,i.useRouter)(),{previousPathname:j}=(0,s.useContext)(n.I),[w,N]=o().useState(""),k=e=>p.K$.getTagStyle(e,"minimal"),z=e=>p.K$.getTagClasses(e,"minimal"),C=e=>({...k(e),transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",transform:"perspective(100px) translateZ(0)",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1)"}),M=(t=e.tags)?t.split(",").map(e=>e.trim()).filter(Boolean):[];return a.jsx(a.Fragment,{children:a.jsx(l.W2,{className:"mt-16 lg:mt-32",children:(0,a.jsxs)("div",{className:"xl:relative",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl",children:[j&&a.jsx("div",{className:"mb-8",children:a.jsx("button",{type:"button",onClick:()=>b.back(),"aria-label":"Go back to version history",className:"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20",children:a.jsx(y,{className:"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400"})})}),(0,a.jsxs)("article",{className:"animate-fade-in-up",children:[(0,a.jsxs)("header",{className:"relative mb-16 p-10 rounded-3xl bg-gradient-to-r from-background/85 via-background/95 to-background/85 border border-border/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl hover:shadow-primary/10 transition-all duration-700 group/header overflow-hidden",style:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"},children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700"}),a.jsx("div",{className:"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300"}),a.jsx("div",{className:"absolute top-0 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent opacity-60"}),a.jsx("div",{className:"absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-primary/30 rounded-tl-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),a.jsx("div",{className:"absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-primary/30 rounded-tr-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),(0,a.jsxs)("div",{className:"relative z-10 space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,a.jsxs)("span",{className:`relative inline-flex items-center px-4 py-2 text-xs font-bold rounded-full hover:scale-105 hover:shadow-lg transition-all duration-300 cursor-default overflow-hidden ${e.is_major?"bg-gradient-to-r from-red-500/15 to-orange-500/15 border-2 border-red-500/30 text-red-700 dark:text-red-300 hover:shadow-red-500/25":"bg-gradient-to-r from-blue-500/15 to-cyan-500/15 border-2 border-blue-500/30 text-blue-700 dark:text-blue-300 hover:shadow-blue-500/25"}`,children:[a.jsx("div",{className:`absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300 ${e.is_major?"bg-gradient-to-r from-red-500/20 to-orange-500/20":"bg-gradient-to-r from-blue-500/20 to-cyan-500/20"}`}),a.jsx("span",{className:"relative z-10 tracking-wide",children:e.version})]}),e.is_major&&(0,a.jsxs)("span",{className:"relative inline-flex items-center gap-1.5 px-4 py-2 text-xs font-bold bg-gradient-to-r from-orange-500/15 to-red-500/15 border-2 border-orange-500/30 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 cursor-default overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300"}),a.jsx(x.Z,{className:"w-3 h-3 fill-current animate-pulse-soft relative z-10"}),a.jsx("span",{className:"relative z-10 tracking-wide",children:"MAJOR RELEASE"})]})]}),a.jsx("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md",style:{transform:"translateZ(20px)",transformStyle:"preserve-3d",fontFamily:'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif'},children:e.title}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-8 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[a.jsx("div",{className:"p-1.5 rounded-lg bg-primary/10 text-primary group-hover/meta:bg-primary/20 group-hover/meta:scale-110 transition-all duration-300",children:a.jsx(g.Z,{className:"w-4 h-4"})}),a.jsx("time",{dateTime:e.release_date,className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:(0,c.p)(e.release_date)})]}),e.author&&(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[a.jsx("div",{className:"p-1.5 rounded-lg bg-blue-500/10 text-blue-500 group-hover/meta:bg-blue-500/20 group-hover/meta:scale-110 transition-all duration-300",children:a.jsx(h.Z,{className:"w-4 h-4"})}),(0,a.jsxs)("span",{className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:["Released by ",e.author]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[a.jsx("div",{className:"p-1.5 rounded-lg bg-purple-500/10 text-purple-500 group-hover/meta:bg-purple-500/20 group-hover/meta:scale-110 transition-all duration-300",children:a.jsx(v.Z,{className:"w-4 h-4"})}),a.jsx("span",{className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:"Version Update"})]}),a.jsx(u.CommentStats,{path:`/versions/${e.id}`,showIcons:!0})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[a.jsx("div",{className:"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm",children:[a.jsx(v.Z,{className:"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300"}),a.jsx("span",{className:"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide",children:"ABSTRACT"})]}),a.jsx("div",{className:"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1"})]}),(0,a.jsxs)("div",{className:"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5",children:[a.jsx("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),a.jsx("div",{className:"absolute top-6 right-6 w-3 h-3 bg-primary/20 rounded-full blur-sm animate-pulse-soft"}),a.jsx("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-secondary/30 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"1s"}}),a.jsx("div",{className:"relative z-10",children:a.jsx("p",{className:"text-lg sm:text-xl leading-relaxed text-muted-foreground group-hover/header:text-foreground transition-colors duration-300 font-medium",style:{fontFamily:'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',fontStyle:"italic"},children:"This version introduces new features, improvements, and bug fixes to enhance the overall user experience and system performance."})})]})]}),M.length>0&&a.jsx("div",{className:"flex flex-wrap gap-2",children:M.map((e,r)=>(0,a.jsxs)("span",{className:(0,f.cn)(z("#10B981"),"hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden"),style:C("#10B981"),children:[a.jsx("div",{className:"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm",style:{backgroundColor:"#10B981"}}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500"}),a.jsx("span",{className:"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300",children:e}),a.jsx("div",{className:"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200"})]},r))})]})]}),a.jsx(d.M,{className:"mt-8","data-mdx-content":!0,children:r})]})]}),a.jsx("div",{className:"hidden 2xl:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto",children:a.jsx(m.r,{content:w})}),a.jsx("div",{className:"hidden xl:block 2xl:hidden fixed right-2 top-1/2 transform -translate-y-1/2 w-72 z-40 pointer-events-auto",children:a.jsx(m.r,{content:w})}),a.jsx("div",{className:"xl:hidden",children:a.jsx(m.r,{content:w})})]})})})}function y(e){return a.jsx("svg",{viewBox:"0 0 16 16",fill:"none","aria-hidden":"true",...e,children:a.jsx("path",{d:"m9.25 10.75-3.5-3.5 3.5-3.5",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}},69926:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,generateMetadata:()=>m,revalidate:()=>d,runtime:()=>l});var a=t(19510),s=t(58585),o=t(96421),i=t(10803);let n=(0,t(68570).createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx#VersionLayout`),l="nodejs",d=3600;async function c(e){try{let r=await fetch(`http://**************:8000/api/website-versions/${e}`,{next:{revalidate:3600}});if(!r.ok)return null;return r.json()}catch(e){return null}}async function m({params:e}){let r=await c(e.id);return r?{title:`${r.version} - ${r.title}`,description:r.content.substring(0,160)+"..."}:{title:"Version Not Found"}}async function u({params:e}){let r=await c(e.id);if(r||(0,s.notFound)(),!r.content)return a.jsx(n,{version:r,children:a.jsx("div",{className:"mt-8 prose dark:prose-invert",children:a.jsx("p",{children:"此版本内容为空。"})})});try{let e=await (0,o.Q)(`version-${r.id}`,"blog",r.content);return a.jsx(n,{version:r,children:a.jsx(i.j,{content:e,className:"mt-8"})})}catch(e){return a.jsx(n,{version:r,children:(0,a.jsxs)("div",{className:"mt-8 prose dark:prose-invert",children:[a.jsx("p",{children:"抱歉，无法加载版本内容。请稍后再试。"}),a.jsx("p",{className:"text-red-500",children:e instanceof Error?e.message:"未知错误"})]})})}}},10803:(e,r,t)=>{"use strict";t.d(r,{j:()=>a});let a=(0,t(68570).createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/blog/OptimizedBlogContent.tsx#OptimizedBlogContent`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[948,499,466,722,116],()=>t(36152));module.exports=a})();