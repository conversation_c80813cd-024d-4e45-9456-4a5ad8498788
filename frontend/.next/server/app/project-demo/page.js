(()=>{var e={};e.id=989,e.ids=[989],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},68997:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),a(24806),a(48109),a(27683);var s=a(23191),r=a(88716),i=a(37922),o=a.n(i),n=a(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let d=["",{children:["project-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,24806)),"/home/<USER>/Code/me/My-web/frontend/src/app/project-demo/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/project-demo/page.tsx"],m="/project-demo/page",p={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/project-demo/page",pathname:"/project-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90118:(e,t,a)=>{Promise.resolve().then(a.bind(a,49390))},49390:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var s=a(10326);a(17577);var r=a(93879);let i=[{id:1,name:"AI Chat Assistant",description:"A modern AI-powered chat assistant built with React and OpenAI API.",categories:[{id:1,name:"AI",slug:"ai",icon:null,color:"#84CC16"},{id:2,name:"Web",slug:"web",icon:null,color:"#10B981"}],tech_stack:["React","TypeScript","OpenAI","Node.js"],stats:{stars:1200,forks:89,views:5600},status:"active",priority:"high",last_updated:"2024-01-15",has_detail_page:!0,slug:"ai-chat-assistant",link:{href:"/projects/ai-chat-assistant",label:"View Project"},type:"work"},{id:2,name:"Mobile E-commerce App",description:"A comprehensive e-commerce mobile application with advanced features including real-time inventory management, secure payment processing, user authentication, shopping cart functionality, order tracking, push notifications, and seamless integration with multiple payment gateways. Built using modern mobile development frameworks.",categories:[{id:3,name:"Mobile",slug:"mobile",icon:null,color:"#14B8A6"},{id:4,name:"E-commerce",slug:"ecommerce",icon:null,color:"#059669"}],tech_stack:["React Native","Redux","Firebase","Stripe","Node.js"],stats:{stars:856,downloads:12e3},status:"completed",priority:"medium",last_updated:"2024-01-10",has_detail_page:!1,link:{href:"https://github.com/example/mobile-ecommerce",label:"View on GitHub"},type:"work"},{id:3,name:"Design System",description:"Modern design system.",categories:[{id:5,name:"Design",slug:"design",icon:null,color:"#06B6D4"}],tech_stack:["Figma","React","Storybook"],stats:{views:3400},status:"in-progress",priority:"low",last_updated:"2024-01-12",has_detail_page:!0,slug:"design-system",link:{href:"/projects/design-system",label:"View Project"},type:"work"},{id:4,name:"Open Source Library",description:"A powerful and flexible open source library for building modern web applications. Features include component composition, state management, routing, and extensive customization options. Designed with developer experience in mind.",categories:[{id:6,name:"Open Source",slug:"opensource",icon:null,color:"#16A34A"},{id:7,name:"Library",slug:"library",icon:null,color:"#059669"}],tech_stack:["TypeScript","React","Rollup","Jest","Storybook"],stats:{stars:2340,forks:234,contributors:45},status:"active",priority:"high",last_updated:"2024-01-14",has_detail_page:!1,link:{href:"https://github.com/example/open-source-library",label:"View on GitHub"},type:"opensource"}],o=function(){return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[s.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"红点奖级别项目卡片演示"}),s.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"展示统一高度、美观的绿色主题卡片设计"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr",children:i.map((e,t)=>s.jsx("div",{className:"min-w-0 w-full",style:{minWidth:"320px"},children:s.jsx(r.s,{project:e,index:t,categoryIndex:0,onPreview:e=>{}},e.id)}))}),s.jsx("div",{className:"mt-12 text-center",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-4 px-6 py-3 bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"统一高度"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-3 h-3 bg-emerald-500 rounded-full"}),s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"绿色主题"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-3 h-3 bg-teal-500 rounded-full"}),s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"智能截断"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-3 h-3 bg-cyan-500 rounded-full"}),s.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"高级交互"})]})]})})]})})}},24806:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o,metadata:()=>n});var s=a(19510),r=a(68570);(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/project/ProjectCardDemo.tsx#ProjectCardDemo`);let i=(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/project/ProjectCardDemo.tsx#default`);function o(){return s.jsx(i,{})}let n={title:"项目卡片演示 - 红点奖级别设计",description:"展示新的项目卡片设计，包含统一高度、绿色主题和高级交互效果"}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[948,499,466,879],()=>a(68997));module.exports=s})();