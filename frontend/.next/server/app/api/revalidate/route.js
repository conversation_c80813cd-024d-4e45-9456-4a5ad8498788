(()=>{var e={};e.id=899,e.ids=[899],e.modules={45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52969:(e,t,a)=>{"use strict";a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>d,serverHooks:()=>f,staticGenerationAsyncStorage:()=>p});var r={};a.r(r),a.d(r,{GET:()=>u,POST:()=>c});var n=a(49303),i=a(88716),s=a(60670),o=a(87070),l=a(57708);async function c(e){try{let{tag:t,path:a,secret:r}=await e.json();if(r&&r!==process.env.REVALIDATE_SECRET)return o.NextResponse.json({message:"Invalid secret"},{status:401});return t&&(Array.isArray(t)?t.forEach(e=>{(0,l.revalidateTag)(e)}):(0,l.revalidateTag)(t)),a&&(Array.isArray(a)?a.forEach(e=>{(0,l.revalidatePath)(e)}):(0,l.revalidatePath)(a)),o.NextResponse.json({message:"Cache revalidated successfully",revalidated:!0,now:Date.now()})}catch(e){return o.NextResponse.json({message:"Error revalidating cache"},{status:500})}}async function u(e){try{let{searchParams:t}=new URL(e.url),a=t.get("action");if(t.get("secret")!==process.env.REVALIDATE_SECRET)return o.NextResponse.json({message:"Invalid secret"},{status:401});switch(a){case"all":(0,l.revalidateTag)("blogs"),(0,l.revalidateTag)("projects"),(0,l.revalidateTag)("tags"),(0,l.revalidateTag)("personal-info"),(0,l.revalidateTag)("theme-colors"),(0,l.revalidateTag)("social-links"),(0,l.revalidateTag)("tech-icons"),(0,l.revalidateTag)("homepage-sections"),(0,l.revalidatePath)("/"),(0,l.revalidatePath)("/blogs"),(0,l.revalidatePath)("/projects");break;case"blogs":(0,l.revalidateTag)("blogs"),(0,l.revalidateTag)("homepage-content"),(0,l.revalidatePath)("/blogs"),(0,l.revalidatePath)("/");break;case"projects":(0,l.revalidateTag)("projects"),(0,l.revalidateTag)("homepage-content"),(0,l.revalidatePath)("/projects"),(0,l.revalidatePath)("/");break;case"homepage":(0,l.revalidateTag)("personal-info"),(0,l.revalidateTag)("homepage-sections"),(0,l.revalidatePath)("/");break;default:return o.NextResponse.json({message:"Invalid action"},{status:400})}return o.NextResponse.json({message:`Cache cleared for: ${a}`,revalidated:!0,now:Date.now()})}catch(e){return o.NextResponse.json({message:"Error revalidating cache"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/revalidate/route",pathname:"/api/revalidate",filename:"route",bundlePath:"app/api/revalidate/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/revalidate/route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:p,serverHooks:f}=d,g="/api/revalidate/route";function v(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:p})}},57708:(e,t,a)=>{let r={unstable_cache:a(19239).A,revalidateTag:a(39487).revalidateTag,revalidatePath:a(39487).revalidatePath,unstable_noStore:a(88104).P};e.exports=r,t.unstable_cache=r.unstable_cache,t.revalidatePath=r.revalidatePath,t.revalidateTag=r.revalidateTag,t.unstable_noStore=r.unstable_noStore},11586:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return i}});let r=a(78168),n=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function s(e){let t,a,i;for(let r of e.split("/"))if(a=n.find(e=>r.startsWith(e))){[t,i]=e.split(a,2);break}if(!t||!a||!i)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,r.normalizeAppPath)(t),a){case"(.)":i="/"===t?`/${i}`:t+"/"+i;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);i=s.slice(0,-2).concat(i).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:i}}},39487:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{revalidatePath:function(){return c},revalidateTag:function(){return l}});let r=a(6278),n=a(56818),i=a(11943),s=a(38834),o=a(45869);function l(e){return u(e,`revalidateTag ${e}`)}function c(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH){console.warn(`Warning: revalidatePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`);return}let a=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?a+=`${a.endsWith("/")?"":"/"}${t}`:(0,n.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "revalidatePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`),u(a,`revalidatePath ${e}`)}function u(e,t){let a=o.staticGenerationAsyncStorage.getStore();if(!a||!a.incrementalCache)throw Error(`Invariant: static generation store missing in ${t}`);if(a.isUnstableCacheCallback)throw Error(`Route ${(0,s.getPathname)(a.urlPathname)} used "${t}" inside a function cached with "unstable_cache(...)" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);(0,r.trackDynamicDataAccessed)(a,t),a.revalidatedTags||(a.revalidatedTags=[]),a.revalidatedTags.includes(e)||a.revalidatedTags.push(e),a.pathWasRevalidated=!0}},19239:(e,t,a)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return l}});let r=a(11943),n=a(60670),i=a(45869),s=0;async function o(e,t,a,n,i,s,o){await t.set(a,{kind:"FETCH",data:{headers:{},body:JSON.stringify(e),status:200,url:""},revalidate:"number"!=typeof i?r.CACHE_ONE_YEAR:i},{revalidate:i,fetchCache:!0,tags:n,fetchIdx:s,fetchUrl:o})}function l(e,t,a={}){if(0===a.revalidate)throw Error(`Invariant revalidate: 0 can not be passed to unstable_cache(), must be "false" or "> 0" ${e.toString()}`);let r=a.tags?(0,n.validateTags)(a.tags,`unstable_cache ${e.toString()}`):[];(0,n.validateRevalidate)(a.revalidate,`unstable_cache ${e.name||e.toString()}`);let l=`${e.toString()}-${Array.isArray(t)&&t.join(",")}`;return async(...t)=>{let c=i.staticGenerationAsyncStorage.getStore(),u=(null==c?void 0:c.incrementalCache)||globalThis.__incrementalCache;if(!u)throw Error(`Invariant: incrementalCache missing in unstable_cache ${e.toString()}`);let{pathname:d,searchParams:h}=new URL((null==c?void 0:c.urlPathname)||"/","http://n"),p=[...h.keys()].sort((e,t)=>e.localeCompare(t)).map(e=>`${e}=${h.get(e)}`).join("&"),f=`${l}-${JSON.stringify(t)}`,g=await u.fetchCacheKey(f),v=`unstable_cache ${d}${p.length?"?":""}${p} ${e.name?` ${e.name}`:g}`,m=(c?c.nextFetchId:s)??1;if(c){if(c.nextFetchId=m+1,"number"==typeof a.revalidate?"number"==typeof c.revalidate&&c.revalidate<a.revalidate||(c.revalidate=a.revalidate):!1===a.revalidate&&void 0===c.revalidate&&(c.revalidate=a.revalidate),c.tags)for(let e of r)c.tags.includes(e)||c.tags.push(e);else c.tags=r.slice();let s=(0,n.addImplicitTags)(c);if("force-no-store"!==c.fetchCache&&!c.isOnDemandRevalidate&&!u.isOnDemandRevalidate&&!c.isDraftMode){let n=await u.get(g,{kindHint:"fetch",revalidate:a.revalidate,tags:r,softTags:s,fetchIdx:m,fetchUrl:v});if(n&&n.value){if("FETCH"!==n.value.kind)console.error(`Invariant invalid cacheEntry returned for ${f}`);else{let s=void 0!==n.value.data.body?JSON.parse(n.value.data.body):void 0;return n.isStale&&(c.pendingRevalidates||(c.pendingRevalidates={}),c.pendingRevalidates[f]=i.staticGenerationAsyncStorage.run({...c,fetchCache:"force-no-store",isUnstableCacheCallback:!0},e,...t).then(e=>o(e,u,g,r,a.revalidate,m,v)).catch(e=>console.error(`revalidating cache with key: ${f}`,e))),s}}}let l=await i.staticGenerationAsyncStorage.run({...c,fetchCache:"force-no-store",isUnstableCacheCallback:!0},e,...t);return c.isDraftMode||o(l,u,g,r,a.revalidate,m,v),l}{if(s+=1,!u.isOnDemandRevalidate){let e=c&&(0,n.addImplicitTags)(c),t=await u.get(g,{kindHint:"fetch",revalidate:a.revalidate,tags:r,fetchIdx:m,fetchUrl:v,softTags:e});if(t&&t.value){if("FETCH"!==t.value.kind)console.error(`Invariant invalid cacheEntry returned for ${f}`);else if(!t.isStale)return void 0!==t.value.data.body?JSON.parse(t.value.data.body):void 0}}let l=await i.staticGenerationAsyncStorage.run({fetchCache:"force-no-store",isUnstableCacheCallback:!0,urlPathname:"/",isStaticGeneration:!1,prerenderState:null},e,...t);return o(l,u,g,r,a.revalidate,m,v),l}}}},88104:(e,t,a)=>{"use strict";Object.defineProperty(t,"P",{enumerable:!0,get:function(){return i}});let r=a(45869),n=a(6278);function i(){let e=r.staticGenerationAsyncStorage.getStore();return e?e.forceStatic?void 0:void(e.isUnstableNoStore=!0,(0,n.markCurrentScopeAsDynamic)(e,"unstable_noStore()")):void 0}},1555:(e,t)=>{"use strict";function a(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return a}})},78168:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return s}});let r=a(1555),n=a(65406);function i(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,a,r)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&a===r.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},56818:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getSortedRoutes:function(){return r.getSortedRoutes},isDynamicRoute:function(){return n.isDynamicRoute}});let r=a(65026),n=a(65714)},65714:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return i}});let r=a(11586),n=/\/\[[^/]+?\](?=\/|$)/;function i(e){return(0,r.isInterceptionRouteAppPath)(e)&&(e=(0,r.extractInterceptionRouteInformation)(e).interceptedRoute),n.test(e)}},65026:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return r}});class a{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let a=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&a.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');a.unshift(t)}return null!==this.restSlugName&&a.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&a.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),a}_insert(e,t,r){if(0===e.length){this.placeholder=!1;return}if(r)throw Error("Catch-all must be the last part of the URL.");let n=e[0];if(n.startsWith("[")&&n.endsWith("]")){let a=n.slice(1,-1),s=!1;if(a.startsWith("[")&&a.endsWith("]")&&(a=a.slice(1,-1),s=!0),a.startsWith("...")&&(a=a.substring(3),r=!0),a.startsWith("[")||a.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+a+"').");if(a.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+a+"').");function i(e,a){if(null!==e&&e!==a)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+a+"').");t.forEach(e=>{if(e===a)throw Error('You cannot have the same slug name "'+a+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===n.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+a+'" differ only by non-word symbols within a single dynamic path')}),t.push(a)}if(r){if(s){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');i(this.optionalRestSlugName,a),this.optionalRestSlugName=a,n="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');i(this.restSlugName,a),this.restSlugName=a,n="[...]"}}else{if(s)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');i(this.slugName,a),this.slugName=a,n="[]"}}this.children.has(n)||this.children.set(n,new a),this.children.get(n)._insert(e.slice(1),t,r)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function r(e){let t=new a;return e.forEach(e=>t.insert(e)),t.smoosh()}},65406:(e,t)=>{"use strict";function a(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DEFAULT_SEGMENT_KEY:function(){return n},PAGE_SEGMENT_KEY:function(){return r},isGroupSegment:function(){return a}});let r="__PAGE__",n="__DEFAULT__"}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[948,972],()=>a(52969));module.exports=r})();