"use strict";(()=>{var e={};e.id=996,e.ids=[996],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50087:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>d,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>u});var n=r(49303),o=r(88716),s=r(60670),i=r(87070);async function u(e){try{var t;let r;let{url:a,loadTime:n,renderTime:o,firstContentfulPaint:s,largestContentfulPaint:u,cumulativeLayoutShift:p,firstInputDelay:c,userAgent:l,timestamp:m=Date.now()}=await e.json(),f=(t={loadTime:n,firstContentfulPaint:s,largestContentfulPaint:u,cumulativeLayoutShift:p,firstInputDelay:c},r=100,t.loadTime&&(t.loadTime>4e3?r-=30:t.loadTime>2e3&&(r-=15)),t.firstContentfulPaint&&(t.firstContentfulPaint>3e3?r-=20:t.firstContentfulPaint>1800&&(r-=10)),t.largestContentfulPaint&&(t.largestContentfulPaint>4e3?r-=25:t.largestContentfulPaint>2500&&(r-=12)),t.cumulativeLayoutShift&&(t.cumulativeLayoutShift>.25?r-=15:t.cumulativeLayoutShift>.1&&(r-=8)),t.firstInputDelay&&(t.firstInputDelay>300?r-=10:t.firstInputDelay>100&&(r-=5)),Math.max(0,r));return i.NextResponse.json({message:"Performance data recorded",score:f,timestamp:m})}catch(e){return i.NextResponse.json({message:"Error recording performance data"},{status:500})}}async function p(){try{return i.NextResponse.json({message:"Performance statistics",averageLoadTime:"1.2s",averageFCP:"0.8s",averageLCP:"1.5s",averageCLS:.05,averageFID:"45ms",overallScore:92})}catch(e){return i.NextResponse.json({message:"Error fetching performance statistics"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/performance/route",pathname:"/api/performance",filename:"route",bundlePath:"app/api/performance/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/performance/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:f}=c,d="/api/performance/route";function g(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,972],()=>r(50087));module.exports=a})();