"use strict";(()=>{var e={};e.id=929,e.ids=[929],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},43151:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>d,patchFetch:()=>w,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>h});var n={};r.r(n),r.d(n,{GET:()=>c});var a=r(49303),o=r(88716),s=r(60670),i=r(87070),u=r(11795);async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("path"),n=t.getAll("paths");if(!r&&0===n.length)return i.NextResponse.json({error:"Path parameter is required"},{status:400});let a=await (0,u.bO)(),o=n.length>0?n:[r],s=`${a.serverURL}/api/comment`,c=new URLSearchParams({type:"count",url:o.join(",")}),l=await fetch(`${s}?${c}`,{headers:{Accept:"application/json"}});if(!l.ok)throw Error(`Waline API error: ${l.status}`);let p=await l.json();if(r&&0===n.length)return i.NextResponse.json({success:!0,data:p[0]||0,path:r});return i.NextResponse.json({success:!0,data:p,paths:o})}catch(e){return i.NextResponse.json({error:"Failed to fetch comment count",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/waline/comment/count/route",pathname:"/api/waline/comment/count",filename:"route",bundlePath:"app/api/waline/comment/count/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/waline/comment/count/route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:m}=l,d="/api/waline/comment/count/route";function w(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:h})}},11795:(e,t,r)=>{r.d(t,{bO:()=>i});let n=null,a=null,o=0;async function s(e=0){try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),r=await fetch("/api/system-config/waline/config",{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal,cache:"default"});if(clearTimeout(t),!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let n=await r.json(),a={serverURL:n.server_url,lang:n.lang||"zh-CN"};if(!a.serverURL||""===a.serverURL.trim())throw Error("Invalid server URL received");return a}catch(r){if(e<3){var t;return await (t=1e3*Math.pow(2,e),new Promise(e=>setTimeout(e,t))),s(e+1)}return{serverURL:"https://waline.jyaochen.cn",lang:"zh-CN"}}}async function i(){let e=Date.now();if(n&&e-o<3e5)return n;if(a)return a;a=s();try{let t=await a;return n=t,o=e,t}catch(e){if(n)return n;throw e}finally{a=null}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[948,972],()=>r(43151));module.exports=n})();