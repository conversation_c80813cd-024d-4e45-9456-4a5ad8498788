"use strict";(()=>{var e={};e.id=186,e.ids=[186],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},80685:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>h,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>w,staticGenerationAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{POST:()=>u});var n=r(49303),i=r(88716),o=r(60670),s=r(87070),l=r(11795);async function u(e){try{let t=await e.json(),{path:r,title:a}=t;if(!r)return s.NextResponse.json({error:"Path parameter is required"},{status:400});let n=await (0,l.bO)(),i=`${n.serverURL}/api/article`,o=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({path:r,title:t.title||"",url:r})});if(!o.ok)throw Error(`Waline API error: ${o.status}`);let u=(await o.json())[0]||{};return s.NextResponse.json({success:!0,time:u.time||0,path:r})}catch(e){return s.NextResponse.json({error:"Failed to increment page views",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/waline/article/pageview/increment/route",pathname:"/api/waline/article/pageview/increment",filename:"route",bundlePath:"app/api/waline/article/pageview/increment/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/waline/article/pageview/increment/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:w}=c,m="/api/waline/article/pageview/increment/route";function h(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:d})}},11795:(e,t,r)=>{r.d(t,{bO:()=>s});let a=null,n=null,i=0;async function o(e=0){try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),r=await fetch("/api/system-config/waline/config",{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal,cache:"default"});if(clearTimeout(t),!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let a=await r.json(),n={serverURL:a.server_url,lang:a.lang||"zh-CN"};if(!n.serverURL||""===n.serverURL.trim())throw Error("Invalid server URL received");return n}catch(r){if(e<3){var t;return await (t=1e3*Math.pow(2,e),new Promise(e=>setTimeout(e,t))),o(e+1)}return{serverURL:"https://waline.jyaochen.cn",lang:"zh-CN"}}}async function s(){let e=Date.now();if(a&&e-i<3e5)return a;if(n)return n;n=o();try{let t=await n;return a=t,i=e,t}catch(e){if(a)return a;throw e}finally{n=null}}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,972],()=>r(80685));module.exports=a})();