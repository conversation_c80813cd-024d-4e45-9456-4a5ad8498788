"use strict";(()=>{var e={};e.id=982,e.ids=[982],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},20340:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>m,requestAsyncStorage:()=>w,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{GET:()=>l,POST:()=>u});var n=r(49303),i=r(88716),o=r(60670),s=r(87070),c=r(11795);async function l(e){try{let{searchParams:t}=new URL(e.url),r=t.get("path");if(!r)return s.NextResponse.json({error:"Path parameter is required"},{status:400});let a=await (0,c.bO)(),n=`${a.serverURL}/api/article`,i=new URLSearchParams({path:r}),o=await fetch(`${n}?${i}`,{headers:{Accept:"application/json"}});if(!o.ok)throw Error(`Waline API error: ${o.status}`);let l=(await o.json())[0]||{};return s.NextResponse.json({success:!0,time:l.time||0,reaction:[l.reaction0||0,l.reaction1||0,l.reaction2||0,l.reaction3||0,l.reaction4||0,l.reaction5||0,l.reaction6||0,l.reaction7||0,l.reaction8||0],path:r})}catch(e){return s.NextResponse.json({error:"Failed to fetch page views",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e){try{let{searchParams:t}=new URL(e.url),r=await e.json(),a=t.get("path")||r.path;if(r.action,!a)return s.NextResponse.json({error:"Path parameter is required"},{status:400});let n=await (0,c.bO)(),i=`${n.serverURL}/api/article`,o=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({path:a,title:r.title||"",url:a})});if(!o.ok)throw Error(`Waline API error: ${o.status}`);let l=(await o.json())[0]||{};return s.NextResponse.json({success:!0,time:l.time||0,reaction:[l.reaction0||0,l.reaction1||0,l.reaction2||0,l.reaction3||0,l.reaction4||0,l.reaction5||0,l.reaction6||0,l.reaction7||0,l.reaction8||0],path:a})}catch(e){return s.NextResponse.json({error:"Failed to update page views",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/waline/article/pageview/route",pathname:"/api/waline/article/pageview",filename:"route",bundlePath:"app/api/waline/article/pageview/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/waline/article/pageview/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:d,serverHooks:h}=p,f="/api/waline/article/pageview/route";function m(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}},11795:(e,t,r)=>{r.d(t,{bO:()=>s});let a=null,n=null,i=0;async function o(e=0){try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),r=await fetch("/api/system-config/waline/config",{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal,cache:"default"});if(clearTimeout(t),!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let a=await r.json(),n={serverURL:a.server_url,lang:a.lang||"zh-CN"};if(!n.serverURL||""===n.serverURL.trim())throw Error("Invalid server URL received");return n}catch(r){if(e<3){var t;return await (t=1e3*Math.pow(2,e),new Promise(e=>setTimeout(e,t))),o(e+1)}return{serverURL:"https://waline.jyaochen.cn",lang:"zh-CN"}}}async function s(){let e=Date.now();if(a&&e-i<3e5)return a;if(n)return n;n=o();try{let t=await n;return a=t,i=e,t}catch(e){if(a)return a;throw e}finally{n=null}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,972],()=>r(20340));module.exports=a})();