"use strict";(()=>{var e={};e.id=681,e.ids=[681],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84266:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>w,routeModule:()=>d,serverHooks:()=>y,staticGenerationAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>u,POST:()=>h});var n=r(49303),i=r(88716),o=r(60670),s=r(87070),c=r(11795);async function l(e){let{serverURL:t,paths:r}=e;try{let e=await fetch(`${t}/api/article`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({paths:r})});if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let a=await e.json();return Array.isArray(a)?a:[a]}catch(e){return r.map(()=>({}))}}async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("path"),a=t.getAll("paths");if(t.get("type"),!r&&0===a.length)return s.NextResponse.json({error:"Path parameter is required"},{status:400});let n=await (0,c.bO)(),i=a.length>0?a:[r],o=await l({serverURL:n.serverURL,lang:n.lang,paths:i,type:["reaction0","reaction1","reaction2","reaction3","reaction4","reaction5","reaction6","reaction7","reaction8"]});if(r&&0===a.length){let e=o[0]||{};return s.NextResponse.json({success:!0,reaction:[e.reaction0||0,e.reaction1||0,e.reaction2||0,e.reaction3||0,e.reaction4||0,e.reaction5||0,e.reaction6||0,e.reaction7||0,e.reaction8||0],like:e.reaction0||0,dislike:e.reaction1||0,path:r})}return s.NextResponse.json({success:!0,data:o,paths:i})}catch(e){return s.NextResponse.json({error:"Failed to fetch reactions",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){let{serverURL:t,path:r,type:a,action:n}=e;try{let e=await fetch(`${t}/api/article`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({path:r,type:a,action:n})});if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let i=await e.json();return Array.isArray(i)?i:[i]}catch(e){return[{}]}}async function h(e){try{let{searchParams:t}=new URL(e.url),r=await e.json(),a=t.get("path")||r.path,n=r.type||"reaction0",i=r.action||"inc";if(!a)return s.NextResponse.json({error:"Path parameter is required"},{status:400});let o=await (0,c.bO)(),l=(await p({serverURL:o.serverURL,lang:o.lang,path:a,type:n,action:i}))[0]||{};return s.NextResponse.json({success:!0,reaction:[l.reaction0||0,l.reaction1||0,l.reaction2||0,l.reaction3||0,l.reaction4||0,l.reaction5||0,l.reaction6||0,l.reaction7||0,l.reaction8||0],like:l.reaction0||0,dislike:l.reaction1||0,path:a})}catch(e){return s.NextResponse.json({error:"Failed to update reactions",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/waline/reaction/route",pathname:"/api/waline/reaction",filename:"route",bundlePath:"app/api/waline/reaction/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/waline/reaction/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:f,serverHooks:y}=d,g="/api/waline/reaction/route";function m(){return(0,o.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:f})}},11795:(e,t,r)=>{r.d(t,{bO:()=>s});let a=null,n=null,i=0;async function o(e=0){try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),r=await fetch("/api/system-config/waline/config",{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal,cache:"default"});if(clearTimeout(t),!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let a=await r.json(),n={serverURL:a.server_url,lang:a.lang||"zh-CN"};if(!n.serverURL||""===n.serverURL.trim())throw Error("Invalid server URL received");return n}catch(r){if(e<3){var t;return await (t=1e3*Math.pow(2,e),new Promise(e=>setTimeout(e,t))),o(e+1)}return{serverURL:"https://waline.jyaochen.cn",lang:"zh-CN"}}}async function s(){let e=Date.now();if(a&&e-i<3e5)return a;if(n)return n;n=o();try{let t=await n;return a=t,i=e,t}catch(e){if(a)return a;throw e}finally{n=null}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,972],()=>r(84266));module.exports=a})();