"use strict";(()=>{var e={};e.id=832,e.ids=[832],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},91921:(e,t,n)=>{n.r(t),n.d(t,{originalPathname:()=>E,patchFetch:()=>N,requestAsyncStorage:()=>h,routeModule:()=>x,serverHooks:()=>m,staticGenerationAsyncStorage:()=>P});var s={};n.r(s),n.d(s,{GET:()=>v});var a=n(49303),r=n(88716),o=n(60670),i=n(87070);let p="https://api.openpanel.dev",l=process.env.NEXT_PUBLIC_OPENPANEL_CLIENT_ID,u=process.env.OPENPANEL_API_SECRET_ID,d=process.env.OPENPANEL_PROJECT_ID,c={totalUV:1e3,dailyUV:100};async function v(){try{if(!l||!u||!d)return i.NextResponse.json(c);let e=await fetch(`${p}/export/events?projectId=${d}&event=screen_view`,{headers:{"openpanel-client-id":l,"openpanel-client-secret":u}});if(!e.ok)return i.NextResponse.json(c);let t=await e.json(),n=t?.meta?.totalCount||c.totalUV,s=new Date,a=new Date(s);a.setDate(a.getDate()-1);let r=a.toISOString().split("T")[0],o=s.toISOString().split("T")[0],v=await fetch(`${p}/export/events?projectId=${d}&event=screen_view&start=${r}&end=${o}`,{headers:{"openpanel-client-id":l,"openpanel-client-secret":u}});if(!v.ok)return i.NextResponse.json({totalUV:n,dailyUV:c.dailyUV});let x=await v.json(),h=x?.meta?.totalCount||c.dailyUV;return i.NextResponse.json({totalUV:n,dailyUV:h})}catch(e){return i.NextResponse.json(c,{status:200})}}let x=new a.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/visit-stats/route",pathname:"/api/visit-stats",filename:"route",bundlePath:"app/api/visit-stats/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/visit-stats/route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:P,serverHooks:m}=x,E="/api/visit-stats/route";function N(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:P})}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[948,972],()=>n(91921));module.exports=s})();