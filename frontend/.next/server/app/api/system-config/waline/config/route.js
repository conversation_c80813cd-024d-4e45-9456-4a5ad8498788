"use strict";(()=>{var e={};e.id=652,e.ids=[652],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},12652:(e,t,n)=>{n.r(t),n.d(t,{originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var r={};n.r(r),n.d(r,{GET:()=>c});var o=n(49303),a=n(88716),s=n(60670),i=n(87070);let p=process.env.BACKEND_URL||"http://localhost:8000";async function c(e){try{let e=await fetch(`${p}/api/system-config/waline/config`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!e.ok)throw Error(`Backend API error: ${e.status} ${e.statusText}`);let t=await e.json();return i.NextResponse.json({server_url:t.server_url||"https://waline.jyaochen.cn",lang:t.lang||"zh-CN"})}catch(e){return i.NextResponse.json({server_url:"https://waline.jyaochen.cn",lang:"zh-CN"})}}let u=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/system-config/waline/config/route",pathname:"/api/system-config/waline/config",filename:"route",bundlePath:"app/api/system-config/waline/config/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/api/system-config/waline/config/route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:g}=u,h="/api/system-config/waline/config/route";function f(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[948,972],()=>n(12652));module.exports=r})();