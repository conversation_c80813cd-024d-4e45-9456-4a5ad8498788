3:I[4707,[],""]
4:I[36423,[],""]
5:I[93285,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
6:I[46021,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Providers"]
7:I[83258,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"ThemeInitializer"]
8:I[59183,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"RouteProgressBar"]
9:I[62989,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"GlobalLoadingManager"]
a:I[20686,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"SmartPrefetch"]
b:I[3864,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Header"]
c:I[72972,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],""]
d:I[18908,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Footer"]
e:I[60827,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
f:I[42545,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PlausibleAnalytics"]
10:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceMonitor"]
11:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceDebugger"]
0:["7n7atQT5AqmloxjVA4Y6t",[[["",{"children":["projects",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["projects",{"children":["__PAGE__",{},[["$L1","$L2",null],null],null]},[null,["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","projects","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/8b6d6f69c7970b5a.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/72074f6a7392446a.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","className":"h-full antialiased","suppressHydrationWarning":true,"children":["$","body",null,{"className":"flex h-full","children":["$","$L5",null,{"children":["$","$L6",null,{"children":[["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","div",null,{"className":"flex w-full","children":[["$","$La",null,{"routes":["/","/about","/projects","/blogs","/gallery"],"priority":"low","delay":1500}],["$","div",null,{"className":"fixed inset-0 flex justify-center sm:px-8","children":["$","div",null,{"className":"flex w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"w-full shadow-xl dark:bg-muted"}]}]}],["$","div",null,{"className":"relative flex w-full flex-col px-4 sm:px-0","children":[["$","$Lb",null,{}],["$","main",null,{"className":"flex-auto","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","div",null,{"className":"sm:px-8 flex h-full items-center pt-16 sm:pt-32","children":["$","div",null,{"className":"mx-auto w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"relative px-4 sm:px-8 lg:px-12","children":["$","div",null,{"className":"mx-auto max-w-2xl lg:max-w-5xl","children":["$","div",null,{"className":"flex flex-col items-center","children":[["$","p",null,{"className":"text-base font-semibold text-zinc-400 dark:text-zinc-500","children":"404"}],["$","h1",null,{"className":"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100","children":"Page not found"}],["$","p",null,{"className":"mt-4 text-base text-zinc-600 dark:text-zinc-400","children":"Sorry, we couldn’t find the page you’re looking for."}],["$","$Lc",null,{"className":"inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70 mt-4","href":"/","children":"Go back home"}]]}]}]}]}]}],"notFoundStyles":[]}]}],["$","$Ld",null,{}]]}]]}],[null,["$","$Le",null,{}],["$","$Lf",null,{}]],["$","$L10",null,{}],["$","$L11",null,{}]]}]}]}]}]],null],null],["$L12",null]]]]
13:I[80089,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","895","static/chunks/app/projects/page-c2ae4e4c1a3a367c.js"],"LazyWrapper"]
14:"$Sreact.suspense"
15:I[81523,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","895","static/chunks/app/projects/page-c2ae4e4c1a3a367c.js"],"BailoutToCSR"]
2:["$","$L13",null,{"children":["$","$14",null,{"fallback":["$","div",null,{"className":"min-h-screen flex items-center justify-center","children":["$","div",null,{"className":"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}]}],"children":["$","$L15",null,{"reason":"next/dynamic","children":"$L16"}]}]}]
12:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"jyao-projects"}],["$","meta","3",{"name":"description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","4",{"name":"author","content":"Jingyao Chen"}],["$","meta","5",{"name":"keywords","content":"Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery"}],["$","meta","6",{"name":"creator","content":"Jingyao Chen"}],["$","meta","7",{"name":"publisher","content":"Jingyao Chen"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"theme-color","content":"#171717"}],["$","meta","10",{"name":"msapplication-TileColor","content":"#171717"}],["$","link","11",{"rel":"canonical","href":"http://**************:3000"}],["$","meta","12",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","13",{"property":"og:title","content":"jyao-projects"}],["$","meta","14",{"property":"og:description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","15",{"property":"og:url","content":"http://**************:3000"}],["$","meta","16",{"property":"og:site_name","content":"Jingyao Chen Portfolio"}],["$","meta","17",{"property":"og:locale","content":"en_US"}],["$","meta","18",{"property":"og:image","content":"http://**************:3000/images/og-jingyao-portfolio.jpg"}],["$","meta","19",{"property":"og:image:width","content":"1200"}],["$","meta","20",{"property":"og:image:height","content":"630"}],["$","meta","21",{"property":"og:image:alt","content":"jyao-projects"}],["$","meta","22",{"property":"og:type","content":"website"}],["$","meta","23",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","24",{"name":"twitter:site","content":"@JingyaoC"}],["$","meta","25",{"name":"twitter:creator","content":"@JingyaoC"}],["$","meta","26",{"name":"twitter:title","content":"jyao-projects"}],["$","meta","27",{"name":"twitter:description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","28",{"name":"twitter:image","content":"http://**************:3000/images/og-jingyao-portfolio.jpg"}],["$","link","29",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"902x902"}]]
1:null
17:I[54394,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","895","static/chunks/app/projects/page-c2ae4e4c1a3a367c.js"],"default"]
18:Tc08,<!--
📝 模板使用说明：
- 这是基于"技术教程模板"模板生成的内容结构
- 请在 [请在此处输入...] 标记的位置填写相应内容
- 带 * 号的字段为必填项
- 填写完成后可以删除这些说明和占位符
- 支持 Markdown 格式
-->

<div style="text-align: center; margin-bottom: 32px;">
<span style="font-size: 28px; color: #1890ff; font-weight: bold;">🎯 [请在此处输入教程标题 *]</span>
<br>
{{#if difficulty}}
<span style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px; margin-top: 8px; display: inline-block;">[请在此处输入difficulty]级程</span>
{{/if}}
<br>
<span style="font-size: 14px; color: #666; margin-top: 8px;">{{date_format(current_date, 'YYYY年MM月DD日')}} · {{#if reading_time}}预计 [请在此处输入reading_time][请在此处输入else]技术教程{{/if}}</span>
</div>

{{#if overview}}
## 📋 教程概述

[请在此处输入教程概述 *]


{{/if}}

{{#if learning_objectives}}
## 🎯 学习目标

{{#each learning_objectives}}
- [请在此处输入this]
{{/each}}
{{/if}}

{{#if prerequisites}}
## ⚙️ 环境准备

### 系统要求
{{#each prerequisites.system}}
- [请在此处输入this]
{{/each}}

{{#if prerequisites.tools}}
### 工具安装
{{#each prerequisites.tools}}
- **{{this.name}}**: {{this.description}}{{#if this.version}} (版本: {{this.version}}){{/if}}
{{/each}}
{{/if}}
{{/if}}

## 📝 详细步骤

{{#if steps}}
{{#each steps}}
### 第{{@index}}步：{{this.title}}

{{this.content}}

{{#if this.code}}
```{{this.language}}
{{this.code}}
```
{{/if}}

{{#if this.note}}
> 💡 **提示：** {{this.note}}
{{/if}}

{{/each}}
[请在此处输入else]
### 第一步：[请在此处输入第一步标题 *]
[请在此处输入第一步内容 *]



### 第二步：[请在此处输入第二步标题 *]
[请在此处输入第二步内容 *]



### 第三步：[请在此处输入第三步标题 *]
[请在此处输入第三步内容 *]


{{/if}}

{{#if code_examples}}
## 💻 完整代码示例

{{#each code_examples}}
### {{this.title}}

```{{this.language}}
{{this.code}}
```

{{#if this.explanation}}
**代码说明：** {{this.explanation}}
{{/if}}

{{/each}}
{{/if}}

{{#if common_issues}}
## ⚠️ 常见问题

{{#each common_issues}}
**Q: {{this.question}}**

A: {{this.answer}}

{{/each}}
{{/if}}

{{#if verification}}
## ✅ 验证结果

[请在此处输入verification]
{{/if}}

{{#if next_steps}}
## 🚀 下一步

{{#each next_steps}}
- [请在此处输入this]
{{/each}}
{{/if}}

{{#if references}}
## 🔗 扩展阅读

{{#each references}}
- [{{this.title}}]({{this.url}})
{{/each}}
{{/if}}

---

<div style="text-align: center; padding: 20px; background: #f6ffed; border-radius: 8px; margin-top: 32px;">
<span style="color: #52c41a; font-size: 14px;">
📚 教程作者：[请在此处输入作者] | 📅 更新时间：{{date_format(current_date, 'YYYY-MM-DD')}}{{#if difficulty}} | 🎯 难度：[请在此处输入difficulty]{{/if}}
</span>
</div>19:Tc08,<!--
📝 模板使用说明：
- 这是基于"技术教程模板"模板生成的内容结构
- 请在 [请在此处输入...] 标记的位置填写相应内容
- 带 * 号的字段为必填项
- 填写完成后可以删除这些说明和占位符
- 支持 Markdown 格式
-->

<div style="text-align: center; margin-bottom: 32px;">
<span style="font-size: 28px; color: #1890ff; font-weight: bold;">🎯 [请在此处输入教程标题 *]</span>
<br>
{{#if difficulty}}
<span style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px; margin-top: 8px; display: inline-block;">[请在此处输入difficulty]级程</span>
{{/if}}
<br>
<span style="font-size: 14px; color: #666; margin-top: 8px;">{{date_format(current_date, 'YYYY年MM月DD日')}} · {{#if reading_time}}预计 [请在此处输入reading_time][请在此处输入else]技术教程{{/if}}</span>
</div>

{{#if overview}}
## 📋 教程概述

[请在此处输入教程概述 *]


{{/if}}

{{#if learning_objectives}}
## 🎯 学习目标

{{#each learning_objectives}}
- [请在此处输入this]
{{/each}}
{{/if}}

{{#if prerequisites}}
## ⚙️ 环境准备

### 系统要求
{{#each prerequisites.system}}
- [请在此处输入this]
{{/each}}

{{#if prerequisites.tools}}
### 工具安装
{{#each prerequisites.tools}}
- **{{this.name}}**: {{this.description}}{{#if this.version}} (版本: {{this.version}}){{/if}}
{{/each}}
{{/if}}
{{/if}}

## 📝 详细步骤

{{#if steps}}
{{#each steps}}
### 第{{@index}}步：{{this.title}}

{{this.content}}

{{#if this.code}}
```{{this.language}}
{{this.code}}
```
{{/if}}

{{#if this.note}}
> 💡 **提示：** {{this.note}}
{{/if}}

{{/each}}
[请在此处输入else]
### 第一步：[请在此处输入第一步标题 *]
[请在此处输入第一步内容 *]



### 第二步：[请在此处输入第二步标题 *]
[请在此处输入第二步内容 *]



### 第三步：[请在此处输入第三步标题 *]
[请在此处输入第三步内容 *]


{{/if}}

{{#if code_examples}}
## 💻 完整代码示例

{{#each code_examples}}
### {{this.title}}

```{{this.language}}
{{this.code}}
```

{{#if this.explanation}}
**代码说明：** {{this.explanation}}
{{/if}}

{{/each}}
{{/if}}

{{#if common_issues}}
## ⚠️ 常见问题

{{#each common_issues}}
**Q: {{this.question}}**

A: {{this.answer}}

{{/each}}
{{/if}}

{{#if verification}}
## ✅ 验证结果

[请在此处输入verification]
{{/if}}

{{#if next_steps}}
## 🚀 下一步

{{#each next_steps}}
- [请在此处输入this]
{{/each}}
{{/if}}

{{#if references}}
## 🔗 扩展阅读

{{#each references}}
- [{{this.title}}]({{this.url}})
{{/each}}
{{/if}}

---

<div style="text-align: center; padding: 20px; background: #f6ffed; border-radius: 8px; margin-top: 32px;">
<span style="color: #52c41a; font-size: 14px;">
📚 教程作者：[请在此处输入作者] | 📅 更新时间：{{date_format(current_date, 'YYYY-MM-DD')}}{{#if difficulty}} | 🎯 难度：[请在此处输入difficulty]{{/if}}
</span>
</div>1a:T201c,<p>这份工作聚焦赵炳南流派的中医皮肤科，提实现了 <strong>Skin-LLM</strong> 的构建方案，通过自指令循环、知识图谱引导的问答对生成，以及精细化的模型微调策略，完成了一个具有中医知识内涵的高质量大语言模型。</p><p>这是在2024年第三届知识图谱大赛（山东青岛）的工作，取得了当时所有参赛队伍测评分数的第一名。目前论文在投，以下是从技术报告中摘录的一些公开思路。</p><p><br></p><p><strong>一、自指令循环：借助知识图谱构造中医问答对</strong></p><p><strong>1.1 自指令循环机制概述</strong></p><p>自指令循环（Self-instruction Loop）是一种用于生成式语言模型训练的技术，核心思想是让模型自身构造训练样本，以最小化人工干预。在初始数据不足的情况下，该方法能通过模型自身生成高质量指令与响应对，极大地提高模型的泛化能力。</p><p>在 Skin-LLM 项目中，我们基于构建好的 <strong>中医皮肤科知识图谱</strong>，借助结构化知识信息，生成丰富且逻辑严谨的问答对，供后续有监督微调使用。</p><p><br></p><p><strong>1.2 图谱路径采样：从“名医”到“疾病”的语义链</strong></p><p>为了系统性地覆盖中医皮肤科的诊疗逻辑，我们定义了七条基于知识图谱的典型路径，体现了“名医-经验-用药-证候-疾病”的复杂联系结构。例如：</p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span><strong>路径1</strong>：</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 名医 → 临证经验 → 经验方 → 中药 → 疾病</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 体现名医通过经验方组成影响疾病治疗的路径。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span><strong>路径3</strong>：</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 名医 → 方剂 → 证候 → 临床表现 → 疾病</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 展示从方剂出发，经证候与临床表现连接疾病的推理链条。</li></ol><p>此类路径真实再现了中医的诊疗思维过程，为问答构造提供了语义基础。</p><p><br></p><p><strong>1.3 五元组表示法：统一输入格式结构化</strong></p><p>为了让语言模型更好地理解图谱信息，我们将路径转换为 <strong>五元组（5-tuple）形式</strong> 作为输入。格式如下：</p><p>五元组 = （头节点，关系，尾节点，节点属性，关系属性）</p><p>如路径1可转换为：</p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>(名医，有临证经验，临证经验，N/A，N/A)</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>(临证经验，临证经验使用，经验方，N/A，N/A)</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>...</li></ol><p>若某实体/关系已出现，则以 "Rep" 表示复用，减少冗余，提升输入效率。</p><p><br></p><p><strong>1.4 多轮自指令：三轮优化构造高质量问答对</strong></p><p>我们设定 <strong>三轮自指令循环</strong> 来构建结构清晰、语义严谨的中医问答对：</p><p><strong>第一轮：问题生成</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>按路径复杂度比例生成题目数（8/6/4/2）。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>提供示例问题模板与风格指令，引导模型生成与实体相关联的问题。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>输出结构为标准 JSON 格式选择题，包含：</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> {</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"type": "选择",</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"question": "... A...；B...；C...；D...",</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"answer": "A",</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"analysis": "详细推理过程..."</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>}</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span><br></li></ol><p><strong>第二轮：题面优化</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>根据原始路径和初代题面，优化题干清晰度、专业性与多跳推理深度。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>要求不可直接摘取答案，需跨多条路径联合推理。</li></ol><p><strong>第三轮：答案深化</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>分点论述，推理路径明确。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>禁止出现“根据知识图谱”类提示，答案必须自然内化于问答本身。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>强调 <strong>诊疗逻辑、证候机制、药物匹配性</strong> 等方面的分析。</li></ol><p>最终，我们构建了约 <strong>3000条高质量中医皮肤科问答对</strong>，为模型微调打下了坚实的数据基础。</p><p><img src="http://document.jyaochen.cn/image/paper/skin-llm/skin-llm.png"></p><p><strong>二、模型微调：Qwen2-7B + LoRA 高效训练</strong></p><p><strong>2.1 微调模型选择</strong></p><p>我们基于 <strong>Qwen2-7B-Chat</strong> 模型进行 LoRA 微调。</p><p><strong>训练平台</strong>：NVIDIA RTX 4090 24G</p><p> <strong>框架</strong>：LLaMA-Factory</p><p><br></p><p><strong>2.2 数据构建与输入格式</strong></p><p>将构建好的问答对转换为以下形式的训练样本：</p><p>{</p><p>&nbsp;"instruction": "朱仁康和张作舟在学术思想上最大的不同是什么？",</p><p>&nbsp;"input": "",</p><p>&nbsp;"output": "朱仁康强调中西医结合... 张作舟强调从毒论治..."</p><p>}</p><p>同时，为提升泛化能力，我们在微调中加入了 <strong>1000条通用数据集</strong>，保持与领域数据 <strong>1:3 的比例混合训练</strong>，防止过拟合。</p><p><br></p><p><strong>2.3 训练参数配置</strong></p><table><tbody><tr><td data-row="1"><strong>参数</strong></td><td data-row="1"><strong>值</strong></td><td data-row="1"><strong>说明</strong></td></tr><tr><td data-row="2">模型名称</td><td data-row="2">Qwen2-7B-Chat</td><td data-row="2">中文能力优秀</td></tr><tr><td data-row="3">微调方式</td><td data-row="3">LoRA</td><td data-row="3">高效低资源</td></tr><tr><td data-row="4">LoRA Rank</td><td data-row="4">8</td><td data-row="4">低秩矩阵秩</td></tr><tr><td data-row="5">LoRA Alpha</td><td data-row="5">16</td><td data-row="5">调整影响程度</td></tr><tr><td data-row="6">Batch Size</td><td data-row="6">2</td><td data-row="6">适配 4090 显存</td></tr><tr><td data-row="7">Epochs</td><td data-row="7">4</td><td data-row="7">平衡训练深度</td></tr><tr><td data-row="8">Learning Rate</td><td data-row="8">5e-5</td><td data-row="8">稳定更新</td></tr><tr><td data-row="9">Grad Accumulation</td><td data-row="9">8</td><td data-row="9">增加等效 batch</td></tr><tr><td data-row="10">Scheduler</td><td data-row="10">cosine</td><td data-row="10">余弦退火</td></tr></tbody></table><p><strong>三、总结与展望</strong></p><p>Skin-LLM 是一个高度垂直化的中医语言模型构建实例。其关键在于：</p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>利用结构化医学知识图谱，设计路径并转化为五元组；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>多轮自指令生成与精修，实现高质量问答对构建.</li></ol><p><br></p>1b:T201c,<p>这份工作聚焦赵炳南流派的中医皮肤科，提实现了 <strong>Skin-LLM</strong> 的构建方案，通过自指令循环、知识图谱引导的问答对生成，以及精细化的模型微调策略，完成了一个具有中医知识内涵的高质量大语言模型。</p><p>这是在2024年第三届知识图谱大赛（山东青岛）的工作，取得了当时所有参赛队伍测评分数的第一名。目前论文在投，以下是从技术报告中摘录的一些公开思路。</p><p><br></p><p><strong>一、自指令循环：借助知识图谱构造中医问答对</strong></p><p><strong>1.1 自指令循环机制概述</strong></p><p>自指令循环（Self-instruction Loop）是一种用于生成式语言模型训练的技术，核心思想是让模型自身构造训练样本，以最小化人工干预。在初始数据不足的情况下，该方法能通过模型自身生成高质量指令与响应对，极大地提高模型的泛化能力。</p><p>在 Skin-LLM 项目中，我们基于构建好的 <strong>中医皮肤科知识图谱</strong>，借助结构化知识信息，生成丰富且逻辑严谨的问答对，供后续有监督微调使用。</p><p><br></p><p><strong>1.2 图谱路径采样：从“名医”到“疾病”的语义链</strong></p><p>为了系统性地覆盖中医皮肤科的诊疗逻辑，我们定义了七条基于知识图谱的典型路径，体现了“名医-经验-用药-证候-疾病”的复杂联系结构。例如：</p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span><strong>路径1</strong>：</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 名医 → 临证经验 → 经验方 → 中药 → 疾病</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 体现名医通过经验方组成影响疾病治疗的路径。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span><strong>路径3</strong>：</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 名医 → 方剂 → 证候 → 临床表现 → 疾病</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> 展示从方剂出发，经证候与临床表现连接疾病的推理链条。</li></ol><p>此类路径真实再现了中医的诊疗思维过程，为问答构造提供了语义基础。</p><p><br></p><p><strong>1.3 五元组表示法：统一输入格式结构化</strong></p><p>为了让语言模型更好地理解图谱信息，我们将路径转换为 <strong>五元组（5-tuple）形式</strong> 作为输入。格式如下：</p><p>五元组 = （头节点，关系，尾节点，节点属性，关系属性）</p><p>如路径1可转换为：</p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>(名医，有临证经验，临证经验，N/A，N/A)</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>(临证经验，临证经验使用，经验方，N/A，N/A)</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>...</li></ol><p>若某实体/关系已出现，则以 "Rep" 表示复用，减少冗余，提升输入效率。</p><p><br></p><p><strong>1.4 多轮自指令：三轮优化构造高质量问答对</strong></p><p>我们设定 <strong>三轮自指令循环</strong> 来构建结构清晰、语义严谨的中医问答对：</p><p><strong>第一轮：问题生成</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>按路径复杂度比例生成题目数（8/6/4/2）。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>提供示例问题模板与风格指令，引导模型生成与实体相关联的问题。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>输出结构为标准 JSON 格式选择题，包含：</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span> {</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"type": "选择",</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"question": "... A...；B...；C...；D...",</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"answer": "A",</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>&nbsp;"analysis": "详细推理过程..."</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>}</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span><br></li></ol><p><strong>第二轮：题面优化</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>根据原始路径和初代题面，优化题干清晰度、专业性与多跳推理深度。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>要求不可直接摘取答案，需跨多条路径联合推理。</li></ol><p><strong>第三轮：答案深化</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>分点论述，推理路径明确。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>禁止出现“根据知识图谱”类提示，答案必须自然内化于问答本身。</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>强调 <strong>诊疗逻辑、证候机制、药物匹配性</strong> 等方面的分析。</li></ol><p>最终，我们构建了约 <strong>3000条高质量中医皮肤科问答对</strong>，为模型微调打下了坚实的数据基础。</p><p><img src="http://document.jyaochen.cn/image/paper/skin-llm/skin-llm.png"></p><p><strong>二、模型微调：Qwen2-7B + LoRA 高效训练</strong></p><p><strong>2.1 微调模型选择</strong></p><p>我们基于 <strong>Qwen2-7B-Chat</strong> 模型进行 LoRA 微调。</p><p><strong>训练平台</strong>：NVIDIA RTX 4090 24G</p><p> <strong>框架</strong>：LLaMA-Factory</p><p><br></p><p><strong>2.2 数据构建与输入格式</strong></p><p>将构建好的问答对转换为以下形式的训练样本：</p><p>{</p><p>&nbsp;"instruction": "朱仁康和张作舟在学术思想上最大的不同是什么？",</p><p>&nbsp;"input": "",</p><p>&nbsp;"output": "朱仁康强调中西医结合... 张作舟强调从毒论治..."</p><p>}</p><p>同时，为提升泛化能力，我们在微调中加入了 <strong>1000条通用数据集</strong>，保持与领域数据 <strong>1:3 的比例混合训练</strong>，防止过拟合。</p><p><br></p><p><strong>2.3 训练参数配置</strong></p><table><tbody><tr><td data-row="1"><strong>参数</strong></td><td data-row="1"><strong>值</strong></td><td data-row="1"><strong>说明</strong></td></tr><tr><td data-row="2">模型名称</td><td data-row="2">Qwen2-7B-Chat</td><td data-row="2">中文能力优秀</td></tr><tr><td data-row="3">微调方式</td><td data-row="3">LoRA</td><td data-row="3">高效低资源</td></tr><tr><td data-row="4">LoRA Rank</td><td data-row="4">8</td><td data-row="4">低秩矩阵秩</td></tr><tr><td data-row="5">LoRA Alpha</td><td data-row="5">16</td><td data-row="5">调整影响程度</td></tr><tr><td data-row="6">Batch Size</td><td data-row="6">2</td><td data-row="6">适配 4090 显存</td></tr><tr><td data-row="7">Epochs</td><td data-row="7">4</td><td data-row="7">平衡训练深度</td></tr><tr><td data-row="8">Learning Rate</td><td data-row="8">5e-5</td><td data-row="8">稳定更新</td></tr><tr><td data-row="9">Grad Accumulation</td><td data-row="9">8</td><td data-row="9">增加等效 batch</td></tr><tr><td data-row="10">Scheduler</td><td data-row="10">cosine</td><td data-row="10">余弦退火</td></tr></tbody></table><p><strong>三、总结与展望</strong></p><p>Skin-LLM 是一个高度垂直化的中医语言模型构建实例。其关键在于：</p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>利用结构化医学知识图谱，设计路径并转化为五元组；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>多轮自指令生成与精修，实现高质量问答对构建.</li></ol><p><br></p>1c:Tb7f,<p>📝 <strong>会议论文</strong>：CCKS-IJCKG 2024</p><p>🔍 <strong>论文地址</strong>：<strong style="background-color: rgb(255, 194, 102);"><a href="http://document.jyaochen.cn/paper/Enhancing%20Traditional%20Chinese%20Medicine%20Information%20Extraction%20Using%20Instruction-Tuned%20Large%20Models.pdf" rel="noopener noreferrer" target="_blank">地址</a></strong></p><p>📖 <strong>引用格式</strong>：Chen J, Xia S, Li J, Yu T. <em>Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models</em>. In: Proceedings of CCKS-IJCKG 2024. Springer CCIS, vol 2229, pp. 331–336. <a href="https://doi.org/10.1007/978-981-96-1809-5_25" rel="noopener noreferrer" target="_blank">https://doi.org/10.1007/978-981-96-1809-5_25</a></p><p><br></p><p>提出了一种基于伪代码的 <strong>大语言模型指令微调</strong> 的中医信息抽取范式，有效提升模型对专业术语和复杂关系的识别能力。</p><p><br></p><p>🚀<strong> 方法</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>💬 利用 GPT4-o 合成<strong>伪代码格式</strong>的指令集，通过结构化指令引导模型完成信息抽取；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>🧱 采用 LLaMA3-8B ，结合 <strong>LoRA</strong>进行指令微调；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>📦 构建包含1800篇中医期刊文献与200条中医医案的数据集，用于模型训练；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>🔍 抽取目标实体包括疾病、药物、治疗方式、证候等，最终输出结构化 JSON 结果，可直接用于中医知识图谱构建。</li></ol><p><br></p><p>📊<strong> 实验效果</strong></p><table><tbody><tr><td data-row="1"><strong>实验设置</strong></td><td data-row="1"><strong>模型</strong></td><td data-row="1"><strong>F1值 </strong>🎯</td></tr><tr><td data-row="2">有监督学习</td><td data-row="2">BERT</td><td data-row="2">0.79</td></tr><tr><td data-row="3">有监督学习</td><td data-row="3">LLaMA3</td><td data-row="3">0.71</td></tr><tr><td data-row="4">有监督学习</td><td data-row="4">TCM-LoRA- LLaMA3</td><td data-row="4"><strong>0.95</strong> ✅</td></tr><tr><td data-row="5">Zero-Shot</td><td data-row="5">LLaMA3</td><td data-row="5">0.45</td></tr><tr><td data-row="6">Zero-Shot</td><td data-row="6">TCM-LoRA- LLaMA3</td><td data-row="6"><strong>0.82</strong> 🔥</td></tr></tbody></table><p>结果表明：通过伪代码指令进行微调，显著提升了模型在有监督与零样本情境下的表现，尤其适用于高专业性场景中的实体抽取任务。</p><p><br></p><p>本方法为 <strong>中医知识图谱构建、智能问答系统、中医药文本结构化处理</strong> 等任务提供了高效可行的技术路径。</p>1d:Tb7f,<p>📝 <strong>会议论文</strong>：CCKS-IJCKG 2024</p><p>🔍 <strong>论文地址</strong>：<strong style="background-color: rgb(255, 194, 102);"><a href="http://document.jyaochen.cn/paper/Enhancing%20Traditional%20Chinese%20Medicine%20Information%20Extraction%20Using%20Instruction-Tuned%20Large%20Models.pdf" rel="noopener noreferrer" target="_blank">地址</a></strong></p><p>📖 <strong>引用格式</strong>：Chen J, Xia S, Li J, Yu T. <em>Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models</em>. In: Proceedings of CCKS-IJCKG 2024. Springer CCIS, vol 2229, pp. 331–336. <a href="https://doi.org/10.1007/978-981-96-1809-5_25" rel="noopener noreferrer" target="_blank">https://doi.org/10.1007/978-981-96-1809-5_25</a></p><p><br></p><p>提出了一种基于伪代码的 <strong>大语言模型指令微调</strong> 的中医信息抽取范式，有效提升模型对专业术语和复杂关系的识别能力。</p><p><br></p><p>🚀<strong> 方法</strong></p><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>💬 利用 GPT4-o 合成<strong>伪代码格式</strong>的指令集，通过结构化指令引导模型完成信息抽取；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>🧱 采用 LLaMA3-8B ，结合 <strong>LoRA</strong>进行指令微调；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>📦 构建包含1800篇中医期刊文献与200条中医医案的数据集，用于模型训练；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>🔍 抽取目标实体包括疾病、药物、治疗方式、证候等，最终输出结构化 JSON 结果，可直接用于中医知识图谱构建。</li></ol><p><br></p><p>📊<strong> 实验效果</strong></p><table><tbody><tr><td data-row="1"><strong>实验设置</strong></td><td data-row="1"><strong>模型</strong></td><td data-row="1"><strong>F1值 </strong>🎯</td></tr><tr><td data-row="2">有监督学习</td><td data-row="2">BERT</td><td data-row="2">0.79</td></tr><tr><td data-row="3">有监督学习</td><td data-row="3">LLaMA3</td><td data-row="3">0.71</td></tr><tr><td data-row="4">有监督学习</td><td data-row="4">TCM-LoRA- LLaMA3</td><td data-row="4"><strong>0.95</strong> ✅</td></tr><tr><td data-row="5">Zero-Shot</td><td data-row="5">LLaMA3</td><td data-row="5">0.45</td></tr><tr><td data-row="6">Zero-Shot</td><td data-row="6">TCM-LoRA- LLaMA3</td><td data-row="6"><strong>0.82</strong> 🔥</td></tr></tbody></table><p>结果表明：通过伪代码指令进行微调，显著提升了模型在有监督与零样本情境下的表现，尤其适用于高专业性场景中的实体抽取任务。</p><p><br></p><p>本方法为 <strong>中医知识图谱构建、智能问答系统、中医药文本结构化处理</strong> 等任务提供了高效可行的技术路径。</p>1e:T164d,<p>📝 <strong>发表期刊</strong>：《世界科学技术—中医药现代化》（<strong>北大中文核心</strong>）</p><p>🔍 <strong>论文地址</strong>：<strong style="background-color: rgb(255, 235, 204);"><u><a href="http://document.jyaochen.cn/paper/%E5%9F%BA%E4%BA%8E%E5%9B%BE%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E5%A2%9E%E5%BC%BA%E5%8F%A5%E5%B5%8C%E5%85%A5%E7%9A%84%E4%B8%AD%E5%8C%BB%E6%96%87%E7%8C%AE%E5%A4%9A%E6%A0%87%E7%AD%BE%E5%88%86%E7%B1%BB%E6%96%B9%E6%B3%95%E7%A0%94%E7%A9%B6.pdf" rel="noopener noreferrer" target="_blank">地址</a></u></strong></p><p>📖 <strong>引用格式</strong>：陈靖耀,李敬华,于彤.基于图神经网络增强句嵌入的中医文献多标签分类方法研究[J].世界科学技术-中医药现代化,2025,27(02):420-430.</p><p><br></p><p><br></p><p>研究聚焦于中医文献分类过程中所面临的 <strong>标签多样性</strong>、<strong>语义复杂性</strong> 与 <strong>样本不均衡</strong> 等挑战，提出了一种结合 <strong>图神经网络（GraphSAGE）</strong> 🧠 与 <strong>句嵌入（Sentence Embedding）</strong> 💬 的多标签分类方法，用于实现中医文献关键词的自动预测与分类。</p><h4>🛠️ 方法简介</h4><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>获取文献摘要的句嵌入向量；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>通过 <strong>BERTopic 模型</strong> 🔗 对句嵌入结果进行主题聚类，构建文献之间的语义关联；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>构建包含 <strong>文章-作者-主题</strong> 三类节点的异构网络 🌐；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>采用 <strong>GraphSAGE 图神经网络模型</strong>，基于重启随机游走算法进行图表示学习；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>最终将图嵌入与句嵌入特征融合，输入分类器进行多标签分类预测 🏷️。</li></ol><p><br></p><h4>📊 实验结果</h4><p>在 CNKI 中医文献数据集上，所提出模型表现优异，分类性能全面超越主流基线模型：相比 BERT、TextCNN 等模型，本方法在应对标签不均衡、多标签依赖等方面表现更加稳定可靠。</p><p><br></p><table><tbody><tr><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">特征向量</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Test accuracy</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Macro precision</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Weighted precision</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">F1</span></td></tr><tr><td data-row="2" class="ql-align-center"><strong style="color: rgb(12, 12, 12);">Our</strong></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.617</span></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.834</span></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.852</span></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.716</span></td></tr><tr><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Seq2Seq-Attention &nbsp;</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.567</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.78</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.782</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.651</span></td></tr><tr><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">TextCNN</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.516</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.719</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.735</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.585</span></td></tr><tr><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">BERT</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.453</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.563</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.601</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.468</span></td></tr><tr><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">句嵌入向量</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.309</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.365</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.384</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.352</span></td></tr></tbody></table><h4><br></h4><p>✨ 本方法有效融合图结构与语义特征，提升了中医文献的组织与检索能力，为 <strong>中医知识图谱构建</strong>、<strong>智能标签推荐</strong> 及 <strong>科研辅助决策</strong> 提供技术支持与理论参考。</p><p><br></p>1f:T164d,<p>📝 <strong>发表期刊</strong>：《世界科学技术—中医药现代化》（<strong>北大中文核心</strong>）</p><p>🔍 <strong>论文地址</strong>：<strong style="background-color: rgb(255, 235, 204);"><u><a href="http://document.jyaochen.cn/paper/%E5%9F%BA%E4%BA%8E%E5%9B%BE%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E5%A2%9E%E5%BC%BA%E5%8F%A5%E5%B5%8C%E5%85%A5%E7%9A%84%E4%B8%AD%E5%8C%BB%E6%96%87%E7%8C%AE%E5%A4%9A%E6%A0%87%E7%AD%BE%E5%88%86%E7%B1%BB%E6%96%B9%E6%B3%95%E7%A0%94%E7%A9%B6.pdf" rel="noopener noreferrer" target="_blank">地址</a></u></strong></p><p>📖 <strong>引用格式</strong>：陈靖耀,李敬华,于彤.基于图神经网络增强句嵌入的中医文献多标签分类方法研究[J].世界科学技术-中医药现代化,2025,27(02):420-430.</p><p><br></p><p><br></p><p>研究聚焦于中医文献分类过程中所面临的 <strong>标签多样性</strong>、<strong>语义复杂性</strong> 与 <strong>样本不均衡</strong> 等挑战，提出了一种结合 <strong>图神经网络（GraphSAGE）</strong> 🧠 与 <strong>句嵌入（Sentence Embedding）</strong> 💬 的多标签分类方法，用于实现中医文献关键词的自动预测与分类。</p><h4>🛠️ 方法简介</h4><ol><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>获取文献摘要的句嵌入向量；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>通过 <strong>BERTopic 模型</strong> 🔗 对句嵌入结果进行主题聚类，构建文献之间的语义关联；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>构建包含 <strong>文章-作者-主题</strong> 三类节点的异构网络 🌐；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>采用 <strong>GraphSAGE 图神经网络模型</strong>，基于重启随机游走算法进行图表示学习；</li><li data-list="bullet"><span class="ql-ui" contenteditable="false"></span>最终将图嵌入与句嵌入特征融合，输入分类器进行多标签分类预测 🏷️。</li></ol><p><br></p><h4>📊 实验结果</h4><p>在 CNKI 中医文献数据集上，所提出模型表现优异，分类性能全面超越主流基线模型：相比 BERT、TextCNN 等模型，本方法在应对标签不均衡、多标签依赖等方面表现更加稳定可靠。</p><p><br></p><table><tbody><tr><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">特征向量</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Test accuracy</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Macro precision</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Weighted precision</span></td><td data-row="1" class="ql-align-center"><span style="color: rgb(12, 12, 12);">F1</span></td></tr><tr><td data-row="2" class="ql-align-center"><strong style="color: rgb(12, 12, 12);">Our</strong></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.617</span></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.834</span></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.852</span></td><td data-row="2" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.716</span></td></tr><tr><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">Seq2Seq-Attention &nbsp;</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.567</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.78</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.782</span></td><td data-row="3" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.651</span></td></tr><tr><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">TextCNN</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.516</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.719</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.735</span></td><td data-row="4" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.585</span></td></tr><tr><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">BERT</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.453</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.563</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.601</span></td><td data-row="5" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.468</span></td></tr><tr><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">句嵌入向量</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.309</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.365</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.384</span></td><td data-row="6" class="ql-align-center"><span style="color: rgb(12, 12, 12);">0.352</span></td></tr></tbody></table><h4><br></h4><p>✨ 本方法有效融合图结构与语义特征，提升了中医文献的组织与检索能力，为 <strong>中医知识图谱构建</strong>、<strong>智能标签推荐</strong> 及 <strong>科研辅助决策</strong> 提供技术支持与理论参考。</p><p><br></p>20:Tef2,<!--
📝 模板使用说明：
- 这是基于"开源项目介绍模板"模板生成的内容结构
- 请在 [请在此处输入...] 标记的位置填写相应内容
- 带 * 号的字段为必填项
- 填写完成后可以删除这些说明和占位符
- 支持 Markdown 格式
-->

<div style="text-align: center; margin-bottom: 32px;">
<span style="font-size: 32px; color: #722ed1; font-weight: bold;">🚀 [请在此处输入project_name]</span>
<br>
<span style="font-size: 18px; color: #1890ff; margin-top: 8px;">[请在此处输入title]</span>
<br>
{{#if github_url}}
<a href="[请在此处输入github_url]" style="display: inline-block; background: #24292e; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px;">
📦 GitHub Repository
</a>
{{/if}}
{{#if demo_url}}
<a href="[请在此处输入demo_url]" style="display: inline-block; background: #52c41a; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px; margin-left: 8px;">
🌐 在线演示
</a>
{{/if}}
</div>

{{#if badges}}
<div style="text-align: center; margin: 24px 0;">
{{#each badges}}
<img src="{{this.url}}" alt="{{this.alt}}" style="margin: 2px;">
{{/each}}
</div>
{{/if}}

## 📖 项目简介

[请在此处输入description]

{{#if features}}
## ✨ 核心功能

{{#each features}}
- **{{this.name}}**: {{this.description}}
{{/each}}
{{/if}}

{{#if tech_stack}}
## 🛠️ 技术栈

{{#each tech_stack}}
- **{{this.category}}**: {{#each this.technologies}}[请在此处输入this]{{#if !@last}}, {{/if}}{{/each}}
{{/each}}
{{/if}}

## 🚀 快速开始

{{#if installation}}
### 安装

{{#if installation.prerequisites}}
**前置要求：**
{{#each installation.prerequisites}}
- [请在此处输入this]
{{/each}}
{{/if}}

```bash
{{installation.command}}
```
{{/if}}

{{#if usage}}
### 基本使用

```{{usage.language}}
{{usage.code}}
```

{{#if usage.explanation}}
{{usage.explanation}}
{{/if}}
{{/if}}

{{#if examples}}
## 📚 使用示例

{{#each examples}}
### {{this.title}}

{{this.description}}

```{{this.language}}
{{this.code}}
```

{{#if this.output}}
**输出结果：**
```
{{this.output}}
```
{{/if}}

{{/each}}
{{/if}}

{{#if api_docs}}
## 📋 API 文档

{{#each api_docs}}
### {{this.method}} {{this.endpoint}}

{{this.description}}

**参数：**
{{#each this.parameters}}
- `{{this.name}}` ({{this.type}}): {{this.description}}{{#if this.required}} *必需*{{/if}}
{{/each}}

**响应示例：**
```json
{{this.response_example}}
```

{{/each}}
{{/if}}

{{#if configuration}}
## ⚙️ 配置选项

{{#each configuration}}
- **{{this.option}}**: {{this.description}}{{#if this.default}} (默认: `{{this.default}}`){{/if}}
{{/each}}
{{/if}}

{{#if roadmap}}
## 🗺️ 开发路线图

{{#each roadmap}}
- {{#if this.completed}}✅[请在此处输入else]🔲{{/if}} **{{this.version}}**: {{this.description}}{{#if this.eta}} (预计: {{this.eta}}){{/if}}
{{/each}}
{{/if}}

{{#if contributing}}
## 🤝 贡献指南

[请在此处输入contributing]

{{#if contribution_types}}
### 贡献方式

{{#each contribution_types}}
- **{{this.type}}**: {{this.description}}
{{/each}}
{{/if}}
{{/if}}

{{#if license}}
## 📄 开源协议

本项目采用 [[请在此处输入license]]([请在此处输入license_url]) 开源协议。
{{/if}}

{{#if acknowledgments}}
## 🙏 致谢

{{#each acknowledgments}}
- [请在此处输入this]
{{/each}}
{{/if}}

---

<div style="text-align: center; padding: 20px; background: #f0f2f5; border-radius: 8px; margin-top: 32px;">
<span style="color: #666; font-size: 14px;">
👨‍💻 项目作者：[请在此处输入author] | 🏷️ 语言：{{#if main_language}}[请在此处输入main_language][请在此处输入else]多语言{{/if}} | ⭐ 如果觉得有用，请给个 Star！
</span>
</div>21:Tef2,<!--
📝 模板使用说明：
- 这是基于"开源项目介绍模板"模板生成的内容结构
- 请在 [请在此处输入...] 标记的位置填写相应内容
- 带 * 号的字段为必填项
- 填写完成后可以删除这些说明和占位符
- 支持 Markdown 格式
-->

<div style="text-align: center; margin-bottom: 32px;">
<span style="font-size: 32px; color: #722ed1; font-weight: bold;">🚀 [请在此处输入project_name]</span>
<br>
<span style="font-size: 18px; color: #1890ff; margin-top: 8px;">[请在此处输入title]</span>
<br>
{{#if github_url}}
<a href="[请在此处输入github_url]" style="display: inline-block; background: #24292e; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px;">
📦 GitHub Repository
</a>
{{/if}}
{{#if demo_url}}
<a href="[请在此处输入demo_url]" style="display: inline-block; background: #52c41a; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px; margin-left: 8px;">
🌐 在线演示
</a>
{{/if}}
</div>

{{#if badges}}
<div style="text-align: center; margin: 24px 0;">
{{#each badges}}
<img src="{{this.url}}" alt="{{this.alt}}" style="margin: 2px;">
{{/each}}
</div>
{{/if}}

## 📖 项目简介

[请在此处输入description]

{{#if features}}
## ✨ 核心功能

{{#each features}}
- **{{this.name}}**: {{this.description}}
{{/each}}
{{/if}}

{{#if tech_stack}}
## 🛠️ 技术栈

{{#each tech_stack}}
- **{{this.category}}**: {{#each this.technologies}}[请在此处输入this]{{#if !@last}}, {{/if}}{{/each}}
{{/each}}
{{/if}}

## 🚀 快速开始

{{#if installation}}
### 安装

{{#if installation.prerequisites}}
**前置要求：**
{{#each installation.prerequisites}}
- [请在此处输入this]
{{/each}}
{{/if}}

```bash
{{installation.command}}
```
{{/if}}

{{#if usage}}
### 基本使用

```{{usage.language}}
{{usage.code}}
```

{{#if usage.explanation}}
{{usage.explanation}}
{{/if}}
{{/if}}

{{#if examples}}
## 📚 使用示例

{{#each examples}}
### {{this.title}}

{{this.description}}

```{{this.language}}
{{this.code}}
```

{{#if this.output}}
**输出结果：**
```
{{this.output}}
```
{{/if}}

{{/each}}
{{/if}}

{{#if api_docs}}
## 📋 API 文档

{{#each api_docs}}
### {{this.method}} {{this.endpoint}}

{{this.description}}

**参数：**
{{#each this.parameters}}
- `{{this.name}}` ({{this.type}}): {{this.description}}{{#if this.required}} *必需*{{/if}}
{{/each}}

**响应示例：**
```json
{{this.response_example}}
```

{{/each}}
{{/if}}

{{#if configuration}}
## ⚙️ 配置选项

{{#each configuration}}
- **{{this.option}}**: {{this.description}}{{#if this.default}} (默认: `{{this.default}}`){{/if}}
{{/each}}
{{/if}}

{{#if roadmap}}
## 🗺️ 开发路线图

{{#each roadmap}}
- {{#if this.completed}}✅[请在此处输入else]🔲{{/if}} **{{this.version}}**: {{this.description}}{{#if this.eta}} (预计: {{this.eta}}){{/if}}
{{/each}}
{{/if}}

{{#if contributing}}
## 🤝 贡献指南

[请在此处输入contributing]

{{#if contribution_types}}
### 贡献方式

{{#each contribution_types}}
- **{{this.type}}**: {{this.description}}
{{/each}}
{{/if}}
{{/if}}

{{#if license}}
## 📄 开源协议

本项目采用 [[请在此处输入license]]([请在此处输入license_url]) 开源协议。
{{/if}}

{{#if acknowledgments}}
## 🙏 致谢

{{#each acknowledgments}}
- [请在此处输入this]
{{/each}}
{{/if}}

---

<div style="text-align: center; padding: 20px; background: #f0f2f5; border-radius: 8px; margin-top: 32px;">
<span style="color: #666; font-size: 14px;">
👨‍💻 项目作者：[请在此处输入author] | 🏷️ 语言：{{#if main_language}}[请在此处输入main_language][请在此处输入else]多语言{{/if}} | ⭐ 如果觉得有用，请给个 Star！
</span>
</div>16:["$","$L17",null,{"sortedCategories":[{"categoryName":"项目实战","projects":[{"id":29,"slug":"project-13","name":"TCM Text Data Processing Tool","description":"A comprehensive toolkit for TCM text processing and analysis.","link":{"href":"https://github.com/JYao-Chen/TCM_Toolkit","label":"TCM Text Data Processing Tool"},"github_link":null,"logo":"","icon":"iconic:scipy","featured":false,"type":"work","is_github_project":false,"has_detail_page":true,"content":"A comprehensive toolkit for TCM text processing and analysis.","detail_content":"A comprehensive toolkit for TCM text processing and analysis.","project_order":4,"date":"2025-04-14T10:14:30","display_date":"2025-04-14T10:14:30","created_at":"2025-04-14T10:14:30","updated_at":"2025-07-18T14:35:24","category":"$undefined","category_icon":null,"tags":["项目实战"],"tag_objects":[{"name":"项目实战","slug":"project-hand","description":"项目实战测试","color":"#f43f5e","icon":"lucide:drafting-compass","category":"content","id":14,"created_at":"2025-07-10T20:02:23","updated_at":"2025-07-18T12:45:32"}],"tech_stack":[],"categories":[],"module_categories":[]}],"icon":"lucide:drafting-compass","description":"项目实战测试","display_order":500,"id":14,"slug":"project-hand"},{"categoryName":"AI","projects":[{"id":39,"slug":"xiang-mu-ce-shi-xiang-qing-ye","name":"项目测试详情页","description":"","link":"$undefined","github_link":null,"logo":null,"icon":"iconic:gemini","featured":false,"type":"work","is_github_project":false,"has_detail_page":true,"content":"$18","detail_content":"$19","project_order":0,"date":"2025-07-14T18:59:37","display_date":"2025-07-14T18:59:37","created_at":"2025-07-14T19:00:10","updated_at":"2025-07-19T07:42:12","category":"AI","category_icon":"iconic:gemini","tags":["AI"],"tag_objects":[{"name":"AI","slug":"ai","description":"llm","color":"#3b82f6","icon":"iconic:gemini","category":"tech","id":10,"created_at":"2025-07-10T20:02:23","updated_at":"2025-07-17T11:56:58"}],"tech_stack":["AI"],"categories":[],"module_categories":[]}],"icon":"iconic:gemini","description":"llm","display_order":500,"id":10,"slug":"ai"},{"categoryName":"Python","projects":[{"id":30,"slug":"project-14","name":"Skin-LLM","description":"中医皮肤科赵炳南流派 Skin-LLM 模型","link":"$undefined","github_link":null,"logo":"","icon":"iconic:blender","featured":false,"type":"work","is_github_project":false,"has_detail_page":true,"content":"$1a","detail_content":"$1b","project_order":5,"date":"2025-04-14T10:06:31","display_date":"2025-04-14T10:06:31","created_at":"2025-04-14T18:06:31","updated_at":"2025-07-18T14:35:23","category":"Python","category_icon":null,"tags":["Python"],"tag_objects":[{"name":"Python","slug":"python","description":null,"color":"#f59e0b","icon":null,"category":"tech","id":5,"created_at":"2025-07-10T20:02:23","updated_at":"2025-07-12T10:01:07"}],"tech_stack":["Python"],"categories":[],"module_categories":[]}],"icon":null,"description":null,"display_order":500,"id":5,"slug":"python"},{"categoryName":"其他","projects":[{"id":28,"slug":"project-12","name":"Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models","description":"提出了一种基于伪代码的 大语言模型指令微调 的中医信息抽取范式，有效提升模型对专业术语和复杂关系的识别能力。","link":"$undefined","github_link":null,"logo":"","icon":null,"featured":true,"type":"work","is_github_project":false,"has_detail_page":true,"content":"$1c","detail_content":"$1d","project_order":1,"date":"2025-04-14T08:04:30","display_date":"2025-04-14T08:04:30","created_at":"2025-04-14T08:04:30","updated_at":"2025-07-18T14:35:24","category":"$undefined","category_icon":null,"tags":[],"tag_objects":[],"tech_stack":[],"categories":[],"module_categories":[]},{"id":27,"slug":"project-11","name":"基于图神经网络增强句嵌入的中医文献多标签分类方法研究 ","description":"提出一种融合图神经网络与句嵌入的中医文献多标签分类方法，显著提升关键词自动预测的准确性与语义表达能力。","link":"$undefined","github_link":null,"logo":"","icon":"GatewayOutlined","featured":true,"type":"work","is_github_project":false,"has_detail_page":true,"content":"$1e","detail_content":"$1f","project_order":2,"date":"2025-04-14T07:25:11","display_date":"2025-04-14T07:25:11","created_at":"2025-04-14T07:25:11","updated_at":"2025-07-18T14:35:24","category":"$undefined","category_icon":null,"tags":[],"tag_objects":[],"tech_stack":[],"categories":[],"module_categories":[]},{"id":26,"slug":"project-10","name":"2nd TCM-KG Competition 2023","description":"第二届中医药知识图谱大赛第一名方案","link":"$undefined","github_link":null,"logo":"","icon":"lucide:wifi-cog","featured":false,"type":"work","is_github_project":false,"has_detail_page":true,"content":"<p><strong>本方案在第二届中医药知识图谱大赛（山东济南，2023）中获得第一名。</strong></p><p><strong style=\"color: rgb(0, 102, 204); background-color: rgb(204, 232, 204);\"><u><a href=\"http://document.jyaochen.cn/file/report/tcm_kg_com_2nd_2023.pdf\" rel=\"noopener noreferrer\" target=\"_blank\">技术报告</a></u></strong></p><h3><br></h3><p>在知识图谱构建方面，采用多种方法提升信息抽取的准确性与图谱质量。通过融合大语言模型对中医医案文本进行实体识别与关系抽取的数据增强<span class=\"ql-size-small\"><span class=\"ql-cursor\">﻿﻿﻿﻿﻿﻿﻿﻿﻿</span></span>，运用替换、掩码、合并、扩展等策略，提升模型对专业术语与罕见实体的识别能力，实体识别准确率达98%以上。结合GPT-4的提示学习技术，围绕中药、诊断、治疗等九类实体以及六类关系实现高质量结构化抽取。通过LangChain框架，通过预设实体类型约束与JSON格式输出，实现知识图谱的自动构建。最终图谱包含36,746个实体与123,358条关系，整合自SymMap与天池大赛数据，覆盖中医药多个核心要素。</p><p>在问答应用方面，实现用户输入语义的准确理解及相关实体的高效检索，显著提升意图识别效果。进一步将GPT-4与Neo4j图数据库结合，构建融合结构化知识的问答系统。</p>","detail_content":"<p><strong>本方案在第二届中医药知识图谱大赛（山东济南，2023）中获得第一名。</strong></p><p><strong style=\"color: rgb(0, 102, 204); background-color: rgb(204, 232, 204);\"><u><a href=\"http://document.jyaochen.cn/file/report/tcm_kg_com_2nd_2023.pdf\" rel=\"noopener noreferrer\" target=\"_blank\">技术报告</a></u></strong></p><h3><br></h3><p>在知识图谱构建方面，采用多种方法提升信息抽取的准确性与图谱质量。通过融合大语言模型对中医医案文本进行实体识别与关系抽取的数据增强<span class=\"ql-size-small\"><span class=\"ql-cursor\">﻿﻿﻿﻿﻿﻿﻿﻿﻿</span></span>，运用替换、掩码、合并、扩展等策略，提升模型对专业术语与罕见实体的识别能力，实体识别准确率达98%以上。结合GPT-4的提示学习技术，围绕中药、诊断、治疗等九类实体以及六类关系实现高质量结构化抽取。通过LangChain框架，通过预设实体类型约束与JSON格式输出，实现知识图谱的自动构建。最终图谱包含36,746个实体与123,358条关系，整合自SymMap与天池大赛数据，覆盖中医药多个核心要素。</p><p>在问答应用方面，实现用户输入语义的准确理解及相关实体的高效检索，显著提升意图识别效果。进一步将GPT-4与Neo4j图数据库结合，构建融合结构化知识的问答系统。</p>","project_order":3,"date":"2025-04-12T20:24:26","display_date":"2025-04-12T20:24:26","created_at":"2025-04-12T20:24:26","updated_at":"2025-07-18T14:35:24","category":"$undefined","category_icon":null,"tags":[],"tag_objects":[],"tech_stack":[],"categories":[],"module_categories":[]},{"id":37,"slug":"ce-shi-xiang-mu","name":"测试项目","description":"","link":"$undefined","github_link":null,"logo":null,"icon":null,"featured":false,"type":"work","is_github_project":false,"has_detail_page":true,"content":"$20","detail_content":"$21","project_order":0,"date":"2025-07-12T13:57:35","display_date":"2025-07-12T13:57:35","created_at":"2025-07-12T13:57:06","updated_at":"2025-07-18T14:35:23","category":"$undefined","category_icon":null,"tags":[],"tag_objects":[],"tech_stack":[],"categories":[],"module_categories":[]},{"id":25,"slug":"project-9","name":"TCM-IE-LLM","description":" Data example for Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models. CCKS-IJCKG 2024","link":{"href":"https://github.com/JYao-Chen/TCM-IE-LLM","label":"TCM-IE-LLM"},"github_link":null,"logo":"","icon":"RobotOutlined","featured":true,"type":"work","is_github_project":false,"has_detail_page":true,"content":" Data example for Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models. CCKS-IJCKG 2024","detail_content":" Data example for Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models. CCKS-IJCKG 2024","project_order":0,"date":"2025-04-12T09:07:41","display_date":"2025-04-12T09:07:41","created_at":"2025-04-12T09:07:41","updated_at":"2025-07-18T14:35:24","category":"$undefined","category_icon":null,"tags":[],"tag_objects":[],"tech_stack":[],"categories":[],"module_categories":[]}],"icon":null,"description":null,"display_order":999,"id":0,"slug":"other"}],"pagesConfig":{"blogs":{"title":"Thoughts","description":"Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation."},"projects":{"title":"Featured ","description":"A showcase of my research projects and applications in Large Language Models and Traditional Chinese Medicine."},"gallery":{"title":"Gallery","description":"Explore my photography collection through different perspectives - timeline memories, organized albums, or browse all photos in a grid."}}}]
