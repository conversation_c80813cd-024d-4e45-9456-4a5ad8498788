3:I[4707,[],""]
4:I[36423,[],""]
5:I[93285,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
6:I[46021,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Providers"]
7:I[83258,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"ThemeInitializer"]
8:I[59183,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"RouteProgressBar"]
9:I[62989,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"GlobalLoadingManager"]
a:I[20686,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"SmartPrefetch"]
b:I[3864,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Header"]
c:I[72972,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],""]
d:I[18908,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Footer"]
e:I[60827,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
f:I[42545,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PlausibleAnalytics"]
10:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceMonitor"]
11:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceDebugger"]
0:["7n7atQT5AqmloxjVA4Y6t",[[["",{"children":["about",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["about",{"children":["__PAGE__",{},[["$L1","$L2",null],null],null]},[null,["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","about","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/8b6d6f69c7970b5a.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/72074f6a7392446a.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","className":"h-full antialiased","suppressHydrationWarning":true,"children":["$","body",null,{"className":"flex h-full","children":["$","$L5",null,{"children":["$","$L6",null,{"children":[["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","div",null,{"className":"flex w-full","children":[["$","$La",null,{"routes":["/","/about","/projects","/blogs","/gallery"],"priority":"low","delay":1500}],["$","div",null,{"className":"fixed inset-0 flex justify-center sm:px-8","children":["$","div",null,{"className":"flex w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"w-full shadow-xl dark:bg-muted"}]}]}],["$","div",null,{"className":"relative flex w-full flex-col px-4 sm:px-0","children":[["$","$Lb",null,{}],["$","main",null,{"className":"flex-auto","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","div",null,{"className":"sm:px-8 flex h-full items-center pt-16 sm:pt-32","children":["$","div",null,{"className":"mx-auto w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"relative px-4 sm:px-8 lg:px-12","children":["$","div",null,{"className":"mx-auto max-w-2xl lg:max-w-5xl","children":["$","div",null,{"className":"flex flex-col items-center","children":[["$","p",null,{"className":"text-base font-semibold text-zinc-400 dark:text-zinc-500","children":"404"}],["$","h1",null,{"className":"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100","children":"Page not found"}],["$","p",null,{"className":"mt-4 text-base text-zinc-600 dark:text-zinc-400","children":"Sorry, we couldn’t find the page you’re looking for."}],["$","$Lc",null,{"className":"inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70 mt-4","href":"/","children":"Go back home"}]]}]}]}]}]}],"notFoundStyles":[]}]}],["$","$Ld",null,{}]]}]]}],[null,["$","$Le",null,{}],["$","$Lf",null,{}]],["$","$L10",null,{}],["$","$L11",null,{}]]}]}]}]}]],null],null],["$L12",null]]]]
12:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"About Jingyao Chen - Award-Winning Designer & Developer"}],["$","meta","3",{"name":"description","content":"Learn more about Jingyao Chen, an international Red Dot Award designer and full-stack developer based in Guangzhou. Discover my background, professional journey, skills, and passion for creating innovative solutions that bridge design and technology."}],["$","meta","4",{"name":"author","content":"Jingyao Chen"}],["$","meta","5",{"name":"keywords","content":"About Jingyao Chen,Red Dot Award Designer,Full Stack Developer,Guangzhou,Professional Background,Design Skills,Development Experience,Innovation,Creative Professional"}],["$","meta","6",{"name":"creator","content":"Jingyao Chen"}],["$","meta","7",{"name":"publisher","content":"Jingyao Chen"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"theme-color","content":"#171717"}],["$","meta","10",{"name":"msapplication-TileColor","content":"#171717"}],["$","link","11",{"rel":"canonical","href":"http://**************:3000/about"}],["$","meta","12",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","13",{"property":"og:title","content":"About Jingyao Chen - Award-Winning Designer & Developer"}],["$","meta","14",{"property":"og:description","content":"Get to know Jingyao Chen, an international Red Dot Award designer and full-stack developer. Professional journey, skills, and passion for innovation."}],["$","meta","15",{"property":"og:url","content":"http://**************:3000/about"}],["$","meta","16",{"property":"og:site_name","content":"Jingyao Chen Portfolio"}],["$","meta","17",{"property":"og:locale","content":"en_US"}],["$","meta","18",{"property":"og:image","content":"http://**************:3000/images/about-jingyao-professional.jpg"}],["$","meta","19",{"property":"og:image:width","content":"1200"}],["$","meta","20",{"property":"og:image:height","content":"630"}],["$","meta","21",{"property":"og:image:alt","content":"About Jingyao Chen - Award-Winning Designer & Developer"}],["$","meta","22",{"property":"og:type","content":"website"}],["$","meta","23",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","24",{"name":"twitter:site","content":"@JingyaoC"}],["$","meta","25",{"name":"twitter:creator","content":"@JingyaoC"}],["$","meta","26",{"name":"twitter:title","content":"About Jingyao Chen - Award-Winning Designer & Developer"}],["$","meta","27",{"name":"twitter:description","content":"Get to know Jingyao Chen, an international Red Dot Award designer and full-stack developer. Professional journey, skills, and passion for innovation."}],["$","meta","28",{"name":"twitter:image","content":"http://**************:3000/images/about-jingyao-professional.jpg"}],["$","link","29",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"902x902"}]]
1:null
13:I[65878,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","301","static/chunks/app/about/page-395e2081ea9f7d67.js"],"Image"]
2:["$","div",null,{"className":"sm:px-8 mt-16 sm:mt-32","children":["$","div",null,{"className":"mx-auto w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"relative px-4 sm:px-8 lg:px-12","children":["$","div",null,{"className":"mx-auto max-w-2xl lg:max-w-5xl","children":["$","div",null,{"className":"grid grid-cols-1 gap-y-16 lg:grid-cols-2 lg:grid-rows-[auto_1fr] lg:gap-y-12","children":[["$","div",null,{"className":"lg:pl-20","children":["$","div",null,{"className":"max-w-xs px-2.5 lg:max-w-none","children":["$","$L13",null,{"src":"/images/profile/me.jpg","alt":"JYao","width":800,"height":800,"sizes":"(min-width: 1024px) 32rem, 20rem","className":"object-cover bg-zinc-100 dark:bg-zinc-800 w-80 h-80 rounded-2xl rotate-3 aspect-square"}]}]}],["$","div",null,{"className":"lg:order-first lg:row-span-2","children":[["$","h1",null,{"className":"text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100","children":"About Me"}],["$","div",null,{"className":"mt-6 prose prose-zinc dark:prose-invert max-w-none","children":[["$","h1",null,{"className":"text-3xl font-bold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"About Me"}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"🎓 Education & Research"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":["I'm a Master's candidate in ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Information Science"}]," at the ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Institute of Information on Traditional Chinese Medicine"}]," (",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"CINTCM"}],"), ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"China Academy of Chinese Medical Sciences"}]," (",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"CACMS"}],")."]}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":["Currently, I'm working at the ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"TCM Health Intelligence R&D Center"}]," under the supervision of Senior Researcher ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Tong Yu"}]," since 2023."]}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"🔬 Research Focus"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":["My primary research centers on developing and applying ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Large Language Models"}]," and ",["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Knowledge Graphs"}]," in the field of Traditional Chinese Medicine, with particular emphasis on:"]}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"🤖 Advanced LLM Development"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":"Designing and fine-tuning specialized LLM architectures for TCM knowledge understanding and clinical reasoning"}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"🔄 Multi-modal Learning"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":"Integrating textual, structural, and domain knowledge for comprehensive TCM knowledge representation"}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"⚡ Clinical Intelligence"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":"Building AI-powered systems for TCM clinical decision support and knowledge discovery"}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"🏆 Research Impact"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":"I'm actively contributing to several high-impact initiatives:"}],"\n",["$","blockquote",null,{"className":"border-l-4 border-teal-500 pl-4 italic text-zinc-600 dark:text-zinc-400 my-4","children":["\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Key Projects:"}]}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"National Key R&D Program"}],": \"Research on Key Technologies for Intelligent Traditional Chinese Medicine Health Services\" (2022YFC3501500)"]}],"\n",["$","li","li-1",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"CACMS Innovation Fund"}],": \"Construction and Application of Traditional Chinese Medicine Knowledge Graph Based on Large Language Models\" (CI2023A00512)"]}],"\n",["$","li","li-2",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Beijing Natural Science Foundation"}],": \"Research on Multi-modal Knowledge Graph Construction and Reasoning Methods for Traditional Chinese Medicine\" (4232037)"]}],"\n"]}],"\n"]}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"📊 Research Metrics"}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Publications"}],": 8+ peer-reviewed papers in top-tier journals"]}],"\n",["$","li","li-1",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Patents"}],": 3 invention patents filed"]}],"\n",["$","li","li-2",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Datasets"}],": 2 large-scale TCM datasets released"]}],"\n",["$","li","li-3",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Citations"}],": 150+ citations (Google Scholar)"]}],"\n"]}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"💻 Technical Expertise"}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"Programming & Development"}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Languages"}],": Python, JavaScript, TypeScript, SQL, R"]}],"\n",["$","li","li-1",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Frameworks"}],": FastAPI, React, Next.js, PyTorch, TensorFlow"]}],"\n",["$","li","li-2",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Databases"}],": PostgreSQL, MySQL, Neo4j, MongoDB"]}],"\n",["$","li","li-3",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Cloud"}],": AWS, Azure, Docker, Kubernetes"]}],"\n"]}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"AI & Machine Learning"}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Deep Learning"}],": Transformer architectures, BERT, GPT, T5"]}],"\n",["$","li","li-1",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Knowledge Graphs"}],": Neo4j, RDF, SPARQL, Graph Neural Networks"]}],"\n",["$","li","li-2",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"NLP"}],": Text mining, Named Entity Recognition, Relation Extraction"]}],"\n",["$","li","li-3",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"MLOps"}],": Model deployment, monitoring, and versioning"]}],"\n"]}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"🌟 Professional Experience"}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"Research Assistant | CACMS (2023-Present)"}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":"Leading development of TCM-specific large language models"}],"\n",["$","li","li-1",{"children":"Designing knowledge graph construction pipelines"}],"\n",["$","li","li-2",{"children":"Collaborating with clinical experts on AI system validation"}],"\n"]}],"\n",["$","h3",null,{"className":"text-xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-6 mb-3","children":"Software Engineer Intern | Tech Company (2022)"}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":"Developed full-stack web applications using React and Node.js"}],"\n",["$","li","li-1",{"children":"Implemented machine learning models for data analysis"}],"\n",["$","li","li-2",{"children":"Contributed to open-source projects"}],"\n"]}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"🎯 Current Goals"}],"\n",["$","ul",null,{"className":"list-disc list-inside text-zinc-600 dark:text-zinc-400 space-y-2 mb-4","children":["\n",["$","li","li-0",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Short-term"}],": Complete my Master's thesis on \"Multi-modal Knowledge Graph Construction for Traditional Chinese Medicine\""]}],"\n",["$","li","li-1",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Medium-term"}],": Pursue PhD in AI/ML with focus on healthcare applications"]}],"\n",["$","li","li-2",{"children":[["$","strong",null,{"className":"font-semibold text-teal-600 dark:text-teal-400","children":"Long-term"}],": Bridge the gap between traditional medicine and modern AI technology"]}],"\n"]}],"\n",["$","h2",null,{"className":"text-2xl font-semibold tracking-tight text-zinc-800 dark:text-zinc-100 mt-8 mb-4","children":"📫 Let's Connect"}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":"I'm always interested in discussing research collaborations, technical challenges, or career opportunities in AI and healthcare. Feel free to reach out!"}],"\n",["$","hr","hr-0",{}],"\n",["$","p",null,{"className":"text-base text-zinc-600 dark:text-zinc-400 leading-relaxed mb-4","children":[["$","em","em-0",{"children":"\"The best way to predict the future is to invent it.\""}]," - Alan Kay"]}]]}]]}],["$","div",null,{"className":"lg:pl-20","children":"$L14"}]]}]}]}]}]}]
15:I[95744,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","301","static/chunks/app/about/page-395e2081ea9f7d67.js"],"default"]
14:["$","$L15",null,{"className":"$undefined","socialLinks":[{"name":"Github","icon":"lucide:github","href":"https://github.com/JYao-Chen"},{"name":"HuggingFace","icon":"tech-stack:huggingface","href":"https://huggingface.co/JYaooo"},{"name":"Email","icon":"email","href":"mailto:<EMAIL>"},{"name":"公众号","icon":"lucide:radio-tower","href":"https://mp.weixin.qq.com/s/YA46b2ccH38-kv2F1eM3Qg"}]}]
