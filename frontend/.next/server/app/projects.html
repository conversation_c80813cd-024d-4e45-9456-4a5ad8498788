<!DOCTYPE html><html lang="en" class="h-full antialiased"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=16&amp;q=75 16w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=32&amp;q=75 32w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=48&amp;q=75 48w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=64&amp;q=75 64w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=96&amp;q=75 96w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=128&amp;q=75 128w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=256&amp;q=75 256w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=384&amp;q=75 384w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=640&amp;q=75 640w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=750&amp;q=75 750w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=828&amp;q=75 828w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=3840&amp;q=75 3840w" imageSizes="2.25rem" fetchPriority="high"/><link rel="stylesheet" href="/_next/static/css/8b6d6f69c7970b5a.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/72074f6a7392446a.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-64cec67748a067e2.js"/><script src="/_next/static/chunks/vendors-3e2ec6721707e77e.js" async=""></script><script src="/_next/static/chunks/main-app-45d29c2622bdc77b.js" async=""></script><script src="/_next/static/chunks/ui-libs-e96f0533cedd4426.js" async=""></script><script src="/_next/static/chunks/icon-libs-9a3ac8a6739d257f.js" async=""></script><script src="/_next/static/chunks/common-92ea84bd7ba12cdf.js" async=""></script><script src="/_next/static/chunks/app/layout-0fa287aaecc84f29.js" async=""></script><script src="/_next/static/chunks/app/page-b49a98deec5a828b.js" async=""></script><script src="/_next/static/chunks/app/projects/page-c2ae4e4c1a3a367c.js" async=""></script><title>jyao-projects</title><meta name="description" content="Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."/><meta name="author" content="Jingyao Chen"/><meta name="keywords" content="Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery"/><meta name="creator" content="Jingyao Chen"/><meta name="publisher" content="Jingyao Chen"/><meta name="robots" content="index, follow"/><meta name="theme-color" content="#171717"/><meta name="msapplication-TileColor" content="#171717"/><link rel="canonical" href="http://**************:3000"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="jyao-projects"/><meta property="og:description" content="Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."/><meta property="og:url" content="http://**************:3000"/><meta property="og:site_name" content="Jingyao Chen Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="http://**************:3000/images/og-jingyao-portfolio.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="jyao-projects"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@JingyaoC"/><meta name="twitter:creator" content="@JingyaoC"/><meta name="twitter:title" content="jyao-projects"/><meta name="twitter:description" content="Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."/><meta name="twitter:image" content="http://**************:3000/images/og-jingyao-portfolio.jpg"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="902x902"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="flex h-full"><script>!function(){try{var d=document.documentElement,c=d.classList;c.remove('light','dark');var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';c.add('dark')}else{d.style.colorScheme = 'light';c.add('light')}}else if(e){c.add(e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><div class="flex w-full"><div class="fixed inset-0 flex justify-center sm:px-8"><div class="flex w-full max-w-7xl lg:px-8"><div class="w-full shadow-xl dark:bg-muted"></div></div></div><div class="relative flex w-full flex-col px-4 sm:px-0"><header class="pointer-events-none relative z-50 flex flex-none flex-col" style="height:var(--header-height);margin-bottom:var(--header-mb)"><div class="top-0 z-10 h-16 pt-6" style="position:var(--header-position)"><div class="sm:px-8 top-[var(--header-top,theme(spacing.6))] w-full" style="position:var(--header-inner-position)"><div class="mx-auto w-full max-w-7xl lg:px-8"><div class="relative px-4 sm:px-8 lg:px-12"><div class="mx-auto max-w-2xl lg:max-w-5xl"><div class="relative flex gap-4"><div class="flex flex-1"><div class="flex flex-row items-center gap-2"><div class="h-10 w-10 rounded-full bg-white/90 p-0.5 shadow-lg shadow-zinc-800/5 ring-1 ring-zinc-900/5 backdrop-blur dark:bg-zinc-800/90 dark:ring-white/10"><a aria-label="Home" class="pointer-events-auto" href="/"><img alt="" fetchPriority="high" width="902" height="902" decoding="async" data-nimg="1" class="rounded-full bg-zinc-100 object-cover dark:bg-zinc-800 h-9 w-9" style="color:transparent" sizes="2.25rem" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=16&amp;q=75 16w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=32&amp;q=75 32w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=48&amp;q=75 48w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=64&amp;q=75 64w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=96&amp;q=75 96w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=128&amp;q=75 128w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=256&amp;q=75 256w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=384&amp;q=75 384w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=640&amp;q=75 640w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=750&amp;q=75 750w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=828&amp;q=75 828w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=3840&amp;q=75 3840w" src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=3840&amp;q=75"/></a></div><a aria-label="Home" class="pointer-events-auto" href="/"><div class="text-md font-semibold capitalize">Jay-Yao</div></a></div></div><div class="flex flex-1 justify-end md:justify-center"><div class="pointer-events-auto md:hidden" data-headlessui-state=""><button class="group flex items-center rounded-full px-4 py-2 text-sm font-medium shadow-lg ring-1 ring-border backdrop-blur-md bg-card/95 transition-all duration-300 hover:shadow-xl hover:ring-primary/20 hover:bg-primary/5" type="button" aria-expanded="false" data-headlessui-state="">Menu<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-3 h-auto w-2 transition-transform duration-200 group-hover:rotate-180"><path d="m6 9 6 6 6-6"></path></svg></button></div><div hidden="" style="position:fixed;top:1px;left:1px;width:1px;height:0;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0;display:none"></div><nav class="pointer-events-auto hidden md:block"><ul class="flex rounded-full px-3 text-sm font-medium bg-card/95 ring-1 ring-border shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-xl hover:ring-primary/20"><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/">Home</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/about">About</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md text-primary bg-primary/10" href="/projects">Projects<span class="absolute inset-x-1 -bottom-px h-[2px] bg-gradient-to-r from-primary/0 via-primary/60 to-primary/0 rounded-full animate-pulse-soft"></span></a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/blogs">Blogs</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/gallery">Gallery</a></li></ul></nav></div><div class="flex justify-end md:flex-1"><div class="pointer-events-auto flex flex-row items-center gap-2 md:mr-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg><span class="sr-only">Toggle theme</span></button></div></div></div></div></div></div></div></div></header><main class="flex-auto"><!--$--><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><div class="min-h-screen flex items-center justify-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div><!--/$--><!--/$--></main><footer class="mt-16 flex-none"><div class="sm:px-8"><div class="mx-auto w-full max-w-7xl lg:px-8"><div class="border-t border-muted pb-16 pt-10"><div class="relative px-4 sm:px-8 lg:px-12"><div class="mx-auto max-w-2xl lg:max-w-5xl"><div class="flex flex-col items-center justify-between gap-6 sm:flex-row sm:items-start"><div class="flex flex-col gap-4"><div class="flex flex-wrap justify-center gap-x-6 gap-y-1 text-sm font-medium"><a class="transition hover:text-primary" href="/">Home</a><a class="transition hover:text-primary" href="/about">About</a><a class="transition hover:text-primary" href="/projects">Projects</a><a class="transition hover:text-primary" href="/blogs">Blogs</a><a class="transition hover:text-primary" href="/gallery">Gallery</a></div><p class="text-xs text-muted-foreground text-center">Frontend template from <a href="https://github.com/iAmCorey/coreychiu-portfolio-template" target="_blank" rel="noopener noreferrer" class="underline hover:text-primary">coreychiu-portfolio-template</a><br/>Full-stack personal website based on Next.js + MySQL + FastAPI</p></div><div class="flex flex-col justify-center items-start"><div class="flex flex-row justify-end items-center gap-2"><p class="text-sm text-muted-foreground">© <!-- -->2025<!-- --> <!-- -->Jay-Yao<!-- -->. All rights reserved.</p><a class="text-xs text-muted-foreground hover:text-primary transition-colors duration-200 underline" title="查看网站版本历史" href="/version-history">Version</a><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg><span class="sr-only">Toggle theme</span></button></div><div class="mt-0">Loading...</div><div class="flex flex-row items-center justify-center gap-2 text-sm text-gray-500 mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 256 256"><path d="M128,56C48,56,16,128,16,128s32,72,112,72,112-72,112-72S208,56,128,56Zm0,112a40,40,0,1,1,40-40A40,40,0,0,1,128,168Z" opacity="0.2"></path><path d="M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z"></path></svg>Total Visits: <!-- -->-<!-- --> / Today Visits: <!-- -->-</div></div></div></div></div></div></div></div></footer></div></div><script src="/_next/static/chunks/webpack-64cec67748a067e2.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/8b6d6f69c7970b5a.css\",\"style\"]\n2:HL[\"/_next/static/css/72074f6a7392446a.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"3:I[12846,[],\"\"]\n6:I[4707,[],\"\"]\n7:I[36423,[],\"\"]\n8:I[93285,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"default\"]\n9:I[46021,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"Providers\"]\na:I[83258,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"ThemeInitializer\"]\nb:I[59183,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"RouteProgressBar\"]\nc:I[62989,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"GlobalLoadingManager\"]\nd:I[20686,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"SmartPrefetch\"]\ne:I[3864,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"Header\"]\nf:I[72972,[\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"931\",\"static/chunks/app/page-b49a98deec5a828b.js\"],\"\"]\n10:I[18908,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aae"])</script><script>self.__next_f.push([1,"cc84f29.js\"],\"Footer\"]\n11:I[60827,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"default\"]\n12:I[42545,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"PlausibleAnalytics\"]\n13:I[11816,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"PerformanceMonitor\"]\n14:I[11816,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"PerformanceDebugger\"]\n16:I[61060,[],\"\"]\n17:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L3\",null,{\"buildId\":\"7n7atQT5AqmloxjVA4Y6t\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"projects\"],\"initialTree\":[\"\",{\"children\":[\"projects\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"projects\",{\"children\":[\"__PAGE__\",{},[[\"$L4\",\"$L5\",null],null],null]},[null,[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"projects\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/8b6d6f69c7970b5a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/72074f6a7392446a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"h-full antialiased\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"flex h-full\",\"children\":[\"$\",\"$L8\",null,{\"children\":[\"$\",\"$L9\",null,{\"children\":[[\"$\",\"$La\",null,{}],[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{}],[\"$\",\"div\",null,{\"className\":\"flex w-full\",\"children\":[[\"$\",\"$Ld\",null,{\"routes\":[\"/\",\"/about\",\"/projects\",\"/blogs\",\"/gallery\"],\"priority\":\"low\",\"delay\":1500}],[\"$\",\"div\",null,{\"className\":\"fixed inset-0 flex justify-center sm:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex w-full max-w-7xl lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full shadow-xl dark:bg-muted\"}]}]}],[\"$\",\"div\",null,{\"className\":\"relative flex w-full flex-col px-4 sm:px-0\",\"children\":[[\"$\",\"$Le\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-auto\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"sm:px-8 flex h-full items-center pt-16 sm:pt-32\",\"children\":[\"$\",\"div\",null,{\"className\":\"mx-auto w-full max-w-7xl lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative px-4 sm:px-8 lg:px-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"mx-auto max-w-2xl lg:max-w-5xl\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-base font-semibold text-zinc-400 dark:text-zinc-500\",\"children\":\"404\"}],[\"$\",\"h1\",null,{\"className\":\"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100\",\"children\":\"Page not found\"}],[\"$\",\"p\",null,{\"className\":\"mt-4 text-base text-zinc-600 dark:text-zinc-400\",\"children\":\"Sorry, we couldn’t find the page you’re looking for.\"}],[\"$\",\"$Lf\",null,{\"className\":\"inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70 mt-4\",\"href\":\"/\",\"children\":\"Go back home\"}]]}]}]}]}]}],\"notFoundStyles\":[]}]}],[\"$\",\"$L10\",null,{}]]}]]}],[null,[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{}]],[\"$\",\"$L13\",null,{}],[\"$\",\"$L14\",null,{}]]}]}]}]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L15\"],\"globalErrorComponent\":\"$16\",\"missingSlots\":\"$W17\"}]\n"])</script><script>self.__next_f.push([1,"18:I[80089,[\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"895\",\"static/chunks/app/projects/page-c2ae4e4c1a3a367c.js\"],\"LazyWrapper\"]\n19:\"$Sreact.suspense\"\n1a:I[81523,[\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"895\",\"static/chunks/app/projects/page-c2ae4e4c1a3a367c.js\"],\"BailoutToCSR\"]\n1b:I[54394,[\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"895\",\"static/chunks/app/projects/page-c2ae4e4c1a3a367c.js\"],\"default\"]\n1c:Tc08,"])</script><script>self.__next_f.push([1,"\u003c!--\n📝 模板使用说明：\n- 这是基于\"技术教程模板\"模板生成的内容结构\n- 请在 [请在此处输入...] 标记的位置填写相应内容\n- 带 * 号的字段为必填项\n- 填写完成后可以删除这些说明和占位符\n- 支持 Markdown 格式\n--\u003e\n\n\u003cdiv style=\"text-align: center; margin-bottom: 32px;\"\u003e\n\u003cspan style=\"font-size: 28px; color: #1890ff; font-weight: bold;\"\u003e🎯 [请在此处输入教程标题 *]\u003c/span\u003e\n\u003cbr\u003e\n{{#if difficulty}}\n\u003cspan style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px; margin-top: 8px; display: inline-block;\"\u003e[请在此处输入difficulty]级程\u003c/span\u003e\n{{/if}}\n\u003cbr\u003e\n\u003cspan style=\"font-size: 14px; color: #666; margin-top: 8px;\"\u003e{{date_format(current_date, 'YYYY年MM月DD日')}} · {{#if reading_time}}预计 [请在此处输入reading_time][请在此处输入else]技术教程{{/if}}\u003c/span\u003e\n\u003c/div\u003e\n\n{{#if overview}}\n## 📋 教程概述\n\n[请在此处输入教程概述 *]\n\n\n{{/if}}\n\n{{#if learning_objectives}}\n## 🎯 学习目标\n\n{{#each learning_objectives}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n{{#if prerequisites}}\n## ⚙️ 环境准备\n\n### 系统要求\n{{#each prerequisites.system}}\n- [请在此处输入this]\n{{/each}}\n\n{{#if prerequisites.tools}}\n### 工具安装\n{{#each prerequisites.tools}}\n- **{{this.name}}**: {{this.description}}{{#if this.version}} (版本: {{this.version}}){{/if}}\n{{/each}}\n{{/if}}\n{{/if}}\n\n## 📝 详细步骤\n\n{{#if steps}}\n{{#each steps}}\n### 第{{@index}}步：{{this.title}}\n\n{{this.content}}\n\n{{#if this.code}}\n```{{this.language}}\n{{this.code}}\n```\n{{/if}}\n\n{{#if this.note}}\n\u003e 💡 **提示：** {{this.note}}\n{{/if}}\n\n{{/each}}\n[请在此处输入else]\n### 第一步：[请在此处输入第一步标题 *]\n[请在此处输入第一步内容 *]\n\n\n\n### 第二步：[请在此处输入第二步标题 *]\n[请在此处输入第二步内容 *]\n\n\n\n### 第三步：[请在此处输入第三步标题 *]\n[请在此处输入第三步内容 *]\n\n\n{{/if}}\n\n{{#if code_examples}}\n## 💻 完整代码示例\n\n{{#each code_examples}}\n### {{this.title}}\n\n```{{this.language}}\n{{this.code}}\n```\n\n{{#if this.explanation}}\n**代码说明：** {{this.explanation}}\n{{/if}}\n\n{{/each}}\n{{/if}}\n\n{{#if common_issues}}\n## ⚠️ 常见问题\n\n{{#each common_issues}}\n**Q: {{this.question}}**\n\nA: {{this.answer}}\n\n{{/each}}\n{{/if}}\n\n{{#if verification}}\n## ✅ 验证结果\n\n[请在此处输入verification]\n{{/if}}\n\n{{#if next_steps}}\n## 🚀 下一步\n\n{{#each next_steps}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n{{#if references}}\n## 🔗 扩展阅读\n\n{{#each references}}\n- [{{this.title}}]({{this.url}})\n{{/each}}\n{{/if}}\n\n---\n\n\u003cdiv style=\"text-align: center; padding: 20px; background: #f6ffed; border-radius: 8px; margin-top: 32px;\"\u003e\n\u003cspan style=\"color: #52c41a; font-size: 14px;\"\u003e\n📚 教程作者：[请在此处输入作者] | 📅 更新时间：{{date_format(current_date, 'YYYY-MM-DD')}}{{#if difficulty}} | 🎯 难度：[请在此处输入difficulty]{{/if}}\n\u003c/span\u003e\n\u003c/div\u003e"])</script><script>self.__next_f.push([1,"1d:Tc08,"])</script><script>self.__next_f.push([1,"\u003c!--\n📝 模板使用说明：\n- 这是基于\"技术教程模板\"模板生成的内容结构\n- 请在 [请在此处输入...] 标记的位置填写相应内容\n- 带 * 号的字段为必填项\n- 填写完成后可以删除这些说明和占位符\n- 支持 Markdown 格式\n--\u003e\n\n\u003cdiv style=\"text-align: center; margin-bottom: 32px;\"\u003e\n\u003cspan style=\"font-size: 28px; color: #1890ff; font-weight: bold;\"\u003e🎯 [请在此处输入教程标题 *]\u003c/span\u003e\n\u003cbr\u003e\n{{#if difficulty}}\n\u003cspan style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px; margin-top: 8px; display: inline-block;\"\u003e[请在此处输入difficulty]级程\u003c/span\u003e\n{{/if}}\n\u003cbr\u003e\n\u003cspan style=\"font-size: 14px; color: #666; margin-top: 8px;\"\u003e{{date_format(current_date, 'YYYY年MM月DD日')}} · {{#if reading_time}}预计 [请在此处输入reading_time][请在此处输入else]技术教程{{/if}}\u003c/span\u003e\n\u003c/div\u003e\n\n{{#if overview}}\n## 📋 教程概述\n\n[请在此处输入教程概述 *]\n\n\n{{/if}}\n\n{{#if learning_objectives}}\n## 🎯 学习目标\n\n{{#each learning_objectives}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n{{#if prerequisites}}\n## ⚙️ 环境准备\n\n### 系统要求\n{{#each prerequisites.system}}\n- [请在此处输入this]\n{{/each}}\n\n{{#if prerequisites.tools}}\n### 工具安装\n{{#each prerequisites.tools}}\n- **{{this.name}}**: {{this.description}}{{#if this.version}} (版本: {{this.version}}){{/if}}\n{{/each}}\n{{/if}}\n{{/if}}\n\n## 📝 详细步骤\n\n{{#if steps}}\n{{#each steps}}\n### 第{{@index}}步：{{this.title}}\n\n{{this.content}}\n\n{{#if this.code}}\n```{{this.language}}\n{{this.code}}\n```\n{{/if}}\n\n{{#if this.note}}\n\u003e 💡 **提示：** {{this.note}}\n{{/if}}\n\n{{/each}}\n[请在此处输入else]\n### 第一步：[请在此处输入第一步标题 *]\n[请在此处输入第一步内容 *]\n\n\n\n### 第二步：[请在此处输入第二步标题 *]\n[请在此处输入第二步内容 *]\n\n\n\n### 第三步：[请在此处输入第三步标题 *]\n[请在此处输入第三步内容 *]\n\n\n{{/if}}\n\n{{#if code_examples}}\n## 💻 完整代码示例\n\n{{#each code_examples}}\n### {{this.title}}\n\n```{{this.language}}\n{{this.code}}\n```\n\n{{#if this.explanation}}\n**代码说明：** {{this.explanation}}\n{{/if}}\n\n{{/each}}\n{{/if}}\n\n{{#if common_issues}}\n## ⚠️ 常见问题\n\n{{#each common_issues}}\n**Q: {{this.question}}**\n\nA: {{this.answer}}\n\n{{/each}}\n{{/if}}\n\n{{#if verification}}\n## ✅ 验证结果\n\n[请在此处输入verification]\n{{/if}}\n\n{{#if next_steps}}\n## 🚀 下一步\n\n{{#each next_steps}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n{{#if references}}\n## 🔗 扩展阅读\n\n{{#each references}}\n- [{{this.title}}]({{this.url}})\n{{/each}}\n{{/if}}\n\n---\n\n\u003cdiv style=\"text-align: center; padding: 20px; background: #f6ffed; border-radius: 8px; margin-top: 32px;\"\u003e\n\u003cspan style=\"color: #52c41a; font-size: 14px;\"\u003e\n📚 教程作者：[请在此处输入作者] | 📅 更新时间：{{date_format(current_date, 'YYYY-MM-DD')}}{{#if difficulty}} | 🎯 难度：[请在此处输入difficulty]{{/if}}\n\u003c/span\u003e\n\u003c/div\u003e"])</script><script>self.__next_f.push([1,"1e:T201c,"])</script><script>self.__next_f.push([1,"\u003cp\u003e这份工作聚焦赵炳南流派的中医皮肤科，提实现了 \u003cstrong\u003eSkin-LLM\u003c/strong\u003e 的构建方案，通过自指令循环、知识图谱引导的问答对生成，以及精细化的模型微调策略，完成了一个具有中医知识内涵的高质量大语言模型。\u003c/p\u003e\u003cp\u003e这是在2024年第三届知识图谱大赛（山东青岛）的工作，取得了当时所有参赛队伍测评分数的第一名。目前论文在投，以下是从技术报告中摘录的一些公开思路。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e一、自指令循环：借助知识图谱构造中医问答对\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.1 自指令循环机制概述\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e自指令循环（Self-instruction Loop）是一种用于生成式语言模型训练的技术，核心思想是让模型自身构造训练样本，以最小化人工干预。在初始数据不足的情况下，该方法能通过模型自身生成高质量指令与响应对，极大地提高模型的泛化能力。\u003c/p\u003e\u003cp\u003e在 Skin-LLM 项目中，我们基于构建好的 \u003cstrong\u003e中医皮肤科知识图谱\u003c/strong\u003e，借助结构化知识信息，生成丰富且逻辑严谨的问答对，供后续有监督微调使用。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.2 图谱路径采样：从“名医”到“疾病”的语义链\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e为了系统性地覆盖中医皮肤科的诊疗逻辑，我们定义了七条基于知识图谱的典型路径，体现了“名医-经验-用药-证候-疾病”的复杂联系结构。例如：\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u003cstrong\u003e路径1\u003c/strong\u003e：\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 名医 → 临证经验 → 经验方 → 中药 → 疾病\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 体现名医通过经验方组成影响疾病治疗的路径。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u003cstrong\u003e路径3\u003c/strong\u003e：\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 名医 → 方剂 → 证候 → 临床表现 → 疾病\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 展示从方剂出发，经证候与临床表现连接疾病的推理链条。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e此类路径真实再现了中医的诊疗思维过程，为问答构造提供了语义基础。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.3 五元组表示法：统一输入格式结构化\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e为了让语言模型更好地理解图谱信息，我们将路径转换为 \u003cstrong\u003e五元组（5-tuple）形式\u003c/strong\u003e 作为输入。格式如下：\u003c/p\u003e\u003cp\u003e五元组 = （头节点，关系，尾节点，节点属性，关系属性）\u003c/p\u003e\u003cp\u003e如路径1可转换为：\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e(名医，有临证经验，临证经验，N/A，N/A)\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e(临证经验，临证经验使用，经验方，N/A，N/A)\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e...\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e若某实体/关系已出现，则以 \"Rep\" 表示复用，减少冗余，提升输入效率。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.4 多轮自指令：三轮优化构造高质量问答对\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e我们设定 \u003cstrong\u003e三轮自指令循环\u003c/strong\u003e 来构建结构清晰、语义严谨的中医问答对：\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e第一轮：问题生成\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e按路径复杂度比例生成题目数（8/6/4/2）。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e提供示例问题模板与风格指令，引导模型生成与实体相关联的问题。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e输出结构为标准 JSON 格式选择题，包含：\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e {\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"type\": \"选择\",\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"question\": \"... A...；B...；C...；D...\",\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"answer\": \"A\",\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"analysis\": \"详细推理过程...\"\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e}\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u003cbr\u003e\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cstrong\u003e第二轮：题面优化\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e根据原始路径和初代题面，优化题干清晰度、专业性与多跳推理深度。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e要求不可直接摘取答案，需跨多条路径联合推理。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cstrong\u003e第三轮：答案深化\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e分点论述，推理路径明确。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e禁止出现“根据知识图谱”类提示，答案必须自然内化于问答本身。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e强调 \u003cstrong\u003e诊疗逻辑、证候机制、药物匹配性\u003c/strong\u003e 等方面的分析。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e最终，我们构建了约 \u003cstrong\u003e3000条高质量中医皮肤科问答对\u003c/strong\u003e，为模型微调打下了坚实的数据基础。\u003c/p\u003e\u003cp\u003e\u003cimg src=\"http://document.jyaochen.cn/image/paper/skin-llm/skin-llm.png\"\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e二、模型微调：Qwen2-7B + LoRA 高效训练\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e2.1 微调模型选择\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e我们基于 \u003cstrong\u003eQwen2-7B-Chat\u003c/strong\u003e 模型进行 LoRA 微调。\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e训练平台\u003c/strong\u003e：NVIDIA RTX 4090 24G\u003c/p\u003e\u003cp\u003e \u003cstrong\u003e框架\u003c/strong\u003e：LLaMA-Factory\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e2.2 数据构建与输入格式\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e将构建好的问答对转换为以下形式的训练样本：\u003c/p\u003e\u003cp\u003e{\u003c/p\u003e\u003cp\u003e\u0026nbsp;\"instruction\": \"朱仁康和张作舟在学术思想上最大的不同是什么？\",\u003c/p\u003e\u003cp\u003e\u0026nbsp;\"input\": \"\",\u003c/p\u003e\u003cp\u003e\u0026nbsp;\"output\": \"朱仁康强调中西医结合... 张作舟强调从毒论治...\"\u003c/p\u003e\u003cp\u003e}\u003c/p\u003e\u003cp\u003e同时，为提升泛化能力，我们在微调中加入了 \u003cstrong\u003e1000条通用数据集\u003c/strong\u003e，保持与领域数据 \u003cstrong\u003e1:3 的比例混合训练\u003c/strong\u003e，防止过拟合。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e2.3 训练参数配置\u003c/strong\u003e\u003c/p\u003e\u003ctable\u003e\u003ctbody\u003e\u003ctr\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e参数\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e值\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e说明\u003c/strong\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"2\"\u003e模型名称\u003c/td\u003e\u003ctd data-row=\"2\"\u003eQwen2-7B-Chat\u003c/td\u003e\u003ctd data-row=\"2\"\u003e中文能力优秀\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"3\"\u003e微调方式\u003c/td\u003e\u003ctd data-row=\"3\"\u003eLoRA\u003c/td\u003e\u003ctd data-row=\"3\"\u003e高效低资源\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"4\"\u003eLoRA Rank\u003c/td\u003e\u003ctd data-row=\"4\"\u003e8\u003c/td\u003e\u003ctd data-row=\"4\"\u003e低秩矩阵秩\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"5\"\u003eLoRA Alpha\u003c/td\u003e\u003ctd data-row=\"5\"\u003e16\u003c/td\u003e\u003ctd data-row=\"5\"\u003e调整影响程度\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"6\"\u003eBatch Size\u003c/td\u003e\u003ctd data-row=\"6\"\u003e2\u003c/td\u003e\u003ctd data-row=\"6\"\u003e适配 4090 显存\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"7\"\u003eEpochs\u003c/td\u003e\u003ctd data-row=\"7\"\u003e4\u003c/td\u003e\u003ctd data-row=\"7\"\u003e平衡训练深度\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"8\"\u003eLearning Rate\u003c/td\u003e\u003ctd data-row=\"8\"\u003e5e-5\u003c/td\u003e\u003ctd data-row=\"8\"\u003e稳定更新\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"9\"\u003eGrad Accumulation\u003c/td\u003e\u003ctd data-row=\"9\"\u003e8\u003c/td\u003e\u003ctd data-row=\"9\"\u003e增加等效 batch\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"10\"\u003eScheduler\u003c/td\u003e\u003ctd data-row=\"10\"\u003ecosine\u003c/td\u003e\u003ctd data-row=\"10\"\u003e余弦退火\u003c/td\u003e\u003c/tr\u003e\u003c/tbody\u003e\u003c/table\u003e\u003cp\u003e\u003cstrong\u003e三、总结与展望\u003c/strong\u003e\u003c/p\u003e\u003cp\u003eSkin-LLM 是一个高度垂直化的中医语言模型构建实例。其关键在于：\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e利用结构化医学知识图谱，设计路径并转化为五元组；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e多轮自指令生成与精修，实现高质量问答对构建.\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e"])</script><script>self.__next_f.push([1,"1f:T201c,"])</script><script>self.__next_f.push([1,"\u003cp\u003e这份工作聚焦赵炳南流派的中医皮肤科，提实现了 \u003cstrong\u003eSkin-LLM\u003c/strong\u003e 的构建方案，通过自指令循环、知识图谱引导的问答对生成，以及精细化的模型微调策略，完成了一个具有中医知识内涵的高质量大语言模型。\u003c/p\u003e\u003cp\u003e这是在2024年第三届知识图谱大赛（山东青岛）的工作，取得了当时所有参赛队伍测评分数的第一名。目前论文在投，以下是从技术报告中摘录的一些公开思路。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e一、自指令循环：借助知识图谱构造中医问答对\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.1 自指令循环机制概述\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e自指令循环（Self-instruction Loop）是一种用于生成式语言模型训练的技术，核心思想是让模型自身构造训练样本，以最小化人工干预。在初始数据不足的情况下，该方法能通过模型自身生成高质量指令与响应对，极大地提高模型的泛化能力。\u003c/p\u003e\u003cp\u003e在 Skin-LLM 项目中，我们基于构建好的 \u003cstrong\u003e中医皮肤科知识图谱\u003c/strong\u003e，借助结构化知识信息，生成丰富且逻辑严谨的问答对，供后续有监督微调使用。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.2 图谱路径采样：从“名医”到“疾病”的语义链\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e为了系统性地覆盖中医皮肤科的诊疗逻辑，我们定义了七条基于知识图谱的典型路径，体现了“名医-经验-用药-证候-疾病”的复杂联系结构。例如：\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u003cstrong\u003e路径1\u003c/strong\u003e：\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 名医 → 临证经验 → 经验方 → 中药 → 疾病\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 体现名医通过经验方组成影响疾病治疗的路径。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u003cstrong\u003e路径3\u003c/strong\u003e：\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 名医 → 方剂 → 证候 → 临床表现 → 疾病\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e 展示从方剂出发，经证候与临床表现连接疾病的推理链条。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e此类路径真实再现了中医的诊疗思维过程，为问答构造提供了语义基础。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.3 五元组表示法：统一输入格式结构化\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e为了让语言模型更好地理解图谱信息，我们将路径转换为 \u003cstrong\u003e五元组（5-tuple）形式\u003c/strong\u003e 作为输入。格式如下：\u003c/p\u003e\u003cp\u003e五元组 = （头节点，关系，尾节点，节点属性，关系属性）\u003c/p\u003e\u003cp\u003e如路径1可转换为：\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e(名医，有临证经验，临证经验，N/A，N/A)\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e(临证经验，临证经验使用，经验方，N/A，N/A)\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e...\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e若某实体/关系已出现，则以 \"Rep\" 表示复用，减少冗余，提升输入效率。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e1.4 多轮自指令：三轮优化构造高质量问答对\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e我们设定 \u003cstrong\u003e三轮自指令循环\u003c/strong\u003e 来构建结构清晰、语义严谨的中医问答对：\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e第一轮：问题生成\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e按路径复杂度比例生成题目数（8/6/4/2）。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e提供示例问题模板与风格指令，引导模型生成与实体相关联的问题。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e输出结构为标准 JSON 格式选择题，包含：\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e {\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"type\": \"选择\",\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"question\": \"... A...；B...；C...；D...\",\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"answer\": \"A\",\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u0026nbsp;\"analysis\": \"详细推理过程...\"\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e}\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e\u003cbr\u003e\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cstrong\u003e第二轮：题面优化\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e根据原始路径和初代题面，优化题干清晰度、专业性与多跳推理深度。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e要求不可直接摘取答案，需跨多条路径联合推理。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cstrong\u003e第三轮：答案深化\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e分点论述，推理路径明确。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e禁止出现“根据知识图谱”类提示，答案必须自然内化于问答本身。\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e强调 \u003cstrong\u003e诊疗逻辑、证候机制、药物匹配性\u003c/strong\u003e 等方面的分析。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e最终，我们构建了约 \u003cstrong\u003e3000条高质量中医皮肤科问答对\u003c/strong\u003e，为模型微调打下了坚实的数据基础。\u003c/p\u003e\u003cp\u003e\u003cimg src=\"http://document.jyaochen.cn/image/paper/skin-llm/skin-llm.png\"\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e二、模型微调：Qwen2-7B + LoRA 高效训练\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e2.1 微调模型选择\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e我们基于 \u003cstrong\u003eQwen2-7B-Chat\u003c/strong\u003e 模型进行 LoRA 微调。\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e训练平台\u003c/strong\u003e：NVIDIA RTX 4090 24G\u003c/p\u003e\u003cp\u003e \u003cstrong\u003e框架\u003c/strong\u003e：LLaMA-Factory\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e2.2 数据构建与输入格式\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e将构建好的问答对转换为以下形式的训练样本：\u003c/p\u003e\u003cp\u003e{\u003c/p\u003e\u003cp\u003e\u0026nbsp;\"instruction\": \"朱仁康和张作舟在学术思想上最大的不同是什么？\",\u003c/p\u003e\u003cp\u003e\u0026nbsp;\"input\": \"\",\u003c/p\u003e\u003cp\u003e\u0026nbsp;\"output\": \"朱仁康强调中西医结合... 张作舟强调从毒论治...\"\u003c/p\u003e\u003cp\u003e}\u003c/p\u003e\u003cp\u003e同时，为提升泛化能力，我们在微调中加入了 \u003cstrong\u003e1000条通用数据集\u003c/strong\u003e，保持与领域数据 \u003cstrong\u003e1:3 的比例混合训练\u003c/strong\u003e，防止过拟合。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong\u003e2.3 训练参数配置\u003c/strong\u003e\u003c/p\u003e\u003ctable\u003e\u003ctbody\u003e\u003ctr\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e参数\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e值\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e说明\u003c/strong\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"2\"\u003e模型名称\u003c/td\u003e\u003ctd data-row=\"2\"\u003eQwen2-7B-Chat\u003c/td\u003e\u003ctd data-row=\"2\"\u003e中文能力优秀\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"3\"\u003e微调方式\u003c/td\u003e\u003ctd data-row=\"3\"\u003eLoRA\u003c/td\u003e\u003ctd data-row=\"3\"\u003e高效低资源\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"4\"\u003eLoRA Rank\u003c/td\u003e\u003ctd data-row=\"4\"\u003e8\u003c/td\u003e\u003ctd data-row=\"4\"\u003e低秩矩阵秩\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"5\"\u003eLoRA Alpha\u003c/td\u003e\u003ctd data-row=\"5\"\u003e16\u003c/td\u003e\u003ctd data-row=\"5\"\u003e调整影响程度\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"6\"\u003eBatch Size\u003c/td\u003e\u003ctd data-row=\"6\"\u003e2\u003c/td\u003e\u003ctd data-row=\"6\"\u003e适配 4090 显存\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"7\"\u003eEpochs\u003c/td\u003e\u003ctd data-row=\"7\"\u003e4\u003c/td\u003e\u003ctd data-row=\"7\"\u003e平衡训练深度\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"8\"\u003eLearning Rate\u003c/td\u003e\u003ctd data-row=\"8\"\u003e5e-5\u003c/td\u003e\u003ctd data-row=\"8\"\u003e稳定更新\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"9\"\u003eGrad Accumulation\u003c/td\u003e\u003ctd data-row=\"9\"\u003e8\u003c/td\u003e\u003ctd data-row=\"9\"\u003e增加等效 batch\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"10\"\u003eScheduler\u003c/td\u003e\u003ctd data-row=\"10\"\u003ecosine\u003c/td\u003e\u003ctd data-row=\"10\"\u003e余弦退火\u003c/td\u003e\u003c/tr\u003e\u003c/tbody\u003e\u003c/table\u003e\u003cp\u003e\u003cstrong\u003e三、总结与展望\u003c/strong\u003e\u003c/p\u003e\u003cp\u003eSkin-LLM 是一个高度垂直化的中医语言模型构建实例。其关键在于：\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e利用结构化医学知识图谱，设计路径并转化为五元组；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e多轮自指令生成与精修，实现高质量问答对构建.\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e"])</script><script>self.__next_f.push([1,"20:Tb7f,"])</script><script>self.__next_f.push([1,"\u003cp\u003e📝 \u003cstrong\u003e会议论文\u003c/strong\u003e：CCKS-IJCKG 2024\u003c/p\u003e\u003cp\u003e🔍 \u003cstrong\u003e论文地址\u003c/strong\u003e：\u003cstrong style=\"background-color: rgb(255, 194, 102);\"\u003e\u003ca href=\"http://document.jyaochen.cn/paper/Enhancing%20Traditional%20Chinese%20Medicine%20Information%20Extraction%20Using%20Instruction-Tuned%20Large%20Models.pdf\" rel=\"noopener noreferrer\" target=\"_blank\"\u003e地址\u003c/a\u003e\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e📖 \u003cstrong\u003e引用格式\u003c/strong\u003e：Chen J, Xia S, Li J, Yu T. \u003cem\u003eEnhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models\u003c/em\u003e. In: Proceedings of CCKS-IJCKG 2024. Springer CCIS, vol 2229, pp. 331–336. \u003ca href=\"https://doi.org/10.1007/978-981-96-1809-5_25\" rel=\"noopener noreferrer\" target=\"_blank\"\u003ehttps://doi.org/10.1007/978-981-96-1809-5_25\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e提出了一种基于伪代码的 \u003cstrong\u003e大语言模型指令微调\u003c/strong\u003e 的中医信息抽取范式，有效提升模型对专业术语和复杂关系的识别能力。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e🚀\u003cstrong\u003e 方法\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e💬 利用 GPT4-o 合成\u003cstrong\u003e伪代码格式\u003c/strong\u003e的指令集，通过结构化指令引导模型完成信息抽取；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e🧱 采用 LLaMA3-8B ，结合 \u003cstrong\u003eLoRA\u003c/strong\u003e进行指令微调；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e📦 构建包含1800篇中医期刊文献与200条中医医案的数据集，用于模型训练；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e🔍 抽取目标实体包括疾病、药物、治疗方式、证候等，最终输出结构化 JSON 结果，可直接用于中医知识图谱构建。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e📊\u003cstrong\u003e 实验效果\u003c/strong\u003e\u003c/p\u003e\u003ctable\u003e\u003ctbody\u003e\u003ctr\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e实验设置\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e模型\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003eF1值 \u003c/strong\u003e🎯\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"2\"\u003e有监督学习\u003c/td\u003e\u003ctd data-row=\"2\"\u003eBERT\u003c/td\u003e\u003ctd data-row=\"2\"\u003e0.79\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"3\"\u003e有监督学习\u003c/td\u003e\u003ctd data-row=\"3\"\u003eLLaMA3\u003c/td\u003e\u003ctd data-row=\"3\"\u003e0.71\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"4\"\u003e有监督学习\u003c/td\u003e\u003ctd data-row=\"4\"\u003eTCM-LoRA- LLaMA3\u003c/td\u003e\u003ctd data-row=\"4\"\u003e\u003cstrong\u003e0.95\u003c/strong\u003e ✅\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"5\"\u003eZero-Shot\u003c/td\u003e\u003ctd data-row=\"5\"\u003eLLaMA3\u003c/td\u003e\u003ctd data-row=\"5\"\u003e0.45\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"6\"\u003eZero-Shot\u003c/td\u003e\u003ctd data-row=\"6\"\u003eTCM-LoRA- LLaMA3\u003c/td\u003e\u003ctd data-row=\"6\"\u003e\u003cstrong\u003e0.82\u003c/strong\u003e 🔥\u003c/td\u003e\u003c/tr\u003e\u003c/tbody\u003e\u003c/table\u003e\u003cp\u003e结果表明：通过伪代码指令进行微调，显著提升了模型在有监督与零样本情境下的表现，尤其适用于高专业性场景中的实体抽取任务。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e本方法为 \u003cstrong\u003e中医知识图谱构建、智能问答系统、中医药文本结构化处理\u003c/strong\u003e 等任务提供了高效可行的技术路径。\u003c/p\u003e"])</script><script>self.__next_f.push([1,"21:Tb7f,"])</script><script>self.__next_f.push([1,"\u003cp\u003e📝 \u003cstrong\u003e会议论文\u003c/strong\u003e：CCKS-IJCKG 2024\u003c/p\u003e\u003cp\u003e🔍 \u003cstrong\u003e论文地址\u003c/strong\u003e：\u003cstrong style=\"background-color: rgb(255, 194, 102);\"\u003e\u003ca href=\"http://document.jyaochen.cn/paper/Enhancing%20Traditional%20Chinese%20Medicine%20Information%20Extraction%20Using%20Instruction-Tuned%20Large%20Models.pdf\" rel=\"noopener noreferrer\" target=\"_blank\"\u003e地址\u003c/a\u003e\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e📖 \u003cstrong\u003e引用格式\u003c/strong\u003e：Chen J, Xia S, Li J, Yu T. \u003cem\u003eEnhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models\u003c/em\u003e. In: Proceedings of CCKS-IJCKG 2024. Springer CCIS, vol 2229, pp. 331–336. \u003ca href=\"https://doi.org/10.1007/978-981-96-1809-5_25\" rel=\"noopener noreferrer\" target=\"_blank\"\u003ehttps://doi.org/10.1007/978-981-96-1809-5_25\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e提出了一种基于伪代码的 \u003cstrong\u003e大语言模型指令微调\u003c/strong\u003e 的中医信息抽取范式，有效提升模型对专业术语和复杂关系的识别能力。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e🚀\u003cstrong\u003e 方法\u003c/strong\u003e\u003c/p\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e💬 利用 GPT4-o 合成\u003cstrong\u003e伪代码格式\u003c/strong\u003e的指令集，通过结构化指令引导模型完成信息抽取；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e🧱 采用 LLaMA3-8B ，结合 \u003cstrong\u003eLoRA\u003c/strong\u003e进行指令微调；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e📦 构建包含1800篇中医期刊文献与200条中医医案的数据集，用于模型训练；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e🔍 抽取目标实体包括疾病、药物、治疗方式、证候等，最终输出结构化 JSON 结果，可直接用于中医知识图谱构建。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e📊\u003cstrong\u003e 实验效果\u003c/strong\u003e\u003c/p\u003e\u003ctable\u003e\u003ctbody\u003e\u003ctr\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e实验设置\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003e模型\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"1\"\u003e\u003cstrong\u003eF1值 \u003c/strong\u003e🎯\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"2\"\u003e有监督学习\u003c/td\u003e\u003ctd data-row=\"2\"\u003eBERT\u003c/td\u003e\u003ctd data-row=\"2\"\u003e0.79\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"3\"\u003e有监督学习\u003c/td\u003e\u003ctd data-row=\"3\"\u003eLLaMA3\u003c/td\u003e\u003ctd data-row=\"3\"\u003e0.71\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"4\"\u003e有监督学习\u003c/td\u003e\u003ctd data-row=\"4\"\u003eTCM-LoRA- LLaMA3\u003c/td\u003e\u003ctd data-row=\"4\"\u003e\u003cstrong\u003e0.95\u003c/strong\u003e ✅\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"5\"\u003eZero-Shot\u003c/td\u003e\u003ctd data-row=\"5\"\u003eLLaMA3\u003c/td\u003e\u003ctd data-row=\"5\"\u003e0.45\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"6\"\u003eZero-Shot\u003c/td\u003e\u003ctd data-row=\"6\"\u003eTCM-LoRA- LLaMA3\u003c/td\u003e\u003ctd data-row=\"6\"\u003e\u003cstrong\u003e0.82\u003c/strong\u003e 🔥\u003c/td\u003e\u003c/tr\u003e\u003c/tbody\u003e\u003c/table\u003e\u003cp\u003e结果表明：通过伪代码指令进行微调，显著提升了模型在有监督与零样本情境下的表现，尤其适用于高专业性场景中的实体抽取任务。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e本方法为 \u003cstrong\u003e中医知识图谱构建、智能问答系统、中医药文本结构化处理\u003c/strong\u003e 等任务提供了高效可行的技术路径。\u003c/p\u003e"])</script><script>self.__next_f.push([1,"22:T164d,"])</script><script>self.__next_f.push([1,"\u003cp\u003e📝 \u003cstrong\u003e发表期刊\u003c/strong\u003e：《世界科学技术—中医药现代化》（\u003cstrong\u003e北大中文核心\u003c/strong\u003e）\u003c/p\u003e\u003cp\u003e🔍 \u003cstrong\u003e论文地址\u003c/strong\u003e：\u003cstrong style=\"background-color: rgb(255, 235, 204);\"\u003e\u003cu\u003e\u003ca href=\"http://document.jyaochen.cn/paper/%E5%9F%BA%E4%BA%8E%E5%9B%BE%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E5%A2%9E%E5%BC%BA%E5%8F%A5%E5%B5%8C%E5%85%A5%E7%9A%84%E4%B8%AD%E5%8C%BB%E6%96%87%E7%8C%AE%E5%A4%9A%E6%A0%87%E7%AD%BE%E5%88%86%E7%B1%BB%E6%96%B9%E6%B3%95%E7%A0%94%E7%A9%B6.pdf\" rel=\"noopener noreferrer\" target=\"_blank\"\u003e地址\u003c/a\u003e\u003c/u\u003e\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e📖 \u003cstrong\u003e引用格式\u003c/strong\u003e：陈靖耀,李敬华,于彤.基于图神经网络增强句嵌入的中医文献多标签分类方法研究[J].世界科学技术-中医药现代化,2025,27(02):420-430.\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e研究聚焦于中医文献分类过程中所面临的 \u003cstrong\u003e标签多样性\u003c/strong\u003e、\u003cstrong\u003e语义复杂性\u003c/strong\u003e 与 \u003cstrong\u003e样本不均衡\u003c/strong\u003e 等挑战，提出了一种结合 \u003cstrong\u003e图神经网络（GraphSAGE）\u003c/strong\u003e 🧠 与 \u003cstrong\u003e句嵌入（Sentence Embedding）\u003c/strong\u003e 💬 的多标签分类方法，用于实现中医文献关键词的自动预测与分类。\u003c/p\u003e\u003ch4\u003e🛠️ 方法简介\u003c/h4\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e获取文献摘要的句嵌入向量；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e通过 \u003cstrong\u003eBERTopic 模型\u003c/strong\u003e 🔗 对句嵌入结果进行主题聚类，构建文献之间的语义关联；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e构建包含 \u003cstrong\u003e文章-作者-主题\u003c/strong\u003e 三类节点的异构网络 🌐；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e采用 \u003cstrong\u003eGraphSAGE 图神经网络模型\u003c/strong\u003e，基于重启随机游走算法进行图表示学习；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e最终将图嵌入与句嵌入特征融合，输入分类器进行多标签分类预测 🏷️。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003ch4\u003e📊 实验结果\u003c/h4\u003e\u003cp\u003e在 CNKI 中医文献数据集上，所提出模型表现优异，分类性能全面超越主流基线模型：相比 BERT、TextCNN 等模型，本方法在应对标签不均衡、多标签依赖等方面表现更加稳定可靠。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003ctable\u003e\u003ctbody\u003e\u003ctr\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e特征向量\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eTest accuracy\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eMacro precision\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eWeighted precision\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eF1\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cstrong style=\"color: rgb(12, 12, 12);\"\u003eOur\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.617\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.834\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.852\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.716\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eSeq2Seq-Attention \u0026nbsp;\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.567\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.78\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.782\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.651\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eTextCNN\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.516\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.719\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.735\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.585\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eBERT\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.453\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.563\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.601\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.468\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e句嵌入向量\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.309\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.365\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.384\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.352\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003c/tbody\u003e\u003c/table\u003e\u003ch4\u003e\u003cbr\u003e\u003c/h4\u003e\u003cp\u003e✨ 本方法有效融合图结构与语义特征，提升了中医文献的组织与检索能力，为 \u003cstrong\u003e中医知识图谱构建\u003c/strong\u003e、\u003cstrong\u003e智能标签推荐\u003c/strong\u003e 及 \u003cstrong\u003e科研辅助决策\u003c/strong\u003e 提供技术支持与理论参考。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e"])</script><script>self.__next_f.push([1,"23:T164d,"])</script><script>self.__next_f.push([1,"\u003cp\u003e📝 \u003cstrong\u003e发表期刊\u003c/strong\u003e：《世界科学技术—中医药现代化》（\u003cstrong\u003e北大中文核心\u003c/strong\u003e）\u003c/p\u003e\u003cp\u003e🔍 \u003cstrong\u003e论文地址\u003c/strong\u003e：\u003cstrong style=\"background-color: rgb(255, 235, 204);\"\u003e\u003cu\u003e\u003ca href=\"http://document.jyaochen.cn/paper/%E5%9F%BA%E4%BA%8E%E5%9B%BE%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C%E5%A2%9E%E5%BC%BA%E5%8F%A5%E5%B5%8C%E5%85%A5%E7%9A%84%E4%B8%AD%E5%8C%BB%E6%96%87%E7%8C%AE%E5%A4%9A%E6%A0%87%E7%AD%BE%E5%88%86%E7%B1%BB%E6%96%B9%E6%B3%95%E7%A0%94%E7%A9%B6.pdf\" rel=\"noopener noreferrer\" target=\"_blank\"\u003e地址\u003c/a\u003e\u003c/u\u003e\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e📖 \u003cstrong\u003e引用格式\u003c/strong\u003e：陈靖耀,李敬华,于彤.基于图神经网络增强句嵌入的中医文献多标签分类方法研究[J].世界科学技术-中医药现代化,2025,27(02):420-430.\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003cp\u003e研究聚焦于中医文献分类过程中所面临的 \u003cstrong\u003e标签多样性\u003c/strong\u003e、\u003cstrong\u003e语义复杂性\u003c/strong\u003e 与 \u003cstrong\u003e样本不均衡\u003c/strong\u003e 等挑战，提出了一种结合 \u003cstrong\u003e图神经网络（GraphSAGE）\u003c/strong\u003e 🧠 与 \u003cstrong\u003e句嵌入（Sentence Embedding）\u003c/strong\u003e 💬 的多标签分类方法，用于实现中医文献关键词的自动预测与分类。\u003c/p\u003e\u003ch4\u003e🛠️ 方法简介\u003c/h4\u003e\u003col\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e获取文献摘要的句嵌入向量；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e通过 \u003cstrong\u003eBERTopic 模型\u003c/strong\u003e 🔗 对句嵌入结果进行主题聚类，构建文献之间的语义关联；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e构建包含 \u003cstrong\u003e文章-作者-主题\u003c/strong\u003e 三类节点的异构网络 🌐；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e采用 \u003cstrong\u003eGraphSAGE 图神经网络模型\u003c/strong\u003e，基于重启随机游走算法进行图表示学习；\u003c/li\u003e\u003cli data-list=\"bullet\"\u003e\u003cspan class=\"ql-ui\" contenteditable=\"false\"\u003e\u003c/span\u003e最终将图嵌入与句嵌入特征融合，输入分类器进行多标签分类预测 🏷️。\u003c/li\u003e\u003c/ol\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003ch4\u003e📊 实验结果\u003c/h4\u003e\u003cp\u003e在 CNKI 中医文献数据集上，所提出模型表现优异，分类性能全面超越主流基线模型：相比 BERT、TextCNN 等模型，本方法在应对标签不均衡、多标签依赖等方面表现更加稳定可靠。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e\u003ctable\u003e\u003ctbody\u003e\u003ctr\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e特征向量\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eTest accuracy\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eMacro precision\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eWeighted precision\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"1\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eF1\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cstrong style=\"color: rgb(12, 12, 12);\"\u003eOur\u003c/strong\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.617\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.834\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.852\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"2\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.716\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eSeq2Seq-Attention \u0026nbsp;\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.567\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.78\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.782\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"3\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.651\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eTextCNN\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.516\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.719\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.735\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"4\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.585\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003eBERT\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.453\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.563\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.601\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"5\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.468\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003ctr\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e句嵌入向量\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.309\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.365\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.384\u003c/span\u003e\u003c/td\u003e\u003ctd data-row=\"6\" class=\"ql-align-center\"\u003e\u003cspan style=\"color: rgb(12, 12, 12);\"\u003e0.352\u003c/span\u003e\u003c/td\u003e\u003c/tr\u003e\u003c/tbody\u003e\u003c/table\u003e\u003ch4\u003e\u003cbr\u003e\u003c/h4\u003e\u003cp\u003e✨ 本方法有效融合图结构与语义特征，提升了中医文献的组织与检索能力，为 \u003cstrong\u003e中医知识图谱构建\u003c/strong\u003e、\u003cstrong\u003e智能标签推荐\u003c/strong\u003e 及 \u003cstrong\u003e科研辅助决策\u003c/strong\u003e 提供技术支持与理论参考。\u003c/p\u003e\u003cp\u003e\u003cbr\u003e\u003c/p\u003e"])</script><script>self.__next_f.push([1,"24:Tef2,"])</script><script>self.__next_f.push([1,"\u003c!--\n📝 模板使用说明：\n- 这是基于\"开源项目介绍模板\"模板生成的内容结构\n- 请在 [请在此处输入...] 标记的位置填写相应内容\n- 带 * 号的字段为必填项\n- 填写完成后可以删除这些说明和占位符\n- 支持 Markdown 格式\n--\u003e\n\n\u003cdiv style=\"text-align: center; margin-bottom: 32px;\"\u003e\n\u003cspan style=\"font-size: 32px; color: #722ed1; font-weight: bold;\"\u003e🚀 [请在此处输入project_name]\u003c/span\u003e\n\u003cbr\u003e\n\u003cspan style=\"font-size: 18px; color: #1890ff; margin-top: 8px;\"\u003e[请在此处输入title]\u003c/span\u003e\n\u003cbr\u003e\n{{#if github_url}}\n\u003ca href=\"[请在此处输入github_url]\" style=\"display: inline-block; background: #24292e; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px;\"\u003e\n📦 GitHub Repository\n\u003c/a\u003e\n{{/if}}\n{{#if demo_url}}\n\u003ca href=\"[请在此处输入demo_url]\" style=\"display: inline-block; background: #52c41a; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px; margin-left: 8px;\"\u003e\n🌐 在线演示\n\u003c/a\u003e\n{{/if}}\n\u003c/div\u003e\n\n{{#if badges}}\n\u003cdiv style=\"text-align: center; margin: 24px 0;\"\u003e\n{{#each badges}}\n\u003cimg src=\"{{this.url}}\" alt=\"{{this.alt}}\" style=\"margin: 2px;\"\u003e\n{{/each}}\n\u003c/div\u003e\n{{/if}}\n\n## 📖 项目简介\n\n[请在此处输入description]\n\n{{#if features}}\n## ✨ 核心功能\n\n{{#each features}}\n- **{{this.name}}**: {{this.description}}\n{{/each}}\n{{/if}}\n\n{{#if tech_stack}}\n## 🛠️ 技术栈\n\n{{#each tech_stack}}\n- **{{this.category}}**: {{#each this.technologies}}[请在此处输入this]{{#if !@last}}, {{/if}}{{/each}}\n{{/each}}\n{{/if}}\n\n## 🚀 快速开始\n\n{{#if installation}}\n### 安装\n\n{{#if installation.prerequisites}}\n**前置要求：**\n{{#each installation.prerequisites}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n```bash\n{{installation.command}}\n```\n{{/if}}\n\n{{#if usage}}\n### 基本使用\n\n```{{usage.language}}\n{{usage.code}}\n```\n\n{{#if usage.explanation}}\n{{usage.explanation}}\n{{/if}}\n{{/if}}\n\n{{#if examples}}\n## 📚 使用示例\n\n{{#each examples}}\n### {{this.title}}\n\n{{this.description}}\n\n```{{this.language}}\n{{this.code}}\n```\n\n{{#if this.output}}\n**输出结果：**\n```\n{{this.output}}\n```\n{{/if}}\n\n{{/each}}\n{{/if}}\n\n{{#if api_docs}}\n## 📋 API 文档\n\n{{#each api_docs}}\n### {{this.method}} {{this.endpoint}}\n\n{{this.description}}\n\n**参数：**\n{{#each this.parameters}}\n- `{{this.name}}` ({{this.type}}): {{this.description}}{{#if this.required}} *必需*{{/if}}\n{{/each}}\n\n**响应示例：**\n```json\n{{this.response_example}}\n```\n\n{{/each}}\n{{/if}}\n\n{{#if configuration}}\n## ⚙️ 配置选项\n\n{{#each configuration}}\n- **{{this.option}}**: {{this.description}}{{#if this.default}} (默认: `{{this.default}}`){{/if}}\n{{/each}}\n{{/if}}\n\n{{#if roadmap}}\n## 🗺️ 开发路线图\n\n{{#each roadmap}}\n- {{#if this.completed}}✅[请在此处输入else]🔲{{/if}} **{{this.version}}**: {{this.description}}{{#if this.eta}} (预计: {{this.eta}}){{/if}}\n{{/each}}\n{{/if}}\n\n{{#if contributing}}\n## 🤝 贡献指南\n\n[请在此处输入contributing]\n\n{{#if contribution_types}}\n### 贡献方式\n\n{{#each contribution_types}}\n- **{{this.type}}**: {{this.description}}\n{{/each}}\n{{/if}}\n{{/if}}\n\n{{#if license}}\n## 📄 开源协议\n\n本项目采用 [[请在此处输入license]]([请在此处输入license_url]) 开源协议。\n{{/if}}\n\n{{#if acknowledgments}}\n## 🙏 致谢\n\n{{#each acknowledgments}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n---\n\n\u003cdiv style=\"text-align: center; padding: 20px; background: #f0f2f5; border-radius: 8px; margin-top: 32px;\"\u003e\n\u003cspan style=\"color: #666; font-size: 14px;\"\u003e\n👨‍💻 项目作者：[请在此处输入author] | 🏷️ 语言：{{#if main_language}}[请在此处输入main_language][请在此处输入else]多语言{{/if}} | ⭐ 如果觉得有用，请给个 Star！\n\u003c/span\u003e\n\u003c/div\u003e"])</script><script>self.__next_f.push([1,"25:Tef2,"])</script><script>self.__next_f.push([1,"\u003c!--\n📝 模板使用说明：\n- 这是基于\"开源项目介绍模板\"模板生成的内容结构\n- 请在 [请在此处输入...] 标记的位置填写相应内容\n- 带 * 号的字段为必填项\n- 填写完成后可以删除这些说明和占位符\n- 支持 Markdown 格式\n--\u003e\n\n\u003cdiv style=\"text-align: center; margin-bottom: 32px;\"\u003e\n\u003cspan style=\"font-size: 32px; color: #722ed1; font-weight: bold;\"\u003e🚀 [请在此处输入project_name]\u003c/span\u003e\n\u003cbr\u003e\n\u003cspan style=\"font-size: 18px; color: #1890ff; margin-top: 8px;\"\u003e[请在此处输入title]\u003c/span\u003e\n\u003cbr\u003e\n{{#if github_url}}\n\u003ca href=\"[请在此处输入github_url]\" style=\"display: inline-block; background: #24292e; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px;\"\u003e\n📦 GitHub Repository\n\u003c/a\u003e\n{{/if}}\n{{#if demo_url}}\n\u003ca href=\"[请在此处输入demo_url]\" style=\"display: inline-block; background: #52c41a; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-top: 12px; margin-left: 8px;\"\u003e\n🌐 在线演示\n\u003c/a\u003e\n{{/if}}\n\u003c/div\u003e\n\n{{#if badges}}\n\u003cdiv style=\"text-align: center; margin: 24px 0;\"\u003e\n{{#each badges}}\n\u003cimg src=\"{{this.url}}\" alt=\"{{this.alt}}\" style=\"margin: 2px;\"\u003e\n{{/each}}\n\u003c/div\u003e\n{{/if}}\n\n## 📖 项目简介\n\n[请在此处输入description]\n\n{{#if features}}\n## ✨ 核心功能\n\n{{#each features}}\n- **{{this.name}}**: {{this.description}}\n{{/each}}\n{{/if}}\n\n{{#if tech_stack}}\n## 🛠️ 技术栈\n\n{{#each tech_stack}}\n- **{{this.category}}**: {{#each this.technologies}}[请在此处输入this]{{#if !@last}}, {{/if}}{{/each}}\n{{/each}}\n{{/if}}\n\n## 🚀 快速开始\n\n{{#if installation}}\n### 安装\n\n{{#if installation.prerequisites}}\n**前置要求：**\n{{#each installation.prerequisites}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n```bash\n{{installation.command}}\n```\n{{/if}}\n\n{{#if usage}}\n### 基本使用\n\n```{{usage.language}}\n{{usage.code}}\n```\n\n{{#if usage.explanation}}\n{{usage.explanation}}\n{{/if}}\n{{/if}}\n\n{{#if examples}}\n## 📚 使用示例\n\n{{#each examples}}\n### {{this.title}}\n\n{{this.description}}\n\n```{{this.language}}\n{{this.code}}\n```\n\n{{#if this.output}}\n**输出结果：**\n```\n{{this.output}}\n```\n{{/if}}\n\n{{/each}}\n{{/if}}\n\n{{#if api_docs}}\n## 📋 API 文档\n\n{{#each api_docs}}\n### {{this.method}} {{this.endpoint}}\n\n{{this.description}}\n\n**参数：**\n{{#each this.parameters}}\n- `{{this.name}}` ({{this.type}}): {{this.description}}{{#if this.required}} *必需*{{/if}}\n{{/each}}\n\n**响应示例：**\n```json\n{{this.response_example}}\n```\n\n{{/each}}\n{{/if}}\n\n{{#if configuration}}\n## ⚙️ 配置选项\n\n{{#each configuration}}\n- **{{this.option}}**: {{this.description}}{{#if this.default}} (默认: `{{this.default}}`){{/if}}\n{{/each}}\n{{/if}}\n\n{{#if roadmap}}\n## 🗺️ 开发路线图\n\n{{#each roadmap}}\n- {{#if this.completed}}✅[请在此处输入else]🔲{{/if}} **{{this.version}}**: {{this.description}}{{#if this.eta}} (预计: {{this.eta}}){{/if}}\n{{/each}}\n{{/if}}\n\n{{#if contributing}}\n## 🤝 贡献指南\n\n[请在此处输入contributing]\n\n{{#if contribution_types}}\n### 贡献方式\n\n{{#each contribution_types}}\n- **{{this.type}}**: {{this.description}}\n{{/each}}\n{{/if}}\n{{/if}}\n\n{{#if license}}\n## 📄 开源协议\n\n本项目采用 [[请在此处输入license]]([请在此处输入license_url]) 开源协议。\n{{/if}}\n\n{{#if acknowledgments}}\n## 🙏 致谢\n\n{{#each acknowledgments}}\n- [请在此处输入this]\n{{/each}}\n{{/if}}\n\n---\n\n\u003cdiv style=\"text-align: center; padding: 20px; background: #f0f2f5; border-radius: 8px; margin-top: 32px;\"\u003e\n\u003cspan style=\"color: #666; font-size: 14px;\"\u003e\n👨‍💻 项目作者：[请在此处输入author] | 🏷️ 语言：{{#if main_language}}[请在此处输入main_language][请在此处输入else]多语言{{/if}} | ⭐ 如果觉得有用，请给个 Star！\n\u003c/span\u003e\n\u003c/div\u003e"])</script><script>self.__next_f.push([1,"5:[\"$\",\"$L18\",null,{\"children\":[\"$\",\"$19\",null,{\"fallback\":[\"$\",\"div\",null,{\"className\":\"min-h-screen flex items-center justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"}]}],\"children\":[\"$\",\"$L1a\",null,{\"reason\":\"next/dynamic\",\"children\":[\"$\",\"$L1b\",null,{\"sortedCategories\":[{\"categoryName\":\"项目实战\",\"projects\":[{\"id\":29,\"slug\":\"project-13\",\"name\":\"TCM Text Data Processing Tool\",\"description\":\"A comprehensive toolkit for TCM text processing and analysis.\",\"link\":{\"href\":\"https://github.com/JYao-Chen/TCM_Toolkit\",\"label\":\"TCM Text Data Processing Tool\"},\"github_link\":null,\"logo\":\"\",\"icon\":\"iconic:scipy\",\"featured\":false,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"A comprehensive toolkit for TCM text processing and analysis.\",\"detail_content\":\"A comprehensive toolkit for TCM text processing and analysis.\",\"project_order\":4,\"date\":\"2025-04-14T10:14:30\",\"display_date\":\"2025-04-14T10:14:30\",\"created_at\":\"2025-04-14T10:14:30\",\"updated_at\":\"2025-07-18T14:35:24\",\"category\":\"$undefined\",\"category_icon\":null,\"tags\":[\"项目实战\"],\"tag_objects\":[{\"name\":\"项目实战\",\"slug\":\"project-hand\",\"description\":\"项目实战测试\",\"color\":\"#f43f5e\",\"icon\":\"lucide:drafting-compass\",\"category\":\"content\",\"id\":14,\"created_at\":\"2025-07-10T20:02:23\",\"updated_at\":\"2025-07-18T12:45:32\"}],\"tech_stack\":[],\"categories\":[],\"module_categories\":[]}],\"icon\":\"lucide:drafting-compass\",\"description\":\"项目实战测试\",\"display_order\":500,\"id\":14,\"slug\":\"project-hand\"},{\"categoryName\":\"AI\",\"projects\":[{\"id\":39,\"slug\":\"xiang-mu-ce-shi-xiang-qing-ye\",\"name\":\"项目测试详情页\",\"description\":\"\",\"link\":\"$undefined\",\"github_link\":null,\"logo\":null,\"icon\":\"iconic:gemini\",\"featured\":false,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"$1c\",\"detail_content\":\"$1d\",\"project_order\":0,\"date\":\"2025-07-14T18:59:37\",\"display_date\":\"2025-07-14T18:59:37\",\"created_at\":\"2025-07-14T19:00:10\",\"updated_at\":\"2025-07-19T07:42:12\",\"category\":\"AI\",\"category_icon\":\"iconic:gemini\",\"tags\":[\"AI\"],\"tag_objects\":[{\"name\":\"AI\",\"slug\":\"ai\",\"description\":\"llm\",\"color\":\"#3b82f6\",\"icon\":\"iconic:gemini\",\"category\":\"tech\",\"id\":10,\"created_at\":\"2025-07-10T20:02:23\",\"updated_at\":\"2025-07-17T11:56:58\"}],\"tech_stack\":[\"AI\"],\"categories\":[],\"module_categories\":[]}],\"icon\":\"iconic:gemini\",\"description\":\"llm\",\"display_order\":500,\"id\":10,\"slug\":\"ai\"},{\"categoryName\":\"Python\",\"projects\":[{\"id\":30,\"slug\":\"project-14\",\"name\":\"Skin-LLM\",\"description\":\"中医皮肤科赵炳南流派 Skin-LLM 模型\",\"link\":\"$undefined\",\"github_link\":null,\"logo\":\"\",\"icon\":\"iconic:blender\",\"featured\":false,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"$1e\",\"detail_content\":\"$1f\",\"project_order\":5,\"date\":\"2025-04-14T10:06:31\",\"display_date\":\"2025-04-14T10:06:31\",\"created_at\":\"2025-04-14T18:06:31\",\"updated_at\":\"2025-07-18T14:35:23\",\"category\":\"Python\",\"category_icon\":null,\"tags\":[\"Python\"],\"tag_objects\":[{\"name\":\"Python\",\"slug\":\"python\",\"description\":null,\"color\":\"#f59e0b\",\"icon\":null,\"category\":\"tech\",\"id\":5,\"created_at\":\"2025-07-10T20:02:23\",\"updated_at\":\"2025-07-12T10:01:07\"}],\"tech_stack\":[\"Python\"],\"categories\":[],\"module_categories\":[]}],\"icon\":null,\"description\":null,\"display_order\":500,\"id\":5,\"slug\":\"python\"},{\"categoryName\":\"其他\",\"projects\":[{\"id\":28,\"slug\":\"project-12\",\"name\":\"Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models\",\"description\":\"提出了一种基于伪代码的 大语言模型指令微调 的中医信息抽取范式，有效提升模型对专业术语和复杂关系的识别能力。\",\"link\":\"$undefined\",\"github_link\":null,\"logo\":\"\",\"icon\":null,\"featured\":true,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"$20\",\"detail_content\":\"$21\",\"project_order\":1,\"date\":\"2025-04-14T08:04:30\",\"display_date\":\"2025-04-14T08:04:30\",\"created_at\":\"2025-04-14T08:04:30\",\"updated_at\":\"2025-07-18T14:35:24\",\"category\":\"$undefined\",\"category_icon\":null,\"tags\":[],\"tag_objects\":[],\"tech_stack\":[],\"categories\":[],\"module_categories\":[]},{\"id\":27,\"slug\":\"project-11\",\"name\":\"基于图神经网络增强句嵌入的中医文献多标签分类方法研究 \",\"description\":\"提出一种融合图神经网络与句嵌入的中医文献多标签分类方法，显著提升关键词自动预测的准确性与语义表达能力。\",\"link\":\"$undefined\",\"github_link\":null,\"logo\":\"\",\"icon\":\"GatewayOutlined\",\"featured\":true,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"$22\",\"detail_content\":\"$23\",\"project_order\":2,\"date\":\"2025-04-14T07:25:11\",\"display_date\":\"2025-04-14T07:25:11\",\"created_at\":\"2025-04-14T07:25:11\",\"updated_at\":\"2025-07-18T14:35:24\",\"category\":\"$undefined\",\"category_icon\":null,\"tags\":[],\"tag_objects\":[],\"tech_stack\":[],\"categories\":[],\"module_categories\":[]},{\"id\":26,\"slug\":\"project-10\",\"name\":\"2nd TCM-KG Competition 2023\",\"description\":\"第二届中医药知识图谱大赛第一名方案\",\"link\":\"$undefined\",\"github_link\":null,\"logo\":\"\",\"icon\":\"lucide:wifi-cog\",\"featured\":false,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"\u003cp\u003e\u003cstrong\u003e本方案在第二届中医药知识图谱大赛（山东济南，2023）中获得第一名。\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong style=\\\"color: rgb(0, 102, 204); background-color: rgb(204, 232, 204);\\\"\u003e\u003cu\u003e\u003ca href=\\\"http://document.jyaochen.cn/file/report/tcm_kg_com_2nd_2023.pdf\\\" rel=\\\"noopener noreferrer\\\" target=\\\"_blank\\\"\u003e技术报告\u003c/a\u003e\u003c/u\u003e\u003c/strong\u003e\u003c/p\u003e\u003ch3\u003e\u003cbr\u003e\u003c/h3\u003e\u003cp\u003e在知识图谱构建方面，采用多种方法提升信息抽取的准确性与图谱质量。通过融合大语言模型对中医医案文本进行实体识别与关系抽取的数据增强\u003cspan class=\\\"ql-size-small\\\"\u003e\u003cspan class=\\\"ql-cursor\\\"\u003e﻿﻿﻿﻿﻿﻿﻿﻿﻿\u003c/span\u003e\u003c/span\u003e，运用替换、掩码、合并、扩展等策略，提升模型对专业术语与罕见实体的识别能力，实体识别准确率达98%以上。结合GPT-4的提示学习技术，围绕中药、诊断、治疗等九类实体以及六类关系实现高质量结构化抽取。通过LangChain框架，通过预设实体类型约束与JSON格式输出，实现知识图谱的自动构建。最终图谱包含36,746个实体与123,358条关系，整合自SymMap与天池大赛数据，覆盖中医药多个核心要素。\u003c/p\u003e\u003cp\u003e在问答应用方面，实现用户输入语义的准确理解及相关实体的高效检索，显著提升意图识别效果。进一步将GPT-4与Neo4j图数据库结合，构建融合结构化知识的问答系统。\u003c/p\u003e\",\"detail_content\":\"\u003cp\u003e\u003cstrong\u003e本方案在第二届中医药知识图谱大赛（山东济南，2023）中获得第一名。\u003c/strong\u003e\u003c/p\u003e\u003cp\u003e\u003cstrong style=\\\"color: rgb(0, 102, 204); background-color: rgb(204, 232, 204);\\\"\u003e\u003cu\u003e\u003ca href=\\\"http://document.jyaochen.cn/file/report/tcm_kg_com_2nd_2023.pdf\\\" rel=\\\"noopener noreferrer\\\" target=\\\"_blank\\\"\u003e技术报告\u003c/a\u003e\u003c/u\u003e\u003c/strong\u003e\u003c/p\u003e\u003ch3\u003e\u003cbr\u003e\u003c/h3\u003e\u003cp\u003e在知识图谱构建方面，采用多种方法提升信息抽取的准确性与图谱质量。通过融合大语言模型对中医医案文本进行实体识别与关系抽取的数据增强\u003cspan class=\\\"ql-size-small\\\"\u003e\u003cspan class=\\\"ql-cursor\\\"\u003e﻿﻿﻿﻿﻿﻿﻿﻿﻿\u003c/span\u003e\u003c/span\u003e，运用替换、掩码、合并、扩展等策略，提升模型对专业术语与罕见实体的识别能力，实体识别准确率达98%以上。结合GPT-4的提示学习技术，围绕中药、诊断、治疗等九类实体以及六类关系实现高质量结构化抽取。通过LangChain框架，通过预设实体类型约束与JSON格式输出，实现知识图谱的自动构建。最终图谱包含36,746个实体与123,358条关系，整合自SymMap与天池大赛数据，覆盖中医药多个核心要素。\u003c/p\u003e\u003cp\u003e在问答应用方面，实现用户输入语义的准确理解及相关实体的高效检索，显著提升意图识别效果。进一步将GPT-4与Neo4j图数据库结合，构建融合结构化知识的问答系统。\u003c/p\u003e\",\"project_order\":3,\"date\":\"2025-04-12T20:24:26\",\"display_date\":\"2025-04-12T20:24:26\",\"created_at\":\"2025-04-12T20:24:26\",\"updated_at\":\"2025-07-18T14:35:24\",\"category\":\"$undefined\",\"category_icon\":null,\"tags\":[],\"tag_objects\":[],\"tech_stack\":[],\"categories\":[],\"module_categories\":[]},{\"id\":37,\"slug\":\"ce-shi-xiang-mu\",\"name\":\"测试项目\",\"description\":\"\",\"link\":\"$undefined\",\"github_link\":null,\"logo\":null,\"icon\":null,\"featured\":false,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\"$24\",\"detail_content\":\"$25\",\"project_order\":0,\"date\":\"2025-07-12T13:57:35\",\"display_date\":\"2025-07-12T13:57:35\",\"created_at\":\"2025-07-12T13:57:06\",\"updated_at\":\"2025-07-18T14:35:23\",\"category\":\"$undefined\",\"category_icon\":null,\"tags\":[],\"tag_objects\":[],\"tech_stack\":[],\"categories\":[],\"module_categories\":[]},{\"id\":25,\"slug\":\"project-9\",\"name\":\"TCM-IE-LLM\",\"description\":\" Data example for Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models. CCKS-IJCKG 2024\",\"link\":{\"href\":\"https://github.com/JYao-Chen/TCM-IE-LLM\",\"label\":\"TCM-IE-LLM\"},\"github_link\":null,\"logo\":\"\",\"icon\":\"RobotOutlined\",\"featured\":true,\"type\":\"work\",\"is_github_project\":false,\"has_detail_page\":true,\"content\":\" Data example for Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models. CCKS-IJCKG 2024\",\"detail_content\":\" Data example for Enhancing Traditional Chinese Medicine Information Extraction Using Instruction-Tuned Large Models. CCKS-IJCKG 2024\",\"project_order\":0,\"date\":\"2025-04-12T09:07:41\",\"display_date\":\"2025-04-12T09:07:41\",\"created_at\":\"2025-04-12T09:07:41\",\"updated_at\":\"2025-07-18T14:35:24\",\"category\":\"$undefined\",\"category_icon\":null,\"tags\":[],\"tag_objects\":[],\"tech_stack\":[],\"categories\":[],\"module_categories\":[]}],\"icon\":null,\"description\":null,\"display_order\":999,\"id\":0,\"slug\":\"other\"}],\"pagesConfig\":{\"blogs\":{\"title\":\"Thoughts\",\"description\":\"Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation.\"},\"projects\":{\"title\":\"Featured \",\"description\":\"A showcase of my research projects and applications in Large Language Models and Traditional Chinese Medicine.\"},\"gallery\":{\"title\":\"Gallery\",\"description\":\"Explore my photography collection through different perspectives - timeline memories, organized albums, or browse all photos in a grid.\"}}}]}]}]}]\n"])</script><script>self.__next_f.push([1,"15:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"jyao-projects\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey.\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"Jingyao Chen\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"Jingyao Chen\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"Jingyao Chen\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"theme-color\",\"content\":\"#171717\"}],[\"$\",\"meta\",\"10\",{\"name\":\"msapplication-TileColor\",\"content\":\"#171717\"}],[\"$\",\"link\",\"11\",{\"rel\":\"canonical\",\"href\":\"http://**************:3000\"}],[\"$\",\"meta\",\"12\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:title\",\"content\":\"jyao-projects\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:description\",\"content\":\"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey.\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:url\",\"content\":\"http://**************:3000\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:site_name\",\"content\":\"Jingyao Chen Portfolio\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image\",\"content\":\"http://**************:3000/images/og-jingyao-portfolio.jpg\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"20\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"21\",{\"property\":\"og:image:alt\",\"content\":\"jyao-projects\"}],[\"$\",\"meta\",\"22\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:site\",\"content\":\"@JingyaoC\"}],[\"$\",\"meta\",\"25\",{\"name\":\"twitter:creator\",\"content\":\"@JingyaoC\"}],[\"$\",\"meta\",\"26\",{\"name\":\"twitter:title\",\"content\":\"jyao-projects\"}],[\"$\",\"meta\",\"27\",{\"name\":\"twitter:description\",\"content\":\"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey.\"}],[\"$\",\"meta\",\"28\",{\"name\":\"twitter:image\",\"content\":\"http://**************:3000/images/og-jingyao-portfolio.jpg\"}],[\"$\",\"link\",\"29\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"902x902\"}]]\n"])</script><script>self.__next_f.push([1,"4:null\n"])</script></body></html>