3:I[93285,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
4:I[46021,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Providers"]
5:I[83258,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"ThemeInitializer"]
6:I[59183,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"RouteProgressBar"]
7:I[62989,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"GlobalLoadingManager"]
8:I[20686,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"SmartPrefetch"]
9:I[3864,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Header"]
a:I[4707,[],""]
b:I[36423,[],""]
c:I[72972,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],""]
d:I[18908,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Footer"]
e:I[60827,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
f:I[42545,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PlausibleAnalytics"]
10:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceMonitor"]
11:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceDebugger"]
0:["7n7atQT5AqmloxjVA4Y6t",[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",{"children":["__PAGE__",{},[["$L1","$L2",null],null],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/8b6d6f69c7970b5a.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/72074f6a7392446a.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","className":"h-full antialiased","suppressHydrationWarning":true,"children":["$","body",null,{"className":"flex h-full","children":["$","$L3",null,{"children":["$","$L4",null,{"children":[["$","$L5",null,{}],["$","$L6",null,{}],["$","$L7",null,{}],["$","div",null,{"className":"flex w-full","children":[["$","$L8",null,{"routes":["/","/about","/projects","/blogs","/gallery"],"priority":"low","delay":1500}],["$","div",null,{"className":"fixed inset-0 flex justify-center sm:px-8","children":["$","div",null,{"className":"flex w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"w-full shadow-xl dark:bg-muted"}]}]}],["$","div",null,{"className":"relative flex w-full flex-col px-4 sm:px-0","children":[["$","$L9",null,{}],["$","main",null,{"className":"flex-auto","children":["$","$La",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$Lb",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","div",null,{"className":"sm:px-8 flex h-full items-center pt-16 sm:pt-32","children":["$","div",null,{"className":"mx-auto w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"relative px-4 sm:px-8 lg:px-12","children":["$","div",null,{"className":"mx-auto max-w-2xl lg:max-w-5xl","children":["$","div",null,{"className":"flex flex-col items-center","children":[["$","p",null,{"className":"text-base font-semibold text-zinc-400 dark:text-zinc-500","children":"404"}],["$","h1",null,{"className":"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100","children":"Page not found"}],["$","p",null,{"className":"mt-4 text-base text-zinc-600 dark:text-zinc-400","children":"Sorry, we couldn’t find the page you’re looking for."}],["$","$Lc",null,{"className":"inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70 mt-4","href":"/","children":"Go back home"}]]}]}]}]}]}],"notFoundStyles":[]}]}],["$","$Ld",null,{}]]}]]}],[null,["$","$Le",null,{}],["$","$Lf",null,{}]],["$","$L10",null,{}],["$","$L11",null,{}]]}]}]}]}]],null],null],["$L12",null]]]]
13:I[21832,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"AnimatedSection"]
15:I[632,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"default"]
16:I[21832,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"ScrollReveal"]
17:I[41920,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"ProjectCard"]
18:I[68766,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"BlogPreloader"]
19:I[95512,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"GlowOnHover"]
1a:I[95512,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"ShimmerEffect"]
1b:I[95512,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"RippleEffect"]
1c:I[99175,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"default"]
1d:I[11457,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"default"]
2:["$","div",null,{"className":"sm:px-8 mt-6 mb-0","children":["$","div",null,{"className":"mx-auto w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"relative px-4 sm:px-8 lg:px-12","children":["$","div",null,{"className":"mx-auto max-w-2xl lg:max-w-5xl","children":[["$","div",null,{"className":"relative mb-4 rounded-2xl overflow-hidden","children":[["$","div",null,{"className":"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-2xl"}],["$","div",null,{"className":"relative","children":[["$","div",null,{"className":"absolute top-1/4 left-1/4 w-32 h-32 bg-primary/10 rounded-full blur-3xl animate-pulse-soft"}],["$","div",null,{"className":"absolute bottom-1/4 right-1/4 w-24 h-24 bg-secondary/10 rounded-full blur-2xl animate-pulse-soft","style":{"animationDelay":"1s"}}],["$","div",null,{"className":"relative p-8 py-16","children":["$","div",null,{"className":"max-w-4xl mx-auto text-center","children":["$","$L13",null,{"children":[["$","div",null,{"className":"relative mb-8","children":["$","h2",null,{"className":"text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl mb-4 relative","children":[["$","span",null,{"className":"bg-clip-text text-transparent font-semibold bg-gradient-to-r from-primary to-primary/70 animate-pulse-soft relative z-10","children":"Welcome!"}],["$","div",null,{"className":"absolute inset-0 text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary/20 blur-sm -z-10","children":"Welcome!"}]]}]}],["$","div",null,{"className":"relative mb-12","children":["$","p",null,{"className":"text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto transition-all duration-300 hover:text-foreground/80","children":"I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support."}]}],["$","div",null,{"className":"relative flex justify-center","children":"$L14"}]]}]}]}]]}]]}],["$","div",null,{"className":"mt-0 border-t border-zinc-100 py-2 dark:border-zinc-700/40","children":["$","$L15",null,{}]}],["$","$L16",null,{"className":"mx-auto flex flex-col max-w-xl gap-3 lg:max-w-none mt-1 mb-2 pt-4 pb-5 border-t border-muted","children":[["$","h2",null,{"className":"text-3xl font-semibold tracking-tight md:text-5xl mb-2","children":["$","span",null,{"className":"bg-clip-text text-transparent font-semibold bg-gradient-to-r from-blue-500 via-teal-500 to-green-500 animate-pulse-soft","children":"Featured Work"}]}],["$","p",null,{"className":"text-base text-muted-foreground max-w-2xl mb-6 leading-relaxed","children":"My research focuses on the intersection of Large Language Models and Traditional Chinese Medicine. I develop AI-driven frameworks for TCM knowledge discovery, clinical decision support, and intelligent healthcare applications. Below are some representative projects that showcase my work in this emerging field."}],["$","ul",null,{"role":"list","className":"grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 md:grid-cols-3","children":[["$","$L17","项目测试详情页",{"project":{"id":39,"title":"项目测试详情页","slug":"xiang-mu-ce-shi-xiang-qing-ye","description":null,"author":"Unknown","display_date":"2025-07-14T18:59:37","published":true,"show_on_homepage":true,"article_type":"project","homepage_order":0,"tags":[{"id":10,"name":"AI","slug":"ai","description":"llm","color":"#3b82f6"}],"project_url":null,"github_url":null,"demo_url":null,"logo_url":null,"icon":"iconic:gemini","project_status":"completed","is_github_project":false,"featured":false,"display_order":0,"name":"项目测试详情页","link":null,"has_detail_page":true},"titleAs":"h3","className":"animate-fade-in-up","style":{"animationDelay":"0ms"}}],["$","$L17","测试项目",{"project":{"id":37,"title":"测试项目","slug":"ce-shi-xiang-mu","description":null,"author":"Unknown","display_date":"2025-07-12T13:57:35","published":true,"show_on_homepage":true,"article_type":"project","homepage_order":0,"tags":[],"project_url":null,"github_url":null,"demo_url":null,"logo_url":null,"icon":null,"project_status":"completed","is_github_project":false,"featured":false,"display_order":0,"name":"测试项目","link":null,"has_detail_page":true},"titleAs":"h3","className":"animate-fade-in-up","style":{"animationDelay":"150ms"}}]]}]]}],[],["$","$L16",null,{"className":"mx-auto flex flex-col max-w-xl gap-4 py-6 my-6 lg:max-w-none border-t border-muted","children":[["$","h2",null,{"className":"text-3xl font-semibold tracking-tight md:text-5xl mb-2","children":["$","span",null,{"className":"bg-clip-text text-transparent font-semibold bg-gradient-to-r from-blue-500 via-teal-500 to-green-500 animate-pulse-soft","children":"Thoughts & Insights"}]}],["$","p",null,{"className":"text-base text-muted-foreground max-w-2xl mb-6 leading-relaxed","children":"I share my research findings, technical insights, and reflections on AI applications in healthcare, particularly in Traditional Chinese Medicine and natural language processing."}]]}],["$","div",null,{"className":"mx-auto grid max-w-xl grid-cols-1 gap-y-14 lg:max-w-none lg:grid-cols-2","children":[["$","div",null,{"className":"flex flex-col gap-12","children":[["$","div","千年之后谁会记得谁",{"className":"animate-fade-in-up","style":{"animationDelay":"0ms"},"children":["$","$L18",null,{"slug":"千年之后谁会记得谁","children":["$","$L19",null,{"className":"group relative animate-fade-in-up","intensity":"medium","children":[["$","div",null,{"className":"absolute -inset-2 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse"}],["$","div",null,{"className":"absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"}],["$","$L1a",null,{"children":["$","article",null,{"className":"relative bg-background/80 backdrop-blur-sm border border-muted-foreground/10 rounded-2xl p-6 transition-all duration-500 ease-smooth hover:shadow-2xl hover:shadow-primary/5 hover:-translate-y-2 hover:scale-[1.02] hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5 group relative flex flex-col items-start","children":[["$","div",null,{"className":"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}],["$","div",null,{"className":"absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}],["$","div",null,{"className":"relative z-10 space-y-5","children":[["$","div",null,{"className":"pb-3 border-b border-border/20","children":["$","time",null,{"className":"flex items-center gap-2 text-sm font-medium text-muted-foreground group-hover:text-primary/70 transition-colors duration-300 relative z-10 order-first mb-3 flex items-center text-sm text-muted-foreground","dateTime":"2025-04-04T01:52:00","children":[false,[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar w-3.5 h-3.5 flex-shrink-0","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],["$","div",null,{"className":"flex flex-col sm:flex-row sm:items-center sm:gap-3","children":[["$","span",null,{"className":"relative","children":["April 4, 2025 at 01:52",["$","div",null,{"className":"absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary/50 transition-all duration-300 group-hover:w-full"}]]}],["$","span",null,{"className":"text-xs text-muted-foreground/80 mt-1 sm:mt-0","children":"Fri"}]]}]]]}]}],["$","div",null,{"className":"space-y-4","children":["$undefined",["$","h3",null,{"className":"text-base font-semibold tracking-normal","children":[["$","div",null,{"className":"absolute -inset-x-4 -inset-y-6 z-0 scale-95 transition group-hover:scale-100 sm:-inset-x-6 sm:rounded-2xl group-hover:bg-muted/50 "}],["$","$Lc",null,{"href":"/blogs/千年之后谁会记得谁","children":[["$","span",null,{"className":"absolute -inset-x-4 -inset-y-6 z-20 sm:-inset-x-6 sm:rounded-2xl"}],["$","span",null,{"className":"relative z-10","children":"千年之后谁会记得谁"}]]}]]}]]}],["$","div",null,{"className":"flex flex-wrap gap-2","children":[[["$","$Lc","16",{"href":"/blogs?tag=日常","children":["$","$L1b",null,{"children":["$","span",null,{"className":"inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 cursor-pointer relative overflow-hidden","style":{"backgroundColor":"rgba(5, 150, 105, 0.08)","color":"rgba(5, 150, 105, 0.8)","border":"1px solid rgba(5, 150, 105, 0.15)","boxShadow":"none"},"children":"日常"}]}]}]],false]}],["$","p",null,{"className":"text-muted-foreground group-hover:text-foreground transition-colors duration-300 leading-relaxed line-clamp-3","children":"这是一个精心设计的读书笔记模板，帮助你系统地记录和整理阅读心得。包含图书信息、核心观点、个人感悟等多个模块，让你的读书笔记更加结构化和有价值。通过这个模板，你可以更好地消化和吸收书中的知识，形成自己的思考体系。"}],["$","$L1b",null,{"children":["$","div",null,{"className":"group/cta inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 border border-primary/20 hover:border-primary/30 rounded-lg text-primary hover:text-primary/90 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary/20 font-medium relative","children":[["$","span",null,{"className":"relative","children":"Continue Reading"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-up-right w-4 h-4 group-hover/cta:translate-x-0.5 group-hover/cta:-translate-y-0.5 transition-transform duration-300","children":[["$","path","1tivn9",{"d":"M7 7h10v10"}],["$","path","1vkiza",{"d":"M7 17 17 7"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 translate-x-[-100%] group-hover/cta:translate-x-[100%] transition-transform duration-500 rounded-lg"}]]}]}]]}]]}]}]]}]}]}],["$","div","鬓毛已衰乡音改",{"className":"animate-fade-in-up","style":{"animationDelay":"150ms"},"children":["$","$L18",null,{"slug":"鬓毛已衰乡音改","children":["$","$L19",null,{"className":"group relative animate-fade-in-up","intensity":"medium","children":[["$","div",null,{"className":"absolute -inset-2 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse"}],["$","div",null,{"className":"absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"}],["$","$L1a",null,{"children":["$","article",null,{"className":"relative bg-background/80 backdrop-blur-sm border border-muted-foreground/10 rounded-2xl p-6 transition-all duration-500 ease-smooth hover:shadow-2xl hover:shadow-primary/5 hover:-translate-y-2 hover:scale-[1.02] hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5 group relative flex flex-col items-start","children":[["$","div",null,{"className":"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}],["$","div",null,{"className":"absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}],["$","div",null,{"className":"relative z-10 space-y-5","children":[["$","div",null,{"className":"pb-3 border-b border-border/20","children":["$","time",null,{"className":"flex items-center gap-2 text-sm font-medium text-muted-foreground group-hover:text-primary/70 transition-colors duration-300 relative z-10 order-first mb-3 flex items-center text-sm text-muted-foreground","dateTime":"2024-01-31T18:06:00","children":[false,[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar w-3.5 h-3.5 flex-shrink-0","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],["$","div",null,{"className":"flex flex-col sm:flex-row sm:items-center sm:gap-3","children":[["$","span",null,{"className":"relative","children":["January 31, 2024 at 18:06",["$","div",null,{"className":"absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary/50 transition-all duration-300 group-hover:w-full"}]]}],["$","span",null,{"className":"text-xs text-muted-foreground/80 mt-1 sm:mt-0","children":"Wed"}]]}]]]}]}],["$","div",null,{"className":"space-y-4","children":["$undefined",["$","h3",null,{"className":"text-base font-semibold tracking-normal","children":[["$","div",null,{"className":"absolute -inset-x-4 -inset-y-6 z-0 scale-95 transition group-hover:scale-100 sm:-inset-x-6 sm:rounded-2xl group-hover:bg-muted/50 "}],["$","$Lc",null,{"href":"/blogs/鬓毛已衰乡音改","children":[["$","span",null,{"className":"absolute -inset-x-4 -inset-y-6 z-20 sm:-inset-x-6 sm:rounded-2xl"}],["$","span",null,{"className":"relative z-10","children":"鬓毛已衰乡音改"}]]}]]}]]}],false,["$","p",null,{"className":"text-muted-foreground group-hover:text-foreground transition-colors duration-300 leading-relaxed line-clamp-3","children":"天苍苍，野茫茫，山之上，国有殇。"}],["$","$L1b",null,{"children":["$","div",null,{"className":"group/cta inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 border border-primary/20 hover:border-primary/30 rounded-lg text-primary hover:text-primary/90 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary/20 font-medium relative","children":[["$","span",null,{"className":"relative","children":"Continue Reading"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-up-right w-4 h-4 group-hover/cta:translate-x-0.5 group-hover/cta:-translate-y-0.5 transition-transform duration-300","children":[["$","path","1tivn9",{"d":"M7 7h10v10"}],["$","path","1vkiza",{"d":"M7 17 17 7"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 translate-x-[-100%] group-hover/cta:translate-x-[100%] transition-transform duration-500 rounded-lg"}]]}]}]]}]]}]}]]}]}]}],["$","div","菩提下静静观想",{"className":"animate-fade-in-up","style":{"animationDelay":"300ms"},"children":["$","$L18",null,{"slug":"菩提下静静观想","children":["$","$L19",null,{"className":"group relative animate-fade-in-up","intensity":"medium","children":[["$","div",null,{"className":"absolute -inset-2 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse"}],["$","div",null,{"className":"absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"}],["$","$L1a",null,{"children":["$","article",null,{"className":"relative bg-background/80 backdrop-blur-sm border border-muted-foreground/10 rounded-2xl p-6 transition-all duration-500 ease-smooth hover:shadow-2xl hover:shadow-primary/5 hover:-translate-y-2 hover:scale-[1.02] hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5 group relative flex flex-col items-start","children":[["$","div",null,{"className":"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}],["$","div",null,{"className":"absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}],["$","div",null,{"className":"relative z-10 space-y-5","children":[["$","div",null,{"className":"pb-3 border-b border-border/20","children":["$","time",null,{"className":"flex items-center gap-2 text-sm font-medium text-muted-foreground group-hover:text-primary/70 transition-colors duration-300 relative z-10 order-first mb-3 flex items-center text-sm text-muted-foreground","dateTime":"2024-01-07T18:00:00","children":[false,[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar w-3.5 h-3.5 flex-shrink-0","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],["$","div",null,{"className":"flex flex-col sm:flex-row sm:items-center sm:gap-3","children":[["$","span",null,{"className":"relative","children":["January 7, 2024 at 18:00",["$","div",null,{"className":"absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary/50 transition-all duration-300 group-hover:w-full"}]]}],["$","span",null,{"className":"text-xs text-muted-foreground/80 mt-1 sm:mt-0","children":"Sun"}]]}]]]}]}],["$","div",null,{"className":"space-y-4","children":["$undefined",["$","h3",null,{"className":"text-base font-semibold tracking-normal","children":[["$","div",null,{"className":"absolute -inset-x-4 -inset-y-6 z-0 scale-95 transition group-hover:scale-100 sm:-inset-x-6 sm:rounded-2xl group-hover:bg-muted/50 "}],["$","$Lc",null,{"href":"/blogs/菩提下静静观想","children":[["$","span",null,{"className":"absolute -inset-x-4 -inset-y-6 z-20 sm:-inset-x-6 sm:rounded-2xl"}],["$","span",null,{"className":"relative z-10","children":"菩提下静静观想"}]]}]]}]]}],false,["$","p",null,{"className":"text-muted-foreground group-hover:text-foreground transition-colors duration-300 leading-relaxed line-clamp-3","children":"举世而誉之而不加勤，举世而非之而不加沮，定乎内外之分，辩乎荣辱之境。 "}],["$","$L1b",null,{"children":["$","div",null,{"className":"group/cta inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 border border-primary/20 hover:border-primary/30 rounded-lg text-primary hover:text-primary/90 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary/20 font-medium relative","children":[["$","span",null,{"className":"relative","children":"Continue Reading"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-up-right w-4 h-4 group-hover/cta:translate-x-0.5 group-hover/cta:-translate-y-0.5 transition-transform duration-300","children":[["$","path","1tivn9",{"d":"M7 7h10v10"}],["$","path","1vkiza",{"d":"M7 17 17 7"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 translate-x-[-100%] group-hover/cta:translate-x-[100%] transition-transform duration-500 rounded-lg"}]]}]}]]}]]}]}]]}]}]}]]}],["$","div",null,{"className":"space-y-8 lg:pl-16 xl:pl-24","children":[["$","$L13",null,{"delay":300,"children":["$","$L1c",null,{}]}],["$","$L13",null,{"delay":400,"children":["$","$L1d",null,{}]}]]}]]}]]}]}]}]}]
12:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"jyaos"}],["$","meta","3",{"name":"description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","4",{"name":"author","content":"Jingyao Chen"}],["$","meta","5",{"name":"keywords","content":"Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery"}],["$","meta","6",{"name":"creator","content":"Jingyao Chen"}],["$","meta","7",{"name":"publisher","content":"Jingyao Chen"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"theme-color","content":"#171717"}],["$","meta","10",{"name":"msapplication-TileColor","content":"#171717"}],["$","link","11",{"rel":"canonical","href":"http://**************:3000"}],["$","link","12",{"rel":"alternate","type":"application/rss+xml","href":"http://**************:3000/feed"}],["$","meta","13",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","14",{"property":"og:title","content":"jyaos"}],["$","meta","15",{"property":"og:description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","16",{"property":"og:url","content":"http://**************:3000"}],["$","meta","17",{"property":"og:site_name","content":"Jingyao Chen Portfolio"}],["$","meta","18",{"property":"og:locale","content":"en_US"}],["$","meta","19",{"property":"og:image","content":"http://**************:3000/images/og-jingyao-portfolio.jpg"}],["$","meta","20",{"property":"og:image:width","content":"1200"}],["$","meta","21",{"property":"og:image:height","content":"630"}],["$","meta","22",{"property":"og:image:alt","content":"jyaos"}],["$","meta","23",{"property":"og:type","content":"website"}],["$","meta","24",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","25",{"name":"twitter:site","content":"@JingyaoC"}],["$","meta","26",{"name":"twitter:creator","content":"@JingyaoC"}],["$","meta","27",{"name":"twitter:title","content":"jyaos"}],["$","meta","28",{"name":"twitter:description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","29",{"name":"twitter:image","content":"http://**************:3000/images/og-jingyao-portfolio.jpg"}],["$","link","30",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"902x902"}]]
1:null
1e:I[52043,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],"default"]
14:["$","$L1e",null,{"className":"animate-fade-in-up","style":{"animationDelay":"300ms"},"socialLinks":[{"name":"Github","icon":"lucide:github","href":"https://github.com/JYao-Chen"},{"name":"HuggingFace","icon":"tech-stack:huggingface","href":"https://huggingface.co/JYaooo"},{"name":"Email","icon":"email","href":"mailto:<EMAIL>"},{"name":"公众号","icon":"lucide:radio-tower","href":"https://mp.weixin.qq.com/s/YA46b2ccH38-kv2F1eM3Qg"}]}]
