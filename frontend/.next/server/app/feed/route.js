(()=>{var exports={};exports.id=84,exports.ids=[84],exports.modules={20399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:t=>{"use strict";t.exports=require("buffer")},92048:t=>{"use strict";t.exports=require("fs")},55315:t=>{"use strict";t.exports=require("path")},76162:t=>{"use strict";t.exports=require("stream")},74026:t=>{"use strict";t.exports=require("string_decoder")},12209:(t,e,i)=>{"use strict";i.r(e),i.d(e,{originalPathname:()=>E,patchFetch:()=>A,requestAsyncStorage:()=>D,routeModule:()=>x,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var n={};i.r(n),i.d(n,{GET:()=>f});var r=i(49303),s=i(88716),a=i(60670),o=i(21233),u=i(14927),c=i(92048),h=i(55315),l=i.n(h),p=i(3673),d=i.n(p);async function f(t){let e="http://**************:3000";if(!e)throw Error("Missing NEXT_PUBLIC_SITE_URL environment variable");let i={name:u.u2,email:u.Do},n=new o.f({title:i.name,description:u.u2+"'s blog",author:i,id:e,link:e,image:`${e}/favicon.ico`,favicon:`${e}/favicon.ico`,copyright:`All rights reserved ${u.u2} ${new Date().getFullYear()}`,feedLinks:{rss2:`${e}/feed`}});for(let t of(await c.promises.readdir(l().join(process.cwd(),"src/content/blog"))).filter(t=>t.endsWith(".mdx"))){let r=t.replace(/\.mdx$/,""),s=l().join(process.cwd(),"src/content/blog",t),a=await c.promises.readFile(s,"utf-8"),{data:o,content:u}=d()(a);n.addItem({title:o.title,id:`${e}/blogs/${r}`,link:`${e}/blogs/${r}`,description:o.description,content:u,author:[i],date:new Date(o.date)})}return new Response(n.rss2(),{status:200,headers:{"Content-Type":"application/xml","cache-control":"s-maxage=86400"}})}let m="standalone",x=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/feed/route",pathname:"/feed",filename:"route",bundlePath:"app/feed/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/feed/route.ts",nextConfigOutput:m,userland:n}),{requestAsyncStorage:D,staticGenerationAsyncStorage:y,serverHooks:g}=x,E="/feed/route";function A(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},97296:function(t){!function(e,i){t.exports=i()}(0,function(){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={exports:{},id:n,loaded:!1};return t[n].call(r.exports,r,r.exports,i),r.loaded=!0,r.exports}return i.m=t,i.c=e,i.p="",i(0)}([function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(1),r=i(3),s=i(8),a=i(15);function o(t,e,i){var a,o=null,u=function(t,e){i&&i(t,e),o&&o.visit(t,e)},c="function"==typeof i?u:null,h=!1;if(e){h="boolean"==typeof e.comment&&e.comment;var l="boolean"==typeof e.attachComment&&e.attachComment;(h||l)&&((o=new n.CommentHandler).attach=l,e.comment=!0,c=u)}var p=!1;e&&"string"==typeof e.sourceType&&(p="module"===e.sourceType),a=e&&"boolean"==typeof e.jsx&&e.jsx?new r.JSXParser(t,e,c):new s.Parser(t,e,c);var d=p?a.parseModule():a.parseScript();return h&&o&&(d.comments=o.comments),a.config.tokens&&(d.tokens=a.tokens),a.config.tolerant&&(d.errors=a.errorHandler.errors),d}function u(t,e,i){var n=e||{};return n.sourceType="module",o(t,n,i)}function c(t,e,i){var n=e||{};return n.sourceType="script",o(t,n,i)}function h(t,e,i){var n,r=new a.Tokenizer(t,e);n=[];try{for(;;){var s=r.getNextToken();if(!s)break;i&&(s=i(s)),n.push(s)}}catch(t){r.errorHandler.tolerate(t)}return r.errorHandler.tolerant&&(n.errors=r.errors()),n}e.parse=o,e.parseModule=u,e.parseScript=c,e.tokenize=h;var l=i(2);e.Syntax=l.Syntax,e.version="4.0.1"},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(2),r=function(){function t(){this.attach=!1,this.comments=[],this.stack=[],this.leading=[],this.trailing=[]}return t.prototype.insertInnerComments=function(t,e){if(t.type===n.Syntax.BlockStatement&&0===t.body.length){for(var i=[],r=this.leading.length-1;r>=0;--r){var s=this.leading[r];e.end.offset>=s.start&&(i.unshift(s.comment),this.leading.splice(r,1),this.trailing.splice(r,1))}i.length&&(t.innerComments=i)}},t.prototype.findTrailingComments=function(t){var e=[];if(this.trailing.length>0){for(var i=this.trailing.length-1;i>=0;--i){var n=this.trailing[i];n.start>=t.end.offset&&e.unshift(n.comment)}return this.trailing.length=0,e}var r=this.stack[this.stack.length-1];if(r&&r.node.trailingComments){var s=r.node.trailingComments[0];s&&s.range[0]>=t.end.offset&&(e=r.node.trailingComments,delete r.node.trailingComments)}return e},t.prototype.findLeadingComments=function(t){for(var e,i=[];this.stack.length>0;){var n=this.stack[this.stack.length-1];if(n&&n.start>=t.start.offset)e=n.node,this.stack.pop();else break}if(e){for(var r=e.leadingComments?e.leadingComments.length:0,s=r-1;s>=0;--s){var a=e.leadingComments[s];a.range[1]<=t.start.offset&&(i.unshift(a),e.leadingComments.splice(s,1))}return e.leadingComments&&0===e.leadingComments.length&&delete e.leadingComments,i}for(var s=this.leading.length-1;s>=0;--s){var n=this.leading[s];n.start<=t.start.offset&&(i.unshift(n.comment),this.leading.splice(s,1))}return i},t.prototype.visitNode=function(t,e){if(t.type!==n.Syntax.Program||!(t.body.length>0)){this.insertInnerComments(t,e);var i=this.findTrailingComments(e),r=this.findLeadingComments(e);r.length>0&&(t.leadingComments=r),i.length>0&&(t.trailingComments=i),this.stack.push({node:t,start:e.start.offset})}},t.prototype.visitComment=function(t,e){var i="L"===t.type[0]?"Line":"Block",n={type:i,value:t.value};if(t.range&&(n.range=t.range),t.loc&&(n.loc=t.loc),this.comments.push(n),this.attach){var r={comment:{type:i,value:t.value,range:[e.start.offset,e.end.offset]},start:e.start.offset};t.loc&&(r.comment.loc=t.loc),t.type=i,this.leading.push(r),this.trailing.push(r)}},t.prototype.visit=function(t,e){"LineComment"===t.type?this.visitComment(t,e):"BlockComment"===t.type?this.visitComment(t,e):this.attach&&this.visitNode(t,e)},t}();e.CommentHandler=r},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Syntax={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DoWhileStatement:"DoWhileStatement",DebuggerStatement:"DebuggerStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForOfStatement:"ForOfStatement",ForInStatement:"ForInStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchCase:"SwitchCase",SwitchStatement:"SwitchStatement",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"}},function(t,e,i){"use strict";var n=this&&this.__extends||function(){var t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var r=i(4),s=i(5),a=i(6),o=i(7),u=i(8),c=i(13),h=i(14);function l(t){var e;switch(t.type){case a.JSXSyntax.JSXIdentifier:e=t.name;break;case a.JSXSyntax.JSXNamespacedName:var i=t;e=l(i.namespace)+":"+l(i.name);break;case a.JSXSyntax.JSXMemberExpression:var n=t;e=l(n.object)+"."+l(n.property)}return e}c.TokenName[100]="JSXIdentifier",c.TokenName[101]="JSXText";var p=function(t){function e(e,i,n){return t.call(this,e,i,n)||this}return n(e,t),e.prototype.parsePrimaryExpression=function(){return this.match("<")?this.parseJSXRoot():t.prototype.parsePrimaryExpression.call(this)},e.prototype.startJSX=function(){this.scanner.index=this.startMarker.index,this.scanner.lineNumber=this.startMarker.line,this.scanner.lineStart=this.startMarker.index-this.startMarker.column},e.prototype.finishJSX=function(){this.nextToken()},e.prototype.reenterJSX=function(){this.startJSX(),this.expectJSX("}"),this.config.tokens&&this.tokens.pop()},e.prototype.createJSXNode=function(){return this.collectComments(),{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},e.prototype.createJSXChildNode=function(){return{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},e.prototype.scanXHTMLEntity=function(t){for(var e="&",i=!0,n=!1,s=!1,a=!1;!this.scanner.eof()&&i&&!n;){var o=this.scanner.source[this.scanner.index];if(o===t)break;if(n=";"===o,e+=o,++this.scanner.index,!n)switch(e.length){case 2:s="#"===o;break;case 3:s&&(i=(a="x"===o)||r.Character.isDecimalDigit(o.charCodeAt(0)),s=s&&!a);break;default:i=(i=i&&!(s&&!r.Character.isDecimalDigit(o.charCodeAt(0))))&&!(a&&!r.Character.isHexDigit(o.charCodeAt(0)))}}if(i&&n&&e.length>2){var u=e.substr(1,e.length-2);s&&u.length>1?e=String.fromCharCode(parseInt(u.substr(1),10)):a&&u.length>2?e=String.fromCharCode(parseInt("0"+u.substr(1),16)):s||a||!h.XHTMLEntities[u]||(e=h.XHTMLEntities[u])}return e},e.prototype.lexJSX=function(){var t=this.scanner.source.charCodeAt(this.scanner.index);if(60===t||62===t||47===t||58===t||61===t||123===t||125===t){var e=this.scanner.source[this.scanner.index++];return{type:7,value:e,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index-1,end:this.scanner.index}}if(34===t||39===t){for(var i=this.scanner.index,n=this.scanner.source[this.scanner.index++],s="";!this.scanner.eof();){var a=this.scanner.source[this.scanner.index++];if(a===n)break;"&"===a?s+=this.scanXHTMLEntity(n):s+=a}return{type:8,value:s,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:i,end:this.scanner.index}}if(46===t){var o=this.scanner.source.charCodeAt(this.scanner.index+1),u=this.scanner.source.charCodeAt(this.scanner.index+2),e=46===o&&46===u?"...":".",i=this.scanner.index;return this.scanner.index+=e.length,{type:7,value:e,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:i,end:this.scanner.index}}if(96===t)return{type:10,value:"",lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index,end:this.scanner.index};if(r.Character.isIdentifierStart(t)&&92!==t){var i=this.scanner.index;for(++this.scanner.index;!this.scanner.eof();){var a=this.scanner.source.charCodeAt(this.scanner.index);if(r.Character.isIdentifierPart(a)&&92!==a)++this.scanner.index;else if(45===a)++this.scanner.index;else break}return{type:100,value:this.scanner.source.slice(i,this.scanner.index),lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:i,end:this.scanner.index}}return this.scanner.lex()},e.prototype.nextJSXToken=function(){this.collectComments(),this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;var t=this.lexJSX();return this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.config.tokens&&this.tokens.push(this.convertToken(t)),t},e.prototype.nextJSXText=function(){this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;for(var t=this.scanner.index,e="";!this.scanner.eof();){var i=this.scanner.source[this.scanner.index];if("{"===i||"<"===i)break;++this.scanner.index,e+=i,r.Character.isLineTerminator(i.charCodeAt(0))&&(++this.scanner.lineNumber,"\r"===i&&"\n"===this.scanner.source[this.scanner.index]&&++this.scanner.index,this.scanner.lineStart=this.scanner.index)}this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart;var n={type:101,value:e,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:t,end:this.scanner.index};return e.length>0&&this.config.tokens&&this.tokens.push(this.convertToken(n)),n},e.prototype.peekJSXToken=function(){var t=this.scanner.saveState();this.scanner.scanComments();var e=this.lexJSX();return this.scanner.restoreState(t),e},e.prototype.expectJSX=function(t){var e=this.nextJSXToken();(7!==e.type||e.value!==t)&&this.throwUnexpectedToken(e)},e.prototype.matchJSX=function(t){var e=this.peekJSXToken();return 7===e.type&&e.value===t},e.prototype.parseJSXIdentifier=function(){var t=this.createJSXNode(),e=this.nextJSXToken();return 100!==e.type&&this.throwUnexpectedToken(e),this.finalize(t,new s.JSXIdentifier(e.value))},e.prototype.parseJSXElementName=function(){var t=this.createJSXNode(),e=this.parseJSXIdentifier();if(this.matchJSX(":")){var i=e;this.expectJSX(":");var n=this.parseJSXIdentifier();e=this.finalize(t,new s.JSXNamespacedName(i,n))}else if(this.matchJSX("."))for(;this.matchJSX(".");){var r=e;this.expectJSX(".");var a=this.parseJSXIdentifier();e=this.finalize(t,new s.JSXMemberExpression(r,a))}return e},e.prototype.parseJSXAttributeName=function(){var t,e=this.createJSXNode(),i=this.parseJSXIdentifier();if(this.matchJSX(":")){var n=i;this.expectJSX(":");var r=this.parseJSXIdentifier();t=this.finalize(e,new s.JSXNamespacedName(n,r))}else t=i;return t},e.prototype.parseJSXStringLiteralAttribute=function(){var t=this.createJSXNode(),e=this.nextJSXToken();8!==e.type&&this.throwUnexpectedToken(e);var i=this.getTokenRaw(e);return this.finalize(t,new o.Literal(e.value,i))},e.prototype.parseJSXExpressionAttribute=function(){var t=this.createJSXNode();this.expectJSX("{"),this.finishJSX(),this.match("}")&&this.tolerateError("JSX attributes must only be assigned a non-empty expression");var e=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(t,new s.JSXExpressionContainer(e))},e.prototype.parseJSXAttributeValue=function(){return this.matchJSX("{")?this.parseJSXExpressionAttribute():this.matchJSX("<")?this.parseJSXElement():this.parseJSXStringLiteralAttribute()},e.prototype.parseJSXNameValueAttribute=function(){var t=this.createJSXNode(),e=this.parseJSXAttributeName(),i=null;return this.matchJSX("=")&&(this.expectJSX("="),i=this.parseJSXAttributeValue()),this.finalize(t,new s.JSXAttribute(e,i))},e.prototype.parseJSXSpreadAttribute=function(){var t=this.createJSXNode();this.expectJSX("{"),this.expectJSX("..."),this.finishJSX();var e=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(t,new s.JSXSpreadAttribute(e))},e.prototype.parseJSXAttributes=function(){for(var t=[];!this.matchJSX("/")&&!this.matchJSX(">");){var e=this.matchJSX("{")?this.parseJSXSpreadAttribute():this.parseJSXNameValueAttribute();t.push(e)}return t},e.prototype.parseJSXOpeningElement=function(){var t=this.createJSXNode();this.expectJSX("<");var e=this.parseJSXElementName(),i=this.parseJSXAttributes(),n=this.matchJSX("/");return n&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(t,new s.JSXOpeningElement(e,n,i))},e.prototype.parseJSXBoundaryElement=function(){var t=this.createJSXNode();if(this.expectJSX("<"),this.matchJSX("/")){this.expectJSX("/");var e=this.parseJSXElementName();return this.expectJSX(">"),this.finalize(t,new s.JSXClosingElement(e))}var i=this.parseJSXElementName(),n=this.parseJSXAttributes(),r=this.matchJSX("/");return r&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(t,new s.JSXOpeningElement(i,r,n))},e.prototype.parseJSXEmptyExpression=function(){var t=this.createJSXChildNode();return this.collectComments(),this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.finalize(t,new s.JSXEmptyExpression)},e.prototype.parseJSXExpressionContainer=function(){var t,e=this.createJSXNode();return this.expectJSX("{"),this.matchJSX("}")?(t=this.parseJSXEmptyExpression(),this.expectJSX("}")):(this.finishJSX(),t=this.parseAssignmentExpression(),this.reenterJSX()),this.finalize(e,new s.JSXExpressionContainer(t))},e.prototype.parseJSXChildren=function(){for(var t=[];!this.scanner.eof();){var e=this.createJSXChildNode(),i=this.nextJSXText();if(i.start<i.end){var n=this.getTokenRaw(i),r=this.finalize(e,new s.JSXText(i.value,n));t.push(r)}if("{"===this.scanner.source[this.scanner.index]){var a=this.parseJSXExpressionContainer();t.push(a)}else break}return t},e.prototype.parseComplexJSXElement=function(t){for(var e=[];!this.scanner.eof();){t.children=t.children.concat(this.parseJSXChildren());var i=this.createJSXChildNode(),n=this.parseJSXBoundaryElement();if(n.type===a.JSXSyntax.JSXOpeningElement){var r=n;if(r.selfClosing){var o=this.finalize(i,new s.JSXElement(r,[],null));t.children.push(o)}else e.push(t),t={node:i,opening:r,closing:null,children:[]}}if(n.type===a.JSXSyntax.JSXClosingElement){t.closing=n;var u=l(t.opening.name);if(u!==l(t.closing.name)&&this.tolerateError("Expected corresponding JSX closing tag for %0",u),e.length>0){var o=this.finalize(t.node,new s.JSXElement(t.opening,t.children,t.closing));(t=e[e.length-1]).children.push(o),e.pop()}else break}}return t},e.prototype.parseJSXElement=function(){var t=this.createJSXNode(),e=this.parseJSXOpeningElement(),i=[],n=null;if(!e.selfClosing){var r=this.parseComplexJSXElement({node:t,opening:e,closing:n,children:i});i=r.children,n=r.closing}return this.finalize(t,new s.JSXElement(e,i,n))},e.prototype.parseJSXRoot=function(){this.config.tokens&&this.tokens.pop(),this.startJSX();var t=this.parseJSXElement();return this.finishJSX(),t},e.prototype.isStartOfExpression=function(){return t.prototype.isStartOfExpression.call(this)||this.match("<")},e}(u.Parser);e.JSXParser=p},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/};e.Character={fromCodePoint:function(t){return t<65536?String.fromCharCode(t):String.fromCharCode(55296+(t-65536>>10))+String.fromCharCode(56320+(t-65536&1023))},isWhiteSpace:function(t){return 32===t||9===t||11===t||12===t||160===t||t>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(t)>=0},isLineTerminator:function(t){return 10===t||13===t||8232===t||8233===t},isIdentifierStart:function(t){return 36===t||95===t||t>=65&&t<=90||t>=97&&t<=122||92===t||t>=128&&i.NonAsciiIdentifierStart.test(e.Character.fromCodePoint(t))},isIdentifierPart:function(t){return 36===t||95===t||t>=65&&t<=90||t>=97&&t<=122||t>=48&&t<=57||92===t||t>=128&&i.NonAsciiIdentifierPart.test(e.Character.fromCodePoint(t))},isDecimalDigit:function(t){return t>=48&&t<=57},isHexDigit:function(t){return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102},isOctalDigit:function(t){return t>=48&&t<=55}}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(6),r=function(){return function(t){this.type=n.JSXSyntax.JSXClosingElement,this.name=t}}();e.JSXClosingElement=r;var s=function(){return function(t,e,i){this.type=n.JSXSyntax.JSXElement,this.openingElement=t,this.children=e,this.closingElement=i}}();e.JSXElement=s;var a=function(){return function(){this.type=n.JSXSyntax.JSXEmptyExpression}}();e.JSXEmptyExpression=a;var o=function(){return function(t){this.type=n.JSXSyntax.JSXExpressionContainer,this.expression=t}}();e.JSXExpressionContainer=o;var u=function(){return function(t){this.type=n.JSXSyntax.JSXIdentifier,this.name=t}}();e.JSXIdentifier=u;var c=function(){return function(t,e){this.type=n.JSXSyntax.JSXMemberExpression,this.object=t,this.property=e}}();e.JSXMemberExpression=c;var h=function(){return function(t,e){this.type=n.JSXSyntax.JSXAttribute,this.name=t,this.value=e}}();e.JSXAttribute=h;var l=function(){return function(t,e){this.type=n.JSXSyntax.JSXNamespacedName,this.namespace=t,this.name=e}}();e.JSXNamespacedName=l;var p=function(){return function(t,e,i){this.type=n.JSXSyntax.JSXOpeningElement,this.name=t,this.selfClosing=e,this.attributes=i}}();e.JSXOpeningElement=p;var d=function(){return function(t){this.type=n.JSXSyntax.JSXSpreadAttribute,this.argument=t}}();e.JSXSpreadAttribute=d;var f=function(){return function(t,e){this.type=n.JSXSyntax.JSXText,this.value=t,this.raw=e}}();e.JSXText=f},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.JSXSyntax={JSXAttribute:"JSXAttribute",JSXClosingElement:"JSXClosingElement",JSXElement:"JSXElement",JSXEmptyExpression:"JSXEmptyExpression",JSXExpressionContainer:"JSXExpressionContainer",JSXIdentifier:"JSXIdentifier",JSXMemberExpression:"JSXMemberExpression",JSXNamespacedName:"JSXNamespacedName",JSXOpeningElement:"JSXOpeningElement",JSXSpreadAttribute:"JSXSpreadAttribute",JSXText:"JSXText"}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(2),r=function(){return function(t){this.type=n.Syntax.ArrayExpression,this.elements=t}}();e.ArrayExpression=r;var s=function(){return function(t){this.type=n.Syntax.ArrayPattern,this.elements=t}}();e.ArrayPattern=s;var a=function(){return function(t,e,i){this.type=n.Syntax.ArrowFunctionExpression,this.id=null,this.params=t,this.body=e,this.generator=!1,this.expression=i,this.async=!1}}();e.ArrowFunctionExpression=a;var o=function(){return function(t,e,i){this.type=n.Syntax.AssignmentExpression,this.operator=t,this.left=e,this.right=i}}();e.AssignmentExpression=o;var u=function(){return function(t,e){this.type=n.Syntax.AssignmentPattern,this.left=t,this.right=e}}();e.AssignmentPattern=u;var c=function(){return function(t,e,i){this.type=n.Syntax.ArrowFunctionExpression,this.id=null,this.params=t,this.body=e,this.generator=!1,this.expression=i,this.async=!0}}();e.AsyncArrowFunctionExpression=c;var h=function(){return function(t,e,i){this.type=n.Syntax.FunctionDeclaration,this.id=t,this.params=e,this.body=i,this.generator=!1,this.expression=!1,this.async=!0}}();e.AsyncFunctionDeclaration=h;var l=function(){return function(t,e,i){this.type=n.Syntax.FunctionExpression,this.id=t,this.params=e,this.body=i,this.generator=!1,this.expression=!1,this.async=!0}}();e.AsyncFunctionExpression=l;var p=function(){return function(t){this.type=n.Syntax.AwaitExpression,this.argument=t}}();e.AwaitExpression=p;var d=function(){return function(t,e,i){var r="||"===t||"&&"===t;this.type=r?n.Syntax.LogicalExpression:n.Syntax.BinaryExpression,this.operator=t,this.left=e,this.right=i}}();e.BinaryExpression=d;var f=function(){return function(t){this.type=n.Syntax.BlockStatement,this.body=t}}();e.BlockStatement=f;var m=function(){return function(t){this.type=n.Syntax.BreakStatement,this.label=t}}();e.BreakStatement=m;var x=function(){return function(t,e){this.type=n.Syntax.CallExpression,this.callee=t,this.arguments=e}}();e.CallExpression=x;var D=function(){return function(t,e){this.type=n.Syntax.CatchClause,this.param=t,this.body=e}}();e.CatchClause=D;var y=function(){return function(t){this.type=n.Syntax.ClassBody,this.body=t}}();e.ClassBody=y;var g=function(){return function(t,e,i){this.type=n.Syntax.ClassDeclaration,this.id=t,this.superClass=e,this.body=i}}();e.ClassDeclaration=g;var E=function(){return function(t,e,i){this.type=n.Syntax.ClassExpression,this.id=t,this.superClass=e,this.body=i}}();e.ClassExpression=E;var A=function(){return function(t,e){this.type=n.Syntax.MemberExpression,this.computed=!0,this.object=t,this.property=e}}();e.ComputedMemberExpression=A;var C=function(){return function(t,e,i){this.type=n.Syntax.ConditionalExpression,this.test=t,this.consequent=e,this.alternate=i}}();e.ConditionalExpression=C;var v=function(){return function(t){this.type=n.Syntax.ContinueStatement,this.label=t}}();e.ContinueStatement=v;var F=function(){return function(){this.type=n.Syntax.DebuggerStatement}}();e.DebuggerStatement=F;var S=function(){return function(t,e){this.type=n.Syntax.ExpressionStatement,this.expression=t,this.directive=e}}();e.Directive=S;var b=function(){return function(t,e){this.type=n.Syntax.DoWhileStatement,this.body=t,this.test=e}}();e.DoWhileStatement=b;var k=function(){return function(){this.type=n.Syntax.EmptyStatement}}();e.EmptyStatement=k;var w=function(){return function(t){this.type=n.Syntax.ExportAllDeclaration,this.source=t}}();e.ExportAllDeclaration=w;var T=function(){return function(t){this.type=n.Syntax.ExportDefaultDeclaration,this.declaration=t}}();e.ExportDefaultDeclaration=T;var N=function(){return function(t,e,i){this.type=n.Syntax.ExportNamedDeclaration,this.declaration=t,this.specifiers=e,this.source=i}}();e.ExportNamedDeclaration=N;var B=function(){return function(t,e){this.type=n.Syntax.ExportSpecifier,this.exported=e,this.local=t}}();e.ExportSpecifier=B;var I=function(){return function(t){this.type=n.Syntax.ExpressionStatement,this.expression=t}}();e.ExpressionStatement=I;var M=function(){return function(t,e,i){this.type=n.Syntax.ForInStatement,this.left=t,this.right=e,this.body=i,this.each=!1}}();e.ForInStatement=M;var O=function(){return function(t,e,i){this.type=n.Syntax.ForOfStatement,this.left=t,this.right=e,this.body=i}}();e.ForOfStatement=O;var P=function(){return function(t,e,i,r){this.type=n.Syntax.ForStatement,this.init=t,this.test=e,this.update=i,this.body=r}}();e.ForStatement=P;var _=function(){return function(t,e,i,r){this.type=n.Syntax.FunctionDeclaration,this.id=t,this.params=e,this.body=i,this.generator=r,this.expression=!1,this.async=!1}}();e.FunctionDeclaration=_;var L=function(){return function(t,e,i,r){this.type=n.Syntax.FunctionExpression,this.id=t,this.params=e,this.body=i,this.generator=r,this.expression=!1,this.async=!1}}();e.FunctionExpression=L;var U=function(){return function(t){this.type=n.Syntax.Identifier,this.name=t}}();e.Identifier=U;var j=function(){return function(t,e,i){this.type=n.Syntax.IfStatement,this.test=t,this.consequent=e,this.alternate=i}}();e.IfStatement=j;var X=function(){return function(t,e){this.type=n.Syntax.ImportDeclaration,this.specifiers=t,this.source=e}}();e.ImportDeclaration=X;var K=function(){return function(t){this.type=n.Syntax.ImportDefaultSpecifier,this.local=t}}();e.ImportDefaultSpecifier=K;var R=function(){return function(t){this.type=n.Syntax.ImportNamespaceSpecifier,this.local=t}}();e.ImportNamespaceSpecifier=R;var J=function(){return function(t,e){this.type=n.Syntax.ImportSpecifier,this.local=t,this.imported=e}}();e.ImportSpecifier=J;var z=function(){return function(t,e){this.type=n.Syntax.LabeledStatement,this.label=t,this.body=e}}();e.LabeledStatement=z;var G=function(){return function(t,e){this.type=n.Syntax.Literal,this.value=t,this.raw=e}}();e.Literal=G;var V=function(){return function(t,e){this.type=n.Syntax.MetaProperty,this.meta=t,this.property=e}}();e.MetaProperty=V;var Y=function(){return function(t,e,i,r,s){this.type=n.Syntax.MethodDefinition,this.key=t,this.computed=e,this.value=i,this.kind=r,this.static=s}}();e.MethodDefinition=Y;var H=function(){return function(t){this.type=n.Syntax.Program,this.body=t,this.sourceType="module"}}();e.Module=H;var W=function(){return function(t,e){this.type=n.Syntax.NewExpression,this.callee=t,this.arguments=e}}();e.NewExpression=W;var q=function(){return function(t){this.type=n.Syntax.ObjectExpression,this.properties=t}}();e.ObjectExpression=q;var $=function(){return function(t){this.type=n.Syntax.ObjectPattern,this.properties=t}}();e.ObjectPattern=$;var Q=function(){return function(t,e,i,r,s,a){this.type=n.Syntax.Property,this.key=e,this.computed=i,this.value=r,this.kind=t,this.method=s,this.shorthand=a}}();e.Property=Q;var Z=function(){return function(t,e,i,r){this.type=n.Syntax.Literal,this.value=t,this.raw=e,this.regex={pattern:i,flags:r}}}();e.RegexLiteral=Z;var tt=function(){return function(t){this.type=n.Syntax.RestElement,this.argument=t}}();e.RestElement=tt;var te=function(){return function(t){this.type=n.Syntax.ReturnStatement,this.argument=t}}();e.ReturnStatement=te;var ti=function(){return function(t){this.type=n.Syntax.Program,this.body=t,this.sourceType="script"}}();e.Script=ti;var tn=function(){return function(t){this.type=n.Syntax.SequenceExpression,this.expressions=t}}();e.SequenceExpression=tn;var tr=function(){return function(t){this.type=n.Syntax.SpreadElement,this.argument=t}}();e.SpreadElement=tr;var ts=function(){return function(t,e){this.type=n.Syntax.MemberExpression,this.computed=!1,this.object=t,this.property=e}}();e.StaticMemberExpression=ts;var ta=function(){return function(){this.type=n.Syntax.Super}}();e.Super=ta;var to=function(){return function(t,e){this.type=n.Syntax.SwitchCase,this.test=t,this.consequent=e}}();e.SwitchCase=to;var tu=function(){return function(t,e){this.type=n.Syntax.SwitchStatement,this.discriminant=t,this.cases=e}}();e.SwitchStatement=tu;var tc=function(){return function(t,e){this.type=n.Syntax.TaggedTemplateExpression,this.tag=t,this.quasi=e}}();e.TaggedTemplateExpression=tc;var th=function(){return function(t,e){this.type=n.Syntax.TemplateElement,this.value=t,this.tail=e}}();e.TemplateElement=th;var tl=function(){return function(t,e){this.type=n.Syntax.TemplateLiteral,this.quasis=t,this.expressions=e}}();e.TemplateLiteral=tl;var tp=function(){return function(){this.type=n.Syntax.ThisExpression}}();e.ThisExpression=tp;var td=function(){return function(t){this.type=n.Syntax.ThrowStatement,this.argument=t}}();e.ThrowStatement=td;var tf=function(){return function(t,e,i){this.type=n.Syntax.TryStatement,this.block=t,this.handler=e,this.finalizer=i}}();e.TryStatement=tf;var tm=function(){return function(t,e){this.type=n.Syntax.UnaryExpression,this.operator=t,this.argument=e,this.prefix=!0}}();e.UnaryExpression=tm;var tx=function(){return function(t,e,i){this.type=n.Syntax.UpdateExpression,this.operator=t,this.argument=e,this.prefix=i}}();e.UpdateExpression=tx;var tD=function(){return function(t,e){this.type=n.Syntax.VariableDeclaration,this.declarations=t,this.kind=e}}();e.VariableDeclaration=tD;var ty=function(){return function(t,e){this.type=n.Syntax.VariableDeclarator,this.id=t,this.init=e}}();e.VariableDeclarator=ty;var tg=function(){return function(t,e){this.type=n.Syntax.WhileStatement,this.test=t,this.body=e}}();e.WhileStatement=tg;var tE=function(){return function(t,e){this.type=n.Syntax.WithStatement,this.object=t,this.body=e}}();e.WithStatement=tE;var tA=function(){return function(t,e){this.type=n.Syntax.YieldExpression,this.argument=t,this.delegate=e}}();e.YieldExpression=tA},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(9),r=i(10),s=i(11),a=i(7),o=i(12),u=i(2),c=i(13),h="ArrowParameterPlaceHolder",l=function(){function t(t,e,i){void 0===e&&(e={}),this.config={range:"boolean"==typeof e.range&&e.range,loc:"boolean"==typeof e.loc&&e.loc,source:null,tokens:"boolean"==typeof e.tokens&&e.tokens,comment:"boolean"==typeof e.comment&&e.comment,tolerant:"boolean"==typeof e.tolerant&&e.tolerant},this.config.loc&&e.source&&null!==e.source&&(this.config.source=String(e.source)),this.delegate=i,this.errorHandler=new r.ErrorHandler,this.errorHandler.tolerant=this.config.tolerant,this.scanner=new o.Scanner(t,this.errorHandler),this.scanner.trackComment=this.config.comment,this.operatorPrecedence={")":0,";":0,",":0,"=":0,"]":0,"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":11,"/":11,"%":11},this.lookahead={type:2,value:"",lineNumber:this.scanner.lineNumber,lineStart:0,start:0,end:0},this.hasLineTerminator=!1,this.context={isModule:!1,await:!1,allowIn:!0,allowStrictDirective:!0,allowYield:!0,firstCoverInitializedNameError:null,isAssignmentTarget:!1,isBindingElement:!1,inFunctionBody:!1,inIteration:!1,inSwitch:!1,labelSet:{},strict:!1},this.tokens=[],this.startMarker={index:0,line:this.scanner.lineNumber,column:0},this.lastMarker={index:0,line:this.scanner.lineNumber,column:0},this.nextToken(),this.lastMarker={index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}return t.prototype.throwError=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var r=Array.prototype.slice.call(arguments,1),s=t.replace(/%(\d)/g,function(t,e){return n.assert(e<r.length,"Message reference must be in range"),r[e]}),a=this.lastMarker.index,o=this.lastMarker.line,u=this.lastMarker.column+1;throw this.errorHandler.createError(a,o,u,s)},t.prototype.tolerateError=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var r=Array.prototype.slice.call(arguments,1),s=t.replace(/%(\d)/g,function(t,e){return n.assert(e<r.length,"Message reference must be in range"),r[e]}),a=this.lastMarker.index,o=this.scanner.lineNumber,u=this.lastMarker.column+1;this.errorHandler.tolerateError(a,o,u,s)},t.prototype.unexpectedTokenError=function(t,e){var i,n=e||s.Messages.UnexpectedToken;if(t?(!e&&(n=2===t.type?s.Messages.UnexpectedEOS:3===t.type?s.Messages.UnexpectedIdentifier:6===t.type?s.Messages.UnexpectedNumber:8===t.type?s.Messages.UnexpectedString:10===t.type?s.Messages.UnexpectedTemplate:s.Messages.UnexpectedToken,4===t.type&&(this.scanner.isFutureReservedWord(t.value)?n=s.Messages.UnexpectedReserved:this.context.strict&&this.scanner.isStrictModeReservedWord(t.value)&&(n=s.Messages.StrictReservedWord))),i=t.value):i="ILLEGAL",n=n.replace("%0",i),t&&"number"==typeof t.lineNumber){var r=t.start,a=t.lineNumber,o=this.lastMarker.index-this.lastMarker.column,u=t.start-o+1;return this.errorHandler.createError(r,a,u,n)}var r=this.lastMarker.index,a=this.lastMarker.line,u=this.lastMarker.column+1;return this.errorHandler.createError(r,a,u,n)},t.prototype.throwUnexpectedToken=function(t,e){throw this.unexpectedTokenError(t,e)},t.prototype.tolerateUnexpectedToken=function(t,e){this.errorHandler.tolerate(this.unexpectedTokenError(t,e))},t.prototype.collectComments=function(){if(this.config.comment){var t=this.scanner.scanComments();if(t.length>0&&this.delegate)for(var e=0;e<t.length;++e){var i=t[e],n=void 0;n={type:i.multiLine?"BlockComment":"LineComment",value:this.scanner.source.slice(i.slice[0],i.slice[1])},this.config.range&&(n.range=i.range),this.config.loc&&(n.loc=i.loc);var r={start:{line:i.loc.start.line,column:i.loc.start.column,offset:i.range[0]},end:{line:i.loc.end.line,column:i.loc.end.column,offset:i.range[1]}};this.delegate(n,r)}}else this.scanner.scanComments()},t.prototype.getTokenRaw=function(t){return this.scanner.source.slice(t.start,t.end)},t.prototype.convertToken=function(t){var e={type:c.TokenName[t.type],value:this.getTokenRaw(t)};if(this.config.range&&(e.range=[t.start,t.end]),this.config.loc&&(e.loc={start:{line:this.startMarker.line,column:this.startMarker.column},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}),9===t.type){var i=t.pattern,n=t.flags;e.regex={pattern:i,flags:n}}return e},t.prototype.nextToken=function(){var t=this.lookahead;this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.collectComments(),this.scanner.index!==this.startMarker.index&&(this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart);var e=this.scanner.lex();return this.hasLineTerminator=t.lineNumber!==e.lineNumber,e&&this.context.strict&&3===e.type&&this.scanner.isStrictModeReservedWord(e.value)&&(e.type=4),this.lookahead=e,this.config.tokens&&2!==e.type&&this.tokens.push(this.convertToken(e)),t},t.prototype.nextRegexToken=function(){this.collectComments();var t=this.scanner.scanRegExp();return this.config.tokens&&(this.tokens.pop(),this.tokens.push(this.convertToken(t))),this.lookahead=t,this.nextToken(),t},t.prototype.createNode=function(){return{index:this.startMarker.index,line:this.startMarker.line,column:this.startMarker.column}},t.prototype.startNode=function(t,e){void 0===e&&(e=0);var i=t.start-t.lineStart,n=t.lineNumber;return i<0&&(i+=e,n--),{index:t.start,line:n,column:i}},t.prototype.finalize=function(t,e){if(this.config.range&&(e.range=[t.index,this.lastMarker.index]),this.config.loc&&(e.loc={start:{line:t.line,column:t.column},end:{line:this.lastMarker.line,column:this.lastMarker.column}},this.config.source&&(e.loc.source=this.config.source)),this.delegate){var i={start:{line:t.line,column:t.column,offset:t.index},end:{line:this.lastMarker.line,column:this.lastMarker.column,offset:this.lastMarker.index}};this.delegate(e,i)}return e},t.prototype.expect=function(t){var e=this.nextToken();(7!==e.type||e.value!==t)&&this.throwUnexpectedToken(e)},t.prototype.expectCommaSeparator=function(){if(this.config.tolerant){var t=this.lookahead;7===t.type&&","===t.value?this.nextToken():7===t.type&&";"===t.value?(this.nextToken(),this.tolerateUnexpectedToken(t)):this.tolerateUnexpectedToken(t,s.Messages.UnexpectedToken)}else this.expect(",")},t.prototype.expectKeyword=function(t){var e=this.nextToken();(4!==e.type||e.value!==t)&&this.throwUnexpectedToken(e)},t.prototype.match=function(t){return 7===this.lookahead.type&&this.lookahead.value===t},t.prototype.matchKeyword=function(t){return 4===this.lookahead.type&&this.lookahead.value===t},t.prototype.matchContextualKeyword=function(t){return 3===this.lookahead.type&&this.lookahead.value===t},t.prototype.matchAssign=function(){if(7!==this.lookahead.type)return!1;var t=this.lookahead.value;return"="===t||"*="===t||"**="===t||"/="===t||"%="===t||"+="===t||"-="===t||"<<="===t||">>="===t||">>>="===t||"&="===t||"^="===t||"|="===t},t.prototype.isolateCoverGrammar=function(t){var e=this.context.isBindingElement,i=this.context.isAssignmentTarget,n=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var r=t.call(this);return null!==this.context.firstCoverInitializedNameError&&this.throwUnexpectedToken(this.context.firstCoverInitializedNameError),this.context.isBindingElement=e,this.context.isAssignmentTarget=i,this.context.firstCoverInitializedNameError=n,r},t.prototype.inheritCoverGrammar=function(t){var e=this.context.isBindingElement,i=this.context.isAssignmentTarget,n=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var r=t.call(this);return this.context.isBindingElement=this.context.isBindingElement&&e,this.context.isAssignmentTarget=this.context.isAssignmentTarget&&i,this.context.firstCoverInitializedNameError=n||this.context.firstCoverInitializedNameError,r},t.prototype.consumeSemicolon=function(){this.match(";")?this.nextToken():this.hasLineTerminator||(2===this.lookahead.type||this.match("}")||this.throwUnexpectedToken(this.lookahead),this.lastMarker.index=this.startMarker.index,this.lastMarker.line=this.startMarker.line,this.lastMarker.column=this.startMarker.column)},t.prototype.parsePrimaryExpression=function(){var t,e,i,n=this.createNode();switch(this.lookahead.type){case 3:(this.context.isModule||this.context.await)&&"await"===this.lookahead.value&&this.tolerateUnexpectedToken(this.lookahead),t=this.matchAsyncFunction()?this.parseFunctionExpression():this.finalize(n,new a.Identifier(this.nextToken().value));break;case 6:case 8:this.context.strict&&this.lookahead.octal&&this.tolerateUnexpectedToken(this.lookahead,s.Messages.StrictOctalLiteral),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,e=this.nextToken(),i=this.getTokenRaw(e),t=this.finalize(n,new a.Literal(e.value,i));break;case 1:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,e=this.nextToken(),i=this.getTokenRaw(e),t=this.finalize(n,new a.Literal("true"===e.value,i));break;case 5:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,e=this.nextToken(),i=this.getTokenRaw(e),t=this.finalize(n,new a.Literal(null,i));break;case 10:t=this.parseTemplateLiteral();break;case 7:switch(this.lookahead.value){case"(":this.context.isBindingElement=!1,t=this.inheritCoverGrammar(this.parseGroupExpression);break;case"[":t=this.inheritCoverGrammar(this.parseArrayInitializer);break;case"{":t=this.inheritCoverGrammar(this.parseObjectInitializer);break;case"/":case"/=":this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.scanner.index=this.startMarker.index,e=this.nextRegexToken(),i=this.getTokenRaw(e),t=this.finalize(n,new a.RegexLiteral(e.regex,i,e.pattern,e.flags));break;default:t=this.throwUnexpectedToken(this.nextToken())}break;case 4:!this.context.strict&&this.context.allowYield&&this.matchKeyword("yield")?t=this.parseIdentifierName():!this.context.strict&&this.matchKeyword("let")?t=this.finalize(n,new a.Identifier(this.nextToken().value)):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.matchKeyword("function")?t=this.parseFunctionExpression():this.matchKeyword("this")?(this.nextToken(),t=this.finalize(n,new a.ThisExpression)):t=this.matchKeyword("class")?this.parseClassExpression():this.throwUnexpectedToken(this.nextToken()));break;default:t=this.throwUnexpectedToken(this.nextToken())}return t},t.prototype.parseSpreadElement=function(){var t=this.createNode();this.expect("...");var e=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.finalize(t,new a.SpreadElement(e))},t.prototype.parseArrayInitializer=function(){var t=this.createNode(),e=[];for(this.expect("[");!this.match("]");)if(this.match(","))this.nextToken(),e.push(null);else if(this.match("...")){var i=this.parseSpreadElement();this.match("]")||(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.expect(",")),e.push(i)}else e.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.match("]")||this.expect(",");return this.expect("]"),this.finalize(t,new a.ArrayExpression(e))},t.prototype.parsePropertyMethod=function(t){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var e=this.context.strict,i=this.context.allowStrictDirective;this.context.allowStrictDirective=t.simple;var n=this.isolateCoverGrammar(this.parseFunctionSourceElements);return this.context.strict&&t.firstRestricted&&this.tolerateUnexpectedToken(t.firstRestricted,t.message),this.context.strict&&t.stricted&&this.tolerateUnexpectedToken(t.stricted,t.message),this.context.strict=e,this.context.allowStrictDirective=i,n},t.prototype.parsePropertyMethodFunction=function(){var t=!1,e=this.createNode(),i=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters(),r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(e,new a.FunctionExpression(null,n.params,r,t))},t.prototype.parsePropertyMethodAsyncFunction=function(){var t=this.createNode(),e=this.context.allowYield,i=this.context.await;this.context.allowYield=!1,this.context.await=!0;var n=this.parseFormalParameters(),r=this.parsePropertyMethod(n);return this.context.allowYield=e,this.context.await=i,this.finalize(t,new a.AsyncFunctionExpression(null,n.params,r))},t.prototype.parseObjectPropertyKey=function(){var t,e=this.createNode(),i=this.nextToken();switch(i.type){case 8:case 6:this.context.strict&&i.octal&&this.tolerateUnexpectedToken(i,s.Messages.StrictOctalLiteral);var n=this.getTokenRaw(i);t=this.finalize(e,new a.Literal(i.value,n));break;case 3:case 1:case 5:case 4:t=this.finalize(e,new a.Identifier(i.value));break;case 7:"["===i.value?(t=this.isolateCoverGrammar(this.parseAssignmentExpression),this.expect("]")):t=this.throwUnexpectedToken(i);break;default:t=this.throwUnexpectedToken(i)}return t},t.prototype.isPropertyKey=function(t,e){return t.type===u.Syntax.Identifier&&t.name===e||t.type===u.Syntax.Literal&&t.value===e},t.prototype.parseObjectProperty=function(t){var e,i=this.createNode(),n=this.lookahead,r=null,o=null,u=!1,c=!1,h=!1,l=!1;if(3===n.type){var p=n.value;this.nextToken(),u=this.match("["),r=(l=!this.hasLineTerminator&&"async"===p&&!this.match(":")&&!this.match("(")&&!this.match("*")&&!this.match(","))?this.parseObjectPropertyKey():this.finalize(i,new a.Identifier(p))}else this.match("*")?this.nextToken():(u=this.match("["),r=this.parseObjectPropertyKey());var d=this.qualifiedPropertyName(this.lookahead);if(3===n.type&&!l&&"get"===n.value&&d)e="get",u=this.match("["),r=this.parseObjectPropertyKey(),this.context.allowYield=!1,o=this.parseGetterMethod();else if(3===n.type&&!l&&"set"===n.value&&d)e="set",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseSetterMethod();else if(7===n.type&&"*"===n.value&&d)e="init",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseGeneratorMethod(),c=!0;else if(r||this.throwUnexpectedToken(this.lookahead),e="init",this.match(":")&&!l)!u&&this.isPropertyKey(r,"__proto__")&&(t.value&&this.tolerateError(s.Messages.DuplicateProtoProperty),t.value=!0),this.nextToken(),o=this.inheritCoverGrammar(this.parseAssignmentExpression);else if(this.match("("))o=l?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0;else if(3===n.type){var p=this.finalize(i,new a.Identifier(n.value));if(this.match("=")){this.context.firstCoverInitializedNameError=this.lookahead,this.nextToken(),h=!0;var f=this.isolateCoverGrammar(this.parseAssignmentExpression);o=this.finalize(i,new a.AssignmentPattern(p,f))}else h=!0,o=p}else this.throwUnexpectedToken(this.nextToken());return this.finalize(i,new a.Property(e,r,u,o,c,h))},t.prototype.parseObjectInitializer=function(){var t=this.createNode();this.expect("{");for(var e=[],i={value:!1};!this.match("}");)e.push(this.parseObjectProperty(i)),this.match("}")||this.expectCommaSeparator();return this.expect("}"),this.finalize(t,new a.ObjectExpression(e))},t.prototype.parseTemplateHead=function(){n.assert(this.lookahead.head,"Template literal must start with a template head");var t=this.createNode(),e=this.nextToken(),i=e.value,r=e.cooked;return this.finalize(t,new a.TemplateElement({raw:i,cooked:r},e.tail))},t.prototype.parseTemplateElement=function(){10!==this.lookahead.type&&this.throwUnexpectedToken();var t=this.createNode(),e=this.nextToken(),i=e.value,n=e.cooked;return this.finalize(t,new a.TemplateElement({raw:i,cooked:n},e.tail))},t.prototype.parseTemplateLiteral=function(){var t=this.createNode(),e=[],i=[],n=this.parseTemplateHead();for(i.push(n);!n.tail;)e.push(this.parseExpression()),n=this.parseTemplateElement(),i.push(n);return this.finalize(t,new a.TemplateLiteral(i,e))},t.prototype.reinterpretExpressionAsPattern=function(t){switch(t.type){case u.Syntax.Identifier:case u.Syntax.MemberExpression:case u.Syntax.RestElement:case u.Syntax.AssignmentPattern:break;case u.Syntax.SpreadElement:t.type=u.Syntax.RestElement,this.reinterpretExpressionAsPattern(t.argument);break;case u.Syntax.ArrayExpression:t.type=u.Syntax.ArrayPattern;for(var e=0;e<t.elements.length;e++)null!==t.elements[e]&&this.reinterpretExpressionAsPattern(t.elements[e]);break;case u.Syntax.ObjectExpression:t.type=u.Syntax.ObjectPattern;for(var e=0;e<t.properties.length;e++)this.reinterpretExpressionAsPattern(t.properties[e].value);break;case u.Syntax.AssignmentExpression:t.type=u.Syntax.AssignmentPattern,delete t.operator,this.reinterpretExpressionAsPattern(t.left)}},t.prototype.parseGroupExpression=function(){var t;if(this.expect("("),this.match(")"))this.nextToken(),this.match("=>")||this.expect("=>"),t={type:h,params:[],async:!1};else{var e=this.lookahead,i=[];if(this.match("..."))t=this.parseRestElement(i),this.expect(")"),this.match("=>")||this.expect("=>"),t={type:h,params:[t],async:!1};else{var n=!1;if(this.context.isBindingElement=!0,t=this.inheritCoverGrammar(this.parseAssignmentExpression),this.match(",")){var r=[];for(this.context.isAssignmentTarget=!1,r.push(t);2!==this.lookahead.type&&this.match(",");){if(this.nextToken(),this.match(")")){this.nextToken();for(var s=0;s<r.length;s++)this.reinterpretExpressionAsPattern(r[s]);n=!0,t={type:h,params:r,async:!1}}else if(this.match("...")){this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),r.push(this.parseRestElement(i)),this.expect(")"),this.match("=>")||this.expect("=>"),this.context.isBindingElement=!1;for(var s=0;s<r.length;s++)this.reinterpretExpressionAsPattern(r[s]);n=!0,t={type:h,params:r,async:!1}}else r.push(this.inheritCoverGrammar(this.parseAssignmentExpression));if(n)break}n||(t=this.finalize(this.startNode(e),new a.SequenceExpression(r)))}if(!n){if(this.expect(")"),this.match("=>")&&(t.type===u.Syntax.Identifier&&"yield"===t.name&&(n=!0,t={type:h,params:[t],async:!1}),!n)){if(this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),t.type===u.Syntax.SequenceExpression)for(var s=0;s<t.expressions.length;s++)this.reinterpretExpressionAsPattern(t.expressions[s]);else this.reinterpretExpressionAsPattern(t);t={type:h,params:t.type===u.Syntax.SequenceExpression?t.expressions:[t],async:!1}}this.context.isBindingElement=!1}}}return t},t.prototype.parseArguments=function(){this.expect("(");var t=[];if(!this.match(")"))for(;;){var e=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAssignmentExpression);if(t.push(e),this.match(")")||(this.expectCommaSeparator(),this.match(")")))break}return this.expect(")"),t},t.prototype.isIdentifierName=function(t){return 3===t.type||4===t.type||1===t.type||5===t.type},t.prototype.parseIdentifierName=function(){var t=this.createNode(),e=this.nextToken();return this.isIdentifierName(e)||this.throwUnexpectedToken(e),this.finalize(t,new a.Identifier(e.value))},t.prototype.parseNewExpression=function(){var t,e=this.createNode(),i=this.parseIdentifierName();if(n.assert("new"===i.name,"New expression must start with `new`"),this.match(".")){if(this.nextToken(),3===this.lookahead.type&&this.context.inFunctionBody&&"target"===this.lookahead.value){var r=this.parseIdentifierName();t=new a.MetaProperty(i,r)}else this.throwUnexpectedToken(this.lookahead)}else{var s=this.isolateCoverGrammar(this.parseLeftHandSideExpression),o=this.match("(")?this.parseArguments():[];t=new a.NewExpression(s,o),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return this.finalize(e,t)},t.prototype.parseAsyncArgument=function(){var t=this.parseAssignmentExpression();return this.context.firstCoverInitializedNameError=null,t},t.prototype.parseAsyncArguments=function(){this.expect("(");var t=[];if(!this.match(")"))for(;;){var e=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAsyncArgument);if(t.push(e),this.match(")")||(this.expectCommaSeparator(),this.match(")")))break}return this.expect(")"),t},t.prototype.parseLeftHandSideExpressionAllowCall=function(){var t,e=this.lookahead,i=this.matchContextualKeyword("async"),n=this.context.allowIn;for(this.context.allowIn=!0,this.matchKeyword("super")&&this.context.inFunctionBody?(t=this.createNode(),this.nextToken(),t=this.finalize(t,new a.Super),this.match("(")||this.match(".")||this.match("[")||this.throwUnexpectedToken(this.lookahead)):t=this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var r=this.parseIdentifierName();t=this.finalize(this.startNode(e),new a.StaticMemberExpression(t,r))}else if(this.match("(")){var s=i&&e.lineNumber===this.lookahead.lineNumber;this.context.isBindingElement=!1,this.context.isAssignmentTarget=!1;var o=s?this.parseAsyncArguments():this.parseArguments();if(t=this.finalize(this.startNode(e),new a.CallExpression(t,o)),s&&this.match("=>")){for(var u=0;u<o.length;++u)this.reinterpretExpressionAsPattern(o[u]);t={type:h,params:o,async:!0}}}else if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var r=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),t=this.finalize(this.startNode(e),new a.ComputedMemberExpression(t,r))}else if(10===this.lookahead.type&&this.lookahead.head){var c=this.parseTemplateLiteral();t=this.finalize(this.startNode(e),new a.TaggedTemplateExpression(t,c))}else break;return this.context.allowIn=n,t},t.prototype.parseSuper=function(){var t=this.createNode();return this.expectKeyword("super"),this.match("[")||this.match(".")||this.throwUnexpectedToken(this.lookahead),this.finalize(t,new a.Super)},t.prototype.parseLeftHandSideExpression=function(){n.assert(this.context.allowIn,"callee of new expression always allow in keyword.");for(var t=this.startNode(this.lookahead),e=this.matchKeyword("super")&&this.context.inFunctionBody?this.parseSuper():this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var i=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),e=this.finalize(t,new a.ComputedMemberExpression(e,i))}else if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var i=this.parseIdentifierName();e=this.finalize(t,new a.StaticMemberExpression(e,i))}else if(10===this.lookahead.type&&this.lookahead.head){var r=this.parseTemplateLiteral();e=this.finalize(t,new a.TaggedTemplateExpression(e,r))}else break;return e},t.prototype.parseUpdateExpression=function(){var t,e=this.lookahead;if(this.match("++")||this.match("--")){var i=this.startNode(e),n=this.nextToken();t=this.inheritCoverGrammar(this.parseUnaryExpression),this.context.strict&&t.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(t.name)&&this.tolerateError(s.Messages.StrictLHSPrefix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment);var r=!0;t=this.finalize(i,new a.UpdateExpression(n.value,t,r)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else if(t=this.inheritCoverGrammar(this.parseLeftHandSideExpressionAllowCall),!this.hasLineTerminator&&7===this.lookahead.type&&(this.match("++")||this.match("--"))){this.context.strict&&t.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(t.name)&&this.tolerateError(s.Messages.StrictLHSPostfix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var o=this.nextToken().value,r=!1;t=this.finalize(this.startNode(e),new a.UpdateExpression(o,t,r))}return t},t.prototype.parseAwaitExpression=function(){var t=this.createNode();this.nextToken();var e=this.parseUnaryExpression();return this.finalize(t,new a.AwaitExpression(e))},t.prototype.parseUnaryExpression=function(){var t;if(this.match("+")||this.match("-")||this.match("~")||this.match("!")||this.matchKeyword("delete")||this.matchKeyword("void")||this.matchKeyword("typeof")){var e=this.startNode(this.lookahead),i=this.nextToken();t=this.inheritCoverGrammar(this.parseUnaryExpression),t=this.finalize(e,new a.UnaryExpression(i.value,t)),this.context.strict&&"delete"===t.operator&&t.argument.type===u.Syntax.Identifier&&this.tolerateError(s.Messages.StrictDelete),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else t=this.context.await&&this.matchContextualKeyword("await")?this.parseAwaitExpression():this.parseUpdateExpression();return t},t.prototype.parseExponentiationExpression=function(){var t=this.lookahead,e=this.inheritCoverGrammar(this.parseUnaryExpression);if(e.type!==u.Syntax.UnaryExpression&&this.match("**")){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var i=e,n=this.isolateCoverGrammar(this.parseExponentiationExpression);e=this.finalize(this.startNode(t),new a.BinaryExpression("**",i,n))}return e},t.prototype.binaryPrecedence=function(t){var e=t.value;return 7===t.type?this.operatorPrecedence[e]||0:4===t.type&&("instanceof"===e||this.context.allowIn&&"in"===e)?7:0},t.prototype.parseBinaryExpression=function(){var t=this.lookahead,e=this.inheritCoverGrammar(this.parseExponentiationExpression),i=this.lookahead,n=this.binaryPrecedence(i);if(n>0){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;for(var r=[t,this.lookahead],s=e,o=this.isolateCoverGrammar(this.parseExponentiationExpression),u=[s,i.value,o],c=[n];!((n=this.binaryPrecedence(this.lookahead))<=0);){for(;u.length>2&&n<=c[c.length-1];){o=u.pop();var h=u.pop();c.pop(),s=u.pop(),r.pop();var l=this.startNode(r[r.length-1]);u.push(this.finalize(l,new a.BinaryExpression(h,s,o)))}u.push(this.nextToken().value),c.push(n),r.push(this.lookahead),u.push(this.isolateCoverGrammar(this.parseExponentiationExpression))}var p=u.length-1;e=u[p];for(var d=r.pop();p>1;){var f=r.pop(),m=d&&d.lineStart,l=this.startNode(f,m),h=u[p-1];e=this.finalize(l,new a.BinaryExpression(h,u[p-2],e)),p-=2,d=f}}return e},t.prototype.parseConditionalExpression=function(){var t=this.lookahead,e=this.inheritCoverGrammar(this.parseBinaryExpression);if(this.match("?")){this.nextToken();var i=this.context.allowIn;this.context.allowIn=!0;var n=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowIn=i,this.expect(":");var r=this.isolateCoverGrammar(this.parseAssignmentExpression);e=this.finalize(this.startNode(t),new a.ConditionalExpression(e,n,r)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return e},t.prototype.checkPatternParam=function(t,e){switch(e.type){case u.Syntax.Identifier:this.validateParam(t,e,e.name);break;case u.Syntax.RestElement:this.checkPatternParam(t,e.argument);break;case u.Syntax.AssignmentPattern:this.checkPatternParam(t,e.left);break;case u.Syntax.ArrayPattern:for(var i=0;i<e.elements.length;i++)null!==e.elements[i]&&this.checkPatternParam(t,e.elements[i]);break;case u.Syntax.ObjectPattern:for(var i=0;i<e.properties.length;i++)this.checkPatternParam(t,e.properties[i].value)}t.simple=t.simple&&e instanceof a.Identifier},t.prototype.reinterpretAsCoverFormalsList=function(t){var e,i=[t],n=!1;switch(t.type){case u.Syntax.Identifier:break;case h:i=t.params,n=t.async;break;default:return null}e={simple:!0,paramSet:{}};for(var r=0;r<i.length;++r){var a=i[r];a.type===u.Syntax.AssignmentPattern?a.right.type===u.Syntax.YieldExpression&&(a.right.argument&&this.throwUnexpectedToken(this.lookahead),a.right.type=u.Syntax.Identifier,a.right.name="yield",delete a.right.argument,delete a.right.delegate):n&&a.type===u.Syntax.Identifier&&"await"===a.name&&this.throwUnexpectedToken(this.lookahead),this.checkPatternParam(e,a),i[r]=a}if(this.context.strict||!this.context.allowYield)for(var r=0;r<i.length;++r){var a=i[r];a.type===u.Syntax.YieldExpression&&this.throwUnexpectedToken(this.lookahead)}if(e.message===s.Messages.StrictParamDupe){var o=this.context.strict?e.stricted:e.firstRestricted;this.throwUnexpectedToken(o,e.message)}return{simple:e.simple,params:i,stricted:e.stricted,firstRestricted:e.firstRestricted,message:e.message}},t.prototype.parseAssignmentExpression=function(){var t;if(!this.context.allowYield&&this.matchKeyword("yield"))t=this.parseYieldExpression();else{var e=this.lookahead,i=e;if(t=this.parseConditionalExpression(),3===i.type&&i.lineNumber===this.lookahead.lineNumber&&"async"===i.value&&(3===this.lookahead.type||this.matchKeyword("yield"))){var n=this.parsePrimaryExpression();this.reinterpretExpressionAsPattern(n),t={type:h,params:[n],async:!0}}if(t.type===h||this.match("=>")){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var r=t.async,o=this.reinterpretAsCoverFormalsList(t);if(o){this.hasLineTerminator&&this.tolerateUnexpectedToken(this.lookahead),this.context.firstCoverInitializedNameError=null;var c=this.context.strict,l=this.context.allowStrictDirective;this.context.allowStrictDirective=o.simple;var p=this.context.allowYield,d=this.context.await;this.context.allowYield=!0,this.context.await=r;var f=this.startNode(e);this.expect("=>");var m=void 0;if(this.match("{")){var x=this.context.allowIn;this.context.allowIn=!0,m=this.parseFunctionSourceElements(),this.context.allowIn=x}else m=this.isolateCoverGrammar(this.parseAssignmentExpression);var D=m.type!==u.Syntax.BlockStatement;this.context.strict&&o.firstRestricted&&this.throwUnexpectedToken(o.firstRestricted,o.message),this.context.strict&&o.stricted&&this.tolerateUnexpectedToken(o.stricted,o.message),t=r?this.finalize(f,new a.AsyncArrowFunctionExpression(o.params,m,D)):this.finalize(f,new a.ArrowFunctionExpression(o.params,m,D)),this.context.strict=c,this.context.allowStrictDirective=l,this.context.allowYield=p,this.context.await=d}}else if(this.matchAssign()){if(this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.strict&&t.type===u.Syntax.Identifier){var y=t;this.scanner.isRestrictedWord(y.name)&&this.tolerateUnexpectedToken(i,s.Messages.StrictLHSAssignment),this.scanner.isStrictModeReservedWord(y.name)&&this.tolerateUnexpectedToken(i,s.Messages.StrictReservedWord)}this.match("=")?this.reinterpretExpressionAsPattern(t):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1);var g=(i=this.nextToken()).value,E=this.isolateCoverGrammar(this.parseAssignmentExpression);t=this.finalize(this.startNode(e),new a.AssignmentExpression(g,t,E)),this.context.firstCoverInitializedNameError=null}}return t},t.prototype.parseExpression=function(){var t=this.lookahead,e=this.isolateCoverGrammar(this.parseAssignmentExpression);if(this.match(",")){var i=[];for(i.push(e);2!==this.lookahead.type&&this.match(",");)this.nextToken(),i.push(this.isolateCoverGrammar(this.parseAssignmentExpression));e=this.finalize(this.startNode(t),new a.SequenceExpression(i))}return e},t.prototype.parseStatementListItem=function(){var t;if(this.context.isAssignmentTarget=!0,this.context.isBindingElement=!0,4===this.lookahead.type)switch(this.lookahead.value){case"export":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalExportDeclaration),t=this.parseExportDeclaration();break;case"import":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalImportDeclaration),t=this.parseImportDeclaration();break;case"const":t=this.parseLexicalDeclaration({inFor:!1});break;case"function":t=this.parseFunctionDeclaration();break;case"class":t=this.parseClassDeclaration();break;case"let":t=this.isLexicalDeclaration()?this.parseLexicalDeclaration({inFor:!1}):this.parseStatement();break;default:t=this.parseStatement()}else t=this.parseStatement();return t},t.prototype.parseBlock=function(){var t=this.createNode();this.expect("{");for(var e=[];!this.match("}");)e.push(this.parseStatementListItem());return this.expect("}"),this.finalize(t,new a.BlockStatement(e))},t.prototype.parseLexicalBinding=function(t,e){var i=this.createNode(),n=[],r=this.parsePattern(n,t);this.context.strict&&r.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(r.name)&&this.tolerateError(s.Messages.StrictVarName);var o=null;return"const"===t?this.matchKeyword("in")||this.matchContextualKeyword("of")||(this.match("=")?(this.nextToken(),o=this.isolateCoverGrammar(this.parseAssignmentExpression)):this.throwError(s.Messages.DeclarationMissingInitializer,"const")):(!e.inFor&&r.type!==u.Syntax.Identifier||this.match("="))&&(this.expect("="),o=this.isolateCoverGrammar(this.parseAssignmentExpression)),this.finalize(i,new a.VariableDeclarator(r,o))},t.prototype.parseBindingList=function(t,e){for(var i=[this.parseLexicalBinding(t,e)];this.match(",");)this.nextToken(),i.push(this.parseLexicalBinding(t,e));return i},t.prototype.isLexicalDeclaration=function(){var t=this.scanner.saveState();this.scanner.scanComments();var e=this.scanner.lex();return this.scanner.restoreState(t),3===e.type||7===e.type&&"["===e.value||7===e.type&&"{"===e.value||4===e.type&&"let"===e.value||4===e.type&&"yield"===e.value},t.prototype.parseLexicalDeclaration=function(t){var e=this.createNode(),i=this.nextToken().value;n.assert("let"===i||"const"===i,"Lexical declaration must be either let or const");var r=this.parseBindingList(i,t);return this.consumeSemicolon(),this.finalize(e,new a.VariableDeclaration(r,i))},t.prototype.parseBindingRestElement=function(t,e){var i=this.createNode();this.expect("...");var n=this.parsePattern(t,e);return this.finalize(i,new a.RestElement(n))},t.prototype.parseArrayPattern=function(t,e){var i=this.createNode();this.expect("[");for(var n=[];!this.match("]");)if(this.match(","))this.nextToken(),n.push(null);else{if(this.match("...")){n.push(this.parseBindingRestElement(t,e));break}n.push(this.parsePatternWithDefault(t,e)),this.match("]")||this.expect(",")}return this.expect("]"),this.finalize(i,new a.ArrayPattern(n))},t.prototype.parsePropertyPattern=function(t,e){var i,n,r=this.createNode(),s=!1,o=!1,u=!1;if(3===this.lookahead.type){var c=this.lookahead;i=this.parseVariableIdentifier();var h=this.finalize(r,new a.Identifier(c.value));if(this.match("=")){t.push(c),o=!0,this.nextToken();var l=this.parseAssignmentExpression();n=this.finalize(this.startNode(c),new a.AssignmentPattern(h,l))}else this.match(":")?(this.expect(":"),n=this.parsePatternWithDefault(t,e)):(t.push(c),o=!0,n=h)}else s=this.match("["),i=this.parseObjectPropertyKey(),this.expect(":"),n=this.parsePatternWithDefault(t,e);return this.finalize(r,new a.Property("init",i,s,n,u,o))},t.prototype.parseObjectPattern=function(t,e){var i=this.createNode(),n=[];for(this.expect("{");!this.match("}");)n.push(this.parsePropertyPattern(t,e)),this.match("}")||this.expect(",");return this.expect("}"),this.finalize(i,new a.ObjectPattern(n))},t.prototype.parsePattern=function(t,e){var i;return this.match("[")?i=this.parseArrayPattern(t,e):this.match("{")?i=this.parseObjectPattern(t,e):(this.matchKeyword("let")&&("const"===e||"let"===e)&&this.tolerateUnexpectedToken(this.lookahead,s.Messages.LetInLexicalBinding),t.push(this.lookahead),i=this.parseVariableIdentifier(e)),i},t.prototype.parsePatternWithDefault=function(t,e){var i=this.lookahead,n=this.parsePattern(t,e);if(this.match("=")){this.nextToken();var r=this.context.allowYield;this.context.allowYield=!0;var s=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowYield=r,n=this.finalize(this.startNode(i),new a.AssignmentPattern(n,s))}return n},t.prototype.parseVariableIdentifier=function(t){var e=this.createNode(),i=this.nextToken();return 4===i.type&&"yield"===i.value?this.context.strict?this.tolerateUnexpectedToken(i,s.Messages.StrictReservedWord):this.context.allowYield||this.throwUnexpectedToken(i):3!==i.type?this.context.strict&&4===i.type&&this.scanner.isStrictModeReservedWord(i.value)?this.tolerateUnexpectedToken(i,s.Messages.StrictReservedWord):(this.context.strict||"let"!==i.value||"var"!==t)&&this.throwUnexpectedToken(i):(this.context.isModule||this.context.await)&&3===i.type&&"await"===i.value&&this.tolerateUnexpectedToken(i),this.finalize(e,new a.Identifier(i.value))},t.prototype.parseVariableDeclaration=function(t){var e=this.createNode(),i=[],n=this.parsePattern(i,"var");this.context.strict&&n.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(n.name)&&this.tolerateError(s.Messages.StrictVarName);var r=null;return this.match("=")?(this.nextToken(),r=this.isolateCoverGrammar(this.parseAssignmentExpression)):n.type===u.Syntax.Identifier||t.inFor||this.expect("="),this.finalize(e,new a.VariableDeclarator(n,r))},t.prototype.parseVariableDeclarationList=function(t){var e={inFor:t.inFor},i=[];for(i.push(this.parseVariableDeclaration(e));this.match(",");)this.nextToken(),i.push(this.parseVariableDeclaration(e));return i},t.prototype.parseVariableStatement=function(){var t=this.createNode();this.expectKeyword("var");var e=this.parseVariableDeclarationList({inFor:!1});return this.consumeSemicolon(),this.finalize(t,new a.VariableDeclaration(e,"var"))},t.prototype.parseEmptyStatement=function(){var t=this.createNode();return this.expect(";"),this.finalize(t,new a.EmptyStatement)},t.prototype.parseExpressionStatement=function(){var t=this.createNode(),e=this.parseExpression();return this.consumeSemicolon(),this.finalize(t,new a.ExpressionStatement(e))},t.prototype.parseIfClause=function(){return this.context.strict&&this.matchKeyword("function")&&this.tolerateError(s.Messages.StrictFunction),this.parseStatement()},t.prototype.parseIfStatement=function(){var t,e=this.createNode(),i=null;this.expectKeyword("if"),this.expect("(");var n=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),t=this.finalize(this.createNode(),new a.EmptyStatement)):(this.expect(")"),t=this.parseIfClause(),this.matchKeyword("else")&&(this.nextToken(),i=this.parseIfClause())),this.finalize(e,new a.IfStatement(n,t,i))},t.prototype.parseDoWhileStatement=function(){var t=this.createNode();this.expectKeyword("do");var e=this.context.inIteration;this.context.inIteration=!0;var i=this.parseStatement();this.context.inIteration=e,this.expectKeyword("while"),this.expect("(");var n=this.parseExpression();return!this.match(")")&&this.config.tolerant?this.tolerateUnexpectedToken(this.nextToken()):(this.expect(")"),this.match(";")&&this.nextToken()),this.finalize(t,new a.DoWhileStatement(i,n))},t.prototype.parseWhileStatement=function(){var t,e=this.createNode();this.expectKeyword("while"),this.expect("(");var i=this.parseExpression();if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),t=this.finalize(this.createNode(),new a.EmptyStatement);else{this.expect(")");var n=this.context.inIteration;this.context.inIteration=!0,t=this.parseStatement(),this.context.inIteration=n}return this.finalize(e,new a.WhileStatement(i,t))},t.prototype.parseForStatement=function(){var t,e,i,n=null,r=null,o=null,c=!0,h=this.createNode();if(this.expectKeyword("for"),this.expect("("),this.match(";"))this.nextToken();else if(this.matchKeyword("var")){n=this.createNode(),this.nextToken();var l=this.context.allowIn;this.context.allowIn=!1;var p=this.parseVariableDeclarationList({inFor:!0});if(this.context.allowIn=l,1===p.length&&this.matchKeyword("in")){var d=p[0];d.init&&(d.id.type===u.Syntax.ArrayPattern||d.id.type===u.Syntax.ObjectPattern||this.context.strict)&&this.tolerateError(s.Messages.ForInOfLoopInitializer,"for-in"),n=this.finalize(n,new a.VariableDeclaration(p,"var")),this.nextToken(),t=n,e=this.parseExpression(),n=null}else 1===p.length&&null===p[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new a.VariableDeclaration(p,"var")),this.nextToken(),t=n,e=this.parseAssignmentExpression(),n=null,c=!1):(n=this.finalize(n,new a.VariableDeclaration(p,"var")),this.expect(";"))}else if(this.matchKeyword("const")||this.matchKeyword("let")){n=this.createNode();var f=this.nextToken().value;if(this.context.strict||"in"!==this.lookahead.value){var l=this.context.allowIn;this.context.allowIn=!1;var p=this.parseBindingList(f,{inFor:!0});this.context.allowIn=l,1===p.length&&null===p[0].init&&this.matchKeyword("in")?(n=this.finalize(n,new a.VariableDeclaration(p,f)),this.nextToken(),t=n,e=this.parseExpression(),n=null):1===p.length&&null===p[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new a.VariableDeclaration(p,f)),this.nextToken(),t=n,e=this.parseAssignmentExpression(),n=null,c=!1):(this.consumeSemicolon(),n=this.finalize(n,new a.VariableDeclaration(p,f)))}else n=this.finalize(n,new a.Identifier(f)),this.nextToken(),t=n,e=this.parseExpression(),n=null}else{var m=this.lookahead,l=this.context.allowIn;if(this.context.allowIn=!1,n=this.inheritCoverGrammar(this.parseAssignmentExpression),this.context.allowIn=l,this.matchKeyword("in"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForIn),this.nextToken(),this.reinterpretExpressionAsPattern(n),t=n,e=this.parseExpression(),n=null;else if(this.matchContextualKeyword("of"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForLoop),this.nextToken(),this.reinterpretExpressionAsPattern(n),t=n,e=this.parseAssignmentExpression(),n=null,c=!1;else{if(this.match(",")){for(var x=[n];this.match(",");)this.nextToken(),x.push(this.isolateCoverGrammar(this.parseAssignmentExpression));n=this.finalize(this.startNode(m),new a.SequenceExpression(x))}this.expect(";")}}if(void 0!==t||(this.match(";")||(r=this.parseExpression()),this.expect(";"),this.match(")")||(o=this.parseExpression())),!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),i=this.finalize(this.createNode(),new a.EmptyStatement);else{this.expect(")");var D=this.context.inIteration;this.context.inIteration=!0,i=this.isolateCoverGrammar(this.parseStatement),this.context.inIteration=D}return void 0===t?this.finalize(h,new a.ForStatement(n,r,o,i)):c?this.finalize(h,new a.ForInStatement(t,e,i)):this.finalize(h,new a.ForOfStatement(t,e,i))},t.prototype.parseContinueStatement=function(){var t=this.createNode();this.expectKeyword("continue");var e=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var i=this.parseVariableIdentifier();e=i;var n="$"+i.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,n)||this.throwError(s.Messages.UnknownLabel,i.name)}return this.consumeSemicolon(),null!==e||this.context.inIteration||this.throwError(s.Messages.IllegalContinue),this.finalize(t,new a.ContinueStatement(e))},t.prototype.parseBreakStatement=function(){var t=this.createNode();this.expectKeyword("break");var e=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var i=this.parseVariableIdentifier(),n="$"+i.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,n)||this.throwError(s.Messages.UnknownLabel,i.name),e=i}return this.consumeSemicolon(),null!==e||this.context.inIteration||this.context.inSwitch||this.throwError(s.Messages.IllegalBreak),this.finalize(t,new a.BreakStatement(e))},t.prototype.parseReturnStatement=function(){this.context.inFunctionBody||this.tolerateError(s.Messages.IllegalReturn);var t=this.createNode();this.expectKeyword("return");var e=(this.match(";")||this.match("}")||this.hasLineTerminator||2===this.lookahead.type)&&8!==this.lookahead.type&&10!==this.lookahead.type?null:this.parseExpression();return this.consumeSemicolon(),this.finalize(t,new a.ReturnStatement(e))},t.prototype.parseWithStatement=function(){this.context.strict&&this.tolerateError(s.Messages.StrictModeWith);var t,e=this.createNode();this.expectKeyword("with"),this.expect("(");var i=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),t=this.finalize(this.createNode(),new a.EmptyStatement)):(this.expect(")"),t=this.parseStatement()),this.finalize(e,new a.WithStatement(i,t))},t.prototype.parseSwitchCase=function(){var t,e=this.createNode();this.matchKeyword("default")?(this.nextToken(),t=null):(this.expectKeyword("case"),t=this.parseExpression()),this.expect(":");for(var i=[];!(this.match("}")||this.matchKeyword("default")||this.matchKeyword("case"));)i.push(this.parseStatementListItem());return this.finalize(e,new a.SwitchCase(t,i))},t.prototype.parseSwitchStatement=function(){var t=this.createNode();this.expectKeyword("switch"),this.expect("(");var e=this.parseExpression();this.expect(")");var i=this.context.inSwitch;this.context.inSwitch=!0;var n=[],r=!1;for(this.expect("{");!this.match("}");){var o=this.parseSwitchCase();null===o.test&&(r&&this.throwError(s.Messages.MultipleDefaultsInSwitch),r=!0),n.push(o)}return this.expect("}"),this.context.inSwitch=i,this.finalize(t,new a.SwitchStatement(e,n))},t.prototype.parseLabelledStatement=function(){var t,e=this.createNode(),i=this.parseExpression();if(i.type===u.Syntax.Identifier&&this.match(":")){this.nextToken();var n=i,r="$"+n.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,r)&&this.throwError(s.Messages.Redeclaration,"Label",n.name),this.context.labelSet[r]=!0;var o=void 0;if(this.matchKeyword("class"))this.tolerateUnexpectedToken(this.lookahead),o=this.parseClassDeclaration();else if(this.matchKeyword("function")){var c=this.lookahead,h=this.parseFunctionDeclaration();this.context.strict?this.tolerateUnexpectedToken(c,s.Messages.StrictFunction):h.generator&&this.tolerateUnexpectedToken(c,s.Messages.GeneratorInLegacyContext),o=h}else o=this.parseStatement();delete this.context.labelSet[r],t=new a.LabeledStatement(n,o)}else this.consumeSemicolon(),t=new a.ExpressionStatement(i);return this.finalize(e,t)},t.prototype.parseThrowStatement=function(){var t=this.createNode();this.expectKeyword("throw"),this.hasLineTerminator&&this.throwError(s.Messages.NewlineAfterThrow);var e=this.parseExpression();return this.consumeSemicolon(),this.finalize(t,new a.ThrowStatement(e))},t.prototype.parseCatchClause=function(){var t=this.createNode();this.expectKeyword("catch"),this.expect("("),this.match(")")&&this.throwUnexpectedToken(this.lookahead);for(var e=[],i=this.parsePattern(e),n={},r=0;r<e.length;r++){var o="$"+e[r].value;Object.prototype.hasOwnProperty.call(n,o)&&this.tolerateError(s.Messages.DuplicateBinding,e[r].value),n[o]=!0}this.context.strict&&i.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(i.name)&&this.tolerateError(s.Messages.StrictCatchVariable),this.expect(")");var c=this.parseBlock();return this.finalize(t,new a.CatchClause(i,c))},t.prototype.parseFinallyClause=function(){return this.expectKeyword("finally"),this.parseBlock()},t.prototype.parseTryStatement=function(){var t=this.createNode();this.expectKeyword("try");var e=this.parseBlock(),i=this.matchKeyword("catch")?this.parseCatchClause():null,n=this.matchKeyword("finally")?this.parseFinallyClause():null;return i||n||this.throwError(s.Messages.NoCatchOrFinally),this.finalize(t,new a.TryStatement(e,i,n))},t.prototype.parseDebuggerStatement=function(){var t=this.createNode();return this.expectKeyword("debugger"),this.consumeSemicolon(),this.finalize(t,new a.DebuggerStatement)},t.prototype.parseStatement=function(){var t;switch(this.lookahead.type){case 1:case 5:case 6:case 8:case 10:case 9:t=this.parseExpressionStatement();break;case 7:var e=this.lookahead.value;t="{"===e?this.parseBlock():"("===e?this.parseExpressionStatement():";"===e?this.parseEmptyStatement():this.parseExpressionStatement();break;case 3:t=this.matchAsyncFunction()?this.parseFunctionDeclaration():this.parseLabelledStatement();break;case 4:switch(this.lookahead.value){case"break":t=this.parseBreakStatement();break;case"continue":t=this.parseContinueStatement();break;case"debugger":t=this.parseDebuggerStatement();break;case"do":t=this.parseDoWhileStatement();break;case"for":t=this.parseForStatement();break;case"function":t=this.parseFunctionDeclaration();break;case"if":t=this.parseIfStatement();break;case"return":t=this.parseReturnStatement();break;case"switch":t=this.parseSwitchStatement();break;case"throw":t=this.parseThrowStatement();break;case"try":t=this.parseTryStatement();break;case"var":t=this.parseVariableStatement();break;case"while":t=this.parseWhileStatement();break;case"with":t=this.parseWithStatement();break;default:t=this.parseExpressionStatement()}break;default:t=this.throwUnexpectedToken(this.lookahead)}return t},t.prototype.parseFunctionSourceElements=function(){var t=this.createNode();this.expect("{");var e=this.parseDirectivePrologues(),i=this.context.labelSet,n=this.context.inIteration,r=this.context.inSwitch,s=this.context.inFunctionBody;for(this.context.labelSet={},this.context.inIteration=!1,this.context.inSwitch=!1,this.context.inFunctionBody=!0;2!==this.lookahead.type&&!this.match("}");)e.push(this.parseStatementListItem());return this.expect("}"),this.context.labelSet=i,this.context.inIteration=n,this.context.inSwitch=r,this.context.inFunctionBody=s,this.finalize(t,new a.BlockStatement(e))},t.prototype.validateParam=function(t,e,i){var n="$"+i;this.context.strict?(this.scanner.isRestrictedWord(i)&&(t.stricted=e,t.message=s.Messages.StrictParamName),Object.prototype.hasOwnProperty.call(t.paramSet,n)&&(t.stricted=e,t.message=s.Messages.StrictParamDupe)):!t.firstRestricted&&(this.scanner.isRestrictedWord(i)?(t.firstRestricted=e,t.message=s.Messages.StrictParamName):this.scanner.isStrictModeReservedWord(i)?(t.firstRestricted=e,t.message=s.Messages.StrictReservedWord):Object.prototype.hasOwnProperty.call(t.paramSet,n)&&(t.stricted=e,t.message=s.Messages.StrictParamDupe)),"function"==typeof Object.defineProperty?Object.defineProperty(t.paramSet,n,{value:!0,enumerable:!0,writable:!0,configurable:!0}):t.paramSet[n]=!0},t.prototype.parseRestElement=function(t){var e=this.createNode();this.expect("...");var i=this.parsePattern(t);return this.match("=")&&this.throwError(s.Messages.DefaultRestParameter),this.match(")")||this.throwError(s.Messages.ParameterAfterRestParameter),this.finalize(e,new a.RestElement(i))},t.prototype.parseFormalParameter=function(t){for(var e=[],i=this.match("...")?this.parseRestElement(e):this.parsePatternWithDefault(e),n=0;n<e.length;n++)this.validateParam(t,e[n],e[n].value);t.simple=t.simple&&i instanceof a.Identifier,t.params.push(i)},t.prototype.parseFormalParameters=function(t){var e;if(e={simple:!0,params:[],firstRestricted:t},this.expect("("),!this.match(")"))for(e.paramSet={};2!==this.lookahead.type&&(this.parseFormalParameter(e),!this.match(")"))&&(this.expect(","),!this.match(")")););return this.expect(")"),{simple:e.simple,params:e.params,stricted:e.stricted,firstRestricted:e.firstRestricted,message:e.message}},t.prototype.matchAsyncFunction=function(){var t=this.matchContextualKeyword("async");if(t){var e=this.scanner.saveState();this.scanner.scanComments();var i=this.scanner.lex();this.scanner.restoreState(e),t=e.lineNumber===i.lineNumber&&4===i.type&&"function"===i.value}return t},t.prototype.parseFunctionDeclaration=function(t){var e,i=this.createNode(),n=this.matchContextualKeyword("async");n&&this.nextToken(),this.expectKeyword("function");var r=!n&&this.match("*");r&&this.nextToken();var o=null,u=null;if(!t||!this.match("(")){var c=this.lookahead;o=this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(c.value)&&this.tolerateUnexpectedToken(c,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(c.value)?(u=c,e=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(c.value)&&(u=c,e=s.Messages.StrictReservedWord)}var h=this.context.await,l=this.context.allowYield;this.context.await=n,this.context.allowYield=!r;var p=this.parseFormalParameters(u),d=p.params,f=p.stricted;u=p.firstRestricted,p.message&&(e=p.message);var m=this.context.strict,x=this.context.allowStrictDirective;this.context.allowStrictDirective=p.simple;var D=this.parseFunctionSourceElements();return this.context.strict&&u&&this.throwUnexpectedToken(u,e),this.context.strict&&f&&this.tolerateUnexpectedToken(f,e),this.context.strict=m,this.context.allowStrictDirective=x,this.context.await=h,this.context.allowYield=l,n?this.finalize(i,new a.AsyncFunctionDeclaration(o,d,D)):this.finalize(i,new a.FunctionDeclaration(o,d,D,r))},t.prototype.parseFunctionExpression=function(){var t,e,i=this.createNode(),n=this.matchContextualKeyword("async");n&&this.nextToken(),this.expectKeyword("function");var r=!n&&this.match("*");r&&this.nextToken();var o=null,u=this.context.await,c=this.context.allowYield;if(this.context.await=n,this.context.allowYield=!r,!this.match("(")){var h=this.lookahead;o=!this.context.strict&&!r&&this.matchKeyword("yield")?this.parseIdentifierName():this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(h.value)&&this.tolerateUnexpectedToken(h,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(h.value)?(e=h,t=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(h.value)&&(e=h,t=s.Messages.StrictReservedWord)}var l=this.parseFormalParameters(e),p=l.params,d=l.stricted;e=l.firstRestricted,l.message&&(t=l.message);var f=this.context.strict,m=this.context.allowStrictDirective;this.context.allowStrictDirective=l.simple;var x=this.parseFunctionSourceElements();return this.context.strict&&e&&this.throwUnexpectedToken(e,t),this.context.strict&&d&&this.tolerateUnexpectedToken(d,t),this.context.strict=f,this.context.allowStrictDirective=m,this.context.await=u,this.context.allowYield=c,n?this.finalize(i,new a.AsyncFunctionExpression(o,p,x)):this.finalize(i,new a.FunctionExpression(o,p,x,r))},t.prototype.parseDirective=function(){var t=this.lookahead,e=this.createNode(),i=this.parseExpression(),n=i.type===u.Syntax.Literal?this.getTokenRaw(t).slice(1,-1):null;return this.consumeSemicolon(),this.finalize(e,n?new a.Directive(i,n):new a.ExpressionStatement(i))},t.prototype.parseDirectivePrologues=function(){for(var t=null,e=[];;){var i=this.lookahead;if(8!==i.type)break;var n=this.parseDirective();e.push(n);var r=n.directive;if("string"!=typeof r)break;"use strict"===r?(this.context.strict=!0,t&&this.tolerateUnexpectedToken(t,s.Messages.StrictOctalLiteral),this.context.allowStrictDirective||this.tolerateUnexpectedToken(i,s.Messages.IllegalLanguageModeDirective)):!t&&i.octal&&(t=i)}return e},t.prototype.qualifiedPropertyName=function(t){switch(t.type){case 3:case 8:case 1:case 5:case 6:case 4:return!0;case 7:return"["===t.value}return!1},t.prototype.parseGetterMethod=function(){var t=this.createNode(),e=!1,i=this.context.allowYield;this.context.allowYield=!e;var n=this.parseFormalParameters();n.params.length>0&&this.tolerateError(s.Messages.BadGetterArity);var r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(t,new a.FunctionExpression(null,n.params,r,e))},t.prototype.parseSetterMethod=function(){var t=this.createNode(),e=!1,i=this.context.allowYield;this.context.allowYield=!e;var n=this.parseFormalParameters();1!==n.params.length?this.tolerateError(s.Messages.BadSetterArity):n.params[0]instanceof a.RestElement&&this.tolerateError(s.Messages.BadSetterRestParameter);var r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(t,new a.FunctionExpression(null,n.params,r,e))},t.prototype.parseGeneratorMethod=function(){var t=this.createNode(),e=!0,i=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters();this.context.allowYield=!1;var r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(t,new a.FunctionExpression(null,n.params,r,e))},t.prototype.isStartOfExpression=function(){var t=!0,e=this.lookahead.value;switch(this.lookahead.type){case 7:t="["===e||"("===e||"{"===e||"+"===e||"-"===e||"!"===e||"~"===e||"++"===e||"--"===e||"/"===e||"/="===e;break;case 4:t="class"===e||"delete"===e||"function"===e||"let"===e||"new"===e||"super"===e||"this"===e||"typeof"===e||"void"===e||"yield"===e}return t},t.prototype.parseYieldExpression=function(){var t=this.createNode();this.expectKeyword("yield");var e=null,i=!1;if(!this.hasLineTerminator){var n=this.context.allowYield;this.context.allowYield=!1,(i=this.match("*"))?(this.nextToken(),e=this.parseAssignmentExpression()):this.isStartOfExpression()&&(e=this.parseAssignmentExpression()),this.context.allowYield=n}return this.finalize(t,new a.YieldExpression(e,i))},t.prototype.parseClassElement=function(t){var e=this.lookahead,i=this.createNode(),n="",r=null,o=null,u=!1,c=!1,h=!1,l=!1;if(this.match("*"))this.nextToken();else if(u=this.match("["),"static"===(r=this.parseObjectPropertyKey()).name&&(this.qualifiedPropertyName(this.lookahead)||this.match("*"))&&(e=this.lookahead,h=!0,u=this.match("["),this.match("*")?this.nextToken():r=this.parseObjectPropertyKey()),3===e.type&&!this.hasLineTerminator&&"async"===e.value){var p=this.lookahead.value;":"!==p&&"("!==p&&"*"!==p&&(l=!0,e=this.lookahead,r=this.parseObjectPropertyKey(),3===e.type&&"constructor"===e.value&&this.tolerateUnexpectedToken(e,s.Messages.ConstructorIsAsync))}var d=this.qualifiedPropertyName(this.lookahead);return 3===e.type?"get"===e.value&&d?(n="get",u=this.match("["),r=this.parseObjectPropertyKey(),this.context.allowYield=!1,o=this.parseGetterMethod()):"set"===e.value&&d&&(n="set",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseSetterMethod()):7===e.type&&"*"===e.value&&d&&(n="init",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseGeneratorMethod(),c=!0),!n&&r&&this.match("(")&&(n="init",o=l?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0),n||this.throwUnexpectedToken(this.lookahead),"init"===n&&(n="method"),!u&&(h&&this.isPropertyKey(r,"prototype")&&this.throwUnexpectedToken(e,s.Messages.StaticPrototype),!h&&this.isPropertyKey(r,"constructor")&&(("method"!==n||!c||o&&o.generator)&&this.throwUnexpectedToken(e,s.Messages.ConstructorSpecialMethod),t.value?this.throwUnexpectedToken(e,s.Messages.DuplicateConstructor):t.value=!0,n="constructor")),this.finalize(i,new a.MethodDefinition(r,u,o,n,h))},t.prototype.parseClassElementList=function(){var t=[],e={value:!1};for(this.expect("{");!this.match("}");)this.match(";")?this.nextToken():t.push(this.parseClassElement(e));return this.expect("}"),t},t.prototype.parseClassBody=function(){var t=this.createNode(),e=this.parseClassElementList();return this.finalize(t,new a.ClassBody(e))},t.prototype.parseClassDeclaration=function(t){var e=this.createNode(),i=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var n=t&&3!==this.lookahead.type?null:this.parseVariableIdentifier(),r=null;this.matchKeyword("extends")&&(this.nextToken(),r=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var s=this.parseClassBody();return this.context.strict=i,this.finalize(e,new a.ClassDeclaration(n,r,s))},t.prototype.parseClassExpression=function(){var t=this.createNode(),e=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var i=3===this.lookahead.type?this.parseVariableIdentifier():null,n=null;this.matchKeyword("extends")&&(this.nextToken(),n=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var r=this.parseClassBody();return this.context.strict=e,this.finalize(t,new a.ClassExpression(i,n,r))},t.prototype.parseModule=function(){this.context.strict=!0,this.context.isModule=!0,this.scanner.isModule=!0;for(var t=this.createNode(),e=this.parseDirectivePrologues();2!==this.lookahead.type;)e.push(this.parseStatementListItem());return this.finalize(t,new a.Module(e))},t.prototype.parseScript=function(){for(var t=this.createNode(),e=this.parseDirectivePrologues();2!==this.lookahead.type;)e.push(this.parseStatementListItem());return this.finalize(t,new a.Script(e))},t.prototype.parseModuleSpecifier=function(){var t=this.createNode();8!==this.lookahead.type&&this.throwError(s.Messages.InvalidModuleSpecifier);var e=this.nextToken(),i=this.getTokenRaw(e);return this.finalize(t,new a.Literal(e.value,i))},t.prototype.parseImportSpecifier=function(){var t,e,i=this.createNode();return 3===this.lookahead.type?(e=t=this.parseVariableIdentifier(),this.matchContextualKeyword("as")&&(this.nextToken(),e=this.parseVariableIdentifier())):(e=t=this.parseIdentifierName(),this.matchContextualKeyword("as")?(this.nextToken(),e=this.parseVariableIdentifier()):this.throwUnexpectedToken(this.nextToken())),this.finalize(i,new a.ImportSpecifier(e,t))},t.prototype.parseNamedImports=function(){this.expect("{");for(var t=[];!this.match("}");)t.push(this.parseImportSpecifier()),this.match("}")||this.expect(",");return this.expect("}"),t},t.prototype.parseImportDefaultSpecifier=function(){var t=this.createNode(),e=this.parseIdentifierName();return this.finalize(t,new a.ImportDefaultSpecifier(e))},t.prototype.parseImportNamespaceSpecifier=function(){var t=this.createNode();this.expect("*"),this.matchContextualKeyword("as")||this.throwError(s.Messages.NoAsAfterImportNamespace),this.nextToken();var e=this.parseIdentifierName();return this.finalize(t,new a.ImportNamespaceSpecifier(e))},t.prototype.parseImportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalImportDeclaration);var t,e=this.createNode();this.expectKeyword("import");var i=[];if(8===this.lookahead.type)t=this.parseModuleSpecifier();else{if(this.match("{")?i=i.concat(this.parseNamedImports()):this.match("*")?i.push(this.parseImportNamespaceSpecifier()):this.isIdentifierName(this.lookahead)&&!this.matchKeyword("default")?(i.push(this.parseImportDefaultSpecifier()),this.match(",")&&(this.nextToken(),this.match("*")?i.push(this.parseImportNamespaceSpecifier()):this.match("{")?i=i.concat(this.parseNamedImports()):this.throwUnexpectedToken(this.lookahead))):this.throwUnexpectedToken(this.nextToken()),!this.matchContextualKeyword("from")){var n=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(n,this.lookahead.value)}this.nextToken(),t=this.parseModuleSpecifier()}return this.consumeSemicolon(),this.finalize(e,new a.ImportDeclaration(i,t))},t.prototype.parseExportSpecifier=function(){var t=this.createNode(),e=this.parseIdentifierName(),i=e;return this.matchContextualKeyword("as")&&(this.nextToken(),i=this.parseIdentifierName()),this.finalize(t,new a.ExportSpecifier(e,i))},t.prototype.parseExportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalExportDeclaration);var t,e=this.createNode();if(this.expectKeyword("export"),this.matchKeyword("default")){if(this.nextToken(),this.matchKeyword("function")){var i=this.parseFunctionDeclaration(!0);t=this.finalize(e,new a.ExportDefaultDeclaration(i))}else if(this.matchKeyword("class")){var i=this.parseClassDeclaration(!0);t=this.finalize(e,new a.ExportDefaultDeclaration(i))}else if(this.matchContextualKeyword("async")){var i=this.matchAsyncFunction()?this.parseFunctionDeclaration(!0):this.parseAssignmentExpression();t=this.finalize(e,new a.ExportDefaultDeclaration(i))}else{this.matchContextualKeyword("from")&&this.throwError(s.Messages.UnexpectedToken,this.lookahead.value);var i=this.match("{")?this.parseObjectInitializer():this.match("[")?this.parseArrayInitializer():this.parseAssignmentExpression();this.consumeSemicolon(),t=this.finalize(e,new a.ExportDefaultDeclaration(i))}}else if(this.match("*")){if(this.nextToken(),!this.matchContextualKeyword("from")){var n=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(n,this.lookahead.value)}this.nextToken();var r=this.parseModuleSpecifier();this.consumeSemicolon(),t=this.finalize(e,new a.ExportAllDeclaration(r))}else if(4===this.lookahead.type){var i=void 0;switch(this.lookahead.value){case"let":case"const":i=this.parseLexicalDeclaration({inFor:!1});break;case"var":case"class":case"function":i=this.parseStatementListItem();break;default:this.throwUnexpectedToken(this.lookahead)}t=this.finalize(e,new a.ExportNamedDeclaration(i,[],null))}else if(this.matchAsyncFunction()){var i=this.parseFunctionDeclaration();t=this.finalize(e,new a.ExportNamedDeclaration(i,[],null))}else{var o=[],u=null,c=!1;for(this.expect("{");!this.match("}");)c=c||this.matchKeyword("default"),o.push(this.parseExportSpecifier()),this.match("}")||this.expect(",");if(this.expect("}"),this.matchContextualKeyword("from"))this.nextToken(),u=this.parseModuleSpecifier(),this.consumeSemicolon();else if(c){var n=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(n,this.lookahead.value)}else this.consumeSemicolon();t=this.finalize(e,new a.ExportNamedDeclaration(null,o,u))}return t},t}();e.Parser=l},function(t,e){"use strict";function i(t,e){if(!t)throw Error("ASSERT: "+e)}Object.defineProperty(e,"__esModule",{value:!0}),e.assert=i},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=function(){function t(){this.errors=[],this.tolerant=!1}return t.prototype.recordError=function(t){this.errors.push(t)},t.prototype.tolerate=function(t){if(this.tolerant)this.recordError(t);else throw t},t.prototype.constructError=function(t,e){var i=Error(t);try{throw i}catch(t){Object.create&&Object.defineProperty&&Object.defineProperty(i=Object.create(t),"column",{value:e})}return i},t.prototype.createError=function(t,e,i,n){var r="Line "+e+": "+n,s=this.constructError(r,i);return s.index=t,s.lineNumber=e,s.description=n,s},t.prototype.throwError=function(t,e,i,n){throw this.createError(t,e,i,n)},t.prototype.tolerateError=function(t,e,i,n){var r=this.createError(t,e,i,n);if(this.tolerant)this.recordError(r);else throw r},t}();e.ErrorHandler=i},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Messages={BadGetterArity:"Getter must not have any formal parameters",BadSetterArity:"Setter must have exactly one formal parameter",BadSetterRestParameter:"Setter function argument must not be a rest parameter",ConstructorIsAsync:"Class constructor may not be an async method",ConstructorSpecialMethod:"Class constructor may not be an accessor",DeclarationMissingInitializer:"Missing initializer in %0 declaration",DefaultRestParameter:"Unexpected token =",DuplicateBinding:"Duplicate binding %0",DuplicateConstructor:"A class may only have one constructor",DuplicateProtoProperty:"Duplicate __proto__ fields are not allowed in object literals",ForInOfLoopInitializer:"%0 loop variable declaration may not have an initializer",GeneratorInLegacyContext:"Generator declarations are not allowed in legacy contexts",IllegalBreak:"Illegal break statement",IllegalContinue:"Illegal continue statement",IllegalExportDeclaration:"Unexpected token",IllegalImportDeclaration:"Unexpected token",IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list",IllegalReturn:"Illegal return statement",InvalidEscapedReservedWord:"Keyword must not contain escaped characters",InvalidHexEscapeSequence:"Invalid hexadecimal escape sequence",InvalidLHSInAssignment:"Invalid left-hand side in assignment",InvalidLHSInForIn:"Invalid left-hand side in for-in",InvalidLHSInForLoop:"Invalid left-hand side in for-loop",InvalidModuleSpecifier:"Unexpected token",InvalidRegExp:"Invalid regular expression",LetInLexicalBinding:"let is disallowed as a lexically bound name",MissingFromClause:"Unexpected token",MultipleDefaultsInSwitch:"More than one default clause in switch statement",NewlineAfterThrow:"Illegal newline after throw",NoAsAfterImportNamespace:"Unexpected token",NoCatchOrFinally:"Missing catch or finally after try",ParameterAfterRestParameter:"Rest parameter must be last formal parameter",Redeclaration:"%0 '%1' has already been declared",StaticPrototype:"Classes may not have static property named prototype",StrictCatchVariable:"Catch variable may not be eval or arguments in strict mode",StrictDelete:"Delete of an unqualified identifier in strict mode.",StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block",StrictFunctionName:"Function name may not be eval or arguments in strict mode",StrictLHSAssignment:"Assignment to eval or arguments is not allowed in strict mode",StrictLHSPostfix:"Postfix increment/decrement may not have eval or arguments operand in strict mode",StrictLHSPrefix:"Prefix increment/decrement may not have eval or arguments operand in strict mode",StrictModeWith:"Strict mode code may not include a with statement",StrictOctalLiteral:"Octal literals are not allowed in strict mode.",StrictParamDupe:"Strict mode function may not have duplicate parameter names",StrictParamName:"Parameter name eval or arguments is not allowed in strict mode",StrictReservedWord:"Use of future reserved word in strict mode",StrictVarName:"Variable name may not be eval or arguments in strict mode",TemplateOctalLiteral:"Octal literals are not allowed in template strings.",UnexpectedEOS:"Unexpected end of input",UnexpectedIdentifier:"Unexpected identifier",UnexpectedNumber:"Unexpected number",UnexpectedReserved:"Unexpected reserved word",UnexpectedString:"Unexpected string",UnexpectedTemplate:"Unexpected quasi %0",UnexpectedToken:"Unexpected token %0",UnexpectedTokenIllegal:"Unexpected token ILLEGAL",UnknownLabel:"Undefined label '%0'",UnterminatedRegExp:"Invalid regular expression: missing /"}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(9),r=i(4),s=i(11);function a(t){return"0123456789abcdef".indexOf(t.toLowerCase())}function o(t){return"01234567".indexOf(t)}var u=function(){function t(t,e){this.source=t,this.errorHandler=e,this.trackComment=!1,this.isModule=!1,this.length=t.length,this.index=0,this.lineNumber=t.length>0?1:0,this.lineStart=0,this.curlyStack=[]}return t.prototype.saveState=function(){return{index:this.index,lineNumber:this.lineNumber,lineStart:this.lineStart}},t.prototype.restoreState=function(t){this.index=t.index,this.lineNumber=t.lineNumber,this.lineStart=t.lineStart},t.prototype.eof=function(){return this.index>=this.length},t.prototype.throwUnexpectedToken=function(t){return void 0===t&&(t=s.Messages.UnexpectedTokenIllegal),this.errorHandler.throwError(this.index,this.lineNumber,this.index-this.lineStart+1,t)},t.prototype.tolerateUnexpectedToken=function(t){void 0===t&&(t=s.Messages.UnexpectedTokenIllegal),this.errorHandler.tolerateError(this.index,this.lineNumber,this.index-this.lineStart+1,t)},t.prototype.skipSingleLineComment=function(t){var e,i,n=[];for(this.trackComment&&(n=[],e=this.index-t,i={start:{line:this.lineNumber,column:this.index-this.lineStart-t},end:{}});!this.eof();){var s=this.source.charCodeAt(this.index);if(++this.index,r.Character.isLineTerminator(s)){if(this.trackComment){i.end={line:this.lineNumber,column:this.index-this.lineStart-1};var a={multiLine:!1,slice:[e+t,this.index-1],range:[e,this.index-1],loc:i};n.push(a)}return 13===s&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,n}}if(this.trackComment){i.end={line:this.lineNumber,column:this.index-this.lineStart};var a={multiLine:!1,slice:[e+t,this.index],range:[e,this.index],loc:i};n.push(a)}return n},t.prototype.skipMultiLineComment=function(){var t,e,i=[];for(this.trackComment&&(i=[],t=this.index-2,e={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{}});!this.eof();){var n=this.source.charCodeAt(this.index);if(r.Character.isLineTerminator(n))13===n&&10===this.source.charCodeAt(this.index+1)&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(42===n){if(47===this.source.charCodeAt(this.index+1)){if(this.index+=2,this.trackComment){e.end={line:this.lineNumber,column:this.index-this.lineStart};var s={multiLine:!0,slice:[t+2,this.index-2],range:[t,this.index],loc:e};i.push(s)}return i}++this.index}else++this.index}if(this.trackComment){e.end={line:this.lineNumber,column:this.index-this.lineStart};var s={multiLine:!0,slice:[t+2,this.index],range:[t,this.index],loc:e};i.push(s)}return this.tolerateUnexpectedToken(),i},t.prototype.scanComments=function(){this.trackComment&&(t=[]);for(var t,e=0===this.index;!this.eof();){var i=this.source.charCodeAt(this.index);if(r.Character.isWhiteSpace(i))++this.index;else if(r.Character.isLineTerminator(i))++this.index,13===i&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,e=!0;else if(47===i){if(47===(i=this.source.charCodeAt(this.index+1))){this.index+=2;var n=this.skipSingleLineComment(2);this.trackComment&&(t=t.concat(n)),e=!0}else if(42===i){this.index+=2;var n=this.skipMultiLineComment();this.trackComment&&(t=t.concat(n))}else break}else if(e&&45===i){if(45===this.source.charCodeAt(this.index+1)&&62===this.source.charCodeAt(this.index+2)){this.index+=3;var n=this.skipSingleLineComment(3);this.trackComment&&(t=t.concat(n))}else break}else if(60!==i||this.isModule)break;else if("!--"===this.source.slice(this.index+1,this.index+4)){this.index+=4;var n=this.skipSingleLineComment(4);this.trackComment&&(t=t.concat(n))}else break}return t},t.prototype.isFutureReservedWord=function(t){switch(t){case"enum":case"export":case"import":case"super":return!0;default:return!1}},t.prototype.isStrictModeReservedWord=function(t){switch(t){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"yield":case"let":return!0;default:return!1}},t.prototype.isRestrictedWord=function(t){return"eval"===t||"arguments"===t},t.prototype.isKeyword=function(t){switch(t.length){case 2:return"if"===t||"in"===t||"do"===t;case 3:return"var"===t||"for"===t||"new"===t||"try"===t||"let"===t;case 4:return"this"===t||"else"===t||"case"===t||"void"===t||"with"===t||"enum"===t;case 5:return"while"===t||"break"===t||"catch"===t||"throw"===t||"const"===t||"yield"===t||"class"===t||"super"===t;case 6:return"return"===t||"typeof"===t||"delete"===t||"switch"===t||"export"===t||"import"===t;case 7:return"default"===t||"finally"===t||"extends"===t;case 8:return"function"===t||"continue"===t||"debugger"===t;case 10:return"instanceof"===t;default:return!1}},t.prototype.codePointAt=function(t){var e=this.source.charCodeAt(t);if(e>=55296&&e<=56319){var i=this.source.charCodeAt(t+1);i>=56320&&i<=57343&&(e=(e-55296)*1024+i-56320+65536)}return e},t.prototype.scanHexEscape=function(t){for(var e="u"===t?4:2,i=0,n=0;n<e;++n){if(!(!this.eof()&&r.Character.isHexDigit(this.source.charCodeAt(this.index))))return null;i=16*i+a(this.source[this.index++])}return String.fromCharCode(i)},t.prototype.scanUnicodeCodePointEscape=function(){var t=this.source[this.index],e=0;for("}"===t&&this.throwUnexpectedToken();!this.eof()&&(t=this.source[this.index++],r.Character.isHexDigit(t.charCodeAt(0)));)e=16*e+a(t);return(e>1114111||"}"!==t)&&this.throwUnexpectedToken(),r.Character.fromCodePoint(e)},t.prototype.getIdentifier=function(){for(var t=this.index++;!this.eof();){var e=this.source.charCodeAt(this.index);if(92===e||e>=55296&&e<57343)return this.index=t,this.getComplexIdentifier();if(r.Character.isIdentifierPart(e))++this.index;else break}return this.source.slice(t,this.index)},t.prototype.getComplexIdentifier=function(){var t,e=this.codePointAt(this.index),i=r.Character.fromCodePoint(e);for(this.index+=i.length,92===e&&(117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,t=this.scanUnicodeCodePointEscape()):null!==(t=this.scanHexEscape("u"))&&"\\"!==t&&r.Character.isIdentifierStart(t.charCodeAt(0))||this.throwUnexpectedToken(),i=t);!this.eof()&&(e=this.codePointAt(this.index),r.Character.isIdentifierPart(e));)i+=t=r.Character.fromCodePoint(e),this.index+=t.length,92===e&&(i=i.substr(0,i.length-1),117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,t=this.scanUnicodeCodePointEscape()):null!==(t=this.scanHexEscape("u"))&&"\\"!==t&&r.Character.isIdentifierPart(t.charCodeAt(0))||this.throwUnexpectedToken(),i+=t);return i},t.prototype.octalToDecimal=function(t){var e="0"!==t,i=o(t);return!this.eof()&&r.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(e=!0,i=8*i+o(this.source[this.index++]),"0123".indexOf(t)>=0&&!this.eof()&&r.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(i=8*i+o(this.source[this.index++]))),{code:i,octal:e}},t.prototype.scanIdentifier=function(){var t,e=this.index,i=92===this.source.charCodeAt(e)?this.getComplexIdentifier():this.getIdentifier();if(3!=(t=1===i.length?3:this.isKeyword(i)?4:"null"===i?5:"true"===i||"false"===i?1:3)&&e+i.length!==this.index){var n=this.index;this.index=e,this.tolerateUnexpectedToken(s.Messages.InvalidEscapedReservedWord),this.index=n}return{type:t,value:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},t.prototype.scanPunctuator=function(){var t=this.index,e=this.source[this.index];switch(e){case"(":case"{":"{"===e&&this.curlyStack.push("{"),++this.index;break;case".":++this.index,"."===this.source[this.index]&&"."===this.source[this.index+1]&&(this.index+=2,e="...");break;case"}":++this.index,this.curlyStack.pop();break;case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;default:">>>="===(e=this.source.substr(this.index,4))?this.index+=4:"==="===(e=e.substr(0,3))||"!=="===e||">>>"===e||"<<="===e||">>="===e||"**="===e?this.index+=3:"&&"===(e=e.substr(0,2))||"||"===e||"=="===e||"!="===e||"+="===e||"-="===e||"*="===e||"/="===e||"++"===e||"--"===e||"<<"===e||">>"===e||"&="===e||"|="===e||"^="===e||"%="===e||"<="===e||">="===e||"=>"===e||"**"===e?this.index+=2:(e=this.source[this.index],"<>=!+-*%&|^/".indexOf(e)>=0&&++this.index)}return this.index===t&&this.throwUnexpectedToken(),{type:7,value:e,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanHexLiteral=function(t){for(var e="";!this.eof()&&r.Character.isHexDigit(this.source.charCodeAt(this.index));)e+=this.source[this.index++];return 0===e.length&&this.throwUnexpectedToken(),r.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseInt("0x"+e,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanBinaryLiteral=function(t){for(var e,i="";!this.eof()&&("0"===(e=this.source[this.index])||"1"===e);)i+=this.source[this.index++];return 0===i.length&&this.throwUnexpectedToken(),!this.eof()&&(e=this.source.charCodeAt(this.index),(r.Character.isIdentifierStart(e)||r.Character.isDecimalDigit(e))&&this.throwUnexpectedToken()),{type:6,value:parseInt(i,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanOctalLiteral=function(t,e){var i="",n=!1;for(r.Character.isOctalDigit(t.charCodeAt(0))?(n=!0,i="0"+this.source[this.index++]):++this.index;!this.eof()&&r.Character.isOctalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];return n||0!==i.length||this.throwUnexpectedToken(),(r.Character.isIdentifierStart(this.source.charCodeAt(this.index))||r.Character.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:6,value:parseInt(i,8),octal:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},t.prototype.isImplicitOctalLiteral=function(){for(var t=this.index+1;t<this.length;++t){var e=this.source[t];if("8"===e||"9"===e)return!1;if(!r.Character.isOctalDigit(e.charCodeAt(0)))break}return!0},t.prototype.scanNumericLiteral=function(){var t=this.index,e=this.source[t];n.assert(r.Character.isDecimalDigit(e.charCodeAt(0))||"."===e,"Numeric literal must start with a decimal digit or a decimal point");var i="";if("."!==e){if(i=this.source[this.index++],e=this.source[this.index],"0"===i){if("x"===e||"X"===e)return++this.index,this.scanHexLiteral(t);if("b"===e||"B"===e)return++this.index,this.scanBinaryLiteral(t);if("o"===e||"O"===e||e&&r.Character.isOctalDigit(e.charCodeAt(0))&&this.isImplicitOctalLiteral())return this.scanOctalLiteral(e,t)}for(;r.Character.isDecimalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];e=this.source[this.index]}if("."===e){for(i+=this.source[this.index++];r.Character.isDecimalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];e=this.source[this.index]}if("e"===e||"E"===e){if(i+=this.source[this.index++],("+"===(e=this.source[this.index])||"-"===e)&&(i+=this.source[this.index++]),r.Character.isDecimalDigit(this.source.charCodeAt(this.index)))for(;r.Character.isDecimalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];else this.throwUnexpectedToken()}return r.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseFloat(i),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanStringLiteral=function(){var t=this.index,e=this.source[t];n.assert("'"===e||'"'===e,"String literal must starts with a quote"),++this.index;for(var i=!1,a="";!this.eof();){var o=this.source[this.index++];if(o===e){e="";break}if("\\"===o){if((o=this.source[this.index++])&&r.Character.isLineTerminator(o.charCodeAt(0)))++this.lineNumber,"\r"===o&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(o){case"u":if("{"===this.source[this.index])++this.index,a+=this.scanUnicodeCodePointEscape();else{var u=this.scanHexEscape(o);null===u&&this.throwUnexpectedToken(),a+=u}break;case"x":var c=this.scanHexEscape(o);null===c&&this.throwUnexpectedToken(s.Messages.InvalidHexEscapeSequence),a+=c;break;case"n":a+="\n";break;case"r":a+="\r";break;case"t":a+="	";break;case"b":a+="\b";break;case"f":a+="\f";break;case"v":a+="\v";break;case"8":case"9":a+=o,this.tolerateUnexpectedToken();break;default:if(o&&r.Character.isOctalDigit(o.charCodeAt(0))){var h=this.octalToDecimal(o);i=h.octal||i,a+=String.fromCharCode(h.code)}else a+=o}}else if(r.Character.isLineTerminator(o.charCodeAt(0)))break;else a+=o}return""!==e&&(this.index=t,this.throwUnexpectedToken()),{type:8,value:a,octal:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanTemplate=function(){var t="",e=!1,i=this.index,n="`"===this.source[i],a=!1,o=2;for(++this.index;!this.eof();){var u=this.source[this.index++];if("`"===u){o=1,a=!0,e=!0;break}if("$"===u){if("{"===this.source[this.index]){this.curlyStack.push("${"),++this.index,e=!0;break}t+=u}else if("\\"===u){if(u=this.source[this.index++],r.Character.isLineTerminator(u.charCodeAt(0)))++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(u){case"n":t+="\n";break;case"r":t+="\r";break;case"t":t+="	";break;case"u":if("{"===this.source[this.index])++this.index,t+=this.scanUnicodeCodePointEscape();else{var c=this.index,h=this.scanHexEscape(u);null!==h?t+=h:(this.index=c,t+=u)}break;case"x":var l=this.scanHexEscape(u);null===l&&this.throwUnexpectedToken(s.Messages.InvalidHexEscapeSequence),t+=l;break;case"b":t+="\b";break;case"f":t+="\f";break;case"v":t+="\v";break;default:"0"===u?(r.Character.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral),t+="\0"):r.Character.isOctalDigit(u.charCodeAt(0))?this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral):t+=u}}else r.Character.isLineTerminator(u.charCodeAt(0))?(++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index,t+="\n"):t+=u}return e||this.throwUnexpectedToken(),n||this.curlyStack.pop(),{type:10,value:this.source.slice(i+1,this.index-o),cooked:t,head:n,tail:a,lineNumber:this.lineNumber,lineStart:this.lineStart,start:i,end:this.index}},t.prototype.testRegExp=function(t,e){var i="￿",n=t,r=this;e.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}|\\u([a-fA-F0-9]{4})/g,function(t,e,n){var a=parseInt(e||n,16);return(a>1114111&&r.throwUnexpectedToken(s.Messages.InvalidRegExp),a<=65535)?String.fromCharCode(a):i}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,i));try{RegExp(n)}catch(t){this.throwUnexpectedToken(s.Messages.InvalidRegExp)}try{return new RegExp(t,e)}catch(t){return null}},t.prototype.scanRegExpBody=function(){var t=this.source[this.index];n.assert("/"===t,"Regular expression literal must start with a slash");for(var e=this.source[this.index++],i=!1,a=!1;!this.eof();)if(e+=t=this.source[this.index++],"\\"===t)t=this.source[this.index++],r.Character.isLineTerminator(t.charCodeAt(0))&&this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),e+=t;else if(r.Character.isLineTerminator(t.charCodeAt(0)))this.throwUnexpectedToken(s.Messages.UnterminatedRegExp);else if(i)"]"===t&&(i=!1);else{if("/"===t){a=!0;break}"["===t&&(i=!0)}return a||this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),e.substr(1,e.length-2)},t.prototype.scanRegExpFlags=function(){for(var t="",e="";!this.eof();){var i=this.source[this.index];if(!r.Character.isIdentifierPart(i.charCodeAt(0)))break;if(++this.index,"\\"!==i||this.eof())e+=i,t+=i;else if("u"===(i=this.source[this.index])){++this.index;var n=this.index,s=this.scanHexEscape("u");if(null!==s)for(e+=s,t+="\\u";n<this.index;++n)t+=this.source[n];else this.index=n,e+="u",t+="\\u";this.tolerateUnexpectedToken()}else t+="\\",this.tolerateUnexpectedToken()}return e},t.prototype.scanRegExp=function(){var t=this.index,e=this.scanRegExpBody(),i=this.scanRegExpFlags(),n=this.testRegExp(e,i);return{type:9,value:"",pattern:e,flags:i,regex:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.lex=function(){if(this.eof())return{type:2,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};var t=this.source.charCodeAt(this.index);return r.Character.isIdentifierStart(t)?this.scanIdentifier():40===t||41===t||59===t?this.scanPunctuator():39===t||34===t?this.scanStringLiteral():46===t?r.Character.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():r.Character.isDecimalDigit(t)?this.scanNumericLiteral():96===t||125===t&&"${"===this.curlyStack[this.curlyStack.length-1]?this.scanTemplate():t>=55296&&t<57343&&r.Character.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()},t}();e.Scanner=u},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TokenName={},e.TokenName[1]="Boolean",e.TokenName[2]="<end>",e.TokenName[3]="Identifier",e.TokenName[4]="Keyword",e.TokenName[5]="Null",e.TokenName[6]="Numeric",e.TokenName[7]="Punctuator",e.TokenName[8]="String",e.TokenName[9]="RegularExpression",e.TokenName[10]="Template"},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.XHTMLEntities={quot:'"',amp:"&",apos:"'",gt:">",nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",lang:"⟨",rang:"⟩"}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(10),r=i(12),s=i(13),a=function(){function t(){this.values=[],this.curly=this.paren=-1}return t.prototype.beforeFunctionExpression=function(t){return["(","{","[","in","typeof","instanceof","new","return","case","delete","throw","void","=","+=","-=","*=","**=","/=","%=","<<=",">>=",">>>=","&=","|=","^=",",","+","-","*","**","/","%","++","--","<<",">>",">>>","&","|","^","!","~","&&","||","?",":","===","==",">=","<=","<",">","!=","!=="].indexOf(t)>=0},t.prototype.isRegexStart=function(){var t=this.values[this.values.length-1],e=null!==t;switch(t){case"this":case"]":e=!1;break;case")":var i=this.values[this.paren-1];e="if"===i||"while"===i||"for"===i||"with"===i;break;case"}":if(e=!1,"function"===this.values[this.curly-3]){var n=this.values[this.curly-4];e=!!n&&!this.beforeFunctionExpression(n)}else if("function"===this.values[this.curly-4]){var n=this.values[this.curly-5];e=!n||!this.beforeFunctionExpression(n)}}return e},t.prototype.push=function(t){7===t.type||4===t.type?("{"===t.value?this.curly=this.values.length:"("===t.value&&(this.paren=this.values.length),this.values.push(t.value)):this.values.push(null)},t}(),o=function(){function t(t,e){this.errorHandler=new n.ErrorHandler,this.errorHandler.tolerant=!!e&&"boolean"==typeof e.tolerant&&e.tolerant,this.scanner=new r.Scanner(t,this.errorHandler),this.scanner.trackComment=!!e&&"boolean"==typeof e.comment&&e.comment,this.trackRange=!!e&&"boolean"==typeof e.range&&e.range,this.trackLoc=!!e&&"boolean"==typeof e.loc&&e.loc,this.buffer=[],this.reader=new a}return t.prototype.errors=function(){return this.errorHandler.errors},t.prototype.getNextToken=function(){if(0===this.buffer.length){var t=this.scanner.scanComments();if(this.scanner.trackComment)for(var e=0;e<t.length;++e){var i=t[e],n=this.scanner.source.slice(i.slice[0],i.slice[1]),r={type:i.multiLine?"BlockComment":"LineComment",value:n};this.trackRange&&(r.range=i.range),this.trackLoc&&(r.loc=i.loc),this.buffer.push(r)}if(!this.scanner.eof()){var a=void 0;this.trackLoc&&(a={start:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},end:{}});var o="/"===this.scanner.source[this.scanner.index]&&this.reader.isRegexStart()?this.scanner.scanRegExp():this.scanner.lex();this.reader.push(o);var u={type:s.TokenName[o.type],value:this.scanner.source.slice(o.start,o.end)};if(this.trackRange&&(u.range=[o.start,o.end]),this.trackLoc&&(a.end={line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},u.loc=a),9===o.type){var c=o.pattern,h=o.flags;u.regex={pattern:c,flags:h}}this.buffer.push(u)}}return this.buffer.shift()},t}();e.Tokenizer=o}])})},58140:(t,e,i)=>{"use strict";var n=i(63498);function r(t,e){for(var i in e)s(e,i)&&(t[i]=e[i])}function s(t,e){return Object.prototype.hasOwnProperty.call(t,e)}t.exports=function(t){n(t)||(t={});for(var e=arguments.length,i=1;i<e;i++){var s=arguments[i];n(s)&&r(t,s)}return t}},92618:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(40880),r=i(78906),s=i(82276);e.default=function(t){var e=t.options,i={_declaration:{_attributes:{version:"1.0",encoding:"utf-8"}},feed:{_attributes:{xmlns:"http://www.w3.org/2005/Atom"},id:e.id,title:e.title,updated:e.updated?e.updated.toISOString():new Date().toISOString(),generator:s.sanitize(e.generator||r.generator)}};e.author&&(i.feed.author=a(e.author)),i.feed.link=[],e.link&&i.feed.link.push({_attributes:{rel:"alternate",href:s.sanitize(e.link)}});var u=s.sanitize(e.feed||e.feedLinks&&e.feedLinks.atom);return u&&i.feed.link.push({_attributes:{rel:"self",href:s.sanitize(u)}}),e.hub&&i.feed.link.push({_attributes:{rel:"hub",href:s.sanitize(e.hub)}}),e.description&&(i.feed.subtitle=e.description),e.image&&(i.feed.logo=e.image),e.favicon&&(i.feed.icon=e.favicon),e.copyright&&(i.feed.rights=e.copyright),i.feed.category=[],t.categories.map(function(t){i.feed.category.push({_attributes:{term:t}})}),i.feed.contributor=[],t.contributors.map(function(t){i.feed.contributor.push(a(t))}),i.feed.entry=[],t.items.map(function(t){var e={title:{_attributes:{type:"html"},_cdata:t.title},id:s.sanitize(t.id||t.link),link:[{_attributes:{href:s.sanitize(t.link)}}],updated:t.date.toISOString()};t.description&&(e.summary={_attributes:{type:"html"},_cdata:t.description}),t.content&&(e.content={_attributes:{type:"html"},_cdata:t.content}),Array.isArray(t.author)&&(e.author=[],t.author.map(function(t){e.author.push(a(t))})),Array.isArray(t.category)&&(e.category=[],t.category.map(function(t){e.category.push(o(t))})),t.contributor&&Array.isArray(t.contributor)&&(e.contributor=[],t.contributor.map(function(t){e.contributor.push(a(t))})),t.published&&(e.published=t.published.toISOString()),t.copyright&&(e.rights=t.copyright),i.feed.entry.push(e)}),n.js2xml(i,{compact:!0,ignoreComment:!0,spaces:4})};var a=function(t){var e=t.name,i=t.email,n=t.link,r={name:e};return i&&(r.email=i),n&&(r.uri=s.sanitize(n)),r},o=function(t){return{_attributes:{label:t.name,scheme:t.scheme,term:t.term}}}},78906:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.generator=void 0,e.generator="https://github.com/jpmonette/feed"},21233:(t,e,i)=>{"use strict";n={value:!0},e.f=void 0;var n,r=i(92618),s=i(15271),a=i(57078),o=function(){return function(t){var e=this;this.items=[],this.categories=[],this.contributors=[],this.extensions=[],this.addItem=function(t){return e.items.push(t)},this.addCategory=function(t){return e.categories.push(t)},this.addContributor=function(t){return e.contributors.push(t)},this.addExtension=function(t){return e.extensions.push(t)},this.atom1=function(){return r.default(e)},this.rss2=function(){return a.default(e)},this.json1=function(){return s.default(e)},this.options=t}}();e.f=o},15271:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=t.options,i=t.items,n=t.extensions,r={version:"https://jsonfeed.org/version/1",title:e.title};return e.link&&(r.home_page_url=e.link),e.feedLinks&&e.feedLinks.json&&(r.feed_url=e.feedLinks.json),e.description&&(r.description=e.description),e.image&&(r.icon=e.image),e.author&&(r.author={},e.author.name&&(r.author.name=e.author.name),e.author.link&&(r.author.url=e.author.link)),n.map(function(t){r[t.name]=t.objects}),r.items=i.map(function(t){var e={id:t.id,content_html:t.content};if(t.link&&(e.url=t.link),t.title&&(e.title=t.title),t.description&&(e.summary=t.description),t.image&&(e.image=t.image),t.date&&(e.date_modified=t.date.toISOString()),t.published&&(e.date_published=t.published.toISOString()),t.author){var i=t.author;i instanceof Array&&(i=i[0]),e.author={},i.name&&(e.author.name=i.name),i.link&&(e.author.url=i.link)}return Array.isArray(t.category)&&(e.tags=[],t.category.map(function(t){t.name&&e.tags.push(t.name)})),t.extensions&&t.extensions.map(function(t){e[t.name]=t.objects}),e}),JSON.stringify(r,null,4)}},57078:function(t,e,i){"use strict";var n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var r=i(40880),s=i(78906),a=i(82276);e.default=function(t){var e=t.options,i=!1,n=!1,c={_declaration:{_attributes:{version:"1.0",encoding:"utf-8"}},rss:{_attributes:{version:"2.0"},channel:{title:{_text:e.title},link:{_text:a.sanitize(e.link)},description:{_text:e.description},lastBuildDate:{_text:e.updated?e.updated.toUTCString():new Date().toUTCString()},docs:{_text:e.docs?e.docs:"https://validator.w3.org/feed/docs/rss2.html"},generator:{_text:e.generator||s.generator}}}};e.language&&(c.rss.channel.language={_text:e.language}),e.ttl&&(c.rss.channel.ttl={_text:e.ttl}),e.image&&(c.rss.channel.image={title:{_text:e.title},url:{_text:e.image},link:{_text:a.sanitize(e.link)}}),e.copyright&&(c.rss.channel.copyright={_text:e.copyright}),t.categories.map(function(t){c.rss.channel.category||(c.rss.channel.category=[]),c.rss.channel.category.push({_text:t})});var h=e.feed||e.feedLinks&&e.feedLinks.rss;return h&&(i=!0,c.rss.channel["atom:link"]=[{_attributes:{href:a.sanitize(h),rel:"self",type:"application/rss+xml"}}]),e.hub&&(i=!0,c.rss.channel["atom:link"]||(c.rss.channel["atom:link"]=[]),c.rss.channel["atom:link"]={_attributes:{href:a.sanitize(e.hub),rel:"hub"}}),c.rss.channel.item=[],t.items.map(function(t){var e={};t.title&&(e.title={_cdata:t.title}),t.link&&(e.link={_text:a.sanitize(t.link)}),t.guid?e.guid={_text:t.guid}:t.id?e.guid={_text:t.id}:t.link&&(e.guid={_text:a.sanitize(t.link)}),t.date&&(e.pubDate={_text:t.date.toUTCString()}),t.published&&(e.pubDate={_text:t.published.toUTCString()}),t.description&&(e.description={_cdata:t.description}),t.content&&(n=!0,e["content:encoded"]={_cdata:t.content}),Array.isArray(t.author)&&(e.author=[],t.author.map(function(t){t.email&&t.name&&e.author.push({_text:t.email+" ("+t.name+")"})})),Array.isArray(t.category)&&(e.category=[],t.category.map(function(t){e.category.push(u(t))})),t.enclosure&&(e.enclosure=o(t.enclosure)),t.image&&(e.enclosure=o(t.image,"image")),t.audio&&(e.enclosure=o(t.audio,"audio")),t.video&&(e.enclosure=o(t.video,"video")),c.rss.channel.item.push(e)}),n&&(c.rss._attributes["xmlns:dc"]="http://purl.org/dc/elements/1.1/",c.rss._attributes["xmlns:content"]="http://purl.org/rss/1.0/modules/content/"),i&&(c.rss._attributes["xmlns:atom"]="http://www.w3.org/2005/Atom"),r.js2xml(c,{compact:!0,ignoreComment:!0,spaces:4})};var o=function(t,e){if(void 0===e&&(e="image"),"string"==typeof t){var i=new URL(t).pathname.split(".").slice(-1)[0];return{_attributes:{url:t,length:0,type:e+"/"+i}}}var r=new URL(t.url).pathname.split(".").slice(-1)[0];return{_attributes:n({length:0,type:e+"/"+r},t)}},u=function(t){return{_text:t.name,_attributes:{domain:t.domain}}}},82276:(t,e)=>{"use strict";function i(t){if(void 0!==t)return t.replace(/&/g,"&amp;")}Object.defineProperty(e,"__esModule",{value:!0}),e.sanitize=void 0,e.sanitize=i},3673:(t,e,i)=>{"use strict";let n=i(92048),r=i(15592),s=i(41340),a=i(3310),o=i(64341),u=i(61661),c=i(29722),h=i(97929),l=i(91126);function p(t,e){if(""===t)return{data:{},content:t,excerpt:"",orig:t};let i=c(t),n=p.cache[i.content];if(!e){if(n)return(i=Object.assign({},n)).orig=n.orig,i;p.cache[i.content]=i}return d(i,e)}function d(t,e){let i=s(e),n=i.delimiters[0],a="\n"+i.delimiters[1],u=t.content;i.language&&(t.language=i.language);let c=n.length;if(!l.startsWith(u,n,c))return o(t,i),t;if(u.charAt(c)===n.slice(-1))return t;let d=(u=u.slice(c)).length,f=p.language(u,i);f.name&&(t.language=f.name,u=u.slice(f.raw.length));let m=u.indexOf(a);return -1===m&&(m=d),t.matter=u.slice(0,m),""===t.matter.replace(/^\s*#[^\n]+/gm,"").trim()?(t.isEmpty=!0,t.empty=t.content,t.data={}):t.data=h(t.language,t.matter,i),m===d?t.content="":(t.content=u.slice(m+a.length),"\r"===t.content[0]&&(t.content=t.content.slice(1)),"\n"===t.content[0]&&(t.content=t.content.slice(1))),o(t,i),(!0===i.sections||"function"==typeof i.section)&&r(t,i.section),t}p.engines=u,p.stringify=function(t,e,i){return"string"==typeof t&&(t=p(t,i)),a(t,e,i)},p.read=function(t,e){let i=p(n.readFileSync(t,"utf8"),e);return i.path=t,i},p.test=function(t,e){return l.startsWith(t,s(e).delimiters[0])},p.language=function(t,e){let i=s(e).delimiters[0];p.test(t)&&(t=t.slice(i.length));let n=t.slice(0,t.search(/\r?\n/));return{raw:n,name:n?n.trim():""}},p.cache={},p.clearCache=function(){p.cache={}},t.exports=p},41340:(t,e,i)=>{"use strict";let n=i(61661),r=i(91126);t.exports=function(t){let e=Object.assign({},t);return e.delimiters=r.arrayify(e.delims||e.delimiters||"---"),1===e.delimiters.length&&e.delimiters.push(e.delimiters[0]),e.language=(e.language||e.lang||"yaml").toLowerCase(),e.engines=Object.assign({},n,e.parsers,e.engines),e}},62853:t=>{"use strict";function e(t){switch(t.toLowerCase()){case"js":case"javascript":return"javascript";case"coffee":case"coffeescript":case"cson":return"coffee";case"yaml":case"yml":return"yaml";default:return t}}t.exports=function(t,i){let n=i.engines[t]||i.engines[e(t)];if(void 0===n)throw Error('gray-matter engine "'+t+'" is not registered');return"function"==typeof n&&(n={parse:n}),n}},61661:(module,exports,__webpack_require__)=>{"use strict";let yaml=__webpack_require__(39297),engines=exports=module.exports;engines.yaml={parse:yaml.safeLoad.bind(yaml),stringify:yaml.safeDump.bind(yaml)},engines.json={parse:JSON.parse.bind(JSON),stringify:function(t,e){let i=Object.assign({replacer:null,space:2},e);return JSON.stringify(t,i.replacer,i.space)}},engines.javascript={parse:function parse(str,options,wrap){try{return!1!==wrap&&(str="(function() {\nreturn "+str.trim()+";\n}());"),eval(str)||{}}catch(err){if(!1!==wrap&&/(unexpected|identifier)/i.test(err.message))return parse(str,options,!1);throw SyntaxError(err)}},stringify:function(){throw Error("stringifying JavaScript is not supported")}}},64341:(t,e,i)=>{"use strict";let n=i(41340);t.exports=function(t,e){let i=n(e);if(null==t.data&&(t.data={}),"function"==typeof i.excerpt)return i.excerpt(t,i);let r=t.data.excerpt_separator||i.excerpt_separator;if(null==r&&(!1===i.excerpt||null==i.excerpt))return t;let s="string"==typeof i.excerpt?i.excerpt:r||i.delimiters[0],a=t.content.indexOf(s);return -1!==a&&(t.excerpt=t.content.slice(0,a)),t}},97929:(t,e,i)=>{"use strict";let n=i(62853),r=i(41340);t.exports=function(t,e,i){let s=r(i),a=n(t,s);if("function"!=typeof a.parse)throw TypeError('expected "'+t+'.parse" to be a function');return a.parse(e,s)}},3310:(t,e,i)=>{"use strict";let n=i(38742),r=i(62853),s=i(41340);function a(t){return"\n"!==t.slice(-1)?t+"\n":t}t.exports=function(t,e,i){if(null==e&&null==i)switch(n(t)){case"object":e=t.data,i={};break;case"string":return t;default:throw TypeError("expected file to be a string or object")}let o=t.content,u=s(i);if(null==e){if(!u.data)return t;e=u.data}let c=t.language||u.language,h=r(c,u);if("function"!=typeof h.stringify)throw TypeError('expected "'+c+'.stringify" to be a function');e=Object.assign({},t.data,e);let l=u.delimiters[0],p=u.delimiters[1],d=h.stringify(e,i).trim(),f="";return"{}"!==d&&(f=a(l)+a(d)+a(p)),"string"==typeof t.excerpt&&""!==t.excerpt&&-1===o.indexOf(t.excerpt.trim())&&(f+=a(t.excerpt)+a(p)),f+a(o)}},29722:(t,e,i)=>{"use strict";let n=i(38742),r=i(3310),s=i(91126);t.exports=function(t){return"object"!==n(t)&&(t={content:t}),"object"!==n(t.data)&&(t.data={}),t.contents&&null==t.content&&(t.content=t.contents),s.define(t,"orig",s.toBuffer(t.content)),s.define(t,"language",t.language||""),s.define(t,"matter",t.matter||""),s.define(t,"stringify",function(e,i){return i&&i.language&&(t.language=i.language),r(t,e,i)}),t.content=s.toString(t.content),t.isEmpty=!1,t.excerpt="",t}},91126:(t,e,i)=>{"use strict";let n=i(18636),r=i(38742);e.define=function(t,e,i){Reflect.defineProperty(t,e,{enumerable:!1,configurable:!0,writable:!0,value:i})},e.isBuffer=function(t){return"buffer"===r(t)},e.isObject=function(t){return"object"===r(t)},e.toBuffer=function(t){return"string"==typeof t?Buffer.from(t):t},e.toString=function(t){if(e.isBuffer(t))return n(String(t));if("string"!=typeof t)throw TypeError("expected input to be a string or buffer");return n(t)},e.arrayify=function(t){return t?Array.isArray(t)?t:[t]:[]},e.startsWith=function(t,e,i){return"number"!=typeof i&&(i=e.length),t.slice(0,i)===e}},39297:(t,e,i)=>{"use strict";var n=i(78261);t.exports=n},78261:(t,e,i)=>{"use strict";var n=i(15668),r=i(94086);function s(t){return function(){throw Error("Function "+t+" is deprecated and cannot be used.")}}t.exports.Type=i(89208),t.exports.Schema=i(91433),t.exports.FAILSAFE_SCHEMA=i(28534),t.exports.JSON_SCHEMA=i(57889),t.exports.CORE_SCHEMA=i(89847),t.exports.DEFAULT_SAFE_SCHEMA=i(17100),t.exports.DEFAULT_FULL_SCHEMA=i(96810),t.exports.load=n.load,t.exports.loadAll=n.loadAll,t.exports.safeLoad=n.safeLoad,t.exports.safeLoadAll=n.safeLoadAll,t.exports.dump=r.dump,t.exports.safeDump=r.safeDump,t.exports.YAMLException=i(51928),t.exports.MINIMAL_SCHEMA=i(28534),t.exports.SAFE_SCHEMA=i(17100),t.exports.DEFAULT_SCHEMA=i(96810),t.exports.scan=s("scan"),t.exports.parse=s("parse"),t.exports.compose=s("compose"),t.exports.addConstructor=s("addConstructor")},48719:t=>{"use strict";function e(t){return null==t}function i(t){return"object"==typeof t&&null!==t}function n(t){return Array.isArray(t)?t:e(t)?[]:[t]}function r(t,e){var i,n,r,s;if(e)for(i=0,n=(s=Object.keys(e)).length;i<n;i+=1)t[r=s[i]]=e[r];return t}function s(t,e){var i,n="";for(i=0;i<e;i+=1)n+=t;return n}function a(t){return 0===t&&Number.NEGATIVE_INFINITY===1/t}t.exports.isNothing=e,t.exports.isObject=i,t.exports.toArray=n,t.exports.repeat=s,t.exports.isNegativeZero=a,t.exports.extend=r},94086:(t,e,i)=>{"use strict";var n=i(48719),r=i(51928),s=i(96810),a=i(17100),o=Object.prototype.toString,u=Object.prototype.hasOwnProperty,c=9,h=10,l=13,p=32,d=33,f=34,m=35,x=37,D=38,y=39,g=42,E=44,A=45,C=58,v=61,F=62,S=63,b=64,k=91,w=93,T=96,N=123,B=124,I=125,M={};M[0]="\\0",M[7]="\\a",M[8]="\\b",M[9]="\\t",M[10]="\\n",M[11]="\\v",M[12]="\\f",M[13]="\\r",M[27]="\\e",M[34]='\\"',M[92]="\\\\",M[133]="\\N",M[160]="\\_",M[8232]="\\L",M[8233]="\\P";var O=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"];function P(t,e){var i,n,r,s,a,o,c;if(null===e)return{};for(r=0,i={},s=(n=Object.keys(e)).length;r<s;r+=1)o=String(e[a=n[r]]),"!!"===a.slice(0,2)&&(a="tag:yaml.org,2002:"+a.slice(2)),(c=t.compiledTypeMap.fallback[a])&&u.call(c.styleAliases,o)&&(o=c.styleAliases[o]),i[a]=o;return i}function _(t){var e,i,s;if(e=t.toString(16).toUpperCase(),t<=255)i="x",s=2;else if(t<=65535)i="u",s=4;else if(t<=4294967295)i="U",s=8;else throw new r("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+i+n.repeat("0",s-e.length)+e}function L(t){this.schema=t.schema||s,this.indent=Math.max(1,t.indent||2),this.noArrayIndent=t.noArrayIndent||!1,this.skipInvalid=t.skipInvalid||!1,this.flowLevel=n.isNothing(t.flowLevel)?-1:t.flowLevel,this.styleMap=P(this.schema,t.styles||null),this.sortKeys=t.sortKeys||!1,this.lineWidth=t.lineWidth||80,this.noRefs=t.noRefs||!1,this.noCompatMode=t.noCompatMode||!1,this.condenseFlow=t.condenseFlow||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function U(t,e){for(var i,r=n.repeat(" ",e),s=0,a=-1,o="",u=t.length;s<u;)-1===(a=t.indexOf("\n",s))?(i=t.slice(s),s=u):(i=t.slice(s,a+1),s=a+1),i.length&&"\n"!==i&&(o+=r),o+=i;return o}function j(t,e){return"\n"+n.repeat(" ",t.indent*e)}function X(t,e){var i,n;for(i=0,n=t.implicitTypes.length;i<n;i+=1)if(t.implicitTypes[i].resolve(e))return!0;return!1}function K(t){return t===p||t===c}function R(t){return 32<=t&&t<=126||161<=t&&t<=55295&&8232!==t&&8233!==t||57344<=t&&t<=65533&&65279!==t||65536<=t&&t<=1114111}function J(t){return R(t)&&!K(t)&&65279!==t&&t!==l&&t!==h}function z(t,e){return R(t)&&65279!==t&&t!==E&&t!==k&&t!==w&&t!==N&&t!==I&&t!==C&&(t!==m||e&&J(e))}function G(t){return R(t)&&65279!==t&&!K(t)&&t!==A&&t!==S&&t!==C&&t!==E&&t!==k&&t!==w&&t!==N&&t!==I&&t!==m&&t!==D&&t!==g&&t!==d&&t!==B&&t!==v&&t!==F&&t!==y&&t!==f&&t!==x&&t!==b&&t!==T}function V(t){return/^\n* /.test(t)}var Y=1,H=2,W=3,q=4,$=5;function Q(t,e,i,n,r){var s,a,o,u=!1,c=!1,l=-1!==n,p=-1,d=G(t.charCodeAt(0))&&!K(t.charCodeAt(t.length-1));if(e)for(s=0;s<t.length;s++){if(!R(a=t.charCodeAt(s)))return $;o=s>0?t.charCodeAt(s-1):null,d=d&&z(a,o)}else{for(s=0;s<t.length;s++){if((a=t.charCodeAt(s))===h)u=!0,l&&(c=c||s-p-1>n&&" "!==t[p+1],p=s);else if(!R(a))return $;o=s>0?t.charCodeAt(s-1):null,d=d&&z(a,o)}c=c||l&&s-p-1>n&&" "!==t[p+1]}return u||c?i>9&&V(t)?$:c?q:W:d&&!r(t)?Y:H}function Z(t,e,i,n){t.dump=function(){if(0===e.length)return"''";if(!t.noCompatMode&&-1!==O.indexOf(e))return"'"+e+"'";var s=t.indent*Math.max(1,i),a=-1===t.lineWidth?-1:Math.max(Math.min(t.lineWidth,40),t.lineWidth-s);function o(e){return X(t,e)}switch(Q(e,n||t.flowLevel>-1&&i>=t.flowLevel,t.indent,a,o)){case Y:return e;case H:return"'"+e.replace(/'/g,"''")+"'";case W:return"|"+tt(e,t.indent)+te(U(e,s));case q:return">"+tt(e,t.indent)+te(U(ti(e,a),s));case $:return'"'+tr(e,a)+'"';default:throw new r("impossible error: invalid scalar style")}}()}function tt(t,e){var i=V(t)?String(e):"",n="\n"===t[t.length-1];return i+(n&&("\n"===t[t.length-2]||"\n"===t)?"+":n?"":"-")+"\n"}function te(t){return"\n"===t[t.length-1]?t.slice(0,-1):t}function ti(t,e){for(var i,n,r=/(\n+)([^\n]*)/g,s=function(){var i=t.indexOf("\n");return i=-1!==i?i:t.length,r.lastIndex=i,tn(t.slice(0,i),e)}(),a="\n"===t[0]||" "===t[0];n=r.exec(t);){var o=n[1],u=n[2];i=" "===u[0],s+=o+(a||i||""===u?"":"\n")+tn(u,e),a=i}return s}function tn(t,e){if(""===t||" "===t[0])return t;for(var i,n,r=/ [^ ]/g,s=0,a=0,o=0,u="";i=r.exec(t);)(o=i.index)-s>e&&(n=a>s?a:o,u+="\n"+t.slice(s,n),s=n+1),a=o;return u+="\n",t.length-s>e&&a>s?u+=t.slice(s,a)+"\n"+t.slice(a+1):u+=t.slice(s),u.slice(1)}function tr(t){for(var e,i,n,r="",s=0;s<t.length;s++){if((e=t.charCodeAt(s))>=55296&&e<=56319&&(i=t.charCodeAt(s+1))>=56320&&i<=57343){r+=_((e-55296)*1024+i-56320+65536),s++;continue}r+=!(n=M[e])&&R(e)?t[s]:n||_(e)}return r}function ts(t,e,i){var n,r,s="",a=t.tag;for(n=0,r=i.length;n<r;n+=1)th(t,e,i[n],!1,!1)&&(0!==n&&(s+=","+(t.condenseFlow?"":" ")),s+=t.dump);t.tag=a,t.dump="["+s+"]"}function ta(t,e,i,n){var r,s,a="",o=t.tag;for(r=0,s=i.length;r<s;r+=1)th(t,e+1,i[r],!0,!0)&&(n&&0===r||(a+=j(t,e)),t.dump&&h===t.dump.charCodeAt(0)?a+="-":a+="- ",a+=t.dump);t.tag=o,t.dump=a||"[]"}function to(t,e,i){var n,r,s,a,o,u="",c=t.tag,h=Object.keys(i);for(n=0,r=h.length;n<r;n+=1)o="",0!==n&&(o+=", "),t.condenseFlow&&(o+='"'),a=i[s=h[n]],th(t,e,s,!1,!1)&&(t.dump.length>1024&&(o+="? "),o+=t.dump+(t.condenseFlow?'"':"")+":"+(t.condenseFlow?"":" "),th(t,e,a,!1,!1)&&(o+=t.dump,u+=o));t.tag=c,t.dump="{"+u+"}"}function tu(t,e,i,n){var s,a,o,u,c,l,p="",d=t.tag,f=Object.keys(i);if(!0===t.sortKeys)f.sort();else if("function"==typeof t.sortKeys)f.sort(t.sortKeys);else if(t.sortKeys)throw new r("sortKeys must be a boolean or a function");for(s=0,a=f.length;s<a;s+=1)l="",n&&0===s||(l+=j(t,e)),u=i[o=f[s]],th(t,e+1,o,!0,!0,!0)&&((c=null!==t.tag&&"?"!==t.tag||t.dump&&t.dump.length>1024)&&(t.dump&&h===t.dump.charCodeAt(0)?l+="?":l+="? "),l+=t.dump,c&&(l+=j(t,e)),th(t,e+1,u,!0,c)&&(t.dump&&h===t.dump.charCodeAt(0)?l+=":":l+=": ",l+=t.dump,p+=l));t.tag=d,t.dump=p||"{}"}function tc(t,e,i){var n,s,a,c,h,l;for(a=0,c=(s=i?t.explicitTypes:t.implicitTypes).length;a<c;a+=1)if(((h=s[a]).instanceOf||h.predicate)&&(!h.instanceOf||"object"==typeof e&&e instanceof h.instanceOf)&&(!h.predicate||h.predicate(e))){if(t.tag=i?h.tag:"?",h.represent){if(l=t.styleMap[h.tag]||h.defaultStyle,"[object Function]"===o.call(h.represent))n=h.represent(e,l);else if(u.call(h.represent,l))n=h.represent[l](e,l);else throw new r("!<"+h.tag+'> tag resolver accepts not "'+l+'" style');t.dump=n}return!0}return!1}function th(t,e,i,n,s,a){t.tag=null,t.dump=i,tc(t,i,!1)||tc(t,i,!0);var u=o.call(t.dump);n&&(n=t.flowLevel<0||t.flowLevel>e);var c,h,l="[object Object]"===u||"[object Array]"===u;if(l&&(h=-1!==(c=t.duplicates.indexOf(i))),(null!==t.tag&&"?"!==t.tag||h||2!==t.indent&&e>0)&&(s=!1),h&&t.usedDuplicates[c])t.dump="*ref_"+c;else{if(l&&h&&!t.usedDuplicates[c]&&(t.usedDuplicates[c]=!0),"[object Object]"===u)n&&0!==Object.keys(t.dump).length?(tu(t,e,t.dump,s),h&&(t.dump="&ref_"+c+t.dump)):(to(t,e,t.dump),h&&(t.dump="&ref_"+c+" "+t.dump));else if("[object Array]"===u){var p=t.noArrayIndent&&e>0?e-1:e;n&&0!==t.dump.length?(ta(t,p,t.dump,s),h&&(t.dump="&ref_"+c+t.dump)):(ts(t,p,t.dump),h&&(t.dump="&ref_"+c+" "+t.dump))}else if("[object String]"===u)"?"!==t.tag&&Z(t,t.dump,e,a);else{if(t.skipInvalid)return!1;throw new r("unacceptable kind of an object to dump "+u)}null!==t.tag&&"?"!==t.tag&&(t.dump="!<"+t.tag+"> "+t.dump)}return!0}function tl(t,e){var i,n,r=[],s=[];for(tp(t,r,s),i=0,n=s.length;i<n;i+=1)e.duplicates.push(r[s[i]]);e.usedDuplicates=Array(n)}function tp(t,e,i){var n,r,s;if(null!==t&&"object"==typeof t){if(-1!==(r=e.indexOf(t)))-1===i.indexOf(r)&&i.push(r);else if(e.push(t),Array.isArray(t))for(r=0,s=t.length;r<s;r+=1)tp(t[r],e,i);else for(r=0,s=(n=Object.keys(t)).length;r<s;r+=1)tp(t[n[r]],e,i)}}function td(t,e){var i=new L(e=e||{});return(i.noRefs||tl(t,i),th(i,0,t,!0,!0))?i.dump+"\n":""}function tf(t,e){return td(t,n.extend({schema:a},e))}t.exports.dump=td,t.exports.safeDump=tf},51928:t=>{"use strict";function e(t,e){Error.call(this),this.name="YAMLException",this.reason=t,this.mark=e,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():""),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack||""}e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e.prototype.toString=function(t){var e=this.name+": ";return e+=this.reason||"(unknown reason)",!t&&this.mark&&(e+=" "+this.mark.toString()),e},t.exports=e},15668:(t,e,i)=>{"use strict";var n=i(48719),r=i(51928),s=i(93649),a=i(17100),o=i(96810),u=Object.prototype.hasOwnProperty,c=1,h=2,l=3,p=4,d=1,f=2,m=3,x=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,D=/[\x85\u2028\u2029]/,y=/[,\[\]\{\}]/,g=/^(?:!|!!|![a-z\-]+!)$/i,E=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function A(t){return Object.prototype.toString.call(t)}function C(t){return 10===t||13===t}function v(t){return 9===t||32===t}function F(t){return 9===t||32===t||10===t||13===t}function S(t){return 44===t||91===t||93===t||123===t||125===t}function b(t){var e;return 48<=t&&t<=57?t-48:97<=(e=32|t)&&e<=102?e-97+10:-1}function k(t){return 120===t?2:117===t?4:85===t?8:0}function w(t){return 48<=t&&t<=57?t-48:-1}function T(t){return 48===t?"\0":97===t?"\x07":98===t?"\b":116===t?"	":9===t?"	":110===t?"\n":118===t?"\v":102===t?"\f":114===t?"\r":101===t?"\x1b":32===t?" ":34===t?'"':47===t?"/":92===t?"\\":78===t?"\x85":95===t?"\xa0":76===t?"\u2028":80===t?"\u2029":""}function N(t){return t<=65535?String.fromCharCode(t):String.fromCharCode((t-65536>>10)+55296,(t-65536&1023)+56320)}for(var B=Array(256),I=Array(256),M=0;M<256;M++)B[M]=T(M)?1:0,I[M]=T(M);function O(t,e){this.input=t,this.filename=e.filename||null,this.schema=e.schema||o,this.onWarning=e.onWarning||null,this.legacy=e.legacy||!1,this.json=e.json||!1,this.listener=e.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=t.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function P(t,e){return new r(e,new s(t.filename,t.input,t.position,t.line,t.position-t.lineStart))}function _(t,e){throw P(t,e)}function L(t,e){t.onWarning&&t.onWarning.call(null,P(t,e))}var U={YAML:function(t,e,i){var n,r,s;null!==t.version&&_(t,"duplication of %YAML directive"),1!==i.length&&_(t,"YAML directive accepts exactly one argument"),null===(n=/^([0-9]+)\.([0-9]+)$/.exec(i[0]))&&_(t,"ill-formed argument of the YAML directive"),r=parseInt(n[1],10),s=parseInt(n[2],10),1!==r&&_(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=s<2,1!==s&&2!==s&&L(t,"unsupported YAML version of the document")},TAG:function(t,e,i){var n,r;2!==i.length&&_(t,"TAG directive accepts exactly two arguments"),n=i[0],r=i[1],g.test(n)||_(t,"ill-formed tag handle (first argument) of the TAG directive"),u.call(t.tagMap,n)&&_(t,'there is a previously declared suffix for "'+n+'" tag handle'),E.test(r)||_(t,"ill-formed tag prefix (second argument) of the TAG directive"),t.tagMap[n]=r}};function j(t,e,i,n){var r,s,a,o;if(e<i){if(o=t.input.slice(e,i),n)for(r=0,s=o.length;r<s;r+=1)9===(a=o.charCodeAt(r))||32<=a&&a<=1114111||_(t,"expected valid JSON character");else x.test(o)&&_(t,"the stream contains non-printable characters");t.result+=o}}function X(t,e,i,r){var s,a,o,c;for(n.isObject(i)||_(t,"cannot merge mappings; the provided source object is unacceptable"),o=0,c=(s=Object.keys(i)).length;o<c;o+=1)a=s[o],u.call(e,a)||(e[a]=i[a],r[a]=!0)}function K(t,e,i,n,r,s,a,o){var c,h;if(Array.isArray(r))for(c=0,h=(r=Array.prototype.slice.call(r)).length;c<h;c+=1)Array.isArray(r[c])&&_(t,"nested arrays are not supported inside keys"),"object"==typeof r&&"[object Object]"===A(r[c])&&(r[c]="[object Object]");if("object"==typeof r&&"[object Object]"===A(r)&&(r="[object Object]"),r=String(r),null===e&&(e={}),"tag:yaml.org,2002:merge"===n){if(Array.isArray(s))for(c=0,h=s.length;c<h;c+=1)X(t,e,s[c],i);else X(t,e,s,i)}else!t.json&&!u.call(i,r)&&u.call(e,r)&&(t.line=a||t.line,t.position=o||t.position,_(t,"duplicated mapping key")),e[r]=s,delete i[r];return e}function R(t){var e;10===(e=t.input.charCodeAt(t.position))?t.position++:13===e?(t.position++,10===t.input.charCodeAt(t.position)&&t.position++):_(t,"a line break is expected"),t.line+=1,t.lineStart=t.position}function J(t,e,i){for(var n=0,r=t.input.charCodeAt(t.position);0!==r;){for(;v(r);)r=t.input.charCodeAt(++t.position);if(e&&35===r)do r=t.input.charCodeAt(++t.position);while(10!==r&&13!==r&&0!==r);if(C(r))for(R(t),r=t.input.charCodeAt(t.position),n++,t.lineIndent=0;32===r;)t.lineIndent++,r=t.input.charCodeAt(++t.position);else break}return -1!==i&&0!==n&&t.lineIndent<i&&L(t,"deficient indentation"),n}function z(t){var e,i=t.position;return!!((45===(e=t.input.charCodeAt(i))||46===e)&&e===t.input.charCodeAt(i+1)&&e===t.input.charCodeAt(i+2)&&(i+=3,0===(e=t.input.charCodeAt(i))||F(e)))}function G(t,e){1===e?t.result+=" ":e>1&&(t.result+=n.repeat("\n",e-1))}function V(t,e,i){var n,r,s,a,o,u,c,h,l=t.kind,p=t.result;if(F(h=t.input.charCodeAt(t.position))||S(h)||35===h||38===h||42===h||33===h||124===h||62===h||39===h||34===h||37===h||64===h||96===h||(63===h||45===h)&&(F(n=t.input.charCodeAt(t.position+1))||i&&S(n)))return!1;for(t.kind="scalar",t.result="",r=s=t.position,a=!1;0!==h;){if(58===h){if(F(n=t.input.charCodeAt(t.position+1))||i&&S(n))break}else if(35===h){if(F(t.input.charCodeAt(t.position-1)))break}else if(t.position===t.lineStart&&z(t)||i&&S(h))break;else if(C(h)){if(o=t.line,u=t.lineStart,c=t.lineIndent,J(t,!1,-1),t.lineIndent>=e){a=!0,h=t.input.charCodeAt(t.position);continue}t.position=s,t.line=o,t.lineStart=u,t.lineIndent=c;break}a&&(j(t,r,s,!1),G(t,t.line-o),r=s=t.position,a=!1),v(h)||(s=t.position+1),h=t.input.charCodeAt(++t.position)}return j(t,r,s,!1),!!t.result||(t.kind=l,t.result=p,!1)}function Y(t,e){var i,n,r;if(39!==(i=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,n=r=t.position;0!==(i=t.input.charCodeAt(t.position));)if(39===i){if(j(t,n,t.position,!0),39!==(i=t.input.charCodeAt(++t.position)))return!0;n=t.position,t.position++,r=t.position}else C(i)?(j(t,n,r,!0),G(t,J(t,!1,e)),n=r=t.position):t.position===t.lineStart&&z(t)?_(t,"unexpected end of the document within a single quoted scalar"):(t.position++,r=t.position);_(t,"unexpected end of the stream within a single quoted scalar")}function H(t,e){var i,n,r,s,a,o;if(34!==(o=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,i=n=t.position;0!==(o=t.input.charCodeAt(t.position));){if(34===o)return j(t,i,t.position,!0),t.position++,!0;if(92===o){if(j(t,i,t.position,!0),C(o=t.input.charCodeAt(++t.position)))J(t,!1,e);else if(o<256&&B[o])t.result+=I[o],t.position++;else if((a=k(o))>0){for(r=a,s=0;r>0;r--)(a=b(o=t.input.charCodeAt(++t.position)))>=0?s=(s<<4)+a:_(t,"expected hexadecimal character");t.result+=N(s),t.position++}else _(t,"unknown escape sequence");i=n=t.position}else C(o)?(j(t,i,n,!0),G(t,J(t,!1,e)),i=n=t.position):t.position===t.lineStart&&z(t)?_(t,"unexpected end of the document within a double quoted scalar"):(t.position++,n=t.position)}_(t,"unexpected end of the stream within a double quoted scalar")}function W(t,e){var i,n,r,s,a,o,u,h,l,p,d=!0,f=t.tag,m=t.anchor,x={};if(91===(p=t.input.charCodeAt(t.position)))r=93,o=!1,n=[];else{if(123!==p)return!1;r=125,o=!0,n={}}for(null!==t.anchor&&(t.anchorMap[t.anchor]=n),p=t.input.charCodeAt(++t.position);0!==p;){if(J(t,!0,e),(p=t.input.charCodeAt(t.position))===r)return t.position++,t.tag=f,t.anchor=m,t.kind=o?"mapping":"sequence",t.result=n,!0;d||_(t,"missed comma between flow collection entries"),h=u=l=null,s=a=!1,63===p&&F(t.input.charCodeAt(t.position+1))&&(s=a=!0,t.position++,J(t,!0,e)),i=t.line,ti(t,e,c,!1,!0),h=t.tag,u=t.result,J(t,!0,e),p=t.input.charCodeAt(t.position),(a||t.line===i)&&58===p&&(s=!0,p=t.input.charCodeAt(++t.position),J(t,!0,e),ti(t,e,c,!1,!0),l=t.result),o?K(t,n,x,h,u,l):s?n.push(K(t,null,x,h,u,l)):n.push(u),J(t,!0,e),44===(p=t.input.charCodeAt(t.position))?(d=!0,p=t.input.charCodeAt(++t.position)):d=!1}_(t,"unexpected end of the stream within a flow collection")}function q(t,e){var i,r,s,a,o=d,u=!1,c=!1,h=e,l=0,p=!1;if(124===(a=t.input.charCodeAt(t.position)))r=!1;else{if(62!==a)return!1;r=!0}for(t.kind="scalar",t.result="";0!==a;)if(43===(a=t.input.charCodeAt(++t.position))||45===a)d===o?o=43===a?m:f:_(t,"repeat of a chomping mode identifier");else if((s=w(a))>=0)0===s?_(t,"bad explicit indentation width of a block scalar; it cannot be less than one"):c?_(t,"repeat of an indentation width identifier"):(h=e+s-1,c=!0);else break;if(v(a)){do a=t.input.charCodeAt(++t.position);while(v(a));if(35===a)do a=t.input.charCodeAt(++t.position);while(!C(a)&&0!==a)}for(;0!==a;){for(R(t),t.lineIndent=0,a=t.input.charCodeAt(t.position);(!c||t.lineIndent<h)&&32===a;)t.lineIndent++,a=t.input.charCodeAt(++t.position);if(!c&&t.lineIndent>h&&(h=t.lineIndent),C(a)){l++;continue}if(t.lineIndent<h){o===m?t.result+=n.repeat("\n",u?1+l:l):o===d&&u&&(t.result+="\n");break}for(r?v(a)?(p=!0,t.result+=n.repeat("\n",u?1+l:l)):p?(p=!1,t.result+=n.repeat("\n",l+1)):0===l?u&&(t.result+=" "):t.result+=n.repeat("\n",l):t.result+=n.repeat("\n",u?1+l:l),u=!0,c=!0,l=0,i=t.position;!C(a)&&0!==a;)a=t.input.charCodeAt(++t.position);j(t,i,t.position,!1)}return!0}function $(t,e){var i,n,r=t.tag,s=t.anchor,a=[],o=!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=a),n=t.input.charCodeAt(t.position);0!==n&&45===n&&F(t.input.charCodeAt(t.position+1));){if(o=!0,t.position++,J(t,!0,-1)&&t.lineIndent<=e){a.push(null),n=t.input.charCodeAt(t.position);continue}if(i=t.line,ti(t,e,l,!1,!0),a.push(t.result),J(t,!0,-1),n=t.input.charCodeAt(t.position),(t.line===i||t.lineIndent>e)&&0!==n)_(t,"bad indentation of a sequence entry");else if(t.lineIndent<e)break}return!!o&&(t.tag=r,t.anchor=s,t.kind="sequence",t.result=a,!0)}function Q(t,e,i){var n,r,s,a,o,u=t.tag,c=t.anchor,l={},d={},f=null,m=null,x=null,D=!1,y=!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=l),o=t.input.charCodeAt(t.position);0!==o;){if(n=t.input.charCodeAt(t.position+1),s=t.line,a=t.position,(63===o||58===o)&&F(n))63===o?(D&&(K(t,l,d,f,m,null),f=m=x=null),y=!0,D=!0,r=!0):D?(D=!1,r=!0):_(t,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),t.position+=1,o=n;else if(ti(t,i,h,!1,!0)){if(t.line===s){for(o=t.input.charCodeAt(t.position);v(o);)o=t.input.charCodeAt(++t.position);if(58===o)F(o=t.input.charCodeAt(++t.position))||_(t,"a whitespace character is expected after the key-value separator within a block mapping"),D&&(K(t,l,d,f,m,null),f=m=x=null),y=!0,D=!1,r=!1,f=t.tag,m=t.result;else{if(!y)return t.tag=u,t.anchor=c,!0;_(t,"can not read an implicit mapping pair; a colon is missed")}}else{if(!y)return t.tag=u,t.anchor=c,!0;_(t,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else break;if((t.line===s||t.lineIndent>e)&&(ti(t,e,p,!0,r)&&(D?m=t.result:x=t.result),D||(K(t,l,d,f,m,x,s,a),f=m=x=null),J(t,!0,-1),o=t.input.charCodeAt(t.position)),t.lineIndent>e&&0!==o)_(t,"bad indentation of a mapping entry");else if(t.lineIndent<e)break}return D&&K(t,l,d,f,m,null),y&&(t.tag=u,t.anchor=c,t.kind="mapping",t.result=l),y}function Z(t){var e,i,n,r,s=!1,a=!1;if(33!==(r=t.input.charCodeAt(t.position)))return!1;if(null!==t.tag&&_(t,"duplication of a tag property"),60===(r=t.input.charCodeAt(++t.position))?(s=!0,r=t.input.charCodeAt(++t.position)):33===r?(a=!0,i="!!",r=t.input.charCodeAt(++t.position)):i="!",e=t.position,s){do r=t.input.charCodeAt(++t.position);while(0!==r&&62!==r);t.position<t.length?(n=t.input.slice(e,t.position),r=t.input.charCodeAt(++t.position)):_(t,"unexpected end of the stream within a verbatim tag")}else{for(;0!==r&&!F(r);)33===r&&(a?_(t,"tag suffix cannot contain exclamation marks"):(i=t.input.slice(e-1,t.position+1),g.test(i)||_(t,"named tag handle cannot contain such characters"),a=!0,e=t.position+1)),r=t.input.charCodeAt(++t.position);n=t.input.slice(e,t.position),y.test(n)&&_(t,"tag suffix cannot contain flow indicator characters")}return n&&!E.test(n)&&_(t,"tag name cannot contain such characters: "+n),s?t.tag=n:u.call(t.tagMap,i)?t.tag=t.tagMap[i]+n:"!"===i?t.tag="!"+n:"!!"===i?t.tag="tag:yaml.org,2002:"+n:_(t,'undeclared tag handle "'+i+'"'),!0}function tt(t){var e,i;if(38!==(i=t.input.charCodeAt(t.position)))return!1;for(null!==t.anchor&&_(t,"duplication of an anchor property"),i=t.input.charCodeAt(++t.position),e=t.position;0!==i&&!F(i)&&!S(i);)i=t.input.charCodeAt(++t.position);return t.position===e&&_(t,"name of an anchor node must contain at least one character"),t.anchor=t.input.slice(e,t.position),!0}function te(t){var e,i,n;if(42!==(n=t.input.charCodeAt(t.position)))return!1;for(n=t.input.charCodeAt(++t.position),e=t.position;0!==n&&!F(n)&&!S(n);)n=t.input.charCodeAt(++t.position);return t.position===e&&_(t,"name of an alias node must contain at least one character"),i=t.input.slice(e,t.position),u.call(t.anchorMap,i)||_(t,'unidentified alias "'+i+'"'),t.result=t.anchorMap[i],J(t,!0,-1),!0}function ti(t,e,i,n,r){var s,a,o,d,f,m,x,D,y=1,g=!1,E=!1;if(null!==t.listener&&t.listener("open",t),t.tag=null,t.anchor=null,t.kind=null,t.result=null,s=a=o=p===i||l===i,n&&J(t,!0,-1)&&(g=!0,t.lineIndent>e?y=1:t.lineIndent===e?y=0:t.lineIndent<e&&(y=-1)),1===y)for(;Z(t)||tt(t);)J(t,!0,-1)?(g=!0,o=s,t.lineIndent>e?y=1:t.lineIndent===e?y=0:t.lineIndent<e&&(y=-1)):o=!1;if(o&&(o=g||r),(1===y||p===i)&&(x=c===i||h===i?e:e+1,D=t.position-t.lineStart,1===y?o&&($(t,D)||Q(t,D,x))||W(t,x)?E=!0:(a&&q(t,x)||Y(t,x)||H(t,x)?E=!0:te(t)?(E=!0,(null!==t.tag||null!==t.anchor)&&_(t,"alias node should not have any properties")):V(t,x,c===i)&&(E=!0,null===t.tag&&(t.tag="?")),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):0===y&&(E=o&&$(t,D))),null!==t.tag&&"!"!==t.tag){if("?"===t.tag){for(null!==t.result&&"scalar"!==t.kind&&_(t,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+t.kind+'"'),d=0,f=t.implicitTypes.length;d<f;d+=1)if((m=t.implicitTypes[d]).resolve(t.result)){t.result=m.construct(t.result),t.tag=m.tag,null!==t.anchor&&(t.anchorMap[t.anchor]=t.result);break}}else u.call(t.typeMap[t.kind||"fallback"],t.tag)?(m=t.typeMap[t.kind||"fallback"][t.tag],null!==t.result&&m.kind!==t.kind&&_(t,"unacceptable node kind for !<"+t.tag+'> tag; it should be "'+m.kind+'", not "'+t.kind+'"'),m.resolve(t.result)?(t.result=m.construct(t.result),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):_(t,"cannot resolve a node with !<"+t.tag+"> explicit tag")):_(t,"unknown tag !<"+t.tag+">")}return null!==t.listener&&t.listener("close",t),null!==t.tag||null!==t.anchor||E}function tn(t){var e,i,n,r,s=t.position,a=!1;for(t.version=null,t.checkLineBreaks=t.legacy,t.tagMap={},t.anchorMap={};0!==(r=t.input.charCodeAt(t.position))&&(J(t,!0,-1),r=t.input.charCodeAt(t.position),!(t.lineIndent>0)&&37===r);){for(a=!0,r=t.input.charCodeAt(++t.position),e=t.position;0!==r&&!F(r);)r=t.input.charCodeAt(++t.position);for(i=t.input.slice(e,t.position),n=[],i.length<1&&_(t,"directive name must not be less than one character in length");0!==r;){for(;v(r);)r=t.input.charCodeAt(++t.position);if(35===r){do r=t.input.charCodeAt(++t.position);while(0!==r&&!C(r));break}if(C(r))break;for(e=t.position;0!==r&&!F(r);)r=t.input.charCodeAt(++t.position);n.push(t.input.slice(e,t.position))}0!==r&&R(t),u.call(U,i)?U[i](t,i,n):L(t,'unknown document directive "'+i+'"')}if(J(t,!0,-1),0===t.lineIndent&&45===t.input.charCodeAt(t.position)&&45===t.input.charCodeAt(t.position+1)&&45===t.input.charCodeAt(t.position+2)?(t.position+=3,J(t,!0,-1)):a&&_(t,"directives end mark is expected"),ti(t,t.lineIndent-1,p,!1,!0),J(t,!0,-1),t.checkLineBreaks&&D.test(t.input.slice(s,t.position))&&L(t,"non-ASCII line breaks are interpreted as content"),t.documents.push(t.result),t.position===t.lineStart&&z(t)){46===t.input.charCodeAt(t.position)&&(t.position+=3,J(t,!0,-1));return}t.position<t.length-1&&_(t,"end of the stream or a document separator is expected")}function tr(t,e){t=String(t),e=e||{},0!==t.length&&(10!==t.charCodeAt(t.length-1)&&13!==t.charCodeAt(t.length-1)&&(t+="\n"),65279===t.charCodeAt(0)&&(t=t.slice(1)));var i=new O(t,e),n=t.indexOf("\0");for(-1!==n&&(i.position=n,_(i,"null byte is not allowed in input")),i.input+="\0";32===i.input.charCodeAt(i.position);)i.lineIndent+=1,i.position+=1;for(;i.position<i.length-1;)tn(i);return i.documents}function ts(t,e,i){null!==e&&"object"==typeof e&&void 0===i&&(i=e,e=null);var n=tr(t,i);if("function"!=typeof e)return n;for(var r=0,s=n.length;r<s;r+=1)e(n[r])}function ta(t,e){var i=tr(t,e);if(0!==i.length){if(1===i.length)return i[0];throw new r("expected a single document in the stream, but found more")}}function to(t,e,i){return"object"==typeof e&&null!==e&&void 0===i&&(i=e,e=null),ts(t,e,n.extend({schema:a},i))}function tu(t,e){return ta(t,n.extend({schema:a},e))}t.exports.loadAll=ts,t.exports.load=ta,t.exports.safeLoadAll=to,t.exports.safeLoad=tu},93649:(t,e,i)=>{"use strict";var n=i(48719);function r(t,e,i,n,r){this.name=t,this.buffer=e,this.position=i,this.line=n,this.column=r}r.prototype.getSnippet=function(t,e){var i,r,s,a,o;if(!this.buffer)return null;for(t=t||4,e=e||75,i="",r=this.position;r>0&&-1==="\0\r\n\x85\u2028\u2029".indexOf(this.buffer.charAt(r-1));)if(r-=1,this.position-r>e/2-1){i=" ... ",r+=5;break}for(s="",a=this.position;a<this.buffer.length&&-1==="\0\r\n\x85\u2028\u2029".indexOf(this.buffer.charAt(a));)if((a+=1)-this.position>e/2-1){s=" ... ",a-=5;break}return o=this.buffer.slice(r,a),n.repeat(" ",t)+i+o+s+"\n"+n.repeat(" ",t+this.position-r+i.length)+"^"},r.prototype.toString=function(t){var e,i="";return this.name&&(i+='in "'+this.name+'" '),i+="at line "+(this.line+1)+", column "+(this.column+1),!t&&(e=this.getSnippet())&&(i+=":\n"+e),i},t.exports=r},91433:(t,e,i)=>{"use strict";var n=i(48719),r=i(51928),s=i(89208);function a(t,e,i){var n=[];return t.include.forEach(function(t){i=a(t,e,i)}),t[e].forEach(function(t){i.forEach(function(e,i){e.tag===t.tag&&e.kind===t.kind&&n.push(i)}),i.push(t)}),i.filter(function(t,e){return -1===n.indexOf(e)})}function o(){var t,e,i={scalar:{},sequence:{},mapping:{},fallback:{}};function n(t){i[t.kind][t.tag]=i.fallback[t.tag]=t}for(t=0,e=arguments.length;t<e;t+=1)arguments[t].forEach(n);return i}function u(t){this.include=t.include||[],this.implicit=t.implicit||[],this.explicit=t.explicit||[],this.implicit.forEach(function(t){if(t.loadKind&&"scalar"!==t.loadKind)throw new r("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=a(this,"implicit",[]),this.compiledExplicit=a(this,"explicit",[]),this.compiledTypeMap=o(this.compiledImplicit,this.compiledExplicit)}u.DEFAULT=null,u.create=function(){var t,e;switch(arguments.length){case 1:t=u.DEFAULT,e=arguments[0];break;case 2:t=arguments[0],e=arguments[1];break;default:throw new r("Wrong number of arguments for Schema.create function")}if(t=n.toArray(t),e=n.toArray(e),!t.every(function(t){return t instanceof u}))throw new r("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!e.every(function(t){return t instanceof s}))throw new r("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new u({include:t,explicit:e})},t.exports=u},89847:(t,e,i)=>{"use strict";var n=i(91433);t.exports=new n({include:[i(57889)]})},96810:(t,e,i)=>{"use strict";var n=i(91433);t.exports=n.DEFAULT=new n({include:[i(17100)],explicit:[i(74728),i(1575),i(93593)]})},17100:(t,e,i)=>{"use strict";var n=i(91433);t.exports=new n({include:[i(89847)],implicit:[i(82627),i(32921)],explicit:[i(18593),i(96392),i(97015),i(55606)]})},28534:(t,e,i)=>{"use strict";var n=i(91433);t.exports=new n({explicit:[i(52898),i(71448),i(15205)]})},57889:(t,e,i)=>{"use strict";var n=i(91433);t.exports=new n({include:[i(28534)],implicit:[i(82228),i(79402),i(84116),i(45602)]})},89208:(t,e,i)=>{"use strict";var n=i(51928),r=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],s=["scalar","sequence","mapping"];function a(t){var e={};return null!==t&&Object.keys(t).forEach(function(i){t[i].forEach(function(t){e[String(t)]=i})}),e}function o(t,e){if(Object.keys(e=e||{}).forEach(function(e){if(-1===r.indexOf(e))throw new n('Unknown option "'+e+'" is met in definition of "'+t+'" YAML type.')}),this.tag=t,this.kind=e.kind||null,this.resolve=e.resolve||function(){return!0},this.construct=e.construct||function(t){return t},this.instanceOf=e.instanceOf||null,this.predicate=e.predicate||null,this.represent=e.represent||null,this.defaultStyle=e.defaultStyle||null,this.styleAliases=a(e.styleAliases||null),-1===s.indexOf(this.kind))throw new n('Unknown kind "'+this.kind+'" is specified for "'+t+'" YAML type.')}t.exports=o},18593:(t,e,i)=>{"use strict";try{var n;n=i(78893).Buffer}catch(t){}var r=i(89208),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";function a(t){if(null===t)return!1;var e,i,n=0,r=t.length,a=s;for(i=0;i<r;i++)if(!((e=a.indexOf(t.charAt(i)))>64)){if(e<0)return!1;n+=6}return n%8==0}function o(t){var e,i,r=t.replace(/[\r\n=]/g,""),a=r.length,o=s,u=0,c=[];for(e=0;e<a;e++)e%4==0&&e&&(c.push(u>>16&255),c.push(u>>8&255),c.push(255&u)),u=u<<6|o.indexOf(r.charAt(e));return(0==(i=a%4*6)?(c.push(u>>16&255),c.push(u>>8&255),c.push(255&u)):18===i?(c.push(u>>10&255),c.push(u>>2&255)):12===i&&c.push(u>>4&255),n)?n.from?n.from(c):new n(c):c}function u(t){var e,i,n="",r=0,a=t.length,o=s;for(e=0;e<a;e++)e%3==0&&e&&(n+=o[r>>18&63]+o[r>>12&63]+o[r>>6&63]+o[63&r]),r=(r<<8)+t[e];return 0==(i=a%3)?n+=o[r>>18&63]+o[r>>12&63]+o[r>>6&63]+o[63&r]:2===i?n+=o[r>>10&63]+o[r>>4&63]+o[r<<2&63]+o[64]:1===i&&(n+=o[r>>2&63]+o[r<<4&63]+o[64]+o[64]),n}function c(t){return n&&n.isBuffer(t)}t.exports=new r("tag:yaml.org,2002:binary",{kind:"scalar",resolve:a,construct:o,predicate:c,represent:u})},79402:(t,e,i)=>{"use strict";var n=i(89208);function r(t){if(null===t)return!1;var e=t.length;return 4===e&&("true"===t||"True"===t||"TRUE"===t)||5===e&&("false"===t||"False"===t||"FALSE"===t)}function s(t){return"true"===t||"True"===t||"TRUE"===t}function a(t){return"[object Boolean]"===Object.prototype.toString.call(t)}t.exports=new n("tag:yaml.org,2002:bool",{kind:"scalar",resolve:r,construct:s,predicate:a,represent:{lowercase:function(t){return t?"true":"false"},uppercase:function(t){return t?"TRUE":"FALSE"},camelcase:function(t){return t?"True":"False"}},defaultStyle:"lowercase"})},45602:(t,e,i)=>{"use strict";var n=i(48719),r=i(89208),s=RegExp("^(?:[-+]?(?:0|[1-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function a(t){return!!(null!==t&&s.test(t)&&"_"!==t[t.length-1])}function o(t){var e,i,n,r;return(i="-"===(e=t.replace(/_/g,"").toLowerCase())[0]?-1:1,r=[],"+-".indexOf(e[0])>=0&&(e=e.slice(1)),".inf"===e)?1===i?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===e?NaN:e.indexOf(":")>=0?(e.split(":").forEach(function(t){r.unshift(parseFloat(t,10))}),e=0,n=1,r.forEach(function(t){e+=t*n,n*=60}),i*e):i*parseFloat(e,10)}var u=/^[-+]?[0-9]+e/;function c(t,e){var i;if(isNaN(t))switch(e){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===t)switch(e){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===t)switch(e){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(n.isNegativeZero(t))return"-0.0";return i=t.toString(10),u.test(i)?i.replace("e",".e"):i}function h(t){return"[object Number]"===Object.prototype.toString.call(t)&&(t%1!=0||n.isNegativeZero(t))}t.exports=new r("tag:yaml.org,2002:float",{kind:"scalar",resolve:a,construct:o,predicate:h,represent:c,defaultStyle:"lowercase"})},84116:(t,e,i)=>{"use strict";var n=i(48719),r=i(89208);function s(t){return 48<=t&&t<=57||65<=t&&t<=70||97<=t&&t<=102}function a(t){return 48<=t&&t<=55}function o(t){return 48<=t&&t<=57}function u(t){if(null===t)return!1;var e,i=t.length,n=0,r=!1;if(!i)return!1;if(("-"===(e=t[n])||"+"===e)&&(e=t[++n]),"0"===e){if(n+1===i)return!0;if("b"===(e=t[++n])){for(n++;n<i;n++)if("_"!==(e=t[n])){if("0"!==e&&"1"!==e)return!1;r=!0}return r&&"_"!==e}if("x"===e){for(n++;n<i;n++)if("_"!==(e=t[n])){if(!s(t.charCodeAt(n)))return!1;r=!0}return r&&"_"!==e}for(;n<i;n++)if("_"!==(e=t[n])){if(!a(t.charCodeAt(n)))return!1;r=!0}return r&&"_"!==e}if("_"===e)return!1;for(;n<i;n++)if("_"!==(e=t[n])){if(":"===e)break;if(!o(t.charCodeAt(n)))return!1;r=!0}return!!r&&"_"!==e&&(":"!==e||/^(:[0-5]?[0-9])+$/.test(t.slice(n)))}function c(t){var e,i,n=t,r=1,s=[];return(-1!==n.indexOf("_")&&(n=n.replace(/_/g,"")),("-"===(e=n[0])||"+"===e)&&("-"===e&&(r=-1),e=(n=n.slice(1))[0]),"0"===n)?0:"0"===e?"b"===n[1]?r*parseInt(n.slice(2),2):"x"===n[1]?r*parseInt(n,16):r*parseInt(n,8):-1!==n.indexOf(":")?(n.split(":").forEach(function(t){s.unshift(parseInt(t,10))}),n=0,i=1,s.forEach(function(t){n+=t*i,i*=60}),r*n):r*parseInt(n,10)}function h(t){return"[object Number]"===Object.prototype.toString.call(t)&&t%1==0&&!n.isNegativeZero(t)}t.exports=new r("tag:yaml.org,2002:int",{kind:"scalar",resolve:u,construct:c,predicate:h,represent:{binary:function(t){return t>=0?"0b"+t.toString(2):"-0b"+t.toString(2).slice(1)},octal:function(t){return t>=0?"0"+t.toString(8):"-0"+t.toString(8).slice(1)},decimal:function(t){return t.toString(10)},hexadecimal:function(t){return t>=0?"0x"+t.toString(16).toUpperCase():"-0x"+t.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},93593:(t,e,i)=>{"use strict";try{var n;n=i(97296)}catch(t){"undefined"!=typeof window&&(n=window.esprima)}var r=i(89208);function s(t){if(null===t)return!1;try{var e="("+t+")",i=n.parse(e,{range:!0});if("Program"!==i.type||1!==i.body.length||"ExpressionStatement"!==i.body[0].type||"ArrowFunctionExpression"!==i.body[0].expression.type&&"FunctionExpression"!==i.body[0].expression.type)return!1;return!0}catch(t){return!1}}function a(t){var e,i="("+t+")",r=n.parse(i,{range:!0}),s=[];if("Program"!==r.type||1!==r.body.length||"ExpressionStatement"!==r.body[0].type||"ArrowFunctionExpression"!==r.body[0].expression.type&&"FunctionExpression"!==r.body[0].expression.type)throw Error("Failed to resolve function");return(r.body[0].expression.params.forEach(function(t){s.push(t.name)}),e=r.body[0].expression.body.range,"BlockStatement"===r.body[0].expression.body.type)?Function(s,i.slice(e[0]+1,e[1]-1)):Function(s,"return "+i.slice(e[0],e[1]))}function o(t){return t.toString()}function u(t){return"[object Function]"===Object.prototype.toString.call(t)}t.exports=new r("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:s,construct:a,predicate:u,represent:o})},1575:(t,e,i)=>{"use strict";var n=i(89208);function r(t){if(null===t||0===t.length)return!1;var e=t,i=/\/([gim]*)$/.exec(t),n="";return"/"!==e[0]||(i&&(n=i[1]),!(n.length>3)&&"/"===e[e.length-n.length-1])}function s(t){var e=t,i=/\/([gim]*)$/.exec(t),n="";return"/"===e[0]&&(i&&(n=i[1]),e=e.slice(1,e.length-n.length-1)),new RegExp(e,n)}function a(t){var e="/"+t.source+"/";return t.global&&(e+="g"),t.multiline&&(e+="m"),t.ignoreCase&&(e+="i"),e}function o(t){return"[object RegExp]"===Object.prototype.toString.call(t)}t.exports=new n("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:r,construct:s,predicate:o,represent:a})},74728:(t,e,i)=>{"use strict";var n=i(89208);function r(){return!0}function s(){}function a(){return""}function o(t){return void 0===t}t.exports=new n("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:r,construct:s,predicate:o,represent:a})},15205:(t,e,i)=>{"use strict";var n=i(89208);t.exports=new n("tag:yaml.org,2002:map",{kind:"mapping",construct:function(t){return null!==t?t:{}}})},32921:(t,e,i)=>{"use strict";var n=i(89208);function r(t){return"<<"===t||null===t}t.exports=new n("tag:yaml.org,2002:merge",{kind:"scalar",resolve:r})},82228:(t,e,i)=>{"use strict";var n=i(89208);function r(t){if(null===t)return!0;var e=t.length;return 1===e&&"~"===t||4===e&&("null"===t||"Null"===t||"NULL"===t)}function s(){return null}function a(t){return null===t}t.exports=new n("tag:yaml.org,2002:null",{kind:"scalar",resolve:r,construct:s,predicate:a,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})},96392:(t,e,i)=>{"use strict";var n=i(89208),r=Object.prototype.hasOwnProperty,s=Object.prototype.toString;function a(t){if(null===t)return!0;var e,i,n,a,o,u=[],c=t;for(e=0,i=c.length;e<i;e+=1){if(n=c[e],o=!1,"[object Object]"!==s.call(n))return!1;for(a in n)if(r.call(n,a)){if(o)return!1;o=!0}if(!o||-1!==u.indexOf(a))return!1;u.push(a)}return!0}function o(t){return null!==t?t:[]}t.exports=new n("tag:yaml.org,2002:omap",{kind:"sequence",resolve:a,construct:o})},97015:(t,e,i)=>{"use strict";var n=i(89208),r=Object.prototype.toString;function s(t){if(null===t)return!0;var e,i,n,s,a,o=t;for(e=0,a=Array(o.length),i=o.length;e<i;e+=1){if(n=o[e],"[object Object]"!==r.call(n)||1!==(s=Object.keys(n)).length)return!1;a[e]=[s[0],n[s[0]]]}return!0}function a(t){if(null===t)return[];var e,i,n,r,s,a=t;for(e=0,s=Array(a.length),i=a.length;e<i;e+=1)r=Object.keys(n=a[e]),s[e]=[r[0],n[r[0]]];return s}t.exports=new n("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:s,construct:a})},71448:(t,e,i)=>{"use strict";var n=i(89208);t.exports=new n("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(t){return null!==t?t:[]}})},55606:(t,e,i)=>{"use strict";var n=i(89208),r=Object.prototype.hasOwnProperty;function s(t){if(null===t)return!0;var e,i=t;for(e in i)if(r.call(i,e)&&null!==i[e])return!1;return!0}function a(t){return null!==t?t:{}}t.exports=new n("tag:yaml.org,2002:set",{kind:"mapping",resolve:s,construct:a})},52898:(t,e,i)=>{"use strict";var n=i(89208);t.exports=new n("tag:yaml.org,2002:str",{kind:"scalar",construct:function(t){return null!==t?t:""}})},82627:(t,e,i)=>{"use strict";var n=i(89208),r=RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),s=RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function a(t){return null!==t&&(null!==r.exec(t)||null!==s.exec(t))}function o(t){var e,i,n,a,o,u,c,h,l=0,p=null;if(null===(e=r.exec(t))&&(e=s.exec(t)),null===e)throw Error("Date resolve error");if(i=+e[1],n=+e[2]-1,a=+e[3],!e[4])return new Date(Date.UTC(i,n,a));if(o=+e[4],u=+e[5],c=+e[6],e[7]){for(l=e[7].slice(0,3);l.length<3;)l+="0";l=+l}return e[9]&&(p=(60*+e[10]+ +(e[11]||0))*6e4,"-"===e[9]&&(p=-p)),h=new Date(Date.UTC(i,n,a,o,u,c,l)),p&&h.setTime(h.getTime()-p),h}function u(t){return t.toISOString()}t.exports=new n("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:a,construct:o,instanceOf:Date,represent:u})},63498:t=>{"use strict";t.exports=function(t){return null!=t&&("object"==typeof t||"function"==typeof t)}},38742:t=>{var e=Object.prototype.toString;function i(t){return"function"==typeof t.constructor?t.constructor.name:null}function n(t){return Array.isArray?Array.isArray(t):t instanceof Array}function r(t){return t instanceof Error||"string"==typeof t.message&&t.constructor&&"number"==typeof t.constructor.stackTraceLimit}function s(t){return t instanceof Date||"function"==typeof t.toDateString&&"function"==typeof t.getDate&&"function"==typeof t.setDate}function a(t){return t instanceof RegExp||"string"==typeof t.flags&&"boolean"==typeof t.ignoreCase&&"boolean"==typeof t.multiline&&"boolean"==typeof t.global}function o(t,e){return"GeneratorFunction"===i(t)}function u(t){return"function"==typeof t.throw&&"function"==typeof t.return&&"function"==typeof t.next}function c(t){try{if("number"==typeof t.length&&"function"==typeof t.callee)return!0}catch(t){if(-1!==t.message.indexOf("callee"))return!0}return!1}function h(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){if(void 0===t)return"undefined";if(null===t)return"null";var l=typeof t;if("boolean"===l)return"boolean";if("string"===l)return"string";if("number"===l)return"number";if("symbol"===l)return"symbol";if("function"===l)return o(t)?"generatorfunction":"function";if(n(t))return"array";if(h(t))return"buffer";if(c(t))return"arguments";if(s(t))return"date";if(r(t))return"error";if(a(t))return"regexp";switch(i(t)){case"Symbol":return"symbol";case"Promise":return"promise";case"WeakMap":return"weakmap";case"WeakSet":return"weakset";case"Map":return"map";case"Set":return"set";case"Int8Array":return"int8array";case"Uint8Array":return"uint8array";case"Uint8ClampedArray":return"uint8clampedarray";case"Int16Array":return"int16array";case"Uint16Array":return"uint16array";case"Int32Array":return"int32array";case"Uint32Array":return"uint32array";case"Float32Array":return"float32array";case"Float64Array":return"float64array"}if(u(t))return"generator";switch(l=e.call(t)){case"[object Object]":return"object";case"[object Map Iterator]":return"mapiterator";case"[object Set Iterator]":return"setiterator";case"[object String Iterator]":return"stringiterator";case"[object Array Iterator]":return"arrayiterator"}return l.slice(8,-1).toLowerCase().replace(/\s/g,"")}},49303:(t,e,i)=>{"use strict";t.exports=i(30517)},65074:(t,e,i)=>{!function(t){t.parser=function(t,e){return new r(t,e)},t.SAXParser=r,t.SAXStream=h,t.createStream=c,t.MAX_BUFFER_LENGTH=65536;var e,n=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];function r(e,i){if(!(this instanceof r))return new r(e,i);var n=this;a(n),n.q=n.c="",n.bufferCheckPosition=t.MAX_BUFFER_LENGTH,n.opt=i||{},n.opt.lowercase=n.opt.lowercase||n.opt.lowercasetags,n.looseCase=n.opt.lowercase?"toLowerCase":"toUpperCase",n.tags=[],n.closed=n.closedRoot=n.sawRoot=!1,n.tag=n.error=null,n.strict=!!e,n.noscript=!!(e||n.opt.noscript),n.state=S.BEGIN,n.strictEntities=n.opt.strictEntities,n.ENTITIES=n.strictEntities?Object.create(t.XML_ENTITIES):Object.create(t.ENTITIES),n.attribList=[],n.opt.xmlns&&(n.ns=Object.create(m)),void 0===n.opt.unquotedAttributeValues&&(n.opt.unquotedAttributeValues=!e),n.trackPosition=!1!==n.opt.position,n.trackPosition&&(n.position=n.line=n.column=0),k(n,"onready")}function s(e){for(var i=Math.max(t.MAX_BUFFER_LENGTH,10),r=0,s=0,a=n.length;s<a;s++){var o=e[n[s]].length;if(o>i)switch(n[s]){case"textNode":T(e);break;case"cdata":w(e,"oncdata",e.cdata),e.cdata="";break;case"script":w(e,"onscript",e.script),e.script="";break;default:B(e,"Max buffer length exceeded: "+n[s])}r=Math.max(r,o)}var u=t.MAX_BUFFER_LENGTH-r;e.bufferCheckPosition=u+e.position}function a(t){for(var e=0,i=n.length;e<i;e++)t[n[e]]=""}function o(t){T(t),""!==t.cdata&&(w(t,"oncdata",t.cdata),t.cdata=""),""!==t.script&&(w(t,"onscript",t.script),t.script="")}t.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(t){function e(){}return e.prototype=t,new e}),Object.keys||(Object.keys=function(t){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);return e}),r.prototype={end:function(){I(this)},write:R,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){o(this)}};try{e=i(76162).Stream}catch(t){e=function(){}}e||(e=function(){});var u=t.EVENTS.filter(function(t){return"error"!==t&&"end"!==t});function c(t,e){return new h(t,e)}function h(t,i){if(!(this instanceof h))return new h(t,i);e.apply(this),this._parser=new r(t,i),this.writable=!0,this.readable=!0;var n=this;this._parser.onend=function(){n.emit("end")},this._parser.onerror=function(t){n.emit("error",t),n._parser.error=null},this._decoder=null,u.forEach(function(t){Object.defineProperty(n,"on"+t,{get:function(){return n._parser["on"+t]},set:function(e){if(!e)return n.removeAllListeners(t),n._parser["on"+t]=e,e;n.on(t,e)},enumerable:!0,configurable:!1})})}h.prototype=Object.create(e.prototype,{constructor:{value:h}}),h.prototype.write=function(t){if("function"==typeof Buffer&&"function"==typeof Buffer.isBuffer&&Buffer.isBuffer(t)){if(!this._decoder){var e=i(74026).StringDecoder;this._decoder=new e("utf8")}t=this._decoder.write(t)}return this._parser.write(t.toString()),this.emit("data",t),!0},h.prototype.end=function(t){return t&&t.length&&this.write(t),this._parser.end(),!0},h.prototype.on=function(t,i){var n=this;return n._parser["on"+t]||-1===u.indexOf(t)||(n._parser["on"+t]=function(){var e=1==arguments.length?[arguments[0]]:Array.apply(null,arguments);e.splice(0,0,t),n.emit.apply(n,e)}),e.prototype.on.call(n,t,i)};var l="[CDATA[",p="DOCTYPE",d="http://www.w3.org/XML/1998/namespace",f="http://www.w3.org/2000/xmlns/",m={xml:d,xmlns:f},x=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,D=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,y=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,g=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function E(t){return" "===t||"\n"===t||"\r"===t||"	"===t}function A(t){return'"'===t||"'"===t}function C(t){return">"===t||E(t)}function v(t,e){return t.test(e)}function F(t,e){return!v(t,e)}var S=0;for(var b in t.STATE={BEGIN:S++,BEGIN_WHITESPACE:S++,TEXT:S++,TEXT_ENTITY:S++,OPEN_WAKA:S++,SGML_DECL:S++,SGML_DECL_QUOTED:S++,DOCTYPE:S++,DOCTYPE_QUOTED:S++,DOCTYPE_DTD:S++,DOCTYPE_DTD_QUOTED:S++,COMMENT_STARTING:S++,COMMENT:S++,COMMENT_ENDING:S++,COMMENT_ENDED:S++,CDATA:S++,CDATA_ENDING:S++,CDATA_ENDING_2:S++,PROC_INST:S++,PROC_INST_BODY:S++,PROC_INST_ENDING:S++,OPEN_TAG:S++,OPEN_TAG_SLASH:S++,ATTRIB:S++,ATTRIB_NAME:S++,ATTRIB_NAME_SAW_WHITE:S++,ATTRIB_VALUE:S++,ATTRIB_VALUE_QUOTED:S++,ATTRIB_VALUE_CLOSED:S++,ATTRIB_VALUE_UNQUOTED:S++,ATTRIB_VALUE_ENTITY_Q:S++,ATTRIB_VALUE_ENTITY_U:S++,CLOSE_TAG:S++,CLOSE_TAG_SAW_WHITE:S++,SCRIPT:S++,SCRIPT_ENDING:S++},t.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},t.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(t.ENTITIES).forEach(function(e){var i=t.ENTITIES[e],n="number"==typeof i?String.fromCharCode(i):i;t.ENTITIES[e]=n}),t.STATE)t.STATE[t.STATE[b]]=b;function k(t,e,i){t[e]&&t[e](i)}function w(t,e,i){t.textNode&&T(t),k(t,e,i)}function T(t){t.textNode=N(t.opt,t.textNode),t.textNode&&k(t,"ontext",t.textNode),t.textNode=""}function N(t,e){return t.trim&&(e=e.trim()),t.normalize&&(e=e.replace(/\s+/g," ")),e}function B(t,e){return T(t),t.trackPosition&&(e+="\nLine: "+t.line+"\nColumn: "+t.column+"\nChar: "+t.c),e=Error(e),t.error=e,k(t,"onerror",e),t}function I(t){return t.sawRoot&&!t.closedRoot&&M(t,"Unclosed root tag"),t.state!==S.BEGIN&&t.state!==S.BEGIN_WHITESPACE&&t.state!==S.TEXT&&B(t,"Unexpected end"),T(t),t.c="",t.closed=!0,k(t,"onend"),r.call(t,t.strict,t.opt),t}function M(t,e){if("object"!=typeof t||!(t instanceof r))throw Error("bad call to strictFail");t.strict&&B(t,e)}function O(t){t.strict||(t.tagName=t.tagName[t.looseCase]());var e=t.tags[t.tags.length-1]||t,i=t.tag={name:t.tagName,attributes:{}};t.opt.xmlns&&(i.ns=e.ns),t.attribList.length=0,w(t,"onopentagstart",i)}function P(t,e){var i=0>t.indexOf(":")?["",t]:t.split(":"),n=i[0],r=i[1];return e&&"xmlns"===t&&(n="xmlns",r=""),{prefix:n,local:r}}function _(t){if(t.strict||(t.attribName=t.attribName[t.looseCase]()),-1!==t.attribList.indexOf(t.attribName)||t.tag.attributes.hasOwnProperty(t.attribName)){t.attribName=t.attribValue="";return}if(t.opt.xmlns){var e=P(t.attribName,!0),i=e.prefix,n=e.local;if("xmlns"===i){if("xml"===n&&t.attribValue!==d)M(t,"xml: prefix must be bound to "+d+"\nActual: "+t.attribValue);else if("xmlns"===n&&t.attribValue!==f)M(t,"xmlns: prefix must be bound to "+f+"\nActual: "+t.attribValue);else{var r=t.tag,s=t.tags[t.tags.length-1]||t;r.ns===s.ns&&(r.ns=Object.create(s.ns)),r.ns[n]=t.attribValue}}t.attribList.push([t.attribName,t.attribValue])}else t.tag.attributes[t.attribName]=t.attribValue,w(t,"onattribute",{name:t.attribName,value:t.attribValue});t.attribName=t.attribValue=""}function L(t,e){if(t.opt.xmlns){var i=t.tag,n=P(t.tagName);i.prefix=n.prefix,i.local=n.local,i.uri=i.ns[n.prefix]||"",i.prefix&&!i.uri&&(M(t,"Unbound namespace prefix: "+JSON.stringify(t.tagName)),i.uri=n.prefix);var r=t.tags[t.tags.length-1]||t;i.ns&&r.ns!==i.ns&&Object.keys(i.ns).forEach(function(e){w(t,"onopennamespace",{prefix:e,uri:i.ns[e]})});for(var s=0,a=t.attribList.length;s<a;s++){var o=t.attribList[s],u=o[0],c=o[1],h=P(u,!0),l=h.prefix,p=h.local,d=""===l?"":i.ns[l]||"",f={name:u,value:c,prefix:l,local:p,uri:d};l&&"xmlns"!==l&&!d&&(M(t,"Unbound namespace prefix: "+JSON.stringify(l)),f.uri=l),t.tag.attributes[u]=f,w(t,"onattribute",f)}t.attribList.length=0}t.tag.isSelfClosing=!!e,t.sawRoot=!0,t.tags.push(t.tag),w(t,"onopentag",t.tag),e||(t.noscript||"script"!==t.tagName.toLowerCase()?t.state=S.TEXT:t.state=S.SCRIPT,t.tag=null,t.tagName=""),t.attribName=t.attribValue="",t.attribList.length=0}function U(t){if(!t.tagName){M(t,"Weird empty close tag."),t.textNode+="</>",t.state=S.TEXT;return}if(t.script){if("script"!==t.tagName){t.script+="</"+t.tagName+">",t.tagName="",t.state=S.SCRIPT;return}w(t,"onscript",t.script),t.script=""}var e=t.tags.length,i=t.tagName;t.strict||(i=i[t.looseCase]());for(var n=i;e--;)if(t.tags[e].name!==n)M(t,"Unexpected close tag");else break;if(e<0){M(t,"Unmatched closing tag: "+t.tagName),t.textNode+="</"+t.tagName+">",t.state=S.TEXT;return}t.tagName=i;for(var r=t.tags.length;r-- >e;){var s=t.tag=t.tags.pop();t.tagName=t.tag.name,w(t,"onclosetag",t.tagName);var a={};for(var o in s.ns)a[o]=s.ns[o];var u=t.tags[t.tags.length-1]||t;t.opt.xmlns&&s.ns!==u.ns&&Object.keys(s.ns).forEach(function(e){var i=s.ns[e];w(t,"onclosenamespace",{prefix:e,uri:i})})}0===e&&(t.closedRoot=!0),t.tagName=t.attribValue=t.attribName="",t.attribList.length=0,t.state=S.TEXT}function j(t){var e,i=t.entity,n=i.toLowerCase(),r="";return t.ENTITIES[i]?t.ENTITIES[i]:t.ENTITIES[n]?t.ENTITIES[n]:("#"===(i=n).charAt(0)&&(r="x"===i.charAt(1)?(e=parseInt(i=i.slice(2),16)).toString(16):(e=parseInt(i=i.slice(1),10)).toString(10)),i=i.replace(/^0+/,""),isNaN(e)||r.toLowerCase()!==i)?(M(t,"Invalid character entity"),"&"+t.entity+";"):String.fromCodePoint(e)}function X(t,e){"<"===e?(t.state=S.OPEN_WAKA,t.startTagPosition=t.position):E(e)||(M(t,"Non-whitespace before first tag."),t.textNode=e,t.state=S.TEXT)}function K(t,e){var i="";return e<t.length&&(i=t.charAt(e)),i}function R(e){var i=this;if(this.error)throw this.error;if(i.closed)return B(i,"Cannot write after close. Assign an onready handler.");if(null===e)return I(i);"object"==typeof e&&(e=e.toString());for(var n=0,r="";r=K(e,n++),i.c=r,r;)switch(i.trackPosition&&(i.position++,"\n"===r?(i.line++,i.column=0):i.column++),i.state){case S.BEGIN:if(i.state=S.BEGIN_WHITESPACE,"\uFEFF"===r)continue;X(i,r);continue;case S.BEGIN_WHITESPACE:X(i,r);continue;case S.TEXT:if(i.sawRoot&&!i.closedRoot){for(var a=n-1;r&&"<"!==r&&"&"!==r;)(r=K(e,n++))&&i.trackPosition&&(i.position++,"\n"===r?(i.line++,i.column=0):i.column++);i.textNode+=e.substring(a,n-1)}"<"!==r||i.sawRoot&&i.closedRoot&&!i.strict?(E(r)||i.sawRoot&&!i.closedRoot||M(i,"Text data outside of root node."),"&"===r?i.state=S.TEXT_ENTITY:i.textNode+=r):(i.state=S.OPEN_WAKA,i.startTagPosition=i.position);continue;case S.SCRIPT:"<"===r?i.state=S.SCRIPT_ENDING:i.script+=r;continue;case S.SCRIPT_ENDING:"/"===r?i.state=S.CLOSE_TAG:(i.script+="<"+r,i.state=S.SCRIPT);continue;case S.OPEN_WAKA:"!"===r?(i.state=S.SGML_DECL,i.sgmlDecl=""):E(r)||(v(x,r)?(i.state=S.OPEN_TAG,i.tagName=r):"/"===r?(i.state=S.CLOSE_TAG,i.tagName=""):"?"===r?(i.state=S.PROC_INST,i.procInstName=i.procInstBody=""):(M(i,"Unencoded <"),i.startTagPosition+1<i.position&&(r=Array(i.position-i.startTagPosition).join(" ")+r),i.textNode+="<"+r,i.state=S.TEXT));continue;case S.SGML_DECL:if(i.sgmlDecl+r==="--"){i.state=S.COMMENT,i.comment="",i.sgmlDecl="";continue}i.doctype&&!0!==i.doctype&&i.sgmlDecl?(i.state=S.DOCTYPE_DTD,i.doctype+="<!"+i.sgmlDecl+r,i.sgmlDecl=""):(i.sgmlDecl+r).toUpperCase()===l?(w(i,"onopencdata"),i.state=S.CDATA,i.sgmlDecl="",i.cdata=""):(i.sgmlDecl+r).toUpperCase()===p?(i.state=S.DOCTYPE,(i.doctype||i.sawRoot)&&M(i,"Inappropriately located doctype declaration"),i.doctype="",i.sgmlDecl=""):">"===r?(w(i,"onsgmldeclaration",i.sgmlDecl),i.sgmlDecl="",i.state=S.TEXT):(A(r)&&(i.state=S.SGML_DECL_QUOTED),i.sgmlDecl+=r);continue;case S.SGML_DECL_QUOTED:r===i.q&&(i.state=S.SGML_DECL,i.q=""),i.sgmlDecl+=r;continue;case S.DOCTYPE:">"===r?(i.state=S.TEXT,w(i,"ondoctype",i.doctype),i.doctype=!0):(i.doctype+=r,"["===r?i.state=S.DOCTYPE_DTD:A(r)&&(i.state=S.DOCTYPE_QUOTED,i.q=r));continue;case S.DOCTYPE_QUOTED:i.doctype+=r,r===i.q&&(i.q="",i.state=S.DOCTYPE);continue;case S.DOCTYPE_DTD:"]"===r?(i.doctype+=r,i.state=S.DOCTYPE):"<"===r?(i.state=S.OPEN_WAKA,i.startTagPosition=i.position):A(r)?(i.doctype+=r,i.state=S.DOCTYPE_DTD_QUOTED,i.q=r):i.doctype+=r;continue;case S.DOCTYPE_DTD_QUOTED:i.doctype+=r,r===i.q&&(i.state=S.DOCTYPE_DTD,i.q="");continue;case S.COMMENT:"-"===r?i.state=S.COMMENT_ENDING:i.comment+=r;continue;case S.COMMENT_ENDING:"-"===r?(i.state=S.COMMENT_ENDED,i.comment=N(i.opt,i.comment),i.comment&&w(i,"oncomment",i.comment),i.comment=""):(i.comment+="-"+r,i.state=S.COMMENT);continue;case S.COMMENT_ENDED:">"!==r?(M(i,"Malformed comment"),i.comment+="--"+r,i.state=S.COMMENT):i.doctype&&!0!==i.doctype?i.state=S.DOCTYPE_DTD:i.state=S.TEXT;continue;case S.CDATA:"]"===r?i.state=S.CDATA_ENDING:i.cdata+=r;continue;case S.CDATA_ENDING:"]"===r?i.state=S.CDATA_ENDING_2:(i.cdata+="]"+r,i.state=S.CDATA);continue;case S.CDATA_ENDING_2:">"===r?(i.cdata&&w(i,"oncdata",i.cdata),w(i,"onclosecdata"),i.cdata="",i.state=S.TEXT):"]"===r?i.cdata+="]":(i.cdata+="]]"+r,i.state=S.CDATA);continue;case S.PROC_INST:"?"===r?i.state=S.PROC_INST_ENDING:E(r)?i.state=S.PROC_INST_BODY:i.procInstName+=r;continue;case S.PROC_INST_BODY:!i.procInstBody&&E(r)||("?"===r?i.state=S.PROC_INST_ENDING:i.procInstBody+=r);continue;case S.PROC_INST_ENDING:">"===r?(w(i,"onprocessinginstruction",{name:i.procInstName,body:i.procInstBody}),i.procInstName=i.procInstBody="",i.state=S.TEXT):(i.procInstBody+="?"+r,i.state=S.PROC_INST_BODY);continue;case S.OPEN_TAG:v(D,r)?i.tagName+=r:(O(i),">"===r?L(i):"/"===r?i.state=S.OPEN_TAG_SLASH:(E(r)||M(i,"Invalid character in tag name"),i.state=S.ATTRIB));continue;case S.OPEN_TAG_SLASH:">"===r?(L(i,!0),U(i)):(M(i,"Forward-slash in opening tag not followed by >"),i.state=S.ATTRIB);continue;case S.ATTRIB:E(r)||(">"===r?L(i):"/"===r?i.state=S.OPEN_TAG_SLASH:v(x,r)?(i.attribName=r,i.attribValue="",i.state=S.ATTRIB_NAME):M(i,"Invalid attribute name"));continue;case S.ATTRIB_NAME:"="===r?i.state=S.ATTRIB_VALUE:">"===r?(M(i,"Attribute without value"),i.attribValue=i.attribName,_(i),L(i)):E(r)?i.state=S.ATTRIB_NAME_SAW_WHITE:v(D,r)?i.attribName+=r:M(i,"Invalid attribute name");continue;case S.ATTRIB_NAME_SAW_WHITE:"="===r?i.state=S.ATTRIB_VALUE:E(r)||(M(i,"Attribute without value"),i.tag.attributes[i.attribName]="",i.attribValue="",w(i,"onattribute",{name:i.attribName,value:""}),i.attribName="",">"===r?L(i):v(x,r)?(i.attribName=r,i.state=S.ATTRIB_NAME):(M(i,"Invalid attribute name"),i.state=S.ATTRIB));continue;case S.ATTRIB_VALUE:E(r)||(A(r)?(i.q=r,i.state=S.ATTRIB_VALUE_QUOTED):(i.opt.unquotedAttributeValues||B(i,"Unquoted attribute value"),i.state=S.ATTRIB_VALUE_UNQUOTED,i.attribValue=r));continue;case S.ATTRIB_VALUE_QUOTED:if(r!==i.q){"&"===r?i.state=S.ATTRIB_VALUE_ENTITY_Q:i.attribValue+=r;continue}_(i),i.q="",i.state=S.ATTRIB_VALUE_CLOSED;continue;case S.ATTRIB_VALUE_CLOSED:E(r)?i.state=S.ATTRIB:">"===r?L(i):"/"===r?i.state=S.OPEN_TAG_SLASH:v(x,r)?(M(i,"No whitespace between attributes"),i.attribName=r,i.attribValue="",i.state=S.ATTRIB_NAME):M(i,"Invalid attribute name");continue;case S.ATTRIB_VALUE_UNQUOTED:if(!C(r)){"&"===r?i.state=S.ATTRIB_VALUE_ENTITY_U:i.attribValue+=r;continue}_(i),">"===r?L(i):i.state=S.ATTRIB;continue;case S.CLOSE_TAG:i.tagName?">"===r?U(i):v(D,r)?i.tagName+=r:i.script?(i.script+="</"+i.tagName,i.tagName="",i.state=S.SCRIPT):(E(r)||M(i,"Invalid tagname in closing tag"),i.state=S.CLOSE_TAG_SAW_WHITE):E(r)||(F(x,r)?i.script?(i.script+="</"+r,i.state=S.SCRIPT):M(i,"Invalid tagname in closing tag."):i.tagName=r);continue;case S.CLOSE_TAG_SAW_WHITE:if(E(r))continue;">"===r?U(i):M(i,"Invalid characters in closing tag");continue;case S.TEXT_ENTITY:case S.ATTRIB_VALUE_ENTITY_Q:case S.ATTRIB_VALUE_ENTITY_U:switch(i.state){case S.TEXT_ENTITY:o=S.TEXT,u="textNode";break;case S.ATTRIB_VALUE_ENTITY_Q:o=S.ATTRIB_VALUE_QUOTED,u="attribValue";break;case S.ATTRIB_VALUE_ENTITY_U:o=S.ATTRIB_VALUE_UNQUOTED,u="attribValue"}if(";"===r){var o,u,c=j(i);i.opt.unparsedEntities&&!Object.values(t.XML_ENTITIES).includes(c)?(i.entity="",i.state=o,i.write(c)):(i[u]+=c,i.entity="",i.state=o)}else v(i.entity.length?g:y,r)?i.entity+=r:(M(i,"Invalid character in entity name"),i[u]+="&"+i.entity+r,i.entity="",i.state=o);continue;default:throw Error(i,"Unknown state: "+i.state)}return i.position>=i.bufferCheckPosition&&s(i),i}S=t.STATE,String.fromCodePoint||function(){var t=String.fromCharCode,e=Math.floor,i=function(){var i,n,r=16384,s=[],a=-1,o=arguments.length;if(!o)return"";for(var u="";++a<o;){var c=Number(arguments[a]);if(!isFinite(c)||c<0||c>1114111||e(c)!==c)throw RangeError("Invalid code point: "+c);c<=65535?s.push(c):(c-=65536,i=(c>>10)+55296,n=c%1024+56320,s.push(i,n)),(a+1===o||s.length>r)&&(u+=t.apply(null,s),s.length=0)}return u};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:i,configurable:!0,writable:!0}):String.fromCodePoint=i}()}(e)},15592:(t,e,i)=>{"use strict";var n=i(38742),r=i(58140);function s(t,e){return t.slice(0,e.length)===e&&t.charAt(e.length+1)!==e.slice(-1)}function a(t){if("object"!==n(t)&&(t={content:t}),"string"!=typeof t.content&&!h(t.content))throw TypeError("expected a buffer or string");return t.content=t.content.toString(),t.sections=[],t}function o(t,e){return t?t.slice(e.length).trim():""}function u(){return{key:"",data:"",content:""}}function c(t){return t}function h(t){return!!t&&!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t,e){"function"==typeof e&&(e={parse:e});var i=a(t),n=r({},{section_delimiter:"---",parse:c},e),h=n.section_delimiter,l=i.content.split(/\r?\n/),p=null,d=u(),f=[],m=[];function x(t){i.content=t,p=[],f=[]}function D(t){m.length&&(d.key=o(m[0],h),d.content=t,n.parse(d,p),p.push(d),d=u(),f=[],m=[])}for(var y=0;y<l.length;y++){var g=l[y],E=m.length,A=g.trim();if(s(A,h)){if(3===A.length&&0!==y){if(0===E||2===E){f.push(g);continue}m.push(A),d.data=f.join("\n"),f=[];continue}null===p&&x(f.join("\n")),2===E&&D(f.join("\n")),m.push(A);continue}f.push(g)}return null===p?x(f.join("\n")):D(f.join("\n")),i.sections=p,i}},18636:t=>{"use strict";t.exports=function(t){return"string"==typeof t&&"\uFEFF"===t.charAt(0)?t.slice(1):t}},47947:t=>{t.exports={isArray:function(t){return Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)}}},40880:(t,e,i)=>{var n=i(70424),r=i(2222),s=i(98688),a=i(54924);t.exports={xml2js:n,xml2json:r,js2xml:s,json2xml:a}},98688:(t,e,i)=>{var n,r,s=i(45393),a=i(47947).isArray;function o(t){var e=s.copyOptions(t);return s.ensureFlagExists("ignoreDeclaration",e),s.ensureFlagExists("ignoreInstruction",e),s.ensureFlagExists("ignoreAttributes",e),s.ensureFlagExists("ignoreText",e),s.ensureFlagExists("ignoreComment",e),s.ensureFlagExists("ignoreCdata",e),s.ensureFlagExists("ignoreDoctype",e),s.ensureFlagExists("compact",e),s.ensureFlagExists("indentText",e),s.ensureFlagExists("indentCdata",e),s.ensureFlagExists("indentAttributes",e),s.ensureFlagExists("indentInstruction",e),s.ensureFlagExists("fullTagEmptyElement",e),s.ensureFlagExists("noQuotesForNativeAttributes",e),s.ensureSpacesExists(e),"number"==typeof e.spaces&&(e.spaces=Array(e.spaces+1).join(" ")),s.ensureKeyExists("declaration",e),s.ensureKeyExists("instruction",e),s.ensureKeyExists("attributes",e),s.ensureKeyExists("text",e),s.ensureKeyExists("comment",e),s.ensureKeyExists("cdata",e),s.ensureKeyExists("doctype",e),s.ensureKeyExists("type",e),s.ensureKeyExists("name",e),s.ensureKeyExists("elements",e),s.checkFnExists("doctype",e),s.checkFnExists("instruction",e),s.checkFnExists("cdata",e),s.checkFnExists("comment",e),s.checkFnExists("text",e),s.checkFnExists("instructionName",e),s.checkFnExists("elementName",e),s.checkFnExists("attributeName",e),s.checkFnExists("attributeValue",e),s.checkFnExists("attributes",e),s.checkFnExists("fullTagEmptyElement",e),e}function u(t,e,i){return(!i&&t.spaces?"\n":"")+Array(e+1).join(t.spaces)}function c(t,e,i){if(e.ignoreAttributes)return"";"attributesFn"in e&&(t=e.attributesFn(t,r,n));var s,a,o,c,h=[];for(s in t)t.hasOwnProperty(s)&&null!==t[s]&&void 0!==t[s]&&(c=e.noQuotesForNativeAttributes&&"string"!=typeof t[s]?"":'"',a=(a=""+t[s]).replace(/"/g,"&quot;"),o="attributeNameFn"in e?e.attributeNameFn(s,a,r,n):s,h.push(e.spaces&&e.indentAttributes?u(e,i+1,!1):" "),h.push(o+"="+c+("attributeValueFn"in e?e.attributeValueFn(a,s,r,n):a)+c));return t&&Object.keys(t).length&&e.spaces&&e.indentAttributes&&h.push(u(e,i,!1)),h.join("")}function h(t,e,i){return n=t,r="xml",e.ignoreDeclaration?"":"<?xml"+c(t[e.attributesKey],e,i)+"?>"}function l(t,e,i){if(e.ignoreInstruction)return"";for(s in t)if(t.hasOwnProperty(s))break;var s,a="instructionNameFn"in e?e.instructionNameFn(s,t[s],r,n):s;if("object"==typeof t[s])return n=t,r=a,"<?"+a+c(t[s][e.attributesKey],e,i)+"?>";var o=t[s]?t[s]:"";return"instructionFn"in e&&(o=e.instructionFn(o,s,r,n)),"<?"+a+(o?" "+o:"")+"?>"}function p(t,e){return e.ignoreComment?"":"<!--"+("commentFn"in e?e.commentFn(t,r,n):t)+"-->"}function d(t,e){return e.ignoreCdata?"":"<![CDATA["+("cdataFn"in e?e.cdataFn(t,r,n):t.replace("]]>","]]]]><![CDATA[>"))+"]]>"}function f(t,e){return e.ignoreDoctype?"":"<!DOCTYPE "+("doctypeFn"in e?e.doctypeFn(t,r,n):t)+">"}function m(t,e){return e.ignoreText?"":(t=(t=(t=""+t).replace(/&amp;/g,"&")).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"textFn"in e?e.textFn(t,r,n):t)}function x(t,e){var i;if(t.elements&&t.elements.length)for(i=0;i<t.elements.length;++i)switch(t.elements[i][e.typeKey]){case"text":if(e.indentText)return!0;break;case"cdata":if(e.indentCdata)return!0;break;case"instruction":if(e.indentInstruction)return!0;break;default:return!0}return!1}function D(t,e,i){n=t,r=t.name;var s=[],a="elementNameFn"in e?e.elementNameFn(t.name,t):t.name;s.push("<"+a),t[e.attributesKey]&&s.push(c(t[e.attributesKey],e,i));var o=t[e.elementsKey]&&t[e.elementsKey].length||t[e.attributesKey]&&"preserve"===t[e.attributesKey]["xml:space"];return o||(o="fullTagEmptyElementFn"in e?e.fullTagEmptyElementFn(t.name,t):e.fullTagEmptyElement),o?(s.push(">"),t[e.elementsKey]&&t[e.elementsKey].length&&(s.push(y(t[e.elementsKey],e,i+1)),n=t,r=t.name),s.push(e.spaces&&x(t,e)?"\n"+Array(i+1).join(e.spaces):""),s.push("</"+a+">")):s.push("/>"),s.join("")}function y(t,e,i,n){return t.reduce(function(t,r){var s=u(e,i,n&&!t);switch(r.type){case"element":return t+s+D(r,e,i);case"comment":return t+s+p(r[e.commentKey],e);case"doctype":return t+s+f(r[e.doctypeKey],e);case"cdata":return t+(e.indentCdata?s:"")+d(r[e.cdataKey],e);case"text":return t+(e.indentText?s:"")+m(r[e.textKey],e);case"instruction":var a={};return a[r[e.nameKey]]=r[e.attributesKey]?r:r[e.instructionKey],t+(e.indentInstruction?s:"")+l(a,e,i)}},"")}function g(t,e,i){var n;for(n in t)if(t.hasOwnProperty(n))switch(n){case e.parentKey:case e.attributesKey:break;case e.textKey:if(e.indentText||i)return!0;break;case e.cdataKey:if(e.indentCdata||i)return!0;break;case e.instructionKey:if(e.indentInstruction||i)return!0;break;case e.doctypeKey:case e.commentKey:default:return!0}return!1}function E(t,e,i,s,a){n=t,r=e;var o="elementNameFn"in i?i.elementNameFn(e,t):e;if(null==t||""===t)return"fullTagEmptyElementFn"in i&&i.fullTagEmptyElementFn(e,t)||i.fullTagEmptyElement?"<"+o+"></"+o+">":"<"+o+"/>";var h=[];if(e){if(h.push("<"+o),"object"!=typeof t)return h.push(">"+m(t,i)+"</"+o+">"),h.join("");t[i.attributesKey]&&h.push(c(t[i.attributesKey],i,s));var l=g(t,i,!0)||t[i.attributesKey]&&"preserve"===t[i.attributesKey]["xml:space"];if(l||(l="fullTagEmptyElementFn"in i?i.fullTagEmptyElementFn(e,t):i.fullTagEmptyElement),!l)return h.push("/>"),h.join("");h.push(">")}return h.push(A(t,i,s+1,!1)),n=t,r=e,e&&h.push((a?u(i,s,!1):"")+"</"+o+">"),h.join("")}function A(t,e,i,n){var r,s,o,c=[];for(s in t)if(t.hasOwnProperty(s))for(r=0,o=a(t[s])?t[s]:[t[s]];r<o.length;++r){switch(s){case e.declarationKey:c.push(h(o[r],e,i));break;case e.instructionKey:c.push((e.indentInstruction?u(e,i,n):"")+l(o[r],e,i));break;case e.attributesKey:case e.parentKey:break;case e.textKey:c.push((e.indentText?u(e,i,n):"")+m(o[r],e));break;case e.cdataKey:c.push((e.indentCdata?u(e,i,n):"")+d(o[r],e));break;case e.doctypeKey:c.push(u(e,i,n)+f(o[r],e));break;case e.commentKey:c.push(u(e,i,n)+p(o[r],e));break;default:c.push(u(e,i,n)+E(o[r],s,e,i,g(o[r],e)))}n=n&&!c.length}return c.join("")}t.exports=function(t,e){e=o(e);var i=[];return n=t,r="_root_",e.compact?i.push(A(t,e,0,!0)):(t[e.declarationKey]&&i.push(h(t[e.declarationKey],e,0)),t[e.elementsKey]&&t[e.elementsKey].length&&i.push(y(t[e.elementsKey],e,0,!i.length))),i.join("")}},54924:(t,e,i)=>{var n=i(98688);t.exports=function(t,e){t instanceof Buffer&&(t=t.toString());var i=null;if("string"==typeof t)try{i=JSON.parse(t)}catch(t){throw Error("The JSON structure is invalid")}else i=t;return n(i,e)}},45393:(t,e,i)=>{var n=i(47947).isArray;t.exports={copyOptions:function(t){var e,i={};for(e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);return i},ensureFlagExists:function(t,e){t in e&&"boolean"==typeof e[t]||(e[t]=!1)},ensureSpacesExists:function(t){"spaces"in t&&("number"==typeof t.spaces||"string"==typeof t.spaces)||(t.spaces=0)},ensureAlwaysArrayExists:function(t){"alwaysArray"in t&&("boolean"==typeof t.alwaysArray||n(t.alwaysArray))||(t.alwaysArray=!1)},ensureKeyExists:function(t,e){t+"Key" in e&&"string"==typeof e[t+"Key"]||(e[t+"Key"]=e.compact?"_"+t:t)},checkFnExists:function(t,e){return t+"Fn" in e}}},70424:(t,e,i)=>{var n,r,s=i(65074),a={on:function(){},parse:function(){}},o=i(45393),u=i(47947).isArray,c=!0;function h(t){return n=o.copyOptions(t),o.ensureFlagExists("ignoreDeclaration",n),o.ensureFlagExists("ignoreInstruction",n),o.ensureFlagExists("ignoreAttributes",n),o.ensureFlagExists("ignoreText",n),o.ensureFlagExists("ignoreComment",n),o.ensureFlagExists("ignoreCdata",n),o.ensureFlagExists("ignoreDoctype",n),o.ensureFlagExists("compact",n),o.ensureFlagExists("alwaysChildren",n),o.ensureFlagExists("addParent",n),o.ensureFlagExists("trim",n),o.ensureFlagExists("nativeType",n),o.ensureFlagExists("nativeTypeAttributes",n),o.ensureFlagExists("sanitize",n),o.ensureFlagExists("instructionHasAttributes",n),o.ensureFlagExists("captureSpacesBetweenElements",n),o.ensureAlwaysArrayExists(n),o.ensureKeyExists("declaration",n),o.ensureKeyExists("instruction",n),o.ensureKeyExists("attributes",n),o.ensureKeyExists("text",n),o.ensureKeyExists("comment",n),o.ensureKeyExists("cdata",n),o.ensureKeyExists("doctype",n),o.ensureKeyExists("type",n),o.ensureKeyExists("name",n),o.ensureKeyExists("elements",n),o.ensureKeyExists("parent",n),o.checkFnExists("doctype",n),o.checkFnExists("instruction",n),o.checkFnExists("cdata",n),o.checkFnExists("comment",n),o.checkFnExists("text",n),o.checkFnExists("instructionName",n),o.checkFnExists("elementName",n),o.checkFnExists("attributeName",n),o.checkFnExists("attributeValue",n),o.checkFnExists("attributes",n),n}function l(t){var e=Number(t);if(!isNaN(e))return e;var i=t.toLowerCase();return"true"===i||"false"!==i&&t}function p(t,e){var i;if(n.compact){if(!r[n[t+"Key"]]&&(u(n.alwaysArray)?-1!==n.alwaysArray.indexOf(n[t+"Key"]):n.alwaysArray)&&(r[n[t+"Key"]]=[]),r[n[t+"Key"]]&&!u(r[n[t+"Key"]])&&(r[n[t+"Key"]]=[r[n[t+"Key"]]]),t+"Fn" in n&&"string"==typeof e&&(e=n[t+"Fn"](e,r)),"instruction"===t&&("instructionFn"in n||"instructionNameFn"in n)){for(i in e)if(e.hasOwnProperty(i)){if("instructionFn"in n)e[i]=n.instructionFn(e[i],i,r);else{var s=e[i];delete e[i],e[n.instructionNameFn(i,s,r)]=s}}}u(r[n[t+"Key"]])?r[n[t+"Key"]].push(e):r[n[t+"Key"]]=e}else{r[n.elementsKey]||(r[n.elementsKey]=[]);var a={};if(a[n.typeKey]=t,"instruction"===t){for(i in e)if(e.hasOwnProperty(i))break;a[n.nameKey]="instructionNameFn"in n?n.instructionNameFn(i,e,r):i,n.instructionHasAttributes?(a[n.attributesKey]=e[i][n.attributesKey],"instructionFn"in n&&(a[n.attributesKey]=n.instructionFn(a[n.attributesKey],i,r))):("instructionFn"in n&&(e[i]=n.instructionFn(e[i],i,r)),a[n.instructionKey]=e[i])}else t+"Fn" in n&&(e=n[t+"Fn"](e,r)),a[n[t+"Key"]]=e;n.addParent&&(a[n.parentKey]=r),r[n.elementsKey].push(a)}}function d(t){if("attributesFn"in n&&t&&(t=n.attributesFn(t,r)),(n.trim||"attributeValueFn"in n||"attributeNameFn"in n||n.nativeTypeAttributes)&&t){var e;for(e in t)if(t.hasOwnProperty(e)&&(n.trim&&(t[e]=t[e].trim()),n.nativeTypeAttributes&&(t[e]=l(t[e])),"attributeValueFn"in n&&(t[e]=n.attributeValueFn(t[e],e,r)),"attributeNameFn"in n)){var i=t[e];delete t[e],t[n.attributeNameFn(e,t[e],r)]=i}}return t}function f(t){var e={};if(t.body&&("xml"===t.name.toLowerCase()||n.instructionHasAttributes)){for(var i,s=/([\w:-]+)\s*=\s*(?:"([^"]*)"|'([^']*)'|(\w+))\s*/g;null!==(i=s.exec(t.body));)e[i[1]]=i[2]||i[3]||i[4];e=d(e)}if("xml"===t.name.toLowerCase()){if(n.ignoreDeclaration)return;r[n.declarationKey]={},Object.keys(e).length&&(r[n.declarationKey][n.attributesKey]=e),n.addParent&&(r[n.declarationKey][n.parentKey]=r)}else{if(n.ignoreInstruction)return;n.trim&&(t.body=t.body.trim());var a={};n.instructionHasAttributes&&Object.keys(e).length?(a[t.name]={},a[t.name][n.attributesKey]=e):a[t.name]=t.body,p("instruction",a)}}function m(t,e){var i,s;if("object"==typeof t&&(e=t.attributes,t=t.name),e=d(e),"elementNameFn"in n&&(t=n.elementNameFn(t,r)),n.compact){if(i={},!n.ignoreAttributes&&e&&Object.keys(e).length)for(s in i[n.attributesKey]={},e)e.hasOwnProperty(s)&&(i[n.attributesKey][s]=e[s]);t in r||(u(n.alwaysArray)?-1===n.alwaysArray.indexOf(t):!n.alwaysArray)||(r[t]=[]),r[t]&&!u(r[t])&&(r[t]=[r[t]]),u(r[t])?r[t].push(i):r[t]=i}else r[n.elementsKey]||(r[n.elementsKey]=[]),(i={})[n.typeKey]="element",i[n.nameKey]=t,!n.ignoreAttributes&&e&&Object.keys(e).length&&(i[n.attributesKey]=e),n.alwaysChildren&&(i[n.elementsKey]=[]),r[n.elementsKey].push(i);i[n.parentKey]=r,r=i}function x(t){!n.ignoreText&&(t.trim()||n.captureSpacesBetweenElements)&&(n.trim&&(t=t.trim()),n.nativeType&&(t=l(t)),n.sanitize&&(t=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")),p("text",t))}function D(t){n.ignoreComment||(n.trim&&(t=t.trim()),p("comment",t))}function y(t){var e=r[n.parentKey];n.addParent||delete r[n.parentKey],r=e}function g(t){n.ignoreCdata||(n.trim&&(t=t.trim()),p("cdata",t))}function E(t){n.ignoreDoctype||(t=t.replace(/^ /,""),n.trim&&(t=t.trim()),p("doctype",t))}function A(t){t.note=t}t.exports=function(t,e){var i=c?s.parser(!0,{}):i=new a.Parser("UTF-8"),o={};if(r=o,n=h(e),c?(i.opt={strictEntities:!0},i.onopentag=m,i.ontext=x,i.oncomment=D,i.onclosetag=y,i.onerror=A,i.oncdata=g,i.ondoctype=E,i.onprocessinginstruction=f):(i.on("startElement",m),i.on("text",x),i.on("comment",D),i.on("endElement",y),i.on("error",A)),c)i.write(t).close();else if(!i.parse(t))throw Error("XML parsing error: "+i.getError());if(o[n.elementsKey]){var u=o[n.elementsKey];delete o[n.elementsKey],o[n.elementsKey]=u,delete o.text}return o}},2222:(t,e,i)=>{var n=i(45393),r=i(70424);function s(t){var e=n.copyOptions(t);return n.ensureSpacesExists(e),e}t.exports=function(t,e){var i,n,a,o;return n=r(t,i=s(e)),o="compact"in i&&i.compact?"_parent":"parent",(a="addParent"in i&&i.addParent?JSON.stringify(n,function(t,e){return t===o?"_":e},i.spaces):JSON.stringify(n,null,i.spaces)).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}},14927:(t,e,i)=>{"use strict";i.d(e,{Do:()=>s,eF:()=>r,u2:()=>n});let n="Jay-Yao",r="I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support.",s="<EMAIL>"}};var __webpack_require__=require("../../webpack-runtime.js");__webpack_require__.C(exports);var __webpack_exec__=t=>__webpack_require__(__webpack_require__.s=t),__webpack_exports__=__webpack_require__.X(0,[948],()=>__webpack_exec__(12209));module.exports=__webpack_exports__})();