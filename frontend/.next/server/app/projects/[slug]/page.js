(()=>{var e={};e.id=101,e.ids=[101],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},9680:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),t(21266),t(48109),t(27683);var a=t(23191),o=t(88716),s=t(37922),n=t.n(s),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["projects",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21266)),"/home/<USER>/Code/me/My-web/frontend/src/app/projects/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/projects/[slug]/page.tsx"],m="/projects/[slug]/page",p={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/projects/[slug]/page",pathname:"/projects/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},12397:(e,r,t)=>{Promise.resolve().then(t.bind(t,5408)),Promise.resolve().then(t.bind(t,19754))},92498:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},7027:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5408:(e,r,t)=>{"use strict";t.d(r,{BlogContent:()=>n});var a=t(10326),o=t(17577);function s({src:e,alt:r,onClose:t}){return a.jsx("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 animate-fade-in",style:{backdropFilter:"blur(10px)"},onClick:t,children:a.jsx("img",{src:e,alt:r,className:"max-w-full max-h-full object-contain rounded-lg shadow-2xl animate-scale-in",onClick:e=>e.stopPropagation()})})}function n({content:e,className:r=""}){let t=(0,o.useRef)(null),[n,i]=(0,o.useState)(null);return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{ref:t,className:`blog-content-container prose prose-zinc dark:prose-invert max-w-none ${r}`,style:{columnCount:"unset",columns:"unset",columnFill:"unset",columnGap:"unset"},dangerouslySetInnerHTML:{__html:e}}),n&&a.jsx(s,{src:n.src,alt:n.alt,onClose:()=>i(null)})]})}t(70331)},19754:(e,r,t)=>{"use strict";t.d(r,{ProjectLayout:()=>w});var a=t(10326),o=t(17577),s=t.n(o),n=t(35047),i=t(12315),l=t(30131),d=t(4205),c=t(42192),m=t(94046),p=t(56006),u=t(39414),x=t(22428);t(23201);var h=t(33734),g=t(6343),v=t(37358),b=t(79635),f=t(7027),y=t(92498);function j(e){return a.jsx("svg",{viewBox:"0 0 16 16",fill:"none","aria-hidden":"true",...e,children:a.jsx("path",{d:"M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}function w({project:e,children:r}){let t=(0,n.useRouter)(),{previousPathname:w}=(0,o.useContext)(i.I),[k,N]=s().useState(""),z=s().useCallback(()=>{if(w){let e=w.includes("/projects")||"/"===w;t.back(),e&&setTimeout(()=>{t.refresh()},150)}else t.push("/projects")},[t,w]);return(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.CK,{}),a.jsx(l.W2,{className:"mt-16 lg:mt-32",children:(0,a.jsxs)("div",{className:"xl:relative",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl",children:[a.jsx("div",{className:"mb-8",children:a.jsx("button",{onClick:z,className:"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20","aria-label":"返回项目列表",children:a.jsx(j,{className:"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400"})})}),(0,a.jsxs)("article",{className:"animate-fade-in-up",children:[(0,a.jsxs)("header",{className:"relative mb-16 p-10 rounded-3xl bg-gradient-to-r from-background/85 via-background/95 to-background/85 border border-border/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl hover:shadow-primary/10 transition-all duration-700 group/header overflow-hidden",style:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"},children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700"}),a.jsx("div",{className:"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300"}),a.jsx("div",{className:"absolute top-0 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent opacity-60"}),a.jsx("div",{className:"absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-primary/30 rounded-tl-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),a.jsx("div",{className:"absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-primary/30 rounded-tr-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),(0,a.jsxs)("div",{className:"relative z-10 space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[e.is_featured&&(0,a.jsxs)("span",{className:"relative inline-flex items-center gap-1.5 px-4 py-2 text-xs font-bold bg-gradient-to-r from-amber-500/15 to-orange-500/15 border-2 border-amber-500/30 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 hover:shadow-lg hover:shadow-amber-500/25 transition-all duration-300 cursor-default overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-500/20 to-orange-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300"}),a.jsx(h.Z,{className:"w-3 h-3 fill-current animate-pulse-soft relative z-10"}),a.jsx("span",{className:"relative z-10 tracking-wide",children:"FEATURED"})]}),e.status&&(0,a.jsxs)("span",{className:"relative inline-flex items-center px-4 py-2 text-xs font-bold bg-gradient-to-r from-green-500/15 to-emerald-500/15 border-2 border-green-500/30 rounded-full text-green-700 dark:text-green-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 cursor-default overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300"}),a.jsx("span",{className:"relative z-10 tracking-wide uppercase",children:e.status})]})]}),a.jsx("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md",style:{transform:"translateZ(20px)",transformStyle:"preserve-3d",fontFamily:'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif'},children:e.name}),e.description&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[a.jsx("div",{className:"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm",children:[a.jsx(g.Z,{className:"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300"}),a.jsx("span",{className:"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide",children:"ABSTRACT"})]}),a.jsx("div",{className:"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1"})]}),(0,a.jsxs)("div",{className:"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5",children:[a.jsx("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),a.jsx("div",{className:"absolute top-6 right-6 w-3 h-3 bg-primary/20 rounded-full blur-sm animate-pulse-soft"}),a.jsx("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-secondary/30 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"1s"}}),a.jsx("div",{className:"relative z-10",children:a.jsx("p",{className:"text-lg sm:text-xl leading-relaxed text-muted-foreground group-hover/header:text-foreground transition-colors duration-300 font-medium",style:{fontFamily:'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',fontStyle:"italic"},children:e.description})})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-8 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[a.jsx("div",{className:"p-1.5 rounded-lg bg-primary/10 text-primary group-hover/meta:bg-primary/20 group-hover/meta:scale-110 transition-all duration-300",children:a.jsx(v.Z,{className:"w-4 h-4"})}),a.jsx("time",{dateTime:e.display_date||e.created_at||e.updated_at,className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:(0,x.p)(e.display_date||e.created_at||e.updated_at)})]}),e.author&&(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[a.jsx("div",{className:"p-1.5 rounded-lg bg-blue-500/10 text-blue-500 group-hover/meta:bg-blue-500/20 group-hover/meta:scale-110 transition-all duration-300",children:a.jsx(b.Z,{className:"w-4 h-4"})}),a.jsx("span",{className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:e.author})]}),e.module_categories&&e.module_categories.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[a.jsx("div",{className:"p-1.5 rounded-lg bg-green-500/10 text-green-500 group-hover/meta:bg-green-500/20 group-hover/meta:scale-110 transition-all duration-300",children:a.jsx(g.Z,{className:"w-4 h-4"})}),a.jsx("span",{className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:e.module_categories[0].name})]}),a.jsx(u.CommentStats,{path:`/projects/${e.slug}`,showIcons:!0})]}),(e.github_url||e.demo_url||e.link)&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[e.github_url&&(0,a.jsxs)("a",{href:e.github_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 group/link",children:[a.jsx("svg",{className:"w-4 h-4 group-hover/link:scale-110 transition-transform duration-300",fill:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})}),"GitHub"]}),e.demo_url&&(0,a.jsxs)("a",{href:e.demo_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-primary to-primary/90 rounded-xl hover:shadow-lg hover:shadow-primary/25 hover:scale-105 transition-all duration-300 group/link",children:[a.jsx("svg",{className:"w-4 h-4 group-hover/link:scale-110 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})}),"Live Demo"]}),e.link&&(0,a.jsxs)("a",{href:"string"==typeof e.link?e.link:e.link.href,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 group/link",children:[a.jsx(f.Z,{className:"w-4 h-4 group-hover/link:scale-110 transition-transform duration-300"}),"Visit Project"]})]}),e.tech_stack&&e.tech_stack.length>0&&a.jsx("div",{className:"flex flex-wrap gap-2",children:e.tech_stack.map((e,r)=>(0,a.jsxs)("span",{className:"group/tag relative inline-flex items-center px-3 py-1.5 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 transition-all duration-300 cursor-pointer overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm bg-primary"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500"}),a.jsx("span",{className:"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300",children:e.name||e}),a.jsx("div",{className:"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200"})]},r))})]})]}),a.jsx(d.M,{className:"mt-8","data-mdx-content":!0,children:r}),a.jsx("section",{className:"mt-20 mb-12",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"}),a.jsx("div",{className:"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent"}),(0,a.jsxs)("div",{className:"pt-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm",children:[a.jsx("div",{className:"p-2 rounded-xl bg-primary/10 text-primary",children:a.jsx(y.Z,{className:"w-5 h-5"})}),a.jsx("span",{className:"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase",children:"Project Discussion"})]}),a.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3",children:"Share Your Experience"}),a.jsx("p",{className:"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed",children:"Questions, suggestions, or just want to share how you're using this project? Let's connect."})]}),a.jsx(p.WalineComment,{path:`/projects/${e.slug}`,title:e.name,className:"max-w-5xl mx-auto"})]})]})})]})]}),a.jsx("div",{className:"hidden 2xl:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto",children:a.jsx(m.r,{content:k})}),a.jsx("div",{className:"hidden xl:block 2xl:hidden fixed right-2 top-1/2 transform -translate-y-1/2 w-72 z-40 pointer-events-auto",children:a.jsx(m.r,{content:k})}),a.jsx("div",{className:"xl:hidden",children:a.jsx(m.r,{content:k})})]})})]})}},21266:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x,generateMetadata:()=>p,revalidate:()=>m,runtime:()=>c});var a=t(19510),o=t(58585),s=t(6879),n=t(96421),i=t(68570);let l=(0,i.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/layout/ProjectLayout.tsx#ProjectLayout`),d=(0,i.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/blog/BlogContent.tsx#BlogContent`),c="nodejs",m=1800;async function p({params:e}){let r=e.slug;try{let e=await (0,s.IQ)(r);if(!e)return{title:"项目未找到"};return{title:e.name,description:e.description}}catch(e){return{title:"加载错误",description:"加载项目时发生了错误"}}}async function u({project:e}){try{let r=await (0,n.Q)(e.slug,"project");return a.jsx(d,{content:r,className:"project-detail-content rich-text-content prose dark:prose-invert max-w-none"})}catch(e){return(0,a.jsxs)("div",{className:"prose dark:prose-invert max-w-none",children:[a.jsx("p",{children:"抱歉，无法加载项目内容。请稍后再试。"}),a.jsx("p",{className:"text-red-500",children:e instanceof Error?e.message:"未知错误"})]})}}async function x({params:e}){let r=e.slug;try{let e=await (0,s.IQ)(r);return e||(0,o.notFound)(),e.has_detail_page||(0,o.notFound)(),a.jsx(l,{project:e,children:e.content&&e.content.trim()?a.jsx(u,{project:e}):e.detail_content?a.jsx("div",{className:"project-detail-content rich-text-content prose dark:prose-invert max-w-none",dangerouslySetInnerHTML:{__html:e.detail_content}}):a.jsx("div",{className:"prose dark:prose-invert max-w-none",children:a.jsx("p",{children:"此项目暂无详细内容。"})})})}catch(e){throw e}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[948,499,469,466,722,26,116,192],()=>t(9680));module.exports=a})();