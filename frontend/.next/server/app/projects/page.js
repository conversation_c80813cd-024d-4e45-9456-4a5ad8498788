(()=>{var e={};e.id=895,e.ids=[895],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},46151:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(73767),t(48109),t(27683);var a=t(23191),s=t(88716),o=t(37922),l=t.n(o),i=t(95231),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let d=["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73767)),"/home/<USER>/Code/me/My-web/frontend/src/app/projects/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/projects/page.tsx"],m="/projects/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21331:(e,r,t)=>{Promise.resolve().then(t.bind(t,933)),Promise.resolve().then(t.bind(t,46618)),Promise.resolve().then(t.bind(t,15372)),Promise.resolve().then(t.bind(t,52052))},37358:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},96633:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},92498:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},7027:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},41137:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},924:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},77506:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},11019:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},83855:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88307:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},33734:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40765:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3634:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},933:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let a=t(94129);function s(e){let{reason:r,children:t}=e;throw new a.BailoutToCSRError(r)}},46618:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"PreloadCss",{enumerable:!0,get:function(){return o}});let a=t(10326),s=t(54580);function o(e){let{moduleIds:r}=e,t=(0,s.getExpectedRequestStore)("next/dynamic css"),o=[];if(t.reactLoadableManifest&&r){let e=t.reactLoadableManifest;for(let t of r){if(!e[t])continue;let r=e[t].files.filter(e=>e.endsWith(".css"));o.push(...r)}}return 0===o.length?null:(0,a.jsx)(a.Fragment,{children:o.map(e=>(0,a.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:t.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},15372:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>R});var a=t(10326),s=t(17577),o=t(30131),l=t(93879),i=t(51223),n=t(62881);let d=(0,n.Z)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);var c=t(924),m=t(88307),u=t(83855),p=t(11019),x=t(941),h=t(96633),g=t(94019),f=t(92498),b=t(33734);let y=(0,n.Z)("GitFork",[["circle",{cx:"12",cy:"18",r:"3",key:"1mpf1b"}],["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["path",{d:"M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9",key:"1uq4wg"}],["path",{d:"M12 12v3",key:"158kv8"}]]);var v=t(12714),j=t(3634),w=t(37358),N=t(7027);let k=(0,n.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);var _=t(46226),C=t(61711);function S({project:e,isOpen:r,onClose:t,onNext:o,onPrev:l}){let[i,n]=(0,s.useState)(!1);if(!r||!e)return null;let d=()=>{if(e.has_detail_page)return`/projects/${e.slug}`;if(e.link){let r="string"==typeof e.link?e.link:e.link.href;return r.startsWith("http")?r:`https://${r}`}return"#"},c=!e.has_detail_page&&e.link;return(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[a.jsx("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:t}),(0,a.jsxs)("div",{className:"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-background/95 backdrop-blur-md rounded-3xl border border-border/50 shadow-2xl overflow-hidden animate-fade-in-up",children:[a.jsx("button",{onClick:t,className:"absolute top-4 right-4 z-10 w-10 h-10 bg-background/80 hover:bg-background rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm border border-border/50",children:a.jsx(g.Z,{size:20})}),l&&a.jsx("button",{onClick:l,className:"absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-background/80 hover:bg-background rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm border border-border/50",children:"←"}),o&&a.jsx("button",{onClick:o,className:"absolute right-16 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-background/80 hover:bg-background rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm border border-border/50",children:"→"}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row h-full max-h-[90vh]",children:[a.jsx("div",{className:"lg:w-1/2 relative bg-gradient-to-br from-primary/5 to-secondary/5 flex items-center justify-center min-h-[300px] lg:min-h-[500px]",children:e.preview_image?(0,a.jsxs)("div",{className:"relative w-full h-full",children:[a.jsx(_.default,{src:e.preview_image,alt:e.name,fill:!0,className:`object-cover transition-opacity duration-300 ${i?"opacity-100":"opacity-0"}`,onLoad:()=>n(!0)}),!i&&a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"})})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[a.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mb-4",children:e.icon?a.jsx(C.AntdIcon,{iconName:e.icon,size:48}):a.jsx(f.Z,{size:48})}),a.jsx("p",{className:"text-sm",children:"No preview available"})]})}),a.jsx("div",{className:"lg:w-1/2 p-8 overflow-y-auto",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center border border-primary/20",children:e.icon?a.jsx(C.AntdIcon,{iconName:e.icon,size:32}):a.jsx(f.Z,{size:32,className:"text-primary"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h2",{className:"text-2xl font-bold text-foreground mb-2",children:e.name}),e.stats&&(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.stats.stars&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(b.Z,{size:14}),a.jsx("span",{children:e.stats.stars})]}),e.stats.forks&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(y,{size:14}),a.jsx("span",{children:e.stats.forks})]}),e.stats.views&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(v.Z,{size:14}),a.jsx("span",{children:e.stats.views})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[a.jsx(j.Z,{size:18,className:"text-primary"}),"Description"]}),a.jsx("p",{className:"text-muted-foreground leading-relaxed",children:e.description})]}),e.tech_stack&&e.tech_stack.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[a.jsx(f.Z,{size:18,className:"text-primary"}),"Tech Stack"]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.tech_stack.map((e,r)=>a.jsx("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20",children:e},r))})]}),e.categories&&e.categories.length>0&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Categories"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.categories.map((e,r)=>a.jsx("span",{className:"px-3 py-1 rounded-full text-sm font-medium border",style:{backgroundColor:e.color?`${e.color}20`:"hsl(var(--muted))",borderColor:e.color?`${e.color}40`:"hsl(var(--border))",color:e.color||"hsl(var(--foreground))"},children:e.name},r))})]}),(e.created_date||e.last_updated)&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[a.jsx(w.Z,{size:18,className:"text-primary"}),"Timeline"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[e.created_date&&(0,a.jsxs)("div",{children:["Created: ",new Date(e.created_date).toLocaleDateString()]}),e.last_updated&&(0,a.jsxs)("div",{children:["Last updated: ",new Date(e.last_updated).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[c?(0,a.jsxs)("a",{href:d(),target:"_blank",rel:"noopener noreferrer",className:"flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-xl font-medium hover:bg-primary/90 transition-colors duration-200 text-center flex items-center justify-center gap-2",children:["View Project",a.jsx(N.Z,{size:16})]}):a.jsx("a",{href:d(),className:"flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-xl font-medium hover:bg-primary/90 transition-colors duration-200 text-center",children:"View Details"}),e.link&&"object"==typeof e.link&&e.link.href?.includes("github")&&(0,a.jsxs)("a",{href:e.link.href,target:"_blank",rel:"noopener noreferrer",className:"px-6 py-3 border border-border/50 rounded-xl font-medium hover:bg-background/80 hover:border-primary/30 transition-all duration-300 backdrop-blur-sm flex items-center justify-center gap-2",children:[a.jsx(k,{size:16}),"GitHub"]})]})]})})]})]})]})}let z={ai:{name:"AI/Machine Learning",colors:{primary:"#667eea",secondary:"#764ba2",accent:"#f093fb",gradient:"from-purple-500/20 via-blue-500/10 to-indigo-500/20",glow:"shadow-purple-500/25",border:"border-purple-500/30"},keywords:["ai","machine learning","ml","neural","deep learning","tensorflow","pytorch","nlp","computer vision"]},web:{name:"Web Development",colors:{primary:"#4facfe",secondary:"#00f2fe",accent:"#43e97b",gradient:"from-blue-500/20 via-cyan-500/10 to-teal-500/20",glow:"shadow-blue-500/25",border:"border-blue-500/30"},keywords:["web","frontend","react","vue","angular","javascript","typescript","html","css","nextjs"]},backend:{name:"Backend Development",colors:{primary:"#43e97b",secondary:"#38f9d7",accent:"#4facfe",gradient:"from-green-500/20 via-teal-500/10 to-emerald-500/20",glow:"shadow-green-500/25",border:"border-green-500/30"},keywords:["backend","api","server","database","node","python","java","go","rust","php"]},mobile:{name:"Mobile Development",colors:{primary:"#fa709a",secondary:"#fee140",accent:"#667eea",gradient:"from-pink-500/20 via-yellow-500/10 to-orange-500/20",glow:"shadow-pink-500/25",border:"border-pink-500/30"},keywords:["mobile","ios","android","react native","flutter","swift","kotlin","xamarin"]},devops:{name:"DevOps & Infrastructure",colors:{primary:"#f093fb",secondary:"#f5576c",accent:"#4facfe",gradient:"from-purple-500/20 via-pink-500/10 to-red-500/20",glow:"shadow-purple-500/25",border:"border-purple-500/30"},keywords:["devops","docker","kubernetes","aws","azure","gcp","ci/cd","terraform","ansible"]},data:{name:"Data Science",colors:{primary:"#4facfe",secondary:"#43e97b",accent:"#f093fb",gradient:"from-blue-500/20 via-green-500/10 to-purple-500/20",glow:"shadow-blue-500/25",border:"border-blue-500/30"},keywords:["data","analytics","visualization","pandas","numpy","jupyter","r","statistics","big data"]},game:{name:"Game Development",colors:{primary:"#667eea",secondary:"#f093fb",accent:"#43e97b",gradient:"from-indigo-500/20 via-purple-500/10 to-pink-500/20",glow:"shadow-indigo-500/25",border:"border-indigo-500/30"},keywords:["game","unity","unreal","godot","c#","c++","3d","graphics","shader"]},blockchain:{name:"Blockchain & Web3",colors:{primary:"#f5576c",secondary:"#f093fb",accent:"#4facfe",gradient:"from-red-500/20 via-pink-500/10 to-purple-500/20",glow:"shadow-red-500/25",border:"border-red-500/30"},keywords:["blockchain","web3","ethereum","solidity","smart contract","defi","nft","crypto"]},default:{name:"General",colors:{primary:"hsl(var(--primary))",secondary:"hsl(var(--secondary))",accent:"hsl(var(--accent))",gradient:"from-primary/20 via-secondary/10 to-accent/20",glow:"shadow-primary/25",border:"border-primary/30"},keywords:[]}},M=(0,s.createContext)(void 0);function P({children:e}){let r=(0,s.useMemo)(()=>({getThemeForProject:e=>{let r=e.categories||e.module_categories||[],t=(e.description||"").toLowerCase(),a=(e.name||"").toLowerCase(),s=e.tech_stack||[],o=[...r.map(e=>e.name.toLowerCase()),t,a,...s.map(e=>e.toLowerCase())].join(" ");for(let[e,r]of Object.entries(z))if("default"!==e&&r.keywords.reduce((e,r)=>e+(o.includes(r)?1:0),0)>0)return r;return z.default},getThemeByType:e=>z[e]||z.default,getAllThemes:()=>z}),[]);return a.jsx(M.Provider,{value:r,children:e})}function Z(){let e=(0,s.useContext)(M);if(void 0===e)throw Error("useProjectTheme must be used within a ProjectThemeProvider");return e}var L=t(41137);let $=(0,n.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var O=t(40765);function T({projects:e,categories:r,onFilterChange:t,className:o=""}){let[l,i]=(0,s.useState)(!1),[n,d]=(0,s.useState)({search:"",selectedCategories:[],selectedTechStack:[],sortBy:"name",sortOrder:"asc",showOnlyFeatured:!1}),{getAllThemes:c}=Z();c();let u=(0,s.useMemo)(()=>{let r=new Set;return e.forEach(e=>{e.tech_stack?.forEach(e=>r.add(e)),e.categories?.forEach(e=>r.add(e.name))}),Array.from(r).sort()},[e]),p=(0,s.useMemo)(()=>{let r=e.filter(e=>{if(n.search){let r=n.search.toLowerCase();if(!(e.name.toLowerCase().includes(r)||e.description?.toLowerCase().includes(r)||e.tech_stack?.some(e=>e.toLowerCase().includes(r))||e.categories?.some(e=>e.name.toLowerCase().includes(r))))return!1}if(n.selectedCategories.length>0){let r=e.categories?.map(e=>e.name)||[];if(!n.selectedCategories.some(e=>r.includes(e)))return!1}if(n.selectedTechStack.length>0){let r=e.tech_stack||[];if(!n.selectedTechStack.some(e=>r.includes(e)))return!1}return!n.showOnlyFeatured||!!e.featured});return r.sort((e,r)=>{let t=0;switch(n.sortBy){case"name":t=e.name.localeCompare(r.name);break;case"date":let a=new Date(e.created_date||e.updated_at||0),s=new Date(r.created_date||r.updated_at||0);t=a.getTime()-s.getTime();break;case"popularity":t=(e.stats?.stars||0)+(e.stats?.views||0)-((r.stats?.stars||0)+(r.stats?.views||0));break;case"category":let o=e.categories?.[0]?.name||"",l=r.categories?.[0]?.name||"";t=o.localeCompare(l)}return"desc"===n.sortOrder?-t:t}),r},[e,n]),x=(e,r)=>{d(t=>({...t,[e]:r}))},h=e=>{d(r=>({...r,selectedCategories:r.selectedCategories.includes(e)?r.selectedCategories.filter(r=>r!==e):[...r.selectedCategories,e]}))},y=e=>{d(r=>({...r,selectedTechStack:r.selectedTechStack.includes(e)?r.selectedTechStack.filter(r=>r!==e):[...r.selectedTechStack,e]}))},v=(n.search?1:0)+n.selectedCategories.length+n.selectedTechStack.length+(n.showOnlyFeatured?1:0);return(0,a.jsxs)("div",{className:`relative ${o}`,children:[a.jsx("div",{className:"relative mb-6 z-[1000]",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(m.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",size:20}),a.jsx("input",{type:"text",placeholder:"Search projects...",value:n.search,onChange:e=>x("search",e.target.value),className:"w-full pl-10 pr-12 py-3 bg-background/60 border border-border/50 rounded-xl backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-300"}),(0,a.jsxs)("button",{onClick:()=>i(!l),className:`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg transition-all duration-300 ${l?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,children:[a.jsx(L.Z,{size:18}),v>0&&a.jsx("span",{className:"absolute -top-1 -right-1 w-4 h-4 bg-primary text-primary-foreground text-xs rounded-full flex items-center justify-center",children:v})]})]})}),l&&a.jsx("div",{className:"absolute top-full left-0 right-0 z-[99999] mt-2 p-6 bg-background backdrop-blur-lg border border-border/50 rounded-2xl shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25)] animate-fade-in-up",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center gap-2",children:[a.jsx(L.Z,{size:20,className:"text-primary"}),"Advanced Filters"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[v>0&&a.jsx("button",{onClick:()=>{d({search:"",selectedCategories:[],selectedTechStack:[],sortBy:"name",sortOrder:"asc",showOnlyFeatured:!1})},className:"text-sm text-muted-foreground hover:text-foreground transition-colors duration-200",children:"Clear All"}),a.jsx("button",{onClick:()=>i(!1),className:"p-1 text-muted-foreground hover:text-foreground transition-colors duration-200",children:a.jsx(g.Z,{size:18})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[a.jsx($,{size:16,className:"text-primary"}),"Sort By"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("select",{value:n.sortBy,onChange:e=>x("sortBy",e.target.value),className:"px-3 py-2 bg-background/60 border border-border/50 rounded-lg backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50",children:[a.jsx("option",{value:"name",children:"Name"}),a.jsx("option",{value:"date",children:"Date"}),a.jsx("option",{value:"popularity",children:"Popularity"}),a.jsx("option",{value:"category",children:"Category"})]}),(0,a.jsxs)("select",{value:n.sortOrder,onChange:e=>x("sortOrder",e.target.value),className:"px-3 py-2 bg-background/60 border border-border/50 rounded-lg backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50",children:[a.jsx("option",{value:"asc",children:"Ascending"}),a.jsx("option",{value:"desc",children:"Descending"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[a.jsx(O.Z,{size:16,className:"text-primary"}),"Categories"]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:r.map(e=>(0,a.jsxs)("button",{onClick:()=>h(e.categoryName),className:`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${n.selectedCategories.includes(e.categoryName)?"bg-primary text-primary-foreground shadow-lg":"bg-background/60 border border-border/50 hover:bg-primary/10 hover:border-primary/30"}`,children:[e.categoryName,a.jsx("span",{className:"ml-2 text-xs opacity-70",children:e.projects.length})]},e.categoryName))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[a.jsx(f.Z,{size:16,className:"text-primary"}),"Tech Stack"]}),a.jsx("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:u.slice(0,20).map(e=>a.jsx("button",{onClick:()=>y(e),className:`px-2 py-1 rounded-md text-xs font-medium transition-all duration-300 ${n.selectedTechStack.includes(e)?"bg-secondary text-secondary-foreground":"bg-muted/50 hover:bg-muted"}`,children:e},e))})]}),a.jsx("div",{children:(0,a.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:n.showOnlyFeatured,onChange:e=>x("showOnlyFeatured",e.target.checked),className:"w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary/50"}),(0,a.jsxs)("span",{className:"text-sm font-medium flex items-center gap-2",children:[a.jsx(b.Z,{size:16,className:"text-primary"}),"Show only featured projects"]})]})}),a.jsx("div",{className:"pt-4 border-t border-border/50",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",p.length," of ",e.length," projects"]})})]})})]})}let E=(0,n.Z)("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]]);function D({projects:e,className:r=""}){let{getAllThemes:t}=Z();t();let[o,l]=(0,s.useState)(null),[i,n]=(0,s.useState)(!0),m=o||(()=>{let r=e.length,t=e.filter(e=>e.featured).length,a=e.filter(e=>e.is_open_source||e.github_link).length,s=new Set;return e.forEach(e=>{e.tag_objects?e.tag_objects.forEach(e=>s.add(e.name)):e.tags&&e.tags.forEach(e=>s.add(e))}),{total_projects:r,featured_projects:t,open_source_projects:a,category_count:s.size}})(),u=[{title:"Total Projects",value:m.total_projects,icon:a.jsx(d,{size:24}),color:"text-blue-500",description:"All projects in portfolio"},{title:"Featured Projects",value:m.featured_projects,icon:a.jsx(b.Z,{size:24}),color:"text-yellow-500",description:"Highlighted showcase projects"},{title:"Open Source",value:m.open_source_projects,icon:a.jsx(E,{size:24}),color:"text-green-500",description:"Open source contributions"},{title:"Categories",value:m.category_count,icon:a.jsx(c.Z,{size:24}),color:"text-purple-500",description:"Project categories covered"}];return i?a.jsx("div",{className:`space-y-8 ${r}`,children:a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(e=>a.jsx("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl",children:[a.jsx("div",{className:"flex items-center justify-between mb-4",children:a.jsx("div",{className:"w-12 h-12 bg-muted rounded-xl"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("div",{className:"h-8 bg-muted rounded w-16"}),a.jsx("div",{className:"h-4 bg-muted rounded w-24"}),a.jsx("div",{className:"h-3 bg-muted rounded w-32"})]})]})},e))})}):(0,a.jsxs)("div",{className:`space-y-8 ${r}`,children:[a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:u.map((e,r)=>(0,a.jsxs)("div",{className:"group relative p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1 transition-all duration-500 animate-fade-in-up enhanced-hover",style:{animationDelay:`${100*r}ms`},children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,a.jsxs)("div",{className:"relative z-10",children:[a.jsx("div",{className:"flex items-center justify-between mb-4",children:a.jsx("div",{className:`p-3 rounded-xl bg-background/80 ${e.color} group-hover:scale-110 transition-transform duration-300`,children:e.icon})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300",children:e.value}),a.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:e.title}),a.jsx("p",{className:"text-xs text-muted-foreground/80",children:e.description})]})]})]},e.title))}),m.last_updated&&a.jsx("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(m.last_updated).toLocaleDateString()]})})]})}var I=t(78357),q=t(36283),A=t(75621),F=t(3574);function H({iconSlugs:e}){let[r,t]=(0,s.useState)([]),{theme:o}=(0,F.F)(),[l,i]=(0,s.useState)(!0),n=(0,s.useRef)(null),d=(0,s.useRef)(null);(0,s.useRef)(null);let[c,m]=(0,s.useState)(180),[u,p]=(0,s.useState)(0),[x,h]=(0,s.useState)(0),[g,f]=(0,s.useState)(null),[b,y]=(0,s.useState)(0),[v,j]=(0,s.useState)(0),[w,N]=(0,s.useState)({x:0,y:0}),[k,C]=(0,s.useState)(!0);if(l)return a.jsx("div",{className:"flex justify-center items-center py-20",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"})});let S=e=>{let r=b*Math.PI/180,t=v*Math.PI/180,a=e.x,s=e.z,o=Math.cos(t),l=Math.sin(t),i=a*o-s*l;s=a*l+s*o,a=i;let n=e.y,d=Math.cos(r),m=Math.sin(r),p=n*d-s*m,h=48*(.85+((s=n*m+s*d)+1)*.2),g=.6+(s+1)*.25;return{x:u+a*c,y:x+(n=p)*c,size:h,opacity:g,zIndex:Math.floor((s+1)*100),blur:s<0?`blur(${.25*Math.abs(s)}px)`:"blur(0px)",scale:.95+(s+1)*.15}};return(0,a.jsxs)("div",{ref:n,className:"w-full h-full relative overflow-visible m-0 flex items-center justify-center group",style:{perspective:"1200px"},children:[a.jsx("div",{className:"absolute bg-gradient-radial from-primary/5 via-transparent to-transparent rounded-full blur-3xl opacity-60 group-hover:opacity-80 transition-opacity duration-500",style:{top:"50%",left:"50%",width:"80%",height:"80%",maxWidth:"200px",maxHeight:"200px",transform:"translate(-50%, -50%)",animation:"glow-pulse 4s ease-in-out infinite"}}),a.jsx("div",{className:`absolute rounded-[50%] ${"dark"===o?"bg-gradient-radial from-primary/8 via-teal-500/5 to-transparent":"bg-gradient-radial from-primary/5 via-teal-500/3 to-transparent"} group-hover:opacity-100 transition-opacity duration-300`,style:{bottom:"25%",left:"50%",width:"60%",height:"30%",maxWidth:"120px",maxHeight:"60px",transform:"translateX(-50%) rotateX(70deg)",filter:"blur(12px)",opacity:.7}}),a.jsx("div",{className:"absolute w-2 h-2 bg-primary/40 rounded-full blur-sm animate-pulse-soft",style:{top:"30%",left:"25%",animationDelay:"0s"}}),a.jsx("div",{className:"absolute w-1.5 h-1.5 bg-secondary/40 rounded-full blur-sm animate-pulse-soft",style:{top:"65%",left:"75%",animationDelay:"1s"}}),a.jsx("div",{className:"absolute w-1 h-1 bg-primary/30 rounded-full blur-sm animate-pulse-soft",style:{top:"45%",left:"80%",animationDelay:"2s"}}),a.jsx("div",{ref:d,className:"absolute overflow-visible",style:{transformStyle:"preserve-3d",top:"50%",left:"50%",transform:"translate(-50%, -50%)",padding:"40px",width:"100%",height:"100%",maxWidth:"300px",maxHeight:"300px"},children:r.map(e=>{let r=S(e),t=g===e.slug;return(0,a.jsxs)("div",{className:"absolute transition-all duration-300 ease-out cursor-pointer group",style:{left:`${r.x}px`,top:`${r.y}px`,transform:`translate(-50%, -50%) scale(${r.scale})`,opacity:r.opacity,zIndex:t?999:r.zIndex,filter:r.blur,transition:"opacity 0.3s, filter 0.3s, transform 0.3s"},onMouseEnter:()=>f(e.slug),onMouseLeave:()=>f(null),children:[a.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{transform:"scale(2)",zIndex:-1}}),(0,a.jsxs)("div",{className:"relative transition-all duration-300 ease-smooth",style:{transform:t?"scale(1.6) rotateY(15deg)":"scale(1)",filter:t?"brightness(1.2) saturate(1.3)":"brightness(1) saturate(1)"},children:[a.jsx("div",{className:"absolute inset-0 rounded-full bg-white/10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{transform:"scale(1.2)",zIndex:-1}}),a.jsx(_.default,{src:e.path,alt:e.title,width:r.size,height:r.size,className:`transition-all duration-300 ${"dark"===o?"brightness-110 drop-shadow-glow group-hover:drop-shadow-[0_0_15px_rgba(var(--primary),0.5)]":"drop-shadow-sm group-hover:drop-shadow-lg"}`}),t&&(0,a.jsxs)("div",{className:"absolute left-1/2 top-full transform -translate-x-1/2 mt-2 px-3 py-1.5 rounded-lg bg-foreground/90 text-background text-xs font-medium whitespace-nowrap backdrop-blur-sm border border-white/20 animate-fade-in-up",style:{opacity:.95,zIndex:1e3,boxShadow:"0 4px 12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.1)"},children:[e.title,a.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-foreground/90"})]})]})]},e.slug)})})]})}let W=["openai","claude","anthropic","gemini","meta","mistral","huggingface","qwen","doubao","hunyuan","zhipu","deepseek","langchain","modelscope","cursor","ollama","openrouter","github"];function G({className:e}){let[r,t]=(0,s.useState)(W),[o,l]=(0,s.useState)(!0);return o?a.jsx("div",{className:`flex items-center justify-center h-96 ${e}`,children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4 text-center",children:[a.jsx("div",{className:"w-32 h-32 bg-muted rounded-full mx-auto"}),a.jsx("div",{className:"h-4 bg-muted rounded w-32 mx-auto"}),a.jsx("div",{className:"text-sm text-muted-foreground",children:"Loading tech stack..."})]})}):a.jsx("div",{className:e,children:a.jsx(H,{iconSlugs:r})})}let B=(0,s.forwardRef)(({variant:e="primary",animate:r=!1,className:t,children:s,...o},l)=>a.jsx("span",{ref:l,className:(0,i.cn)("bg-clip-text text-transparent font-semibold",{primary:"bg-gradient-to-r from-primary to-primary/70",secondary:"bg-gradient-to-r from-secondary to-muted",rainbow:"bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600",ocean:"bg-gradient-to-r from-blue-500 via-teal-500 to-green-500"}[e],r&&"animate-pulse-soft",t),...o,children:s}));function R({sortedCategories:e,pagesConfig:r}){let[t,n]=(0,s.useState)(null),[g,f]=(0,s.useState)(!1),[b,y]=(0,s.useState)(0),[v,j]=(0,s.useState)([]),[w,N]=(0,s.useState)(e),[k,_]=(0,s.useState)(!1),[z,M]=(0,s.useState)(new Set),Z=(0,s.useCallback)(e=>{M(r=>{let t=new Set(r);return t.has(e)?t.delete(e):t.add(e),t})},[]),L=(0,s.useCallback)(e=>{y(v.findIndex(r=>r.id===e.id)),n(e),f(!0)},[v]),$=(0,s.useCallback)(()=>{let e=(b+1)%v.length;y(e),n(v[e])},[b,v]),O=(0,s.useCallback)(()=>{let e=0===b?v.length-1:b-1;y(e),n(v[e])},[b,v]),E=(0,s.useCallback)(r=>{let t=new Map;r.forEach((e,r)=>{t.set(e.id,r)}),N(e.map(e=>{let a=e.projects.filter(e=>r.some(r=>r.id===e.id));return a.sort((e,r)=>(t.get(e.id)??1/0)-(t.get(r.id)??1/0)),{...e,projects:a}}).filter(e=>e.projects.length>0))},[e]);return a.jsx(P,{children:(0,a.jsxs)("div",{className:"min-h-screen",children:[a.jsx(o.W2,{className:"mt-16 sm:mt-24",children:(0,a.jsxs)("div",{className:"relative mb-16 rounded-3xl overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/12 via-green-500/5 to-secondary/12"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-background/95 via-background/85 to-background/95"}),a.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent"}),a.jsx("div",{className:"absolute top-8 right-8 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-3xl animate-pulse-slow"}),a.jsx("div",{className:"absolute bottom-8 left-8 w-24 h-24 bg-gradient-to-tr from-green-500/10 to-transparent rounded-full blur-2xl animate-pulse-slow",style:{animationDelay:"1s"}}),a.jsx("div",{className:"absolute top-1/2 left-1/4 w-2 h-2 bg-primary/30 rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-green-500/40 rounded-full animate-pulse",style:{animationDelay:"2s"}}),(0,a.jsxs)("div",{className:"relative py-16 px-8 lg:py-20 lg:px-12 min-h-[500px]",children:[a.jsx("div",{className:"relative z-10 max-w-3xl",children:(0,a.jsxs)(A.AnimatedSection,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h1",{className:"text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl",children:a.jsx(B,{variant:"primary",animate:!0,children:r.projects.title})}),a.jsx("p",{className:"text-lg text-muted-foreground leading-relaxed max-w-2xl",children:r.projects.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 pt-8",children:[a.jsx("div",{className:"group",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-5 rounded-2xl bg-background/60 border border-border/50 backdrop-blur-sm transition-all duration-500 hover:bg-background/90 hover:border-primary/30 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1 enhanced-hover relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),a.jsx("div",{className:"p-3 rounded-xl bg-primary/10 text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300 relative z-10",children:a.jsx(d,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"relative z-10",children:[a.jsx("div",{className:"text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300",children:e.reduce((e,r)=>e+r.projects.length,0)}),a.jsx("div",{className:"text-sm text-muted-foreground",children:"Projects"})]})]})}),a.jsx("div",{className:"group",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-5 rounded-2xl bg-background/60 border border-border/50 backdrop-blur-sm transition-all duration-500 hover:bg-background/90 hover:border-green-500/30 hover:shadow-xl hover:shadow-green-500/10 hover:-translate-y-1 enhanced-hover relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),a.jsx("div",{className:"p-3 rounded-xl bg-green-500/10 text-green-500 group-hover:bg-green-500/20 group-hover:scale-110 transition-all duration-300 relative z-10",children:a.jsx(c.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"relative z-10",children:[a.jsx("div",{className:"text-2xl font-bold text-foreground group-hover:text-green-500 transition-colors duration-300",children:e.length}),a.jsx("div",{className:"text-sm text-muted-foreground",children:"Categories"})]})]})})]})]})}),a.jsx(A.AnimatedSection,{delay:200,direction:"right",className:"absolute top-1/2 right-0 transform -translate-y-4 hidden xl:block",children:(0,a.jsxs)("div",{className:"relative",style:{width:"min(10vw, 160px)",height:"min(10vw, 160px)",minWidth:"120px",minHeight:"120px"},children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-radial from-primary/10 via-transparent to-transparent rounded-full blur-3xl animate-glow-pulse"}),a.jsx("div",{className:"absolute top-4 right-4 w-2 h-2 bg-primary/40 rounded-full blur-sm animate-pulse-soft"}),a.jsx("div",{className:"absolute bottom-8 left-6 w-1.5 h-1.5 bg-secondary/40 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"1s"}}),a.jsx("div",{className:"absolute top-1/2 left-2 w-1 h-1 bg-primary/30 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"2s"}}),a.jsx(G,{})]})})]})]})}),(0,a.jsxs)(o.W2,{className:"mb-16",children:[a.jsx("div",{className:"space-y-8 relative z-[2000]",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 rounded-2xl"}),(0,a.jsxs)("div",{className:"relative p-6 rounded-2xl border border-border/50 backdrop-blur-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[a.jsx("div",{className:"p-2 rounded-lg bg-primary/10 text-primary",children:a.jsx(m.Z,{className:"w-5 h-5"})}),a.jsx("h3",{className:"text-lg font-semibold",children:"Search & Filter Projects"})]}),a.jsx(T,{projects:v,categories:e,onFilterChange:E,className:"w-full"})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 items-start mt-8",children:[a.jsx("div",{className:"flex-1"}),a.jsx("div",{className:"flex gap-3",children:a.jsx("button",{onClick:()=>_(!k),className:"px-6 py-3 bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-xl font-medium hover:bg-primary/20 transition-all duration-300 backdrop-blur-sm",children:k?"Hide Stats":"Show Stats"})})]}),k&&a.jsx("div",{className:"mt-8",children:a.jsx(D,{projects:v})})]}),a.jsx("div",{className:"sm:px-8",children:a.jsx("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:a.jsx("div",{className:"relative px-4 sm:px-8 lg:px-12",children:a.jsx("div",{className:"mx-auto max-w-6xl",children:a.jsx("div",{className:"space-y-20",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/50 hidden lg:block"}),w.map((e,r)=>{let t=z.has(e.categoryName);return a.jsx(A.AnimatedSection,{delay:200*r,children:(0,a.jsxs)("div",{className:"relative mb-16 last:mb-0",children:[(0,a.jsxs)("button",{onClick:()=>Z(e.categoryName),className:"absolute left-2 top-8 w-12 h-12 bg-gradient-to-br from-primary to-primary/70 rounded-full border-4 border-background shadow-lg hidden lg:flex items-center justify-center z-20 hover:scale-110 hover:shadow-xl transition-all duration-300 group/node",children:[(0,a.jsxs)("div",{className:"relative w-full h-full rounded-full overflow-hidden",children:[a.jsx("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-primary via-primary/80 to-primary/60"}),a.jsx("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-white/30 via-transparent to-transparent"}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:t?a.jsx(p.Z,{className:"w-4 h-4 text-white drop-shadow-sm"}):a.jsx(u.Z,{className:"w-4 h-4 text-white drop-shadow-sm"})})]}),a.jsx("div",{className:"absolute inset-0 bg-primary/30 rounded-full animate-ping opacity-0 group-hover/node:opacity-100"})]}),(0,a.jsxs)("div",{className:"relative lg:ml-16",children:[(0,a.jsxs)("div",{className:"relative mb-8",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 rounded-2xl blur-xl"}),a.jsx("button",{onClick:()=>Z(e.categoryName),className:"relative w-full p-6 rounded-2xl bg-background/60 border border-border/30 backdrop-blur-sm hover:bg-background/80 transition-all duration-300 text-left group/header",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl blur opacity-50"}),a.jsx("div",{className:"relative w-12 h-12 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center border border-primary/20 backdrop-blur-sm group-hover/header:scale-105 transition-transform duration-300",children:e.icon?e.icon.includes(":")?a.jsx(I.CustomIcon,{name:e.icon,size:24}):a.jsx(C.AntdIcon,{iconName:e.icon,size:24}):e.categoryName.toLowerCase().includes("open source")||e.categoryName.toLowerCase().includes("开源")?a.jsx(I.CustomIcon,{name:"github",size:24}):a.jsx(q.Z,{size:24})})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("h2",{className:"text-2xl font-bold",children:a.jsx(B,{variant:"primary",children:e.categoryName})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[a.jsx("span",{className:"text-xs font-medium lg:hidden",children:t?"Collapse":"Expand"}),a.jsx("div",{className:"p-1 rounded-md hover:bg-muted/50 transition-colors",children:t?a.jsx(h.Z,{className:"w-4 h-4 transition-transform duration-300"}):a.jsx(x.Z,{className:"w-4 h-4 transition-transform duration-300"})})]})]}),e.description&&a.jsx("p",{className:"text-muted-foreground mt-1",children:e.description})]})]}),a.jsx("div",{className:"px-4 py-2 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full border border-primary/20 backdrop-blur-sm group-hover/header:scale-105 transition-transform duration-300",children:(0,a.jsxs)("span",{className:"text-sm font-medium text-primary",children:[e.projects.length," projects"]})})]})})]}),a.jsx("div",{className:(0,i.cn)("transition-all duration-700 ease-in-out overflow-hidden",t?"max-h-[10000px] opacity-100 transform scale-100 translate-y-0":"max-h-0 opacity-0 transform scale-95 -translate-y-4"),style:{transformOrigin:"top center"},children:t&&a.jsx("div",{className:"project-grid-container px-6 sm:px-8 lg:px-12 py-6",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 sm:gap-10 lg:gap-12 auto-rows-fr",children:e.projects.map((e,t)=>a.jsx("div",{className:"min-w-0 w-full",style:{minWidth:"320px"},children:a.jsx(l.s,{project:e,index:t,categoryIndex:r,onPreview:L})},`${e.id||e.name}-${t}`))})})})]})]})},e.categoryName)})]})})})})})}),a.jsx(S,{project:t,isOpen:g,onClose:()=>f(!1),onNext:$,onPrev:O})]})})}B.displayName="GradientText",(0,s.forwardRef)(({intensity:e="medium",tint:r="none",className:t,children:s,...o},l)=>a.jsx("div",{ref:l,className:(0,i.cn)("rounded-xl border backdrop-blur-md","dark:bg-black/10 dark:border-white/10",{light:"bg-white/5 backdrop-blur-sm border-white/10",medium:"bg-white/10 backdrop-blur-md border-white/20",strong:"bg-white/20 backdrop-blur-lg border-white/30"}[e],{none:"",primary:"bg-primary/5 border-primary/20",secondary:"bg-secondary/5 border-secondary/20"}[r],t),...o,children:s})).displayName="GlassCard",(0,s.forwardRef)(({variant:e="raised",size:r="md",className:t,children:s,...o},l)=>a.jsx("div",{ref:l,className:(0,i.cn)("bg-background border-0 transition-all duration-300",{raised:"shadow-[8px_8px_16px_rgba(0,0,0,0.1),-8px_-8px_16px_rgba(255,255,255,0.1)]",inset:"shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)]",flat:"shadow-[0_0_0_1px_rgba(0,0,0,0.05)]"}[e],{sm:"p-4 rounded-lg",md:"p-6 rounded-xl",lg:"p-8 rounded-2xl"}[r],t),...o,children:s})).displayName="NeumorphismCard",(0,s.forwardRef)(({color:e="primary",intensity:r="medium",animate:t=!1,className:s,children:o,...l},n)=>a.jsx("div",{ref:n,className:(0,i.cn)("transition-all duration-300",{primary:"shadow-primary/50",secondary:"shadow-secondary/50",success:"shadow-green-500/50",warning:"shadow-yellow-500/50",error:"shadow-red-500/50"}[e],{subtle:"shadow-lg",medium:"shadow-xl",strong:"shadow-2xl"}[r],t&&"animate-pulse-soft",s),...l,children:o})).displayName="GlowEffect",(0,s.forwardRef)(({pattern:e="dots",opacity:r=.1,className:t,children:s,...o},l)=>{let n={dots:{backgroundImage:`radial-gradient(circle, currentColor ${100*r}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},grid:{backgroundImage:`linear-gradient(currentColor ${100*r}% 1px, transparent 1px), linear-gradient(90deg, currentColor ${100*r}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},diagonal:{backgroundImage:`repeating-linear-gradient(45deg, transparent, transparent 10px, currentColor ${100*r}% 10px, currentColor ${100*r}% 11px)`},waves:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000' fill-opacity='${r}'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}};return a.jsx("div",{ref:l,className:(0,i.cn)("relative",t),style:n[e],...o,children:s})}).displayName="PatternBackground",(0,s.forwardRef)(({gradient:e="rainbow",width:r=2,animate:t=!1,className:s,children:o,...l},n)=>a.jsx("div",{ref:n,className:(0,i.cn)("relative rounded-xl overflow-hidden",t&&"animate-pulse-soft",s),...l,children:a.jsx("div",{className:(0,i.cn)("absolute inset-0 bg-gradient-to-r",{rainbow:"from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"from-orange-500 via-pink-500 to-purple-600",ocean:"from-blue-500 via-teal-500 to-green-500",forest:"from-green-600 via-emerald-500 to-teal-500",custom:"from-primary via-secondary to-accent"}[e],t&&"animate-spin-slow"),style:{padding:`${r}px`},children:a.jsx("div",{className:"w-full h-full bg-background rounded-xl",children:o})})})).displayName="ColorfulBorder"},52052:(e,r,t)=>{"use strict";t.d(r,{LazyWrapper:()=>i});var a=t(10326),s=t(17577),o=t(77506);function l({size:e="default"}){return a.jsx("div",{className:"flex items-center justify-center p-4",children:a.jsx(o.Z,{className:`${{sm:"w-4 h-4",default:"w-6 h-6",lg:"w-8 h-8"}[e]} animate-spin text-primary`})})}function i({children:e,fallback:r=a.jsx(l,{}),errorFallback:t=a.jsx("div",{className:"text-center text-muted-foreground",children:"加载失败"})}){return a.jsx(s.Suspense,{fallback:r,children:e})}},75621:(e,r,t)=>{"use strict";t.r(r),t.d(r,{AnimatedSection:()=>n,FloatingElement:()=>m,PageTransition:()=>i,PulseOnHover:()=>u,ScrollReveal:()=>c,StaggeredList:()=>d});var a=t(10326),s=t(17577),o=t(35047),l=t(51223);function i({children:e,className:r}){(0,o.usePathname)();let[t,i]=(0,s.useState)(!1),[n,d]=(0,s.useState)(e);return(0,a.jsxs)("div",{className:(0,l.cn)("relative",r),children:[t&&a.jsx("div",{className:"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-fade-in",children:a.jsx("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[a.jsx("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin-slow"}),a.jsx("p",{className:"text-sm text-muted-foreground animate-pulse-soft",children:"加载中..."})]})})}),a.jsx("div",{className:(0,l.cn)("transition-all duration-300 ease-smooth",t?"opacity-0 scale-95":"opacity-100 scale-100 animate-fade-in-up"),children:n})]})}function n({children:e,className:r,delay:t=0,direction:o="up"}){let[i,n]=(0,s.useState)(!1);return a.jsx("div",{className:(0,l.cn)("transition-all duration-500 ease-smooth",i?({up:"animate-fade-in-up",down:"animate-fade-in-down",left:"animate-slide-in-left",right:"animate-slide-in-right"})[o]:"opacity-0 translate-y-4",r),children:e})}function d({children:e,className:r,staggerDelay:t=100}){return a.jsx("div",{className:r,children:e.map((e,r)=>a.jsx(n,{delay:r*t,className:"mb-4 last:mb-0",children:e},r))})}function c({children:e,className:r,threshold:t=.1,rootMargin:o="0px 0px -50px 0px"}){let[i,n]=(0,s.useState)(!1),[d,c]=(0,s.useState)(null);return a.jsx("div",{ref:c,className:(0,l.cn)("transition-all duration-700 ease-smooth",i?"opacity-100 translate-y-0 scale-100":"opacity-0 translate-y-8 scale-95",r),children:e})}function m({children:e,className:r,intensity:t="normal"}){return a.jsx("div",{className:(0,l.cn)("transition-transform duration-300 ease-smooth",{subtle:"hover:translate-y-[-2px]",normal:"hover:translate-y-[-4px]",strong:"hover:translate-y-[-8px]"}[t],r),children:e})}function u({children:e,className:r}){return a.jsx("div",{className:(0,l.cn)("transition-all duration-300 ease-smooth hover:animate-pulse-soft",r),children:e})}},55782:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var a=t(34567),s=t.n(a)},34567:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o}});let a=t(53370);t(19510),t(71159);let s=a._(t(26155));function o(e,r){var t;let a={loading:e=>{let{error:r,isLoading:t,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let o={...a,...r};return(0,s.default)({...o,modules:null==(t=o.loadableGenerated)?void 0:t.modules})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},13689:(e,r,t)=>{"use strict";let{createProxy:a}=t(68570);e.exports=a("/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js")},26155:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return d}});let a=t(19510),s=t(71159),o=t(13689),l=t(44459);function i(e){return{default:e&&"default"in e?e.default:e}}let n={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let r={...n,...e},t=(0,s.lazy)(()=>r.loader().then(i)),d=r.loading;function c(e){let i=d?(0,a.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,n=r.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.PreloadCss,{moduleIds:r.modules}),(0,a.jsx)(t,{...e})]}):(0,a.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(t,{...e})});return(0,a.jsx)(s.Suspense,{fallback:i,children:n})}return c.displayName="LoadableComponent",c}},44459:(e,r,t)=>{"use strict";let{createProxy:a}=t(68570);e.exports=a("/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js")},73767:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,generateMetadata:()=>u,revalidate:()=>m});var a=t(19510),s=t(6879),o=t(68570);(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#LoadingSpinner`),(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#SkeletonCard`),(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#SkeletonList`);let l=(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#LazyWrapper`);(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#createLazyComponent`),(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#useIntersectionObserver`),(0,o.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx#ViewportLazy`);var i=t(55782),n=t(59979),d=t(53722);let c=(0,i.default)(()=>t.e(240).then(t.bind(t,90240)),{loadableGenerated:{modules:["app/projects/page.tsx -> ./ProjectsClient"]},loading:()=>a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),ssr:!1}),m=120;async function u(){try{return await (0,n.ve)("projects")}catch(e){return{title:"Projects",description:"My projects and work"}}}async function p(){let[e,r]=await Promise.all([(0,s.V$)(),(0,d.$2)()]);return a.jsx(l,{children:a.jsx(c,{sortedCategories:e,pagesConfig:r})})}},6879:(e,r,t)=>{"use strict";t.d(r,{IQ:()=>l,V$:()=>o});var a=t(53722);function s(e){let r=e.project_url?{href:e.project_url,label:e.title}:void 0,t=e.tags?.filter(e=>"tech"===e.category)||[],a=t.length>0?t[0]:null,s=!!(e.content&&e.content.trim().length>0);return{id:e.id,slug:e.slug,name:e.title,description:e.description||"",link:r,github_link:e.github_url,logo:e.logo_url,icon:e.icon||null,featured:e.featured,type:"work",is_github_project:e.is_github_project,has_detail_page:s,content:e.content,detail_content:e.content,project_order:e.display_order,date:e.display_date||e.date||e.created_at,display_date:e.display_date||e.date,created_at:e.created_at,updated_at:e.updated_at,category:a?.name,category_icon:a?.icon||null,tags:e.tags?.map(e=>e.name)||[],tag_objects:e.tags||[],tech_stack:e.tags?.filter(e=>"tech"===e.category).map(e=>e.name)||[],categories:[],module_categories:[]}}async function o(){try{let e=`${a.CT}/blogs/?article_type=project&published_only=true`,r=await fetch(e,{next:{revalidate:10,tags:["projects"]},headers:{"Cache-Control":"public, s-maxage=10, stale-while-revalidate=30",...(0,a.g0)()}});if(!r.ok)throw Error(`Failed to fetch projects: ${r.status}`);let t=(await r.json()).map(s),o={};t.forEach(e=>{let r="其他",t=null,a=null,s=999,l=0,i="other";if(e.module_categories&&e.module_categories.length>0){let o=e.module_categories[0];r=o.name,t=o.icon,a=o.description||null,s=o.display_order||999,l=o.id,i=o.slug}else if(e.tag_objects&&e.tag_objects.length>0){let o=e.tag_objects[0];r=o.name,t=o.icon||null,a=o.description||null,s=500,l=o.id,i=o.slug||o.name.toLowerCase().replace(/\s+/g,"-")}else e.tags&&e.tags.length>0&&(r=e.tags[0],t=null,a=null,s=500,l=0,i=e.tags[0].toLowerCase().replace(/\s+/g,"-"));o[r]||(o[r]={projects:[],icon:t,description:a,display_order:s,id:l,slug:i}),o[r].projects.push(e)}),Object.keys(o).forEach(e=>{o[e].projects.sort((e,r)=>{let t=e.project_order||999,a=r.project_order||999;return t!==a?t-a:e.name.localeCompare(r.name)})});let l=Object.entries(o).map(([e,r])=>({categoryName:e,...r}));return l.sort((e,r)=>{let t=e.display_order,a=r.display_order;return t!==a?t-a:e.categoryName.localeCompare(r.categoryName)}),l}catch(e){return[]}}async function l(e){try{let r=`${a.CT}/blogs/${e}`,t=await fetch(r,{next:{revalidate:10,tags:[`project-${e}`,"projects"]},headers:{"Cache-Control":"public, s-maxage=10, stale-while-revalidate=30",...(0,a.g0)()}});if(!t.ok)throw Error(`Failed to fetch project: ${t.status}`);let o=await t.json();if("project"===o.article_type)return s(o);return null}catch(e){return null}}},53370:(e,r,t)=>{"use strict";function a(e){return e&&e.__esModule?e:{default:e}}t.r(r),t.d(r,{_:()=>a,_interop_require_default:()=>a})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[948,499,466,722,879],()=>t(46151));module.exports=a})();