"use strict";(()=>{var e={};e.id=703,e.ids=[703],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},11335:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>c,patchFetch:()=>m,requestAsyncStorage:()=>u,routeModule:()=>l,serverHooks:()=>x,staticGenerationAsyncStorage:()=>d});var a={};o.r(a),o.d(a,{GET:()=>p});var r=o(49303),s=o(88716),n=o(60670),i=o(87070);async function p(e){try{let e=await fetch("http://100.90.150.110:8000/api/seo/robots.txt",{method:"GET",headers:{"Content-Type":"text/plain"},next:{revalidate:3600}});if(!e.ok)throw Error("Failed to fetch robots.txt from API");let t=await e.text();return new i.NextResponse(t,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(t){let e=`User-agent: *
Allow: /

# Sitemap
Sitemap: http://100.90.150.110:3000/sitemap.xml

# Disallow admin and API paths
Disallow: /admin/
Disallow: /api/

# Allow specific public API endpoints
Allow: /api/seo/sitemap.xml
Allow: /api/seo/robots.txt
`;return new i.NextResponse(e,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}}let l=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"route",bundlePath:"app/robots.txt/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/robots.txt/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:d,serverHooks:x}=l,c="/robots.txt/route";function m(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:d})}}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[948,972],()=>o(11335));module.exports=a})();