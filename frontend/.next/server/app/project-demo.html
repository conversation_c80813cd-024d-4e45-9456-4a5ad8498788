<!DOCTYPE html><html lang="en" class="h-full antialiased"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=16&amp;q=75 16w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=32&amp;q=75 32w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=48&amp;q=75 48w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=64&amp;q=75 64w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=96&amp;q=75 96w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=128&amp;q=75 128w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=256&amp;q=75 256w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=384&amp;q=75 384w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=640&amp;q=75 640w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=750&amp;q=75 750w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=828&amp;q=75 828w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=3840&amp;q=75 3840w" imageSizes="2.25rem" fetchPriority="high"/><link rel="stylesheet" href="/_next/static/css/8b6d6f69c7970b5a.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/72074f6a7392446a.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-64cec67748a067e2.js"/><script src="/_next/static/chunks/vendors-3e2ec6721707e77e.js" async=""></script><script src="/_next/static/chunks/main-app-45d29c2622bdc77b.js" async=""></script><script src="/_next/static/chunks/icon-libs-9a3ac8a6739d257f.js" async=""></script><script src="/_next/static/chunks/common-92ea84bd7ba12cdf.js" async=""></script><script src="/_next/static/chunks/app/project-demo/page-31ae0f46b19bbf2a.js" async=""></script><script src="/_next/static/chunks/ui-libs-e96f0533cedd4426.js" async=""></script><script src="/_next/static/chunks/app/layout-0fa287aaecc84f29.js" async=""></script><script src="/_next/static/chunks/app/page-b49a98deec5a828b.js" async=""></script><title>项目卡片演示 - 红点奖级别设计</title><meta name="description" content="展示新的项目卡片设计，包含统一高度、绿色主题和高级交互效果"/><meta name="author" content="Jingyao Chen"/><meta name="keywords" content="Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery"/><meta name="creator" content="Jingyao Chen"/><meta name="publisher" content="Jingyao Chen"/><meta name="robots" content="index, follow"/><meta name="theme-color" content="#171717"/><meta name="msapplication-TileColor" content="#171717"/><link rel="canonical" href="http://**************:3000"/><link rel="alternate" type="application/rss+xml" href="http://**************:3000/feed"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="jyaos"/><meta property="og:description" content="Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."/><meta property="og:url" content="http://**************:3000"/><meta property="og:site_name" content="Jingyao Chen Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="http://**************:3000/images/og-jingyao-portfolio.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="jyaos"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@JingyaoC"/><meta name="twitter:creator" content="@JingyaoC"/><meta name="twitter:title" content="jyaos"/><meta name="twitter:description" content="Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."/><meta name="twitter:image" content="http://**************:3000/images/og-jingyao-portfolio.jpg"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="902x902"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="flex h-full"><script>!function(){try{var d=document.documentElement,c=d.classList;c.remove('light','dark');var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';c.add('dark')}else{d.style.colorScheme = 'light';c.add('light')}}else if(e){c.add(e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><div class="flex w-full"><div class="fixed inset-0 flex justify-center sm:px-8"><div class="flex w-full max-w-7xl lg:px-8"><div class="w-full shadow-xl dark:bg-muted"></div></div></div><div class="relative flex w-full flex-col px-4 sm:px-0"><header class="pointer-events-none relative z-50 flex flex-none flex-col" style="height:var(--header-height);margin-bottom:var(--header-mb)"><div class="top-0 z-10 h-16 pt-6" style="position:var(--header-position)"><div class="sm:px-8 top-[var(--header-top,theme(spacing.6))] w-full" style="position:var(--header-inner-position)"><div class="mx-auto w-full max-w-7xl lg:px-8"><div class="relative px-4 sm:px-8 lg:px-12"><div class="mx-auto max-w-2xl lg:max-w-5xl"><div class="relative flex gap-4"><div class="flex flex-1"><div class="flex flex-row items-center gap-2"><div class="h-10 w-10 rounded-full bg-white/90 p-0.5 shadow-lg shadow-zinc-800/5 ring-1 ring-zinc-900/5 backdrop-blur dark:bg-zinc-800/90 dark:ring-white/10"><a aria-label="Home" class="pointer-events-auto" href="/"><img alt="" fetchPriority="high" width="902" height="902" decoding="async" data-nimg="1" class="rounded-full bg-zinc-100 object-cover dark:bg-zinc-800 h-9 w-9" style="color:transparent" sizes="2.25rem" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=16&amp;q=75 16w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=32&amp;q=75 32w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=48&amp;q=75 48w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=64&amp;q=75 64w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=96&amp;q=75 96w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=128&amp;q=75 128w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=256&amp;q=75 256w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=384&amp;q=75 384w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=640&amp;q=75 640w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=750&amp;q=75 750w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=828&amp;q=75 828w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=3840&amp;q=75 3840w" src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar.700ad1ea.jpg&amp;w=3840&amp;q=75"/></a></div><a aria-label="Home" class="pointer-events-auto" href="/"><div class="text-md font-semibold capitalize">Jay-Yao</div></a></div></div><div class="flex flex-1 justify-end md:justify-center"><div class="pointer-events-auto md:hidden" data-headlessui-state=""><button class="group flex items-center rounded-full px-4 py-2 text-sm font-medium shadow-lg ring-1 ring-border backdrop-blur-md bg-card/95 transition-all duration-300 hover:shadow-xl hover:ring-primary/20 hover:bg-primary/5" type="button" aria-expanded="false" data-headlessui-state="">Menu<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-3 h-auto w-2 transition-transform duration-200 group-hover:rotate-180"><path d="m6 9 6 6 6-6"></path></svg></button></div><div hidden="" style="position:fixed;top:1px;left:1px;width:1px;height:0;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0;display:none"></div><nav class="pointer-events-auto hidden md:block"><ul class="flex rounded-full px-3 text-sm font-medium bg-card/95 ring-1 ring-border shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-xl hover:ring-primary/20"><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/">Home</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/about">About</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/projects">Projects</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/blogs">Blogs</a></li><li class="flex items-center"><div class="h-4 w-px bg-muted-foreground/30"></div></li><li><a class="relative block px-3 py-2 transition-all duration-300 rounded-md opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5" href="/gallery">Gallery</a></li></ul></nav></div><div class="flex justify-end md:flex-1"><div class="pointer-events-auto flex flex-row items-center gap-2 md:mr-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg><span class="sr-only">Toggle theme</span></button></div></div></div></div></div></div></div></div></header><main class="flex-auto"><div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8"><div class="max-w-7xl mx-auto"><div class="text-center mb-12"><h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">红点奖级别项目卡片演示</h1><p class="text-lg text-gray-600 dark:text-gray-300">展示统一高度、美观的绿色主题卡片设计</p></div><div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr"><div class="min-w-0 w-full" style="min-width:320px"><li class="group relative flex flex-col items-start animate-fade-in-up focus-within:outline-none w-full" style="animation-delay:0ms;height:480px;min-width:320px;max-width:100%" role="article" aria-label="Project: AI Chat Assistant" tabindex="0"><div class="absolute -inset-2 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse-slow" style="background:radial-gradient(circle at 0px 0px, rgba(132, 204, 22, 0.4), transparent 70%)"></div><div class="absolute -inset-1 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500" style="background:linear-gradient(135deg, rgba(132, 204, 22, 0.08) 0%, rgba(101, 163, 13, 0.05) 100%)"></div><div class="relative w-full h-full backdrop-blur-xl rounded-2xl border shadow-2xl overflow-hidden project-card-hover focus-within:outline-none" style="background:linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(255, 255, 255, 0.90) 50%,
            rgba(255, 255, 255, 0.85) 100%);border-color:#84CC1640;box-shadow:0 25px 50px -12px rgba(132, 204, 22, 0.25), 0 0 0 1px #84CC1620"><div class="absolute inset-0 rounded-2xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" style="box-shadow:0 0 0 3px #84CC1660, 0 0 0 6px #84CC1620"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.90) 50%,
              rgba(51, 65, 85, 0.85) 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
                 rgba(15, 23, 42, 0.95) 0%,
                 rgba(30, 41, 59, 0.90) 50%,
                 rgba(51, 65, 85, 0.85) 100%)"></div><canvas class="absolute inset-0 pointer-events-none z-0" style="width:100%;height:100%;pointer-events:none"></canvas><div class="absolute top-0 left-0 right-0 h-1 opacity-80 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(135deg, #84CC16 0%, #65A30D 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none" style="background:radial-gradient(circle at 50% 0%, #84CC1620, transparent 50%)"></div><div class="relative z-10 h-full p-3 sm:p-4 lg:p-6 flex flex-col justify-between"><div class="flex items-start justify-between gap-2 mb-3"><div class="relative flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 flex-shrink-0" style="background:linear-gradient(135deg, #84CC16 0%, #65A30D 100%);box-shadow:0 6px 24px rgba(132, 204, 22, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2)" role="img" aria-label="AI Chat Assistant project icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text text-white"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg><div class="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur-md" style="background:radial-gradient(circle, rgba(132, 204, 22, 0.4), transparent 70%)"></div><div class="absolute inset-0 rounded-xl border-2 opacity-0 group-hover:opacity-40 transition-opacity duration-300" style="border-image:linear-gradient(45deg, #84CC16, #65A30D, #BEF264) 1;animation:none"></div><div class="absolute inset-1 rounded-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300" style="background:linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent 50%)"></div></div><div class="flex items-center gap-1 flex-shrink-0"><div class="inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 " style="background-color:#10B98120;color:#10B981;border:1px solid #10B98140" title="Currently active and maintained"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><div class="w-2 h-2 rounded-full animate-pulse" style="background-color:#10B981"></div></div><div class="flex items-center gap-1 " title="High Priority"><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#EF4444"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#EF4444"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#EF4444"></div></div></div></div><div class="mb-2"><h3 class="text-sm sm:text-base font-bold text-gray-900 dark:text-white leading-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300 line-clamp-2" id="project-title-ai-chat-assistant" title="AI Chat Assistant">AI Chat Assistant</h3><div class="h-0.5 transition-all duration-500 group-hover:w-full rounded-full" style="width:0%;background:linear-gradient(135deg, #84CC16 0%, #65A30D 100%);box-shadow:0 0 8px rgba(132, 204, 22, 0.4)"></div></div><div class="flex-1 mb-3 min-h-[80px] flex flex-col"><p class="text-xs text-gray-600 dark:text-gray-300 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 line-clamp-4" aria-describedby="project-title-ai-chat-assistant" title="A modern AI-powered chat assistant built with React and OpenAI API.">A modern AI-powered chat assistant built with React and OpenAI API.</p><div class="flex-1"></div></div><div class="space-y-3"><div class="space-y-2"><div class="flex items-center gap-2 "><div class="flex -space-x-2"><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#61DAFB;z-index:3" title="React">R</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#3178C6;z-index:2" title="TypeScript">T</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#6B7280;z-index:1" title="OpenAI">O</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-500 flex items-center justify-center text-xs font-bold text-white shadow-lg" title="+1 more technologies">+<!-- -->1</div></div><div class="flex-1 max-w-20"><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"><div class="h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500" style="width:40%"></div></div><div class="text-xs text-gray-500 dark:text-gray-400 mt-1">4<!-- --> tech<!-- -->s</div></div></div><div class="grid grid-cols-2 gap-2 "><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">⭐</span><span class="font-medium">1.2K</span><span class="text-gray-500 dark:text-gray-400 truncate">Stars</span></div><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">🍴</span><span class="font-medium">89</span><span class="text-gray-500 dark:text-gray-400 truncate">Forks</span></div><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">👁️</span><span class="font-medium">5.6K</span><span class="text-gray-500 dark:text-gray-400 truncate">Views</span></div></div></div><div class="flex flex-wrap gap-1.5"><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#84CC1615;border-color:#84CC1630;color:#84CC16">AI</span><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#10B98115;border-color:#10B98130;color:#10B981">Web</span></div><div class="flex items-center justify-between gap-2 relative z-30 mt-auto"><div class="relative overflow-hidden rounded-lg flex-1"><a href="/projects/ai-chat-assistant" class="inline-flex items-center justify-center gap-1.5 px-3 py-2 text-xs font-medium text-white rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 magnetic-effect w-full relative overflow-hidden z-30" style="background:linear-gradient(135deg, #84CC16 0%, #65A30D 100%);box-shadow:0 4px 20px rgba(132, 204, 22, 0.25);min-height:36px" target="_self" aria-label="View details for AI Chat Assistant"><div class="absolute inset-0 opacity-0 hover:opacity-20 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent)"></div><span class="relative z-10 truncate">Details</span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right relative z-10 transition-transform duration-300 group-hover:translate-x-1 flex-shrink-0"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div><div class="relative overflow-hidden rounded-full flex-shrink-0"><button class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 hover:scale-110 magnetic-effect relative z-30 rounded-full" style="min-width:36px;min-height:36px" aria-label="Preview AI Chat Assistant"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></button></div></div></div></div><div class="absolute pointer-events-none opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-full blur-xl" style="width:120px;height:120px;background:radial-gradient(circle, rgba(132, 204, 22, 0.4), #84CC1630, transparent);left:-60px;top:-60px;transform:translate3d(0, 0, 0)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none" style="background:linear-gradient(135deg, transparent, #84CC1620, transparent);filter:blur(1px)"></div></div></li></div><div class="min-w-0 w-full" style="min-width:320px"><li class="group relative flex flex-col items-start animate-fade-in-up focus-within:outline-none w-full" style="animation-delay:100ms;height:480px;min-width:320px;max-width:100%" role="article" aria-label="Project: Mobile E-commerce App" tabindex="0"><div class="absolute -inset-2 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse-slow" style="background:radial-gradient(circle at 0px 0px, rgba(20, 184, 166, 0.4), transparent 70%)"></div><div class="absolute -inset-1 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500" style="background:linear-gradient(135deg, rgba(20, 184, 166, 0.08) 0%, rgba(13, 148, 136, 0.05) 100%)"></div><div class="relative w-full h-full backdrop-blur-xl rounded-2xl border shadow-2xl overflow-hidden project-card-hover focus-within:outline-none" style="background:linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(255, 255, 255, 0.90) 50%,
            rgba(255, 255, 255, 0.85) 100%);border-color:#14B8A640;box-shadow:0 25px 50px -12px rgba(20, 184, 166, 0.25), 0 0 0 1px #14B8A620"><div class="absolute inset-0 rounded-2xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" style="box-shadow:0 0 0 3px #14B8A660, 0 0 0 6px #14B8A620"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.90) 50%,
              rgba(51, 65, 85, 0.85) 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
                 rgba(15, 23, 42, 0.95) 0%,
                 rgba(30, 41, 59, 0.90) 50%,
                 rgba(51, 65, 85, 0.85) 100%)"></div><canvas class="absolute inset-0 pointer-events-none z-0" style="width:100%;height:100%;pointer-events:none"></canvas><div class="absolute top-0 left-0 right-0 h-1 opacity-80 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(135deg, #14B8A6 0%, #0D9488 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none" style="background:radial-gradient(circle at 50% 0%, #14B8A620, transparent 50%)"></div><div class="relative z-10 h-full p-3 sm:p-4 lg:p-6 flex flex-col justify-between"><div class="flex items-start justify-between gap-2 mb-3"><div class="relative flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 flex-shrink-0" style="background:linear-gradient(135deg, #14B8A6 0%, #0D9488 100%);box-shadow:0 6px 24px rgba(20, 184, 166, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2)" role="img" aria-label="Mobile E-commerce App project icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles text-white"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg><div class="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur-md" style="background:radial-gradient(circle, rgba(20, 184, 166, 0.4), transparent 70%)"></div><div class="absolute inset-0 rounded-xl border-2 opacity-0 group-hover:opacity-40 transition-opacity duration-300" style="border-image:linear-gradient(45deg, #14B8A6, #0D9488, #5EEAD4) 1;animation:none"></div><div class="absolute inset-1 rounded-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300" style="background:linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent 50%)"></div></div><div class="flex items-center gap-1 flex-shrink-0"><div class="inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 " style="background-color:#05966920;color:#059669;border:1px solid #05966940" title="Project completed successfully"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><div class="w-2 h-2 rounded-full animate-pulse" style="background-color:#059669"></div></div><div class="flex items-center gap-1 " title="Medium Priority"><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#F59E0B"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#F59E0B"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-30" style="background-color:#F59E0B"></div></div></div></div><div class="mb-2"><h3 class="text-sm sm:text-base font-bold text-gray-900 dark:text-white leading-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300 line-clamp-2" id="project-title-mobile-e-commerce-app" title="Mobile E-commerce App">Mobile E-commerce App</h3><div class="h-0.5 transition-all duration-500 group-hover:w-full rounded-full" style="width:0%;background:linear-gradient(135deg, #14B8A6 0%, #0D9488 100%);box-shadow:0 0 8px rgba(20, 184, 166, 0.4)"></div></div><div class="flex-1 mb-3 min-h-[80px] flex flex-col"><p class="text-xs text-gray-600 dark:text-gray-300 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 line-clamp-4" aria-describedby="project-title-mobile-e-commerce-app" title="A comprehensive e-commerce mobile application with advanced features including real-time inventory management, secure payment processing, user authentication, shopping cart functionality, order tracking, push notifications, and seamless integration with multiple payment gateways. Built using modern mobile development frameworks.">A comprehensive e-commerce mobile application with advanced features including real-time inventory management, secure payment processing, user authentication, shopping cart functionality, order tracking, push notifications, and seamless integration with multiple payment gateways. Built using modern mobile development frameworks.</p><div class="flex-1"></div></div><div class="space-y-3"><div class="space-y-2"><div class="flex items-center gap-2 "><div class="flex -space-x-2"><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#6B7280;z-index:3" title="React Native">R</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#6B7280;z-index:2" title="Redux">R</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#6B7280;z-index:1" title="Firebase">F</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-500 flex items-center justify-center text-xs font-bold text-white shadow-lg" title="+2 more technologies">+<!-- -->2</div></div><div class="flex-1 max-w-20"><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"><div class="h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500" style="width:50%"></div></div><div class="text-xs text-gray-500 dark:text-gray-400 mt-1">5<!-- --> tech<!-- -->s</div></div></div><div class="grid grid-cols-2 gap-2 "><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">⭐</span><span class="font-medium">856</span><span class="text-gray-500 dark:text-gray-400 truncate">Stars</span></div><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">⬇️</span><span class="font-medium">12.0K</span><span class="text-gray-500 dark:text-gray-400 truncate">Downloads</span></div></div></div><div class="flex flex-wrap gap-1.5"><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#14B8A615;border-color:#14B8A630;color:#14B8A6">Mobile</span><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#05966915;border-color:#05966930;color:#059669">E-commerce</span></div><div class="flex items-center justify-between gap-2 relative z-30 mt-auto"><div class="relative overflow-hidden rounded-lg flex-1"><a href="https://github.com/example/mobile-ecommerce" class="inline-flex items-center justify-center gap-1.5 px-3 py-2 text-xs font-medium text-white rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 magnetic-effect w-full relative overflow-hidden z-30" style="background:linear-gradient(135deg, #14B8A6 0%, #0D9488 100%);box-shadow:0 4px 20px rgba(20, 184, 166, 0.25);min-height:36px" target="_blank" rel="noopener noreferrer" aria-label="Visit Mobile E-commerce App"><div class="absolute inset-0 opacity-0 hover:opacity-20 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent)"></div><span class="relative z-10 truncate">Visit</span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right relative z-10 transition-transform duration-300 group-hover:translate-x-1 flex-shrink-0"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div><div class="relative overflow-hidden rounded-full flex-shrink-0"><button class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 hover:scale-110 magnetic-effect relative z-30 rounded-full" style="min-width:36px;min-height:36px" aria-label="Preview Mobile E-commerce App"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></button></div></div></div></div><div class="absolute pointer-events-none opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-full blur-xl" style="width:120px;height:120px;background:radial-gradient(circle, rgba(20, 184, 166, 0.4), #14B8A630, transparent);left:-60px;top:-60px;transform:translate3d(0, 0, 0)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none" style="background:linear-gradient(135deg, transparent, #14B8A620, transparent);filter:blur(1px)"></div></div></li></div><div class="min-w-0 w-full" style="min-width:320px"><li class="group relative flex flex-col items-start animate-fade-in-up focus-within:outline-none w-full" style="animation-delay:200ms;height:480px;min-width:320px;max-width:100%" role="article" aria-label="Project: Design System" tabindex="0"><div class="absolute -inset-2 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse-slow" style="background:radial-gradient(circle at 0px 0px, rgba(6, 182, 212, 0.4), transparent 70%)"></div><div class="absolute -inset-1 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500" style="background:linear-gradient(135deg, rgba(6, 182, 212, 0.08) 0%, rgba(8, 145, 178, 0.05) 100%)"></div><div class="relative w-full h-full backdrop-blur-xl rounded-2xl border shadow-2xl overflow-hidden project-card-hover focus-within:outline-none" style="background:linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(255, 255, 255, 0.90) 50%,
            rgba(255, 255, 255, 0.85) 100%);border-color:#06B6D440;box-shadow:0 25px 50px -12px rgba(6, 182, 212, 0.25), 0 0 0 1px #06B6D420"><div class="absolute inset-0 rounded-2xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" style="box-shadow:0 0 0 3px #06B6D460, 0 0 0 6px #06B6D420"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.90) 50%,
              rgba(51, 65, 85, 0.85) 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
                 rgba(15, 23, 42, 0.95) 0%,
                 rgba(30, 41, 59, 0.90) 50%,
                 rgba(51, 65, 85, 0.85) 100%)"></div><canvas class="absolute inset-0 pointer-events-none z-0" style="width:100%;height:100%;pointer-events:none"></canvas><div class="absolute top-0 left-0 right-0 h-1 opacity-80 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none" style="background:radial-gradient(circle at 50% 0%, #06B6D420, transparent 50%)"></div><div class="relative z-10 h-full p-3 sm:p-4 lg:p-6 flex flex-col justify-between"><div class="flex items-start justify-between gap-2 mb-3"><div class="relative flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 flex-shrink-0" style="background:linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);box-shadow:0 6px 24px rgba(6, 182, 212, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2)" role="img" aria-label="Design System project icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text text-white"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg><div class="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur-md" style="background:radial-gradient(circle, rgba(6, 182, 212, 0.4), transparent 70%)"></div><div class="absolute inset-0 rounded-xl border-2 opacity-0 group-hover:opacity-40 transition-opacity duration-300" style="border-image:linear-gradient(45deg, #06B6D4, #0891B2, #67E8F9) 1;animation:none"></div><div class="absolute inset-1 rounded-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300" style="background:linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent 50%)"></div></div><div class="flex items-center gap-1 flex-shrink-0"><div class="inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 " style="background-color:#F59E0B20;color:#F59E0B;border:1px solid #F59E0B40" title="Currently under development"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg><div class="w-2 h-2 rounded-full animate-pulse" style="background-color:#F59E0B"></div></div><div class="flex items-center gap-1 " title="Low Priority"><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#10B981"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-30" style="background-color:#10B981"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-30" style="background-color:#10B981"></div></div></div></div><div class="mb-2"><h3 class="text-sm sm:text-base font-bold text-gray-900 dark:text-white leading-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300 line-clamp-2" id="project-title-design-system" title="Design System">Design System</h3><div class="h-0.5 transition-all duration-500 group-hover:w-full rounded-full" style="width:0%;background:linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);box-shadow:0 0 8px rgba(6, 182, 212, 0.4)"></div></div><div class="flex-1 mb-3 min-h-[80px] flex flex-col"><p class="text-xs text-gray-600 dark:text-gray-300 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 line-clamp-4" aria-describedby="project-title-design-system" title="Modern design system.">Modern design system.</p><div class="flex-1"></div></div><div class="space-y-3"><div class="space-y-2"><div class="flex items-center gap-2 "><div class="flex -space-x-2"><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#F24E1E;z-index:3" title="Figma">F</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#61DAFB;z-index:2" title="React">R</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#6B7280;z-index:1" title="Storybook">S</div></div><div class="flex-1 max-w-20"><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"><div class="h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500" style="width:30%"></div></div><div class="text-xs text-gray-500 dark:text-gray-400 mt-1">3<!-- --> tech<!-- -->s</div></div></div><div class="grid grid-cols-2 gap-2 "><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">👁️</span><span class="font-medium">3.4K</span><span class="text-gray-500 dark:text-gray-400 truncate">Views</span></div></div></div><div class="flex flex-wrap gap-1.5"><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#06B6D415;border-color:#06B6D430;color:#06B6D4">Design</span></div><div class="flex items-center justify-between gap-2 relative z-30 mt-auto"><div class="relative overflow-hidden rounded-lg flex-1"><a href="/projects/design-system" class="inline-flex items-center justify-center gap-1.5 px-3 py-2 text-xs font-medium text-white rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 magnetic-effect w-full relative overflow-hidden z-30" style="background:linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);box-shadow:0 4px 20px rgba(6, 182, 212, 0.25);min-height:36px" target="_self" aria-label="View details for Design System"><div class="absolute inset-0 opacity-0 hover:opacity-20 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent)"></div><span class="relative z-10 truncate">Details</span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right relative z-10 transition-transform duration-300 group-hover:translate-x-1 flex-shrink-0"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div><div class="relative overflow-hidden rounded-full flex-shrink-0"><button class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 hover:scale-110 magnetic-effect relative z-30 rounded-full" style="min-width:36px;min-height:36px" aria-label="Preview Design System"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></button></div></div></div></div><div class="absolute pointer-events-none opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-full blur-xl" style="width:120px;height:120px;background:radial-gradient(circle, rgba(6, 182, 212, 0.4), #06B6D430, transparent);left:-60px;top:-60px;transform:translate3d(0, 0, 0)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none" style="background:linear-gradient(135deg, transparent, #06B6D420, transparent);filter:blur(1px)"></div></div></li></div><div class="min-w-0 w-full" style="min-width:320px"><li class="group relative flex flex-col items-start animate-fade-in-up focus-within:outline-none w-full" style="animation-delay:300ms;height:480px;min-width:320px;max-width:100%" role="article" aria-label="Project: Open Source Library" tabindex="0"><div class="absolute -inset-2 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse-slow" style="background:radial-gradient(circle at 0px 0px, rgba(22, 163, 74, 0.4), transparent 70%)"></div><div class="absolute -inset-1 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500" style="background:linear-gradient(135deg, rgba(22, 163, 74, 0.08) 0%, rgba(21, 128, 61, 0.05) 100%)"></div><div class="relative w-full h-full backdrop-blur-xl rounded-2xl border shadow-2xl overflow-hidden project-card-hover focus-within:outline-none" style="background:linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(255, 255, 255, 0.90) 50%,
            rgba(255, 255, 255, 0.85) 100%);border-color:#16A34A40;box-shadow:0 25px 50px -12px rgba(22, 163, 74, 0.25), 0 0 0 1px #16A34A20"><div class="absolute inset-0 rounded-2xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" style="box-shadow:0 0 0 3px #16A34A60, 0 0 0 6px #16A34A20"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.90) 50%,
              rgba(51, 65, 85, 0.85) 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300" style="background:linear-gradient(135deg,
                 rgba(15, 23, 42, 0.95) 0%,
                 rgba(30, 41, 59, 0.90) 50%,
                 rgba(51, 65, 85, 0.85) 100%)"></div><canvas class="absolute inset-0 pointer-events-none z-0" style="width:100%;height:100%;pointer-events:none"></canvas><div class="absolute top-0 left-0 right-0 h-1 opacity-80 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(135deg, #16A34A 0%, #15803D 100%)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none" style="background:radial-gradient(circle at 50% 0%, #16A34A20, transparent 50%)"></div><div class="relative z-10 h-full p-3 sm:p-4 lg:p-6 flex flex-col justify-between"><div class="flex items-start justify-between gap-2 mb-3"><div class="relative flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 flex-shrink-0" style="background:linear-gradient(135deg, #16A34A 0%, #15803D 100%);box-shadow:0 6px 24px rgba(22, 163, 74, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2)" role="img" aria-label="Open Source Library project icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles text-white"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg><div class="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur-md" style="background:radial-gradient(circle, rgba(22, 163, 74, 0.4), transparent 70%)"></div><div class="absolute inset-0 rounded-xl border-2 opacity-0 group-hover:opacity-40 transition-opacity duration-300" style="border-image:linear-gradient(45deg, #16A34A, #15803D, #86EFAC) 1;animation:none"></div><div class="absolute inset-1 rounded-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300" style="background:linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent 50%)"></div></div><div class="flex items-center gap-1 flex-shrink-0"><div class="inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 " style="background-color:#10B98120;color:#10B981;border:1px solid #10B98140" title="Currently active and maintained"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><div class="w-2 h-2 rounded-full animate-pulse" style="background-color:#10B981"></div></div><div class="flex items-center gap-1 " title="High Priority"><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#EF4444"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#EF4444"></div><div class="w-1 h-3 rounded-full transition-all duration-300 opacity-100" style="background-color:#EF4444"></div></div></div></div><div class="mb-2"><h3 class="text-sm sm:text-base font-bold text-gray-900 dark:text-white leading-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300 line-clamp-2" id="project-title-open-source-library" title="Open Source Library">Open Source Library</h3><div class="h-0.5 transition-all duration-500 group-hover:w-full rounded-full" style="width:0%;background:linear-gradient(135deg, #16A34A 0%, #15803D 100%);box-shadow:0 0 8px rgba(22, 163, 74, 0.4)"></div></div><div class="flex-1 mb-3 min-h-[80px] flex flex-col"><p class="text-xs text-gray-600 dark:text-gray-300 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 line-clamp-4" aria-describedby="project-title-open-source-library" title="A powerful and flexible open source library for building modern web applications. Features include component composition, state management, routing, and extensive customization options. Designed with developer experience in mind.">A powerful and flexible open source library for building modern web applications. Features include component composition, state management, routing, and extensive customization options. Designed with developer experience in mind.</p><div class="flex-1"></div></div><div class="space-y-3"><div class="space-y-2"><div class="flex items-center gap-2 "><div class="flex -space-x-2"><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#3178C6;z-index:3" title="TypeScript">T</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#61DAFB;z-index:2" title="React">R</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10" style="background-color:#6B7280;z-index:1" title="Rollup">R</div><div class="relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-500 flex items-center justify-center text-xs font-bold text-white shadow-lg" title="+2 more technologies">+<!-- -->2</div></div><div class="flex-1 max-w-20"><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"><div class="h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500" style="width:50%"></div></div><div class="text-xs text-gray-500 dark:text-gray-400 mt-1">5<!-- --> tech<!-- -->s</div></div></div><div class="grid grid-cols-2 gap-2 "><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">⭐</span><span class="font-medium">2.3K</span><span class="text-gray-500 dark:text-gray-400 truncate">Stars</span></div><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">🍴</span><span class="font-medium">234</span><span class="text-gray-500 dark:text-gray-400 truncate">Forks</span></div><div class="flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105"><span class="text-xs">👥</span><span class="font-medium">45</span><span class="text-gray-500 dark:text-gray-400 truncate">Contributors</span></div></div></div><div class="flex flex-wrap gap-1.5"><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#16A34A15;border-color:#16A34A30;color:#16A34A">Open Source</span><span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105" style="background-color:#05966915;border-color:#05966930;color:#059669">Library</span></div><div class="flex items-center justify-between gap-2 relative z-30 mt-auto"><div class="relative overflow-hidden rounded-lg flex-1"><a href="https://github.com/example/open-source-library" class="inline-flex items-center justify-center gap-1.5 px-3 py-2 text-xs font-medium text-white rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 magnetic-effect w-full relative overflow-hidden z-30" style="background:linear-gradient(135deg, #16A34A 0%, #15803D 100%);box-shadow:0 4px 20px rgba(22, 163, 74, 0.25);min-height:36px" target="_blank" rel="noopener noreferrer" aria-label="Visit Open Source Library"><div class="absolute inset-0 opacity-0 hover:opacity-20 transition-opacity duration-300 pointer-events-none" style="background:linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent)"></div><span class="relative z-10 truncate">Visit</span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right relative z-10 transition-transform duration-300 group-hover:translate-x-1 flex-shrink-0"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div><div class="relative overflow-hidden rounded-full flex-shrink-0"><button class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 hover:scale-110 magnetic-effect relative z-30 rounded-full" style="min-width:36px;min-height:36px" aria-label="Preview Open Source Library"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></button></div></div></div></div><div class="absolute pointer-events-none opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-full blur-xl" style="width:120px;height:120px;background:radial-gradient(circle, rgba(22, 163, 74, 0.4), #16A34A30, transparent);left:-60px;top:-60px;transform:translate3d(0, 0, 0)"></div><div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none" style="background:linear-gradient(135deg, transparent, #16A34A20, transparent);filter:blur(1px)"></div></div></li></div></div><div class="mt-12 text-center"><div class="inline-flex items-center gap-4 px-6 py-3 bg-white dark:bg-gray-800 rounded-lg shadow-lg"><div class="flex items-center gap-2"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-sm text-gray-600 dark:text-gray-300">统一高度</span></div><div class="flex items-center gap-2"><div class="w-3 h-3 bg-emerald-500 rounded-full"></div><span class="text-sm text-gray-600 dark:text-gray-300">绿色主题</span></div><div class="flex items-center gap-2"><div class="w-3 h-3 bg-teal-500 rounded-full"></div><span class="text-sm text-gray-600 dark:text-gray-300">智能截断</span></div><div class="flex items-center gap-2"><div class="w-3 h-3 bg-cyan-500 rounded-full"></div><span class="text-sm text-gray-600 dark:text-gray-300">高级交互</span></div></div></div></div></div></main><footer class="mt-16 flex-none"><div class="sm:px-8"><div class="mx-auto w-full max-w-7xl lg:px-8"><div class="border-t border-muted pb-16 pt-10"><div class="relative px-4 sm:px-8 lg:px-12"><div class="mx-auto max-w-2xl lg:max-w-5xl"><div class="flex flex-col items-center justify-between gap-6 sm:flex-row sm:items-start"><div class="flex flex-col gap-4"><div class="flex flex-wrap justify-center gap-x-6 gap-y-1 text-sm font-medium"><a class="transition hover:text-primary" href="/">Home</a><a class="transition hover:text-primary" href="/about">About</a><a class="transition hover:text-primary" href="/projects">Projects</a><a class="transition hover:text-primary" href="/blogs">Blogs</a><a class="transition hover:text-primary" href="/gallery">Gallery</a></div><p class="text-xs text-muted-foreground text-center">Frontend template from <a href="https://github.com/iAmCorey/coreychiu-portfolio-template" target="_blank" rel="noopener noreferrer" class="underline hover:text-primary">coreychiu-portfolio-template</a><br/>Full-stack personal website based on Next.js + MySQL + FastAPI</p></div><div class="flex flex-col justify-center items-start"><div class="flex flex-row justify-end items-center gap-2"><p class="text-sm text-muted-foreground">© <!-- -->2025<!-- --> <!-- -->Jay-Yao<!-- -->. All rights reserved.</p><a class="text-xs text-muted-foreground hover:text-primary transition-colors duration-200 underline" title="查看网站版本历史" href="/version-history">Version</a><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg><span class="sr-only">Toggle theme</span></button></div><div class="mt-0">Loading...</div><div class="flex flex-row items-center justify-center gap-2 text-sm text-gray-500 mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 256 256"><path d="M128,56C48,56,16,128,16,128s32,72,112,72,112-72,112-72S208,56,128,56Zm0,112a40,40,0,1,1,40-40A40,40,0,0,1,128,168Z" opacity="0.2"></path><path d="M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z"></path></svg>Total Visits: <!-- -->-<!-- --> / Today Visits: <!-- -->-</div></div></div></div></div></div></div></div></footer></div></div><script src="/_next/static/chunks/webpack-64cec67748a067e2.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/8b6d6f69c7970b5a.css\",\"style\"]\n2:HL[\"/_next/static/css/72074f6a7392446a.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"3:I[12846,[],\"\"]\n5:I[12177,[\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"989\",\"static/chunks/app/project-demo/page-31ae0f46b19bbf2a.js\"],\"default\"]\n6:I[4707,[],\"\"]\n7:I[36423,[],\"\"]\n8:I[93285,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"default\"]\n9:I[46021,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"Providers\"]\na:I[83258,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"ThemeInitializer\"]\nb:I[59183,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"RouteProgressBar\"]\nc:I[62989,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"GlobalLoadingManager\"]\nd:I[20686,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"SmartPrefetch\"]\ne:I[3864,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"Header\"]\nf:I[72972,[\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"931\",\"static/chunks/app/page-b49a98deec5a828b.js\"],\"\"]\n10:I[18908,[\"446"])</script><script>self.__next_f.push([1,"\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"Footer\"]\n11:I[60827,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"default\"]\n12:I[42545,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"PlausibleAnalytics\"]\n13:I[11816,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"PerformanceMonitor\"]\n14:I[11816,[\"446\",\"static/chunks/ui-libs-e96f0533cedd4426.js\",\"105\",\"static/chunks/icon-libs-9a3ac8a6739d257f.js\",\"592\",\"static/chunks/common-92ea84bd7ba12cdf.js\",\"185\",\"static/chunks/app/layout-0fa287aaecc84f29.js\"],\"PerformanceDebugger\"]\n16:I[61060,[],\"\"]\n17:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L3\",null,{\"buildId\":\"7n7atQT5AqmloxjVA4Y6t\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"project-demo\"],\"initialTree\":[\"\",{\"children\":[\"project-demo\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"project-demo\",{\"children\":[\"__PAGE__\",{},[[\"$L4\",[\"$\",\"$L5\",null,{}],null],null],null]},[null,[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"project-demo\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/8b6d6f69c7970b5a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/72074f6a7392446a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"h-full antialiased\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"flex h-full\",\"children\":[\"$\",\"$L8\",null,{\"children\":[\"$\",\"$L9\",null,{\"children\":[[\"$\",\"$La\",null,{}],[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{}],[\"$\",\"div\",null,{\"className\":\"flex w-full\",\"children\":[[\"$\",\"$Ld\",null,{\"routes\":[\"/\",\"/about\",\"/projects\",\"/blogs\",\"/gallery\"],\"priority\":\"low\",\"delay\":1500}],[\"$\",\"div\",null,{\"className\":\"fixed inset-0 flex justify-center sm:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex w-full max-w-7xl lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full shadow-xl dark:bg-muted\"}]}]}],[\"$\",\"div\",null,{\"className\":\"relative flex w-full flex-col px-4 sm:px-0\",\"children\":[[\"$\",\"$Le\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-auto\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"sm:px-8 flex h-full items-center pt-16 sm:pt-32\",\"children\":[\"$\",\"div\",null,{\"className\":\"mx-auto w-full max-w-7xl lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative px-4 sm:px-8 lg:px-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"mx-auto max-w-2xl lg:max-w-5xl\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-base font-semibold text-zinc-400 dark:text-zinc-500\",\"children\":\"404\"}],[\"$\",\"h1\",null,{\"className\":\"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100\",\"children\":\"Page not found\"}],[\"$\",\"p\",null,{\"className\":\"mt-4 text-base text-zinc-600 dark:text-zinc-400\",\"children\":\"Sorry, we couldn’t find the page you’re looking for.\"}],[\"$\",\"$Lf\",null,{\"className\":\"inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70 mt-4\",\"href\":\"/\",\"children\":\"Go back home\"}]]}]}]}]}]}],\"notFoundStyles\":[]}]}],[\"$\",\"$L10\",null,{}]]}]]}],[null,[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{}]],[\"$\",\"$L13\",null,{}],[\"$\",\"$L14\",null,{}]]}]}]}]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L15\"],\"globalErrorComponent\":\"$16\",\"missingSlots\":\"$W17\"}]\n"])</script><script>self.__next_f.push([1,"15:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"项目卡片演示 - 红点奖级别设计\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"展示新的项目卡片设计，包含统一高度、绿色主题和高级交互效果\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"Jingyao Chen\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"Jingyao Chen\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"Jingyao Chen\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"theme-color\",\"content\":\"#171717\"}],[\"$\",\"meta\",\"10\",{\"name\":\"msapplication-TileColor\",\"content\":\"#171717\"}],[\"$\",\"link\",\"11\",{\"rel\":\"canonical\",\"href\":\"http://**************:3000\"}],[\"$\",\"link\",\"12\",{\"rel\":\"alternate\",\"type\":\"application/rss+xml\",\"href\":\"http://**************:3000/feed\"}],[\"$\",\"meta\",\"13\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:title\",\"content\":\"jyaos\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:description\",\"content\":\"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey.\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:url\",\"content\":\"http://**************:3000\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:site_name\",\"content\":\"Jingyao Chen Portfolio\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:image\",\"content\":\"http://**************:3000/images/og-jingyao-portfolio.jpg\"}],[\"$\",\"meta\",\"20\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"21\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"22\",{\"property\":\"og:image:alt\",\"content\":\"jyaos\"}],[\"$\",\"meta\",\"23\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"25\",{\"name\":\"twitter:site\",\"content\":\"@JingyaoC\"}],[\"$\",\"meta\",\"26\",{\"name\":\"twitter:creator\",\"content\":\"@JingyaoC\"}],[\"$\",\"meta\",\"27\",{\"name\":\"twitter:title\",\"content\":\"jyaos\"}],[\"$\",\"meta\",\"28\",{\"name\":\"twitter:description\",\"content\":\"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey.\"}],[\"$\",\"meta\",\"29\",{\"name\":\"twitter:image\",\"content\":\"http://**************:3000/images/og-jingyao-portfolio.jpg\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"902x902\"}]]\n"])</script><script>self.__next_f.push([1,"4:null\n"])</script></body></html>