(()=>{var e={};e.id=928,e.ids=[928],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},69169:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),a(42535),a(48109),a(27683);var r=a(23191),s=a(88716),l=a(37922),i=a.n(l),n=a(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let d=["",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,42535)),"/home/<USER>/Code/me/My-web/frontend/src/app/gallery/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/gallery/page.tsx"],m="/gallery/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/gallery/page",pathname:"/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70504:(e,t,a)=>{Promise.resolve().then(a.bind(a,933)),Promise.resolve().then(a.bind(a,46618)),Promise.resolve().then(a.bind(a,2633))},24230:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},96633:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},48998:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},67427:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},94893:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},88307:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1572:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},40765:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(62881).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},933:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let r=a(94129);function s(e){let{reason:t,children:a}=e;throw new r.BailoutToCSRError(t)}},46618:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return l}});let r=a(10326),s=a(54580);function l(e){let{moduleIds:t}=e,a=(0,s.getExpectedRequestStore)("next/dynamic css"),l=[];if(a.reactLoadableManifest&&t){let e=a.reactLoadableManifest;for(let a of t){if(!e[a])continue;let t=e[a].files.filter(e=>e.endsWith(".css"));l.push(...t)}}return 0===l.length?null:(0,r.jsx)(r.Fragment,{children:l.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:a.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},2633:(e,t,a)=>{"use strict";a.r(t),a.d(t,{EnhancedGalleryContainer:()=>E});var r=a(10326),s=a(17577),l=a.n(s),i=a(48998),n=a(62881);let o=(0,n.Z)("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),d=(0,n.Z)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]),c=(0,n.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var m=a(37358),x=a(1572),u=a(27524),h=a(23201),g=a(46226),y=a(941),p=a(77636),b=a(51896),f=a(12714),j=a(96633),v=a(94019),w=a(75970),k=a(71754);function N({entries:e,highlightedEntryId:t}){let[a,i]=(0,s.useState)(null),[n,o]=(0,s.useState)(0),[d,c]=(0,s.useState)([]),[x,N]=(0,s.useState)({}),[S,C]=(0,s.useState)([]),[A,_]=(0,s.useState)(null),[Z,P]=(0,s.useState)(!1),[M,q]=(0,s.useState)(null),z=e=>{if(!e)return"";let t=e.thumbnail_url||e.url;return t?t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`):""},T=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch{return e}},$=(e,t)=>{let a=t.findIndex(t=>t.id===e.id);i(e),o(a),c(t)},L=()=>{i(null),o(0),c([])},E=e=>{C(t=>t.map(t=>t.year===e?{...t,isCollapsed:!t.isCollapsed}:t))},I=l().memo(({year:e,isCollapsed:t,entryCount:a})=>{let l=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".year-node"),a=e.currentTarget.querySelector(".year-label");t&&(t.style.transform="scale(1.05) translateY(-2px)",t.style.boxShadow=`0 8px 25px ${h.AS.primary.main}40, ${h.AS.shadows.glow}`),a&&(a.style.transform="translateY(-50%) translateX(4px) scale(1.02)",a.style.boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)")},[]),i=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".year-node"),a=e.currentTarget.querySelector(".year-label");t&&(t.style.transform="scale(1) translateY(0)",t.style.boxShadow=h.AS.shadows.glow),a&&(a.style.transform="translateY(-50%) translateX(0) scale(1)",a.style.boxShadow="0 4px 6px rgba(0, 0, 0, 0.1)")},[]);return(0,r.jsxs)("div",{className:"relative group cursor-pointer",onClick:()=>E(e),style:{paddingLeft:"1rem",paddingRight:"1rem",paddingTop:"0.5rem",paddingBottom:"0.5rem"},onMouseEnter:l,onMouseLeave:i,children:[(0,r.jsxs)("div",{className:"year-node relative w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg transform-gpu transition-all duration-300",style:{background:h.AS.gradients.primary,boxShadow:h.AS.shadows.glow},children:[r.jsx(k.Vj,{intensity:"normal",children:r.jsx("span",{className:"relative z-10",children:e})}),r.jsx("div",{className:"absolute inset-0 rounded-full animate-ping opacity-0 group-hover:opacity-30 transition-opacity duration-300",style:{backgroundColor:h.AS.primary.main}})]}),r.jsx("div",{className:"year-label absolute left-20 top-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transform-gpu transition-all duration-300",style:{boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"font-semibold text-gray-900 dark:text-white",children:e}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",a," memories)"]}),r.jsx("div",{className:"transition-transform duration-200",style:{transform:t?"rotate(0deg)":"rotate(180deg)"},children:r.jsx(y.Z,{className:"w-4 h-4"})})]})})]})}),W=l().memo(({entry:e,index:t})=>{let{elementRef:a,isVisible:l}=(0,w.$p)(.2),i=M?.id===e.id,n=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".timeline-card");t&&(t.style.transform="translateY(-8px) scale(1.02)",t.style.boxShadow="0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.1)")},[]),o=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".timeline-card");t&&(t.style.transform="translateY(0) scale(1)",t.style.boxShadow="0 10px 25px rgba(0, 0, 0, 0.1)")},[]);return r.jsx(k.ay,{delay:100*t,children:(0,r.jsxs)("div",{id:`timeline-entry-${e.id}`,ref:a,className:(0,u.cn)("relative ml-8 mb-8",i&&"highlight-entry"),style:{paddingLeft:"1rem",paddingRight:"1rem",paddingTop:"0.5rem",paddingBottom:"0.5rem",marginLeft:"2rem",marginRight:"1rem"},children:[r.jsx("div",{className:"absolute -left-12 top-8 w-8 h-0.5 bg-gradient-to-r from-gray-300 to-transparent dark:from-gray-600"}),r.jsx("div",{className:"absolute -left-14 top-7 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 shadow-sm z-10",style:{backgroundColor:h.AS.primary.main}}),r.jsx(k.Uf,{children:(0,r.jsxs)("div",{className:"timeline-card relative overflow-hidden rounded-2xl border cursor-pointer group transition-all duration-300",style:{...h.WD.getTimelineCardStyle(),boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)",borderColor:`${h.AS.primary.main}20`},onMouseEnter:n,onMouseLeave:o,children:[r.jsx("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:h.AS.gradients.glow}}),(0,r.jsxs)("div",{className:"relative z-10 p-6",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),r.jsx("span",{children:T(e.date)})]}),e.location&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(p.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),r.jsx("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(b.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{children:[e.images.length," photos"]})]})]}),e.content&&r.jsx("div",{className:"mb-6",children:r.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e.content})}),e.images.length>0&&(0,r.jsxs)("div",{className:(0,u.cn)("grid gap-3 rounded-lg",1===e.images.length?"grid-cols-1":2===e.images.length?"grid-cols-2":3===e.images.length?"grid-cols-3":"grid-cols-2 sm:grid-cols-3"),style:{padding:"0.25rem",margin:"-0.25rem"},children:[e.images.slice(0,6).map((t,a)=>r.jsx(k.O1,{delay:50*a,children:(0,r.jsxs)("div",{className:(0,u.cn)("relative cursor-pointer overflow-hidden rounded-lg group/image transform-gpu",1===e.images.length?"aspect-video":"aspect-square"),onClick:()=>$(t,e.images),style:{transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",transformOrigin:"center center"},onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.02) translateY(-2px)",e.currentTarget.style.boxShadow=`0 8px 20px ${h.AS.primary.main}20, 0 4px 8px rgba(0, 0, 0, 0.1)`},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1) translateY(0)",e.currentTarget.style.boxShadow="none"},children:[r.jsx(g.default,{src:z(t),alt:t.alt||`Gallery image ${a+1}`,fill:!0,className:"object-cover transition-transform duration-300 group-hover/image:scale-110",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),r.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover/image:bg-black/20 transition-colors duration-300"}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-300",children:r.jsx(f.Z,{className:"w-6 h-6 text-white"})})]})},t.id)),e.images.length>6&&r.jsx("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-gray-500 dark:text-gray-400 font-medium",children:["+",e.images.length-6]})})]})]})]})})]})})});return(0,r.jsxs)("div",{className:"timeline-container relative",children:[(0,r.jsxs)("div",{className:"relative",style:{paddingLeft:"2rem",paddingRight:"2rem",paddingTop:"1rem",paddingBottom:"1rem"},children:[r.jsx("div",{className:"absolute top-0 bottom-0 w-0.5 opacity-30",style:{backgroundColor:h.AS.primary.main,left:"2.5rem"}}),S.map((e,t)=>(0,r.jsxs)("div",{className:"relative mb-12",children:[r.jsx(k.ay,{delay:200*t,children:r.jsx(I,{year:e.year,isCollapsed:e.isCollapsed,entryCount:e.entries.length})}),!e.isCollapsed&&r.jsx("div",{className:"mt-8",children:r.jsx(k.dA,{staggerDelay:100,children:e.entries.map((e,t)=>r.jsx(W,{entry:e,index:t},e.id))})})]},e.year))]}),a&&(0,r.jsxs)("div",{className:"fixed inset-0 z-[60] flex items-center justify-center backdrop-blur-md bg-white/95 dark:bg-gray-900/95",onClick:L,children:[(0,r.jsxs)("div",{className:"relative max-w-7xl max-h-[90vh] m-4 flex items-center justify-center",onClick:e=>e.stopPropagation(),children:[d.length>1&&r.jsx("button",{onClick:()=>{if(0===d.length)return;let e=n>0?n-1:d.length-1;o(e),i(d[e])},className:"absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:r.jsx(j.Z,{className:"w-6 h-6 rotate-[-90deg]"})}),r.jsx("div",{className:"relative",children:r.jsx(g.default,{src:(e=>{if(!e||!e.url)return"";let t=e.url;return t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`)})(a),alt:a.alt||"Gallery image",width:1200,height:800,className:(0,u.cn)("object-contain rounded-lg max-w-[85vw] transition-all duration-300",Z?"max-h-[70vh]":"max-h-[85vh]"),quality:90})}),d.length>1&&r.jsx("button",{onClick:()=>{if(0===d.length)return;let e=n<d.length-1?n+1:0;o(e),i(d[e])},className:"absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:r.jsx(j.Z,{className:"w-6 h-6 rotate-90"})}),r.jsx("button",{onClick:L,className:"absolute top-4 right-4 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:r.jsx(v.Z,{className:"w-6 h-6"})})]}),r.jsx("div",{className:"fixed bottom-6 left-1/2 -translate-x-1/2 z-20 pointer-events-none",children:r.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl px-6 py-4 text-gray-700 dark:text-gray-300 shadow-2xl border border-gray-200/20 dark:border-gray-700/20 max-w-lg pointer-events-auto",children:(0,r.jsxs)("div",{className:"text-center",children:[a.alt&&r.jsx("p",{className:"font-medium mb-2 max-w-md truncate",children:a.alt}),d.length>1&&(0,r.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[n+1," of ",d.length]}),d.length>1&&d.length<=10&&(0,r.jsxs)("button",{onClick:e=>{e.stopPropagation(),P(!Z)},className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-all duration-200 flex items-center gap-1 px-3 py-1.5 rounded-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/50 border border-transparent hover:border-gray-300 dark:hover:border-gray-600",children:[(0,r.jsxs)("span",{children:[Z?"Hide":"Show"," thumbnails"]}),r.jsx(j.Z,{className:(0,u.cn)("w-3 h-3 transition-transform duration-200",Z?"rotate-180":"")})]})]}),d.length>1&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center mt-2",children:["Use ← → keys to navigate",d.length<=10?", T to toggle thumbnails":""]})]})})}),d.length>1&&d.length<=10&&r.jsx("div",{className:(0,u.cn)("fixed bottom-0 left-0 right-0 z-30 transition-all duration-500 ease-out",Z?"translate-y-0 opacity-100":"translate-y-full opacity-0 pointer-events-none"),onClick:e=>e.stopPropagation(),children:r.jsx("div",{className:"bg-gradient-to-t from-black/80 via-black/60 to-transparent backdrop-blur-xl",children:r.jsx("div",{className:"container mx-auto px-4 py-4 sm:py-6",children:r.jsx("div",{className:"flex justify-center",children:r.jsx("div",{className:"bg-white/10 dark:bg-gray-800/20 backdrop-blur-md rounded-2xl p-3 sm:p-4 shadow-2xl border border-white/20 dark:border-gray-700/30 max-w-full",children:r.jsx("div",{className:"flex gap-2 sm:gap-3 justify-center max-w-full overflow-x-auto pb-2 thumbnail-scroll",children:d.map((e,t)=>(0,r.jsxs)("button",{onClick:a=>{a.stopPropagation(),o(t),i(e)},className:(0,u.cn)("relative rounded-xl overflow-hidden border-2 transition-all duration-300 flex-shrink-0 group","w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16",t===n?"border-white/80 scale-110 shadow-2xl ring-2 ring-white/40":"border-white/20 hover:border-white/60 opacity-60 hover:opacity-100 hover:scale-105"),children:[r.jsx(g.default,{src:z(e),alt:e.alt||`Thumbnail ${t+1}`,width:64,height:64,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"}),t===n&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),r.jsx("div",{className:"absolute top-1 right-1 w-2 h-2 bg-white rounded-full shadow-lg"})]})]},e.id))})})})})})})]})]})}var S=a(35047),C=a(94893),A=a(24230);function _({albums:e}){let t=(0,S.useRouter)(),a=e=>{t.push(`/gallery/album/${e.id}`)},i=e=>{let t;return e.cover_image&&(t="object"==typeof e.cover_image&&null!==e.cover_image?e.cover_image.thumbnail_url||e.cover_image.url:e.cover_image)?t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`):"/images/placeholder.jpg"},n=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short"})}catch{return e}},d=l().memo(({album:e,index:t})=>{let l=(0,w.Lv)(.15),d=(0,w.wb)(8),[x,y]=(0,s.useState)(!1),p=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".album-card"),a=e.currentTarget;t&&a&&(t.style.transform="translateY(-8px) scale(1.02)",t.style.boxShadow=h.AS.shadows.cardHover,a.style.zIndex="10")},[]),b=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".album-card"),a=e.currentTarget;t&&a&&(t.style.transform="translateY(0) scale(1)",t.style.boxShadow=h.WD.getAlbumCardStyle().boxShadow||"",a.style.zIndex="1")},[]);return r.jsx(k.ay,{delay:150*t,children:r.jsx(k.Uf,{children:(0,r.jsxs)("div",{ref:l,className:"group relative cursor-pointer",onClick:t=>{t.preventDefault(),t.stopPropagation(),a(e)},onMouseEnter:p,onMouseLeave:b,children:[r.jsx("div",{className:"absolute -inset-2 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl",style:{background:h.AS.gradients.glow}}),(0,r.jsxs)("div",{ref:d,className:"album-card relative rounded-2xl transition-all duration-500 transform-gpu h-[420px] group",style:{...h.WD.getAlbumCardStyle(),overflow:"visible"},children:[(0,r.jsxs)("div",{className:"relative h-64 overflow-hidden rounded-t-2xl",children:[r.jsx("div",{className:"absolute inset-0 z-10",style:{background:h.AS.gradients.overlay}}),r.jsx(g.default,{src:i(e),alt:e.title,fill:!0,className:(0,u.cn)("object-cover transition-all duration-700 group-hover:scale-110",x?"opacity-100":"opacity-0"),sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onLoad:()=>y(!0)}),!x&&r.jsx("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse flex items-center justify-center",children:r.jsx(c,{className:"w-12 h-12 text-gray-400"})}),r.jsx("div",{className:"absolute inset-0 z-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:r.jsx(k.Vj,{intensity:"intense",children:r.jsx("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white shadow-2xl transition-transform duration-300 group-hover:scale-110",style:{backgroundColor:h.AS.primary.main},children:r.jsx(C.Z,{className:"w-6 h-6 ml-1",fill:"currentColor"})})})}),r.jsx("div",{className:"absolute top-4 right-4 z-20",children:(0,r.jsxs)("div",{className:"px-3 py-1 rounded-full text-white text-sm font-medium backdrop-blur-sm",style:{backgroundColor:`${h.AS.primary.main}CC`},children:[r.jsx(c,{className:"w-4 h-4 inline mr-1"}),e.image_count]})})]}),(0,r.jsxs)("div",{className:"relative z-10 p-6 flex flex-col h-40 bg-white dark:bg-gray-800 rounded-b-2xl",children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-1",children:e.title}),r.jsx("div",{className:"h-10 mb-3",children:r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-2",children:e.description||"暂无描述"})}),r.jsx("div",{className:"mb-3",children:e.category&&(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",style:{backgroundColor:`${h.AS.primary.main}20`,color:h.AS.primary.main,border:`1px solid ${h.AS.primary.main}30`},children:[r.jsx(o,{className:"w-3 h-3 mr-1"}),e.category.name]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mt-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m.Z,{className:"w-4 h-4"}),r.jsx("span",{children:n(e.created_at)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-primary font-medium group-hover:gap-2 transition-all duration-300",children:[r.jsx("span",{children:"View Album"}),r.jsx(A.Z,{className:"w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"})]})]})]}),r.jsx("div",{className:"absolute bottom-0 left-0 h-1 bg-gradient-to-r transition-all duration-500 transform origin-left scale-x-0 group-hover:scale-x-100",style:{background:h.AS.gradients.primary,width:"100%"}})]})]})})})});return 0===e.length?(0,r.jsxs)("div",{className:"py-12 text-center",children:[r.jsx("div",{className:"w-24 h-24 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:r.jsx(o,{className:"w-12 h-12 text-gray-400"})}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No albums yet"}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Create your first album to get started"})]}):(0,r.jsxs)("div",{className:"space-y-8",children:[r.jsx(()=>{let t=e.reduce((e,t)=>e+t.image_count,0),a=e.length;return r.jsx(k.ay,{children:r.jsx("div",{className:"mb-8 p-6 rounded-2xl border",style:h.WD.getAlbumCardStyle(),children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:a}),r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Albums"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:t}),r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Photos"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:Math.round(t/a)||0}),r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avg per Album"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:[new Date().getFullYear()-Math.min(...e.map(e=>new Date(e.created_at).getFullYear())),"+"]}),r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Years"})]})]})})})},{}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4",children:e.map((e,t)=>r.jsx("div",{className:"relative",style:{paddingTop:"12px",paddingBottom:"12px"},children:r.jsx(d,{album:e,index:t})},e.id))})]})}let Z=(0,n.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var P=a(67427);let M=(0,n.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);function q({images:e,loading:t=!1,searchQuery:a="",selectedCategory:l=null,selectedTags:i=[],layout:n="masonry"}){let[o,d]=(0,s.useState)(null),[c,m]=(0,s.useState)(0),[x,y]=(0,s.useState)(null),[p,b]=(0,s.useState)(new Set),[N,S]=(0,s.useState)(new Set),[C,A]=(0,s.useState)(!1),{preloadImage:_,isImageLoaded:q}=(0,w.o1)(),z=(0,s.useRef)(null),T=(0,s.useRef)(null),$=e=>{if(!e)return"";let t=e.thumbnail_url||e.url;return t?t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`):""},L=t=>{let a=e.findIndex(e=>e.id===t.id);d(t),m(a)},E=()=>{d(null),m(0)},I=({image:e,index:t})=>{let a=(0,s.useRef)(null),[l,i]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=a.current;return e&&T.current&&T.current.observe(e),()=>{e&&T.current&&T.current.unobserve(e)}},[]);let c=e.width&&e.height?e.width/e.height:1;return r.jsx(k.ay,{delay:50*t,children:(0,r.jsxs)("div",{ref:a,"data-index":t,className:"group relative cursor-pointer break-inside-avoid mb-4",style:{aspectRatio:"masonry"===n?c:"1"},onMouseEnter:()=>{d(!0),y(t)},onMouseLeave:()=>{d(!1),y(null)},onClick:()=>L(e),children:[r.jsx("div",{className:"absolute -inset-1 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 blur-sm",style:{background:h.AS.gradients.glow}}),(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-lg transition-all duration-500 transform-gpu group-hover:scale-[1.02] group-hover:shadow-2xl",style:h.WD.getGridCardStyle(),children:[N.has(t)&&(0,r.jsxs)(r.Fragment,{children:[!l&&r.jsx("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse"}),r.jsx(g.default,{src:$(e),alt:e.alt||`Gallery image ${t+1}`,fill:!0,className:(0,u.cn)("object-cover transition-all duration-700 group-hover:scale-110",l?"opacity-100":"opacity-0"),sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",onLoad:()=>i(!0),quality:75})]}),r.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300"}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[r.jsx(k.Vj,{intensity:"normal",children:r.jsx("button",{className:"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors duration-200 flex items-center justify-center",onClick:t=>{t.stopPropagation(),L(e)},children:r.jsx(Z,{className:"w-5 h-5"})})}),r.jsx(k.Vj,{intensity:"normal",children:r.jsx("button",{className:"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors duration-200 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:r.jsx(P.Z,{className:"w-5 h-5"})})}),r.jsx(k.Vj,{intensity:"normal",children:r.jsx("button",{className:"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors duration-200 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:r.jsx(M,{className:"w-5 h-5"})})})]})}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[e.alt&&r.jsx("p",{className:"text-white text-sm font-medium mb-1 line-clamp-2",children:e.alt}),e.tags&&e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,t)=>{let a="string"==typeof e?e:e.name||e;return r.jsx("span",{className:"px-2 py-1 text-xs rounded-full bg-white/20 backdrop-blur-sm text-white",children:a},t)}),e.tags.length>3&&(0,r.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full bg-white/20 backdrop-blur-sm text-white",children:["+",e.tags.length-3]})]})]}),N.has(t)&&!l&&r.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:r.jsx("div",{className:"w-8 h-8 border-2 border-t-transparent rounded-full animate-spin",style:{borderColor:h.AS.primary.main}})})]})]})})};return t?r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:12}).map((e,t)=>r.jsx("div",{className:"aspect-square bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"},t))}):0===e.length?(0,r.jsxs)("div",{className:"py-12 text-center",children:[r.jsx("div",{className:"w-24 h-24 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:r.jsx(f.Z,{className:"w-12 h-12 text-gray-400"})}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No images found"}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:a?`No results for "${a}"`:"No images to display"})]}):(0,r.jsxs)("div",{ref:z,className:"relative",children:[(()=>{switch(n){case"masonry":return r.jsx("div",{className:"columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4",children:e.map((e,t)=>r.jsx(I,{image:e,index:t},e.id))});case"grid":return r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:e.map((e,t)=>r.jsx(I,{image:e,index:t},e.id))});case"justified":return r.jsx("div",{className:"flex flex-wrap gap-2",children:e.map((e,t)=>{let a=e.width&&e.height?e.width/e.height:1,s=Math.max(200,Math.min(400,200*a));return r.jsx("div",{style:{width:`${s}px`,flexGrow:a},children:r.jsx(I,{image:e,index:t})},e.id)})});default:return null}})(),o&&(0,r.jsxs)("div",{className:"fixed inset-0 z-[60] flex items-center justify-center backdrop-blur-md bg-white/95 dark:bg-gray-900/95",onClick:E,children:[(0,r.jsxs)("div",{className:"relative max-w-7xl max-h-[90vh] m-4 flex items-center justify-center",onClick:e=>e.stopPropagation(),children:[e.length>1&&r.jsx("button",{onClick:()=>{if(0===e.length)return;let t=c>0?c-1:e.length-1;m(t),d(e[t])},className:"absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:r.jsx(j.Z,{className:"w-6 h-6 rotate-[-90deg]"})}),r.jsx("div",{className:"relative",children:r.jsx(g.default,{src:(e=>{if(!e||!e.url)return"";let t=e.url;return t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`)})(o),alt:o.alt||"Gallery image",width:1200,height:800,className:(0,u.cn)("object-contain rounded-lg max-w-[85vw] transition-all duration-300",C?"max-h-[70vh]":"max-h-[85vh]"),quality:90})}),e.length>1&&r.jsx("button",{onClick:()=>{if(0===e.length)return;let t=c<e.length-1?c+1:0;m(t),d(e[t])},className:"absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:r.jsx(j.Z,{className:"w-6 h-6 rotate-90"})}),r.jsx("button",{onClick:E,className:"absolute top-4 right-4 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:r.jsx(v.Z,{className:"w-6 h-6"})})]}),r.jsx("div",{className:"fixed bottom-6 left-1/2 -translate-x-1/2 z-20 pointer-events-none",children:r.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl px-6 py-4 text-gray-700 dark:text-gray-300 shadow-2xl border border-gray-200/20 dark:border-gray-700/20 max-w-lg pointer-events-auto",children:(0,r.jsxs)("div",{className:"text-center",children:[o.alt&&r.jsx("p",{className:"font-medium mb-2 max-w-md truncate",children:o.alt}),o.tags&&o.tags.length>0&&r.jsx("div",{className:"flex flex-wrap gap-2 justify-center mb-3",children:o.tags.map((e,t)=>{let a="string"==typeof e?e:e.name||e;return r.jsx("span",{className:"px-2 py-1 text-xs rounded-full bg-gray-200/50 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300",children:a},t)})}),e.length>1&&(0,r.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[c+1," of ",e.length]}),e.length>1&&e.length<=20&&(0,r.jsxs)("button",{onClick:e=>{e.stopPropagation(),A(!C)},className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-all duration-200 flex items-center gap-1 px-3 py-1.5 rounded-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/50 border border-transparent hover:border-gray-300 dark:hover:border-gray-600",children:[(0,r.jsxs)("span",{children:[C?"Hide":"Show"," thumbnails"]}),r.jsx(j.Z,{className:(0,u.cn)("w-3 h-3 transition-transform duration-200",C?"rotate-180":"")})]})]}),e.length>1&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center mt-2",children:["Use ← → keys to navigate",e.length<=20?", T to toggle thumbnails":""]})]})})}),e.length>1&&e.length<=20&&r.jsx("div",{className:(0,u.cn)("fixed bottom-0 left-0 right-0 z-30 transition-all duration-500 ease-out",C?"translate-y-0 opacity-100":"translate-y-full opacity-0 pointer-events-none"),onClick:e=>e.stopPropagation(),children:r.jsx("div",{className:"bg-gradient-to-t from-black/80 via-black/60 to-transparent backdrop-blur-xl",children:r.jsx("div",{className:"container mx-auto px-4 py-4 sm:py-6",children:r.jsx("div",{className:"flex justify-center",children:r.jsx("div",{className:"bg-white/10 dark:bg-gray-800/20 backdrop-blur-md rounded-2xl p-3 sm:p-4 shadow-2xl border border-white/20 dark:border-gray-700/30 max-w-full",children:r.jsx("div",{className:"flex gap-2 sm:gap-3 justify-center max-w-full overflow-x-auto pb-2 thumbnail-scroll",children:e.slice(Math.max(0,c-6),c+7).map((e,t)=>{let a=Math.max(0,c-6)+t;return(0,r.jsxs)("button",{onClick:t=>{t.stopPropagation(),m(a),d(e)},className:(0,u.cn)("relative rounded-xl overflow-hidden border-2 transition-all duration-300 flex-shrink-0 group","w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16",a===c?"border-white/80 scale-110 shadow-2xl ring-2 ring-white/40":"border-white/20 hover:border-white/60 opacity-60 hover:opacity-100 hover:scale-105"),children:[r.jsx(g.default,{src:$(e),alt:e.alt||`Thumbnail ${a+1}`,width:64,height:64,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"}),a===c&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),r.jsx("div",{className:"absolute top-1 right-1 w-2 h-2 bg-white rounded-full shadow-lg"})]})]},e.id)})})})})})})})]})]})}var z=a(88307);let T=(0,n.Z)("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var $=a(40765);function L({searchQuery:e,onSearchChange:t,categories:a,selectedCategory:l,onCategorySelect:i,selectedTags:n,onTagSelect:d,availableTags:c,onClearFilters:m,showFilters:x,onToggleFilters:g}){let[y,p]=(0,s.useState)(!1),[b,f]=(0,s.useState)(!1),[j,w]=(0,s.useState)(!1),N=(0,s.useRef)(null),S=e=>{d(n.includes(e)?n.filter(t=>t!==e):[...n,e])},C=()=>{let e=0;return l&&e++,n.length>0&&(e+=n.length),e};return(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(k.ay,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("div",{className:(0,u.cn)("relative flex items-center rounded-2xl border-2 transition-all duration-300 backdrop-blur-sm",y?"border-primary shadow-lg shadow-primary/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),style:{background:y?h.AS.gradients.card:"rgba(255, 255, 255, 0.8)"},children:[r.jsx("div",{className:"absolute left-4 flex items-center",children:r.jsx(z.Z,{className:(0,u.cn)("w-5 h-5 transition-colors duration-300",y?"text-primary":"text-gray-400")})}),r.jsx("input",{ref:N,type:"text",placeholder:"Search photos, albums, or tags...",value:e,onChange:e=>t(e.target.value),onFocus:()=>p(!0),onBlur:()=>p(!1),className:"w-full pl-12 pr-20 py-4 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none text-lg"}),e&&r.jsx("button",{onClick:()=>t(""),className:"absolute right-16 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:r.jsx(v.Z,{className:"w-4 h-4"})}),(0,r.jsxs)("button",{onClick:g,className:(0,u.cn)("absolute right-4 p-2 rounded-lg transition-all duration-300 flex items-center gap-2",x?"bg-primary text-white shadow-lg":"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"),children:[r.jsx(T,{className:"w-5 h-5"}),C()>0&&r.jsx("span",{className:"w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:C()})]})]}),y&&e.length>0&&r.jsx(k.oX,{direction:"down",children:r.jsx("div",{className:"absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-xl z-10 max-h-60 overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-4 text-sm text-gray-500 dark:text-gray-400",children:['Press Enter to search for "',e,'"']})})})]})}),x&&r.jsx(k.oX,{direction:"down",children:(0,r.jsxs)("div",{className:"p-6 rounded-2xl border backdrop-blur-sm",style:{background:h.AS.gradients.card,borderColor:h.AS.primary.main+"30"},children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[r.jsx(o,{className:"w-5 h-5",style:{color:h.AS.primary.main}}),r.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Categories"})]}),(0,r.jsxs)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[(0,r.jsxs)("button",{onClick:()=>i(null),className:(0,u.cn)("w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-between",null===l?"bg-primary text-white shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"),children:[r.jsx("span",{children:"All Categories"}),r.jsx("span",{className:"text-sm opacity-70",children:a.reduce((e,t)=>e+t.image_count,0)})]}),a.map(e=>(0,r.jsxs)("button",{onClick:()=>i(l===e.id?null:e.id),className:(0,u.cn)("w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-between",l===e.id?"bg-primary text-white shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon&&r.jsx("span",{className:"text-lg",children:e.icon}),r.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),r.jsx("span",{children:e.name})]}),r.jsx("span",{className:"text-sm opacity-70",children:e.image_count})]},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[r.jsx($.Z,{className:"w-5 h-5",style:{color:h.AS.primary.main}}),r.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Tags"}),n.length>0&&r.jsx("button",{onClick:()=>d([]),className:"text-sm text-primary hover:text-primary/80 transition-colors",children:"Clear all"})]}),r.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:c.slice(0,20).map(e=>(0,r.jsxs)("button",{onClick:()=>S(e.name),className:(0,u.cn)("w-full text-left px-4 py-2 rounded-lg transition-all duration-200 flex items-center justify-between text-sm",n.includes(e.name)?"bg-primary text-white shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"),children:[(0,r.jsxs)("span",{children:["#",e.name]}),r.jsx("span",{className:"text-xs opacity-70",children:e.count})]},e.name))})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[r.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:C()>0&&(0,r.jsxs)("span",{children:[C()," filter(s) active"]})}),(0,r.jsxs)("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:m,className:"px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"Clear All"}),r.jsx("button",{onClick:g,className:"px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium",children:"Apply Filters"})]})]})]})}),(l||n.length>0)&&r.jsx(k.ay,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[l&&(0,r.jsxs)("span",{className:"inline-flex items-center gap-2 px-3 py-1 bg-primary text-white rounded-full text-sm",children:[r.jsx(o,{className:"w-3 h-3"}),a.find(e=>e.id===l)?.name,r.jsx("button",{onClick:()=>i(null),className:"hover:bg-white/20 rounded-full p-0.5 transition-colors",children:r.jsx(v.Z,{className:"w-3 h-3"})})]}),n.map(e=>(0,r.jsxs)("span",{className:"inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary border border-primary/20 rounded-full text-sm",children:[r.jsx($.Z,{className:"w-3 h-3"}),e,r.jsx("button",{onClick:()=>S(e),className:"hover:bg-primary/20 rounded-full p-0.5 transition-colors",children:r.jsx(v.Z,{className:"w-3 h-3"})})]},e))]})})]})}function E({albums:e=[],timelineEntries:t=[],initialView:a="timeline",highlightedEntryId:n,highlightedAlbumId:g,galleryTitle:y="Photo Gallery",galleryDescription:p="Explore my photography collection through different perspectives - timeline memories, organized albums, or browse all photos in a grid."}){let b=e.map(e=>({id:e.id,title:e.title,description:e.description,cover_image:e.coverImage?{id:parseInt(e.coverImage.id)||0,url:e.coverImage.url,thumbnail_url:e.coverImage.thumbnail_url,display_name:e.coverImage.alt,original_filename:e.coverImage.alt||"image",width:e.coverImage.width,height:e.coverImage.height}:null,date:e.date,location:e.location,type:"theme",image_count:e.images.length,created_at:e.date,updated_at:e.date,slug:e.slug})),[f,j]=(0,s.useState)(a),[v,w]=(0,s.useState)([]),[S,C]=(0,s.useState)([]),[A,Z]=(0,s.useState)([]),[P,M]=(0,s.useState)(!1),[z,T]=(0,s.useState)(""),[$,E]=(0,s.useState)(null),[I,W]=(0,s.useState)([]),[G,R]=(0,s.useState)(!1),D=l().useMemo(()=>{let e=new Map;return v.forEach(t=>{t.tags?.forEach(t=>{let a="string"==typeof t?t:t.name||t;e.set(a,(e.get(a)||0)+1)})}),Array.from(e.entries()).map(([e,t])=>({name:e,count:t})).sort((e,t)=>t.count-e.count)},[v]);(0,s.useCallback)(()=>{(async()=>{M(!0);try{let e=await fetch("http://**************:8000/api/gallery/public");if(e.ok){let t=await e.json(),a=t.featured_items.map(e=>{let t=e.image,a="http://**************:8000";return{id:t.id,url:t.url.startsWith("http")?t.url:`${a}${t.url}`,thumbnail_url:t.thumbnail_url?t.thumbnail_url.startsWith("http")?t.thumbnail_url:`${a}${t.thumbnail_url}`:null,alt:t.display_name||t.original_filename,width:t.width||800,height:t.height||600,category:e.category,tags:e.tags||[],created_at:t.created_at}});w(a),C(a);let r=new Map;t.featured_items.forEach(e=>{e.category&&r.set(e.category.id,{...e.category,image_count:(r.get(e.category.id)?.image_count||0)+1})}),Z(Array.from(r.values()))}}catch(e){}finally{M(!1)}})()},[]);let F=()=>{T(""),E(null),W([])};return(0,r.jsxs)("div",{className:"space-y-8",children:[r.jsx(k.ay,{children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[r.jsx(k.Vj,{intensity:"normal",children:r.jsx(x.Z,{className:"w-8 h-8",style:{color:h.AS.primary.main}})}),r.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white",children:y}),r.jsx(k.Vj,{intensity:"normal",children:r.jsx(x.Z,{className:"w-8 h-8",style:{color:h.AS.primary.main}})})]}),r.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:p})]})}),r.jsx(L,{searchQuery:z,onSearchChange:T,categories:A,selectedCategory:$,onCategorySelect:E,selectedTags:I,onTagSelect:W,availableTags:D,onClearFilters:F,showFilters:G,onToggleFilters:()=>R(!G)}),r.jsx(()=>{let e=[{key:"timeline",label:"Timeline",icon:i.Z,description:"Chronological view"},{key:"albums",label:"Albums",icon:o,description:"Organized collections"},{key:"grid",label:"Grid",icon:d,description:"All photos"}];return r.jsx(k.ay,{children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx("div",{className:"inline-flex rounded-2xl p-1 backdrop-blur-sm border",style:{background:h.AS.gradients.card,borderColor:h.AS.primary.main+"30"},children:e.map(e=>{let t=e.icon,a=f===e.key;return(0,r.jsxs)("button",{onClick:()=>j(e.key),className:(0,u.cn)("relative flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 font-medium",a?"text-white shadow-lg transform scale-105":"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"),style:a?{background:h.AS.gradients.primary,boxShadow:h.AS.shadows.glow}:{},children:[a&&r.jsx("div",{className:"absolute inset-0 rounded-xl opacity-20 animate-pulse",style:{background:h.AS.gradients.glow}}),r.jsx(t,{className:"w-5 h-5 relative z-10"}),r.jsx("span",{className:"relative z-10",children:e.label}),a&&r.jsx("div",{className:"absolute -inset-1 rounded-xl opacity-30 blur-sm",style:{background:h.AS.gradients.primary}})]},e.key)})})})})},{}),r.jsx(()=>{let a=v.length,s=S.length,l=e.length,i=t.length;return r.jsx(k.ay,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl backdrop-blur-sm border",style:{background:h.AS.gradients.subtle,borderColor:h.AS.primary.main+"20"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(c,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[s," of ",a," photos"]})]}),l>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(o,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[l," albums"]})]}),i>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[i," memories"]})]})]}),(z||$||I.length>0)&&r.jsx("button",{onClick:F,className:"text-sm text-primary hover:text-primary/80 transition-colors font-medium",children:"Clear filters"})]})})},{}),(0,r.jsxs)("div",{className:"min-h-[400px]",children:["timeline"===f&&r.jsx(k.oX,{direction:"up",children:r.jsx(N,{entries:t,highlightedEntryId:n})}),"albums"===f&&r.jsx(k.oX,{direction:"up",children:r.jsx(_,{albums:b})}),"grid"===f&&r.jsx(k.oX,{direction:"up",children:r.jsx(q,{images:S,loading:P,searchQuery:z,selectedCategory:$,selectedTags:I,layout:"masonry"})})]})]})}},75970:(e,t,a)=>{"use strict";a.d(t,{$p:()=>n,Lv:()=>s,o1:()=>i,wb:()=>l});var r=a(17577);function s(e=.3){let t=(0,r.useRef)(null),[a,s]=(0,r.useState)(!1);return t}function l(e=10){return(0,r.useRef)(null)}function i(){let[e,t]=(0,r.useState)(new Set),[a,s]=(0,r.useState)(new Set),l=(0,r.useCallback)(r=>new Promise((l,i)=>{if(e.has(r)){l();return}if(a.has(r)){let t=()=>{e.has(r)?l():setTimeout(t,100)};t();return}s(e=>new Set(e).add(r));let n=new Image;n.onload=()=>{t(e=>new Set(e).add(r)),s(e=>{let t=new Set(e);return t.delete(r),t}),l()},n.onerror=()=>{s(e=>{let t=new Set(e);return t.delete(r),t}),i(Error(`Failed to load image: ${r}`))},n.src=r}),[e,a]),i=(0,r.useCallback)(e=>Promise.allSettled(e.map(e=>l(e))),[l]);return{preloadImage:l,preloadImages:i,isImageLoaded:(0,r.useCallback)(t=>e.has(t),[e]),isImageLoading:(0,r.useCallback)(e=>a.has(e),[a]),loadedImages:Array.from(e),loadingImages:Array.from(a)}}function n(e=.1){let t=(0,r.useRef)(null),[a,s]=(0,r.useState)(!1);return{elementRef:t,isVisible:a}}},55782:(e,t,a)=>{"use strict";a.d(t,{default:()=>s.a});var r=a(34567),s=a.n(r)},34567:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=a(53370);a(19510),a(71159);let s=r._(a(26155));function l(e,t){var a;let r={loading:e=>{let{error:t,isLoading:a,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let l={...r,...t};return(0,s.default)({...l,modules:null==(a=l.loadableGenerated)?void 0:a.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13689:(e,t,a)=>{"use strict";let{createProxy:r}=a(68570);e.exports=r("/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js")},26155:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let r=a(19510),s=a(71159),l=a(13689),i=a(44459);function n(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},a=(0,s.lazy)(()=>t.loader().then(n)),d=t.loading;function c(e){let n=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.PreloadCss,{moduleIds:t.modules}),(0,r.jsx)(a,{...e})]}):(0,r.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(a,{...e})});return(0,r.jsx)(s.Suspense,{fallback:n,children:o})}return c.displayName="LoadableComponent",c}},44459:(e,t,a)=>{"use strict";let{createProxy:r}=a(68570);e.exports=r("/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js")},42535:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u,dynamic:()=>m,generateMetadata:()=>c,revalidate:()=>x});var r=a(19510),s=a(12904),l=a(63979),i=a(59979),n=a(55782),o=a(53722);let d=(0,n.default)(()=>a.e(302).then(a.bind(a,76302)).then(e=>({default:e.EnhancedGalleryContainer})),{loadableGenerated:{modules:["app/gallery/page.tsx -> @/components/gallery/EnhancedGalleryContainer"]},loading:()=>r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:9}).map((e,t)=>r.jsx("div",{className:"animate-pulse",children:r.jsx("div",{className:"bg-muted rounded-lg aspect-square"})},t))}),ssr:!1});async function c(){try{return await (0,i.ve)("gallery")}catch(e){return{title:"Photo Gallery",description:"My photo collection"}}}let m="force-dynamic",x=3600;async function u({searchParams:e}){let t="albums"===e.view?"albums":"timeline",a=e.entry,i=e.album,[{albums:n,timelineEntries:c},m]=await Promise.all([(0,l.Eh)(),(0,o.$2)()]);return r.jsx(s.X,{children:r.jsx(d,{albums:n,timelineEntries:c,initialView:t,highlightedEntryId:a,highlightedAlbumId:i,galleryTitle:m.gallery.title,galleryDescription:m.gallery.description})})}},12904:(e,t,a)=>{"use strict";a.d(t,{X:()=>l});var r=a(19510),s=a(91925);function l({title:e,intro:t,children:a}){return(0,r.jsxs)(s.W2,{className:"mt-8 sm:mt-16",children:[(e||t)&&(0,r.jsxs)("header",{className:"max-w-2xl",children:[e&&r.jsx("h1",{className:"text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100",children:e}),t&&r.jsx("p",{className:"mt-6 text-base text-zinc-600 dark:text-zinc-400",children:t})]}),a&&r.jsx("div",{className:`${e||t?"mt-8 sm:mt-12":"mt-0"}`,children:a})]})}},53370:(e,t,a)=>{"use strict";function r(e){return e&&e.__esModule?e:{default:e}}a.r(t),a.d(t,{_:()=>r,_interop_require_default:()=>r})}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[948,499,712,466,722,167],()=>a(69169));module.exports=r})();