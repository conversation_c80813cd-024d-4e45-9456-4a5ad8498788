(()=>{var e={};e.id=404,e.ids=[404],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},26322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u}),r(31969),r(48109),r(27683);var a=r(23191),i=r(88716),n=r(37922),s=r.n(n),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let u=["",{children:["gallery",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31969)),"/home/<USER>/Code/me/My-web/frontend/src/app/gallery/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/gallery/[slug]/page.tsx"],d="/gallery/[slug]/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/gallery/[slug]/page",pathname:"/gallery/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},85458:(e,t,r)=>{Promise.resolve().then(r.bind(r,91728))},86333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},924:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},49163:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])},91728:(e,t,r)=>{"use strict";r.d(t,{GalleryCategoryView:()=>y});var a=r(10326),i=r(17577),n=r(46226),s=r(90434),l=r(35047),o=r(86333),u=r(924);let c=(0,r(62881).Z)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var d=r(12714),m=r(67427),p=r(49163),g=r(27524),h=r(75970),f=r(56006),x=r(39414);function y({category:e,items:t,total:r}){(0,l.useRouter)();let[y,b]=(0,i.useState)("grid"),[v,_]=(0,i.useState)({isOpen:!1,currentIndex:0,images:[]}),[w,j]=(0,i.useState)({}),[S,N]=(0,i.useState)(new Set);(0,h.Lv)(.3),(0,h.wb)(8);let k=t.map(e=>{let t=e.image,r="http://**************:8000/api".replace("/api",""),a=e=>e.startsWith("http://")||e.startsWith("https://")?e:`${r}${e}`;return{id:e.id.toString(),url:a(t.url),thumbnail_url:a(t.thumbnail_url?t.thumbnail_url:t.url),alt:e.title||t.display_name||t.original_filename||"Gallery Image",width:t.width||1200,height:t.height||800,date:new Date().toISOString().split("T")[0],location:void 0,tags:[],caption:e.description||void 0}}),C=(0,i.useCallback)(e=>{_({isOpen:!0,currentIndex:e,images:k})},[k]);(0,i.useCallback)(()=>{_(e=>({...e,isOpen:!1}))},[]),(0,i.useCallback)(()=>{_(e=>({...e,currentIndex:e.currentIndex>0?e.currentIndex-1:e.images.length-1}))},[]),(0,i.useCallback)(()=>{_(e=>({...e,currentIndex:e.currentIndex<e.images.length-1?e.currentIndex+1:0}))},[]);let R=()=>"masonry"===e.layout_type?"columns-2 sm:columns-3 lg:columns-4":`grid-cols-2 sm:grid-cols-${Math.min(e.columns,4)} lg:grid-cols-${e.columns}`,M=()=>{switch(e.image_ratio){case"1:1":return"aspect-square";case"4:3":return"aspect-[4/3]";case"16:9":return"aspect-video";default:return"aspect-auto"}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[a.jsx("div",{className:"flex items-center gap-4",children:(0,a.jsxs)(s.default,{href:"/gallery",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors",children:[a.jsx(o.Z,{className:"w-4 h-4 mr-2"}),"Back to Gallery"]})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[r," ",1===r?"item":"items"]}),(0,a.jsxs)("div",{className:"inline-flex h-9 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",children:[(0,a.jsxs)("button",{onClick:()=>b("grid"),className:(0,g.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all","grid"===y?"bg-background text-foreground shadow-sm":"hover:bg-background/50 hover:text-foreground"),children:[a.jsx(u.Z,{className:"w-4 h-4 mr-2"}),"Grid"]}),(0,a.jsxs)("button",{onClick:()=>b("masonry"),className:(0,g.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all","masonry"===y?"bg-background text-foreground shadow-sm":"hover:bg-background/50 hover:text-foreground"),children:[a.jsx(c,{className:"w-4 h-4 mr-2"}),"Masonry"]})]})]})]}),t.length>0?a.jsx("div",{className:(0,g.cn)("masonry"===y?`${R()} gap-4 space-y-4`:`grid ${R()} gap-4`),children:t.map((e,t)=>{let r=e.image,i="http://**************:8000",s=r?.url?r.url.startsWith("http")?r.url:`${i}${r.url}`:`${i}/uploads/images/placeholder.jpg`,l=r?.thumbnail_url?r.thumbnail_url.startsWith("http")?r.thumbnail_url:`${i}${r.thumbnail_url}`:s;return(0,a.jsxs)("div",{className:(0,g.cn)("group relative cursor-pointer overflow-hidden rounded-lg bg-muted","grid"===y&&M(),"masonry"===y&&"break-inside-avoid mb-4"),onClick:()=>C(t),children:[a.jsx(n.default,{src:l,alt:e.title||r.display_name||r.original_filename,width:r.width||400,height:r.height||300,className:(0,g.cn)("transition-transform duration-500 group-hover:scale-105","grid"===y?"w-full h-full object-cover":"w-full h-auto"),unoptimized:!0}),a.jsx("div",{className:"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-white text-center",children:[a.jsx(d.Z,{className:"w-8 h-8 mx-auto mb-2"}),a.jsx("p",{className:"text-sm font-medium",children:"View Image"})]})}),(e.title||e.description)&&(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity",children:[e.title&&a.jsx("h3",{className:"font-medium text-sm mb-1 line-clamp-1",children:e.title}),e.description&&a.jsx("p",{className:"text-xs text-white/80 line-clamp-2",children:e.description})]}),(0,a.jsxs)("div",{className:"absolute top-2 right-2 flex items-center gap-2 text-white text-xs bg-black/50 rounded-full px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(d.Z,{className:"w-3 h-3"}),a.jsx("span",{children:e.view_count})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(m.Z,{className:"w-3 h-3"}),a.jsx("span",{children:e.like_count})]})]})]},e.id)})}):a.jsx("div",{className:"py-12 text-center",children:a.jsx("p",{className:"text-muted-foreground",children:"No images in this category yet"})}),a.jsx("section",{className:"mt-20 mb-12",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"}),a.jsx("div",{className:"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent"}),(0,a.jsxs)("div",{className:"pt-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm",children:[a.jsx("div",{className:"p-2 rounded-xl bg-primary/10 text-primary",children:a.jsx(p.Z,{className:"w-5 h-5"})}),a.jsx("span",{className:"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase",children:"Gallery Discussion"})]}),a.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3",children:"Explore Together"}),a.jsx("p",{className:"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed mb-8",children:"Discover stories behind the visuals. Share your perspective on this collection."}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx(x.CommentStats,{path:`/gallery/${e.slug}`,showIcons:!0})})]}),a.jsx(f.WalineComment,{path:`/gallery/${e.slug}`,title:e.name,className:"max-w-5xl mx-auto"})]})]})})]})}},75970:(e,t,r)=>{"use strict";r.d(t,{$p:()=>l,Lv:()=>i,o1:()=>s,wb:()=>n});var a=r(17577);function i(e=.3){let t=(0,a.useRef)(null),[r,i]=(0,a.useState)(!1);return t}function n(e=10){return(0,a.useRef)(null)}function s(){let[e,t]=(0,a.useState)(new Set),[r,i]=(0,a.useState)(new Set),n=(0,a.useCallback)(a=>new Promise((n,s)=>{if(e.has(a)){n();return}if(r.has(a)){let t=()=>{e.has(a)?n():setTimeout(t,100)};t();return}i(e=>new Set(e).add(a));let l=new Image;l.onload=()=>{t(e=>new Set(e).add(a)),i(e=>{let t=new Set(e);return t.delete(a),t}),n()},l.onerror=()=>{i(e=>{let t=new Set(e);return t.delete(a),t}),s(Error(`Failed to load image: ${a}`))},l.src=a}),[e,r]),s=(0,a.useCallback)(e=>Promise.allSettled(e.map(e=>n(e))),[n]);return{preloadImage:n,preloadImages:s,isImageLoaded:(0,a.useCallback)(t=>e.has(t),[e]),isImageLoading:(0,a.useCallback)(e=>r.has(e),[r]),loadedImages:Array.from(e),loadingImages:Array.from(r)}}function l(e=.1){let t=(0,a.useRef)(null),[r,i]=(0,a.useState)(!1);return{elementRef:t,isVisible:r}}},27524:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(41135),i=r(31009);function n(...e){return(0,i.m6)((0,a.W)(e))}},58585:(e,t,r)=>{"use strict";var a=r(61085);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return a.RedirectType},notFound:function(){return i.notFound},permanentRedirect:function(){return a.permanentRedirect},redirect:function(){return a.redirect}});let a=r(83953),i=r(16399);class n extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new n}delete(){throw new n}set(){throw new n}sort(){throw new n}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return i},notFound:function(){return a}});let r="NEXT_NOT_FOUND";function a(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var a;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return a},getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return g},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return m},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return u}});let i=r(54580),n=r(72934),s=r(8586),l="NEXT_REDIRECT";function o(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let a=Error(l);a.digest=l+";"+t+";"+e+";"+r+";";let n=i.requestAsyncStorage.getStore();return n&&(a.mutableCookies=n.mutableCookies),a}function u(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw o(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw o(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,a,i]=e.digest.split(";",4),n=Number(i);return t===l&&("replace"===r||"push"===r)&&"string"==typeof a&&!isNaN(n)&&n in s.RedirectStatusCode}function m(e){return d(e)?e.digest.split(";",3)[2]:null}function p(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function g(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(a||(a={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31969:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,dynamic:()=>u,generateMetadata:()=>o,revalidate:()=>c});var a=r(19510),i=r(58585),n=r(12904);let s=(0,r(68570).createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/gallery/GalleryCategoryView.tsx#GalleryCategoryView`);var l=r(63979);async function o({params:e}){try{let{category:t}=await (0,l.gn)(e.slug);return{title:`${t.name} - Gallery`,description:t.description||`View ${t.name} gallery`}}catch(e){return{title:"Gallery Category",description:"Gallery category not found"}}}let u="force-dynamic",c=3600;async function d({params:e}){try{let{category:t,items:r,total:o}=await (0,l.gn)(e.slug);return t.id||(0,i.notFound)(),a.jsx(n.X,{title:t.name,intro:t.description||`Browse ${t.name} gallery`,children:a.jsx(s,{category:t,items:r,total:o})})}catch(e){(0,i.notFound)()}}},12904:(e,t,r)=>{"use strict";r.d(t,{X:()=>n});var a=r(19510),i=r(91925);function n({title:e,intro:t,children:r}){return(0,a.jsxs)(i.W2,{className:"mt-8 sm:mt-16",children:[(e||t)&&(0,a.jsxs)("header",{className:"max-w-2xl",children:[e&&a.jsx("h1",{className:"text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100",children:e}),t&&a.jsx("p",{className:"mt-6 text-base text-zinc-600 dark:text-zinc-400",children:t})]}),r&&a.jsx("div",{className:`${e||t?"mt-8 sm:mt-12":"mt-0"}`,children:r})]})}},63979:(e,t,r)=>{"use strict";r.d(t,{Eh:()=>m,RQ:()=>o,gn:()=>s});var a=r(29712),i=r(53722);async function n(){try{return(await a.Z.get(`${i.CT}/gallery/public`)).data}catch(e){return{categories:[],featured_items:[],total_items:0}}}async function s(e){try{return(await a.Z.get(`${i.CT}/gallery/public/category/${e}`)).data}catch(e){return{category:{},items:[],total:0}}}async function l(){try{return(await a.Z.get(`${i.CT}/gallery/public/timeline`)).data}catch(e){return[]}}async function o(e){if(e.startsWith("category-"))try{let t=(await m()).albums.find(t=>t.id===e);if(t)return t;throw Error(`Gallery category album not found: ${e}`)}catch(e){throw e}try{let t=(await a.Z.get(`${i.CT}/images/albums/${e}`)).data;return{id:t.id.toString(),title:t.title,description:t.description,coverImage:t.cover_image?{id:t.cover_image.id.toString(),url:t.cover_image.url,thumbnail_url:t.cover_image.thumbnail_url,alt:t.cover_image.display_name||t.title,width:t.cover_image.width||400,height:t.cover_image.height||300,date:t.created_at||new Date().toISOString(),tags:[]}:{id:"0",url:"",thumbnail_url:"",alt:t.title,width:400,height:300,date:t.created_at||new Date().toISOString(),tags:[]},images:t.images?.map(e=>({id:e.id.toString(),url:e.url,thumbnail_url:e.thumbnail_url,alt:e.display_name||e.original_filename,width:e.width||400,height:e.height||300,placeholderUrl:e.thumbnail_url||e.url,fullsizeUrl:e.url,display_name:e.display_name,original_filename:e.original_filename}))||[],date:t.date,location:t.location,tags:t.images?.flatMap(e=>e.tags?.map(e=>e.name)||[])||[]}}catch(e){throw e}}function u(e){return e.map(e=>{let t=e.image,r=i.CT.replace("/api",""),a=e=>e.startsWith("http://")||e.startsWith("https://")?e:`${r}${e}`;return{id:e.id.toString(),url:a(t.url),thumbnail_url:a(t.thumbnail_url?t.thumbnail_url:t.url),alt:e.title||t.display_name||t.original_filename||"Gallery Image",width:t.width||1200,height:t.height||800,date:new Date().toISOString().split("T")[0],location:void 0,tags:[],caption:e.description||void 0}})}function c(e){let t={};return e.forEach(e=>{let r=e.date;t[r]||(t[r]=[]),t[r].push(e)}),Object.entries(t).map(([e,t])=>{let r=[...new Set(t.map(e=>e.location).filter(e=>!!e))],a=1===r.length?r[0]:void 0,i=[...new Set(t.flatMap(e=>e.tags||[]))];return{id:`timeline-${e}`,content:new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),images:t,date:e,location:a,tags:i}}).sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())}async function d(){try{return(await a.Z.get(`${i.CT}/images/albums`)).data.map(e=>({id:e.id.toString(),title:e.title,description:e.description,coverImage:e.cover_image?{id:e.cover_image.id.toString(),url:e.cover_image.url,thumbnail_url:e.cover_image.thumbnail_url,alt:e.cover_image.display_name||e.title,width:e.cover_image.width||400,height:e.cover_image.height||300,placeholderUrl:e.cover_image.thumbnail_url||e.cover_image.url,fullsizeUrl:e.cover_image.url,display_name:e.cover_image.display_name,original_filename:e.cover_image.original_filename}:{id:"0",url:"",thumbnail_url:"",alt:e.title,width:400,height:300,placeholderUrl:"",fullsizeUrl:"",display_name:"",original_filename:""},images:[],date:e.date,location:e.location,tags:[]}))}catch(e){return[]}}async function m(){let e=await n(),t=await l(),r=[...await d()];for(let t of e.categories)if(t.item_count>0)try{let e=await s(t.slug),a=u(e.items);a.length>0&&r.push({id:`category-${t.id}`,title:t.name,description:t.description,coverImage:a[0],images:a,date:new Date().toISOString().split("T")[0],tags:[t.name],slug:t.slug})}catch(e){}let a=t;return 0===t.length&&e.featured_items.length>0&&(a=c(u(e.featured_items))),0===a.length&&r.length>0&&(a=c(r.flatMap(e=>e.images))),{albums:r,timelineEntries:a,allImages:e.featured_items.length>0?u(e.featured_items):r.flatMap(e=>e.images),galleryData:e}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,499,469,712,466,722,26],()=>r(26322));module.exports=a})();