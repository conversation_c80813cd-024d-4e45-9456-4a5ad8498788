(()=>{var e={};e.id=516,e.ids=[516],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},48271:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(34148),r(48109),r(27683);var a=r(23191),s=r(88716),i=r(37922),n=r.n(i),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d=["",{children:["gallery",{children:["album",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34148)),"/home/<USER>/Code/me/My-web/frontend/src/app/gallery/album/[id]/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/gallery/album/[id]/page.tsx"],u="/gallery/album/[id]/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/gallery/album/[id]/page",pathname:"/gallery/album/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54496:(e,t,r)=>{Promise.resolve().then(r.bind(r,83326))},86333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39183:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},924:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},49163:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])},83326:(e,t,r)=>{"use strict";r.d(t,{AlbumDetailFixed:()=>j});var a=r(10326),s=r(17577),i=r(46226),n=r(35047),l=r(86333),o=r(49163),d=r(924),c=r(37358),u=r(77636),m=r(51896),h=r(12714),x=r(94019);let p=(0,r(62881).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var g=r(39183),b=r(27524),f=r(71754),y=r(56006),v=r(39414);function j({album:e}){var t;let r=(0,n.useRouter)(),[j,w]=(0,s.useState)({isOpen:!1,currentIndex:0,images:[]}),[N,k]=(0,s.useState)("masonry"),[_,R]=(0,s.useState)({}),[C,P]=(0,s.useState)(new Set),S=(0,s.useCallback)((e,t)=>{let r=t.findIndex(t=>t.id===e.id);w({isOpen:!0,currentIndex:r>=0?r:0,images:t})},[]),O=(0,s.useCallback)(()=>{w({isOpen:!1,currentIndex:0,images:[]})},[]),M=(0,s.useCallback)(()=>{w(e=>({...e,currentIndex:(e.currentIndex+1)%e.images.length}))},[]),q=(0,s.useCallback)(()=>{w(e=>({...e,currentIndex:0===e.currentIndex?e.images.length-1:e.currentIndex-1}))},[]),I=(0,s.useCallback)((e,t)=>{if(!e)return;let r=new IntersectionObserver(([e])=>{e.isIntersecting&&P(e=>new Set([...e,t]))},{threshold:.1});return r.observe(e),()=>r.disconnect()},[]);return(0,a.jsxs)("div",{className:"mt-16 sm:mt-32 sm:px-8",children:[a.jsx("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:a.jsx("div",{className:"relative px-4 sm:px-8 lg:px-12",children:(0,a.jsxs)("div",{className:"mx-auto max-w-2xl lg:max-w-5xl",children:[a.jsx("div",{className:"mb-8",children:(0,a.jsxs)("button",{onClick:()=>r.push("/gallery?view=albums"),className:(0,b.cn)("group flex items-center gap-2 px-4 py-2 rounded-xl","bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700","text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700","transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md"),children:[a.jsx(l.Z,{className:"w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1"}),a.jsx("span",{className:"font-medium",children:"Back to Albums"})]})}),(0,a.jsxs)("div",{className:"relative h-64 md:h-80 lg:h-96 overflow-hidden rounded-2xl mb-12",children:[(0,a.jsxs)("div",{className:"absolute inset-0",children:[a.jsx(i.default,{src:e.images[0]?.url||e.coverImage.url,alt:e.title,fill:!0,className:"object-cover",priority:!0}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"})]}),a.jsx("div",{className:"absolute top-4 right-4 z-20",children:(0,a.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-1",children:[a.jsx("button",{onClick:()=>k("masonry"),className:(0,b.cn)("p-2 rounded-lg transition-all duration-300","masonry"===N?"bg-emerald-500 text-white":"text-white/70 hover:text-white hover:bg-white/10"),children:a.jsx(o.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>k("grid"),className:(0,b.cn)("p-2 rounded-lg transition-all duration-300","grid"===N?"bg-emerald-500 text-white":"text-white/70 hover:text-white hover:bg-white/10"),children:a.jsx(d.Z,{className:"w-4 h-4"})})]})}),a.jsx("div",{className:"absolute bottom-0 left-0 right-0 z-10 p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h1",{className:"text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 leading-tight",children:e.title}),e.description&&a.jsx("p",{className:"text-lg text-white/90 mb-4 max-w-2xl mx-auto",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3 text-white/90",children:[e.date&&(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:[a.jsx(c.Z,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:new Date(e.date).toLocaleDateString("zh-CN")})]}),e.location&&(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:[a.jsx(u.Z,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:[a.jsx(m.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{className:"text-sm",children:[e.images.length," Photos"]})]}),a.jsx("div",{className:"bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:a.jsx(v.CommentStats,{path:`/gallery/album/${e.id}`,showIcons:!0})})]})]})})]}),(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h2",{className:"text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2",children:"Photo Collection"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Explore every moment captured in this beautiful album"})]}),a.jsx("div",{className:(0,b.cn)("masonry"===N?"columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-4":`grid gap-4 ${1===(t=e.images.length)?"grid-cols-1":2===t?"grid-cols-1 md:grid-cols-2":t<=4?"grid-cols-2 md:grid-cols-2":"grid-cols-2 md:grid-cols-3 lg:grid-cols-4"}`),children:e.images.map((t,r)=>a.jsx(f.O1,{delay:100*r,children:a.jsx(f.Uf,{children:(0,a.jsxs)("div",{ref:e=>I(e,t.id),className:(0,b.cn)("group relative cursor-pointer overflow-hidden","bg-white dark:bg-gray-800 rounded-2xl","border border-gray-200 dark:border-gray-700","transition-all duration-500 hover:shadow-xl hover:shadow-emerald-500/10","hover:-translate-y-2 hover:scale-[1.02]","masonry"===N?"break-inside-avoid mb-4":1===e.images.length?"aspect-video":"aspect-square"),onClick:()=>S(t,e.images),children:[C.has(t.id)&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(i.default,{src:t.thumbnail_url||t.url,alt:t.alt,fill:"masonry"!==N,width:"masonry"===N?400:void 0,height:"masonry"===N?300:void 0,className:(0,b.cn)("object-cover transition-all duration-700","group-hover:scale-110 group-hover:brightness-110",_[t.id]?"opacity-100":"opacity-0","masonry"===N?"w-full h-auto":""),sizes:"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw",onLoad:()=>R(e=>({...e,[t.id]:!0})),loading:"lazy"}),(0,a.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300",children:[a.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:a.jsx("h3",{className:"text-white font-semibold text-sm mb-1 truncate",children:t.alt})}),a.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300",children:a.jsx("div",{className:"w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center",children:a.jsx(h.Z,{className:"w-4 h-4 text-white"})})})]})]}),!C.has(t.id)&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-2xl flex items-center justify-center",children:a.jsx("div",{className:"w-8 h-8 border-2 border-emerald-300 rounded-full animate-spin border-t-transparent"})})]})})},t.id))})]}),e.tags&&e.tags.length>0&&a.jsx("div",{className:"py-8 border-t border-gray-200 dark:border-gray-700 mt-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Tags"}),a.jsx("div",{className:"flex flex-wrap justify-center gap-2",children:e.tags.map((e,t)=>a.jsx("span",{className:(0,b.cn)("px-3 py-1.5 rounded-full text-sm font-medium","bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300","border border-emerald-200 dark:border-emerald-700","hover:bg-emerald-200 dark:hover:bg-emerald-800/50 transition-colors duration-200"),children:"string"==typeof e?e:e.name||JSON.stringify(e)},"string"==typeof e?e:e.id||t))})]})}),a.jsx("section",{className:"mt-20 mb-12",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"}),a.jsx("div",{className:"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent"}),(0,a.jsxs)("div",{className:"pt-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm",children:[a.jsx("div",{className:"p-2 rounded-xl bg-primary/10 text-primary",children:a.jsx(m.Z,{className:"w-5 h-5"})}),a.jsx("span",{className:"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase",children:"Album Discussion"})]}),a.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3",children:"Share Your Memories"}),a.jsx("p",{className:"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed",children:"What story do these moments tell you? Share your thoughts and connect through shared experiences."})]}),a.jsx(y.WalineComment,{path:`/gallery/album/${e.id}`,title:e.title,className:"max-w-5xl mx-auto"})]})]})})]})})}),j.isOpen&&a.jsx("div",{className:"fixed inset-0 z-50 bg-black/95 backdrop-blur-sm",children:(0,a.jsxs)("div",{className:"w-full h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 text-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:j.images[j.currentIndex]?.alt}),(0,a.jsxs)("span",{className:"text-sm text-white/70",children:[j.currentIndex+1," / ",j.images.length]})]}),a.jsx("button",{onClick:O,className:"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors",children:a.jsx(x.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex-grow flex items-center justify-center relative",children:[j.images.length>1&&a.jsx("button",{onClick:q,className:"absolute left-6 z-10 p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-300 hover:scale-110",children:a.jsx(p,{className:"w-6 h-6"})}),a.jsx("div",{className:"relative max-w-[90vw] max-h-[80vh] flex items-center justify-center",children:a.jsx(i.default,{src:j.images[j.currentIndex]?.url,alt:j.images[j.currentIndex]?.alt||"",width:j.images[j.currentIndex]?.width||800,height:j.images[j.currentIndex]?.height||600,className:"max-w-full max-h-full object-contain rounded-lg select-none",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.5)"},unoptimized:!0,draggable:!1})}),j.images.length>1&&a.jsx("button",{onClick:M,className:"absolute right-6 z-10 p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-300 hover:scale-110",children:a.jsx(g.Z,{className:"w-6 h-6"})})]}),j.images.length>1&&a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"flex justify-center gap-2 overflow-x-auto max-w-full",children:j.images.map((e,t)=>a.jsx("button",{onClick:()=>w(e=>({...e,currentIndex:t})),className:(0,b.cn)("relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0","border-2 transition-all duration-300",t===j.currentIndex?"border-white scale-110":"border-transparent hover:border-white/50"),children:a.jsx(i.default,{src:e.thumbnail_url||e.url,alt:e.alt,fill:!0,className:"object-cover",sizes:"64px"})},e.id))})})]})})]})}},58585:(e,t,r)=>{"use strict";var a=r(61085);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return n},RedirectType:function(){return a.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return a.permanentRedirect},redirect:function(){return a.redirect}});let a=r(83953),s=r(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class n extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return a}});let r="NEXT_NOT_FOUND";function a(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var a;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return a},getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return x},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return m},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return d}});let s=r(54580),i=r(72934),n=r(8586),l="NEXT_REDIRECT";function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let a=Error(l);a.digest=l+";"+t+";"+e+";"+r+";";let i=s.requestAsyncStorage.getStore();return i&&(a.mutableCookies=i.mutableCookies),a}function d(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw o(e,t,(null==r?void 0:r.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw o(e,t,(null==r?void 0:r.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,a,s]=e.digest.split(";",4),i=Number(s);return t===l&&("replace"===r||"push"===r)&&"string"==typeof a&&!isNaN(i)&&i in n.RedirectStatusCode}function m(e){return u(e)?e.digest.split(";",3)[2]:null}function h(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function x(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(a||(a={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,dynamic:()=>o,generateMetadata:()=>l,revalidate:()=>d});var a=r(19510),s=r(58585);let i=(0,r(68570).createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/gallery/AlbumDetailFixed.tsx#AlbumDetailFixed`);var n=r(63979);async function l({params:e}){try{let t=await (0,n.RQ)(e.id);return{title:`${t.title} - Gallery Album`,description:t.description||`View ${t.title} photo album`}}catch(e){return{title:"Album Not Found",description:"The requested album could not be found"}}}let o="force-dynamic",d=3600;async function c({params:e}){try{let t=await (0,n.RQ)(e.id);return t.id||(0,s.notFound)(),a.jsx(i,{album:t})}catch(e){(0,s.notFound)()}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,499,469,712,466,722,26,167],()=>r(48271));module.exports=a})();