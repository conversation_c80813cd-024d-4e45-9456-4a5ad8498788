2:I[12177,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","989","static/chunks/app/project-demo/page-31ae0f46b19bbf2a.js"],"default"]
3:I[4707,[],""]
4:I[36423,[],""]
5:I[93285,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
6:I[46021,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Providers"]
7:I[83258,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"ThemeInitializer"]
8:I[59183,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"RouteProgressBar"]
9:I[62989,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"GlobalLoadingManager"]
a:I[20686,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"SmartPrefetch"]
b:I[3864,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Header"]
c:I[72972,["105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","931","static/chunks/app/page-b49a98deec5a828b.js"],""]
d:I[18908,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"Footer"]
e:I[60827,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"default"]
f:I[42545,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PlausibleAnalytics"]
10:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceMonitor"]
11:I[11816,["446","static/chunks/ui-libs-e96f0533cedd4426.js","105","static/chunks/icon-libs-9a3ac8a6739d257f.js","592","static/chunks/common-92ea84bd7ba12cdf.js","185","static/chunks/app/layout-0fa287aaecc84f29.js"],"PerformanceDebugger"]
0:["7n7atQT5AqmloxjVA4Y6t",[[["",{"children":["project-demo",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["project-demo",{"children":["__PAGE__",{},[["$L1",["$","$L2",null,{}],null],null],null]},[null,["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","project-demo","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/8b6d6f69c7970b5a.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/72074f6a7392446a.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","className":"h-full antialiased","suppressHydrationWarning":true,"children":["$","body",null,{"className":"flex h-full","children":["$","$L5",null,{"children":["$","$L6",null,{"children":[["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","div",null,{"className":"flex w-full","children":[["$","$La",null,{"routes":["/","/about","/projects","/blogs","/gallery"],"priority":"low","delay":1500}],["$","div",null,{"className":"fixed inset-0 flex justify-center sm:px-8","children":["$","div",null,{"className":"flex w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"w-full shadow-xl dark:bg-muted"}]}]}],["$","div",null,{"className":"relative flex w-full flex-col px-4 sm:px-0","children":[["$","$Lb",null,{}],["$","main",null,{"className":"flex-auto","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","div",null,{"className":"sm:px-8 flex h-full items-center pt-16 sm:pt-32","children":["$","div",null,{"className":"mx-auto w-full max-w-7xl lg:px-8","children":["$","div",null,{"className":"relative px-4 sm:px-8 lg:px-12","children":["$","div",null,{"className":"mx-auto max-w-2xl lg:max-w-5xl","children":["$","div",null,{"className":"flex flex-col items-center","children":[["$","p",null,{"className":"text-base font-semibold text-zinc-400 dark:text-zinc-500","children":"404"}],["$","h1",null,{"className":"mt-4 text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl dark:text-zinc-100","children":"Page not found"}],["$","p",null,{"className":"mt-4 text-base text-zinc-600 dark:text-zinc-400","children":"Sorry, we couldn’t find the page you’re looking for."}],["$","$Lc",null,{"className":"inline-flex items-center gap-2 justify-center rounded-md py-2 px-3 text-sm outline-offset-2 transition active:transition-none bg-zinc-50 font-medium text-zinc-900 hover:bg-zinc-100 active:bg-zinc-100 active:text-zinc-900/60 dark:bg-zinc-800/50 dark:text-zinc-300 dark:hover:bg-zinc-800 dark:hover:text-zinc-50 dark:active:bg-zinc-800/50 dark:active:text-zinc-50/70 mt-4","href":"/","children":"Go back home"}]]}]}]}]}]}],"notFoundStyles":[]}]}],["$","$Ld",null,{}]]}]]}],[null,["$","$Le",null,{}],["$","$Lf",null,{}]],["$","$L10",null,{}],["$","$L11",null,{}]]}]}]}]}]],null],null],["$L12",null]]]]
12:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"项目卡片演示 - 红点奖级别设计"}],["$","meta","3",{"name":"description","content":"展示新的项目卡片设计，包含统一高度、绿色主题和高级交互效果"}],["$","meta","4",{"name":"author","content":"Jingyao Chen"}],["$","meta","5",{"name":"keywords","content":"Jingyao Chen,Red Dot Award,Designer,Developer,Portfolio,UI/UX Design,Web Development,Full Stack,Innovation,Creative Design,Technical Blog,Project Gallery"}],["$","meta","6",{"name":"creator","content":"Jingyao Chen"}],["$","meta","7",{"name":"publisher","content":"Jingyao Chen"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"theme-color","content":"#171717"}],["$","meta","10",{"name":"msapplication-TileColor","content":"#171717"}],["$","link","11",{"rel":"canonical","href":"http://**************:3000"}],["$","link","12",{"rel":"alternate","type":"application/rss+xml","href":"http://**************:3000/feed"}],["$","meta","13",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","14",{"property":"og:title","content":"jyaos"}],["$","meta","15",{"property":"og:description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","16",{"property":"og:url","content":"http://**************:3000"}],["$","meta","17",{"property":"og:site_name","content":"Jingyao Chen Portfolio"}],["$","meta","18",{"property":"og:locale","content":"en_US"}],["$","meta","19",{"property":"og:image","content":"http://**************:3000/images/og-jingyao-portfolio.jpg"}],["$","meta","20",{"property":"og:image:width","content":"1200"}],["$","meta","21",{"property":"og:image:height","content":"630"}],["$","meta","22",{"property":"og:image:alt","content":"jyaos"}],["$","meta","23",{"property":"og:type","content":"website"}],["$","meta","24",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","25",{"name":"twitter:site","content":"@JingyaoC"}],["$","meta","26",{"name":"twitter:creator","content":"@JingyaoC"}],["$","meta","27",{"name":"twitter:title","content":"jyaos"}],["$","meta","28",{"name":"twitter:description","content":"Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative design projects, insightful technical blogs, creative gallery works, and professional development journey."}],["$","meta","29",{"name":"twitter:image","content":"http://**************:3000/images/og-jingyao-portfolio.jpg"}],["$","link","30",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"902x902"}]]
1:null
