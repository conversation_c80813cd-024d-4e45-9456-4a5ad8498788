"use strict";(()=>{var e={};e.id=717,e.ids=[717],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},70539:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>d,patchFetch:()=>h,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>u,staticGenerationAsyncStorage:()=>c});var a={};r.r(a),r.d(a,{GET:()=>s});var o=r(49303),i=r(88716),l=r(60670),n=r(87070);async function s(e){try{let e=await fetch("http://100.90.150.110:8000/api/seo/sitemap.xml",{method:"GET",headers:{"Content-Type":"application/xml"},next:{revalidate:3600}});if(!e.ok)throw Error("Failed to fetch sitemap from API");let t=await e.text();return new n.NextResponse(t,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(t){let e=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>http://100.90.150.110:3000</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>http://100.90.150.110:3000/about</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>http://100.90.150.110:3000/projects</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>http://100.90.150.110:3000/blogs</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>http://100.90.150.110:3000/gallery</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
</urlset>`;return new n.NextResponse(e,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}}let p=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"route",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"/home/<USER>/Code/me/My-web/frontend/src/app/sitemap.xml/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:c,serverHooks:u}=p,d="/sitemap.xml/route";function h(){return(0,l.patchFetch)({serverHooks:u,staticGenerationAsyncStorage:c})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,972],()=>r(70539));module.exports=a})();