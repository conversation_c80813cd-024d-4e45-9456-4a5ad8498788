(()=>{var e={};e.id=606,e.ids=[606],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},61906:(e,a,i)=>{"use strict";i.r(a),i.d(a,{GlobalError:()=>s.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>p}),i(30532),i(48109),i(27683);var n=i(23191),t=i(88716),o=i(37922),s=i.n(o),r=i(95231),c={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>r[e]);i.d(a,c);let p=["",{children:["blogs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,30532)),"/home/<USER>/Code/me/My-web/frontend/src/app/blogs/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["/home/<USER>/Code/me/My-web/frontend/src/app/blogs/page.tsx"],u="/blogs/page",d={require:i,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/blogs/page",pathname:"/blogs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},38218:(e,a,i)=>{Promise.resolve().then(i.bind(i,933)),Promise.resolve().then(i.bind(i,46618)),Promise.resolve().then(i.bind(i,89146)),Promise.resolve().then(i.bind(i,75621))},73930:(e,a,i)=>{e.exports={parallel:i(37638),serial:i(70545),serialOrdered:i(57779)}},89774:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},1281:(e,a,i)=>{var n=i(19336);e.exports=function(e){var a=!1;return n(function(){a=!0}),function(i,t){a?e(i,t):n(function(){e(i,t)})}}},19336:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},72534:(e,a,i)=>{var n=i(1281),t=i(89774);e.exports=function(e,a,i,o){var s,r,c=i.keyedList?i.keyedList[i.index]:i.index;i.jobs[c]=(s=e[c],r=function(e,a){c in i.jobs&&(delete i.jobs[c],e?t(i):i.results[c]=a,o(e,i.results))},2==a.length?a(s,n(r)):a(s,c,n(r)))}},66807:e=>{e.exports=function(e,a){var i=!Array.isArray(e),n={index:0,keyedList:i||a?Object.keys(e):null,jobs:{},results:i?{}:[],size:i?Object.keys(e).length:e.length};return a&&n.keyedList.sort(i?a:function(i,n){return a(e[i],e[n])}),n}},19867:(e,a,i)=>{var n=i(89774),t=i(1281);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,n(this),t(e)(null,this.results))}},37638:(e,a,i)=>{var n=i(72534),t=i(66807),o=i(19867);e.exports=function(e,a,i){for(var s=t(e);s.index<(s.keyedList||e).length;)n(e,a,s,function(e,a){if(e){i(e,a);return}if(0===Object.keys(s.jobs).length){i(null,s.results);return}}),s.index++;return o.bind(s,i)}},70545:(e,a,i)=>{var n=i(57779);e.exports=function(e,a,i){return n(e,a,null,i)}},57779:(e,a,i)=>{var n=i(72534),t=i(66807),o=i(19867);function s(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,i,s){var r=t(e,i);return n(e,a,r,function i(t,o){if(t){s(t,o);return}if(r.index++,r.index<(r.keyedList||e).length){n(e,a,r,i);return}s(null,r.results)}),o.bind(r,s)},e.exports.ascending=s,e.exports.descending=function(e,a){return -1*s(e,a)}},44432:(e,a,i)=>{"use strict";var n=i(49759),t=i(66802),o=i(77336),s=i(61596);e.exports=s||n.call(o,t)},66802:e=>{"use strict";e.exports=Function.prototype.apply},77336:e=>{"use strict";e.exports=Function.prototype.call},36892:(e,a,i)=>{"use strict";var n=i(49759),t=i(12823),o=i(77336),s=i(44432);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new t("a function is required");return s(n,o,e)}},61596:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},38726:(e,a,i)=>{var n=i(21764),t=i(76162).Stream,o=i(4130);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,n.inherits(s,t),s.create=function(e){var a=new this;for(var i in e=e||{})a[i]=e[i];return a},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof o)){var a=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,a){return t.prototype.pipe.call(this,e,a),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},39662:(e,a,i)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let i="color: "+this.color;a.splice(1,0,i,"color: inherit");let n=0,t=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(t=n))}),a.splice(t,0,i)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")||a.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=i(47507)(a);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},47507:(e,a,i)=>{e.exports=function(e){function a(e){let i,t,o;let s=null;function r(...e){if(!r.enabled)return;let n=Number(new Date),t=n-(i||n);r.diff=t,r.prev=i,r.curr=n,i=n,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(i,n)=>{if("%%"===i)return"%";o++;let t=a.formatters[n];if("function"==typeof t){let a=e[o];i=t.call(r,a),e.splice(o,1),o--}return i}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=n,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(t!==a.namespaces&&(t=a.namespaces,o=a.enabled(e)),o),set:e=>{s=e}}),"function"==typeof a.init&&a.init(r),r}function n(e,i){let n=a(this.namespace+(void 0===i?":":i)+e);return n.log=this.log,n}function t(e,a){let i=0,n=0,t=-1,o=0;for(;i<e.length;)if(n<a.length&&(a[n]===e[i]||"*"===a[n]))"*"===a[n]?(t=n,o=i):i++,n++;else{if(-1===t)return!1;n=t+1,i=++o}for(;n<a.length&&"*"===a[n];)n++;return n===a.length}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names,...a.skips.map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){for(let i of(a.save(e),a.namespaces=e,a.names=[],a.skips=[],("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===i[0]?a.skips.push(i.slice(1)):a.names.push(i)},a.enabled=function(e){for(let i of a.skips)if(t(e,i))return!1;for(let i of a.names)if(t(e,i))return!0;return!1},a.humanize=i(87914),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(i=>{a[i]=e[i]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let i=0;for(let a=0;a<e.length;a++)i=(i<<5)-i+e.charCodeAt(a)|0;return a.colors[Math.abs(i)%a.colors.length]},a.enable(a.load()),a}},8589:(e,a,i)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=i(39662):e.exports=i(56027)},56027:(e,a,i)=>{let n=i(74175),t=i(21764);a.init=function(e){e.inspectOpts={};let i=Object.keys(a.inspectOpts);for(let n=0;n<i.length;n++)e.inspectOpts[i[n]]=a.inspectOpts[i[n]]},a.log=function(...e){return process.stderr.write(t.formatWithOptions(a.inspectOpts,...e)+"\n")},a.formatArgs=function(i){let{namespace:n,useColors:t}=this;if(t){let a=this.color,t="\x1b[3"+(a<8?a:"8;5;"+a),o=`  ${t};1m${n} \u001B[0m`;i[0]=o+i[0].split("\n").join("\n"+o),i.push(t+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else i[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+i[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:n.isatty(process.stderr.fd)},a.destroy=t.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=i(67495);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let i=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),n=process.env[a];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[i]=n,e},{}),e.exports=i(47507)(a);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,t.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,t.inspect(e,this.inspectOpts)}},4130:(e,a,i)=>{var n=i(76162).Stream,t=i(21764);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,t.inherits(o,n),o.create=function(e,a){var i=new this;for(var n in a=a||{})i[n]=a[n];i.source=e;var t=e.emit;return e.emit=function(){return i._handleEmit(arguments),t.apply(e,arguments)},e.on("error",function(){}),i.pauseStream&&e.pause(),i},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},37186:(e,a,i)=>{"use strict";var n,t=i(36892),o=i(22924);try{n=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var s=!!n&&o&&o(Object.prototype,"__proto__"),r=Object,c=r.getPrototypeOf;e.exports=s&&"function"==typeof s.get?t([s.get]):"function"==typeof c&&function(e){return c(null==e?e:r(e))}},5702:e=>{"use strict";var a=Object.defineProperty||!1;if(a)try{a({},"a",{value:1})}catch(e){a=!1}e.exports=a},71073:e=>{"use strict";e.exports=EvalError},88170:e=>{"use strict";e.exports=Error},45553:e=>{"use strict";e.exports=RangeError},66861:e=>{"use strict";e.exports=ReferenceError},37450:e=>{"use strict";e.exports=SyntaxError},12823:e=>{"use strict";e.exports=TypeError},64606:e=>{"use strict";e.exports=URIError},87713:e=>{"use strict";e.exports=Object},60406:(e,a,i)=>{"use strict";var n=i(88197)("%Object.defineProperty%",!0),t=i(85247)(),o=i(7238),s=i(12823),r=t?Symbol.toStringTag:null;e.exports=function(e,a){var i=arguments.length>2&&!!arguments[2]&&arguments[2].force,t=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==i&&"boolean"!=typeof i||void 0!==t&&"boolean"!=typeof t)throw new s("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");r&&(i||!o(e,r))&&(n?n(e,r,{configurable:!t,enumerable:!1,value:a,writable:!1}):e[r]=a)}},59057:(e,a,i)=>{var n;e.exports=function(){if(!n){try{n=i(8589)("follow-redirects")}catch(e){}"function"!=typeof n&&(n=function(){})}n.apply(null,arguments)}},19831:(e,a,i)=>{var n=i(17360),t=n.URL,o=i(32615),s=i(35240),r=i(76162).Writable,c=i(27790),p=i(59057);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,i=O(Error.captureStackTrace);e||!a&&i||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new t(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,i,n){this._redirectable.emit(e,a,i,n)}});var x=C("ERR_INVALID_URL","Invalid URL",TypeError),f=C("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),h=C("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",f),v=C("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=C("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||j;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var i=this;this._onNativeResponse=function(e){try{i._processResponse(e)}catch(e){i.emit("error",e instanceof f?e:new f({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},i={};return Object.keys(e).forEach(function(n){var o=n+":",s=i[o]=e[n],r=a[n]=Object.create(s);Object.defineProperties(r,{request:{value:function(e,n,s){var r;return(r=e,t&&r instanceof t)?e=E(e):R(e)?e=E(k(e)):(s=n,n=_(e),e={protocol:o}),O(n)&&(s=n,n=null),(n=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,n)).nativeProtocols=i,R(n.host)||R(n.hostname)||(n.hostname="::1"),c.equal(n.protocol,o,"protocol mismatch"),p("options",n),new y(n,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,i){var n=r.request(e,a,i);return n.end(),n},configurable:!0,enumerable:!0,writable:!0}})}),a}function j(){}function k(e){var a;if(l)a=new t(e);else if(!R((a=_(n.parse(e))).protocol))throw new x({input:e});return a}function _(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new x({input:e.href||e});return e}function E(e,a){var i=a||{};for(var n of u)i[n]=e[n];return i.hostname.startsWith("[")&&(i.hostname=i.hostname.slice(1,-1)),""!==i.port&&(i.port=Number(i.port)),i.path=i.search?i.pathname+i.search:i.pathname,i}function S(e,a){var i;for(var n in a)e.test(n)&&(i=a[n],delete a[n]);return null==i?void 0:String(i).trim()}function C(e,a,i){function n(i){O(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,i||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return n.prototype=new(i||Error),Object.defineProperties(n.prototype,{constructor:{value:n,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),n}function N(e,a){for(var i of d)e.removeListener(i,m[i]);e.on("error",j),e.destroy(a)}function R(e){return"string"==typeof e||e instanceof String}function O(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){N(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return N(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,i){if(this._ending)throw new b;if(!R(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(O(a)&&(i=a,a=null),0===e.length){i&&i();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,i)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,i){if(O(e)?(i=e,e=a=null):O(a)&&(i=a,a=null),e){var n=this,t=this._currentRequest;this.write(e,a,function(){n._ended=!0,t.end(null,null,i)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,i)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var i=this;function n(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function t(a){i._timeout&&clearTimeout(i._timeout),i._timeout=setTimeout(function(){i.emit("timeout"),o()},e),n(a)}function o(){i._timeout&&(clearTimeout(i._timeout),i._timeout=null),i.removeListener("abort",o),i.removeListener("error",o),i.removeListener("response",o),i.removeListener("close",o),a&&i.removeListener("timeout",a),i.socket||i._currentRequest.removeListener("socket",t)}return a&&this.on("timeout",a),this.socket?t(this.socket):this._currentRequest.once("socket",t),this.on("socket",n),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,i){return this._currentRequest[e](a,i)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var i=e.slice(0,-1);this._options.agent=this._options.agents[i]}var t=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var o of(t._redirectable=this,d))t.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?n.format(this._options):this._options.path,this._isRedirect){var s=0,r=this,c=this._requestBodyBuffers;!function e(a){if(t===r._currentRequest){if(a)r.emit("error",a);else if(s<c.length){var i=c[s++];t.finished||t.write(i.data,i.encoding,e)}else r._ended&&t.end()}}()}},y.prototype._processResponse=function(e){var a,i,o,s=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:s});var r=e.headers.location;if(!r||!1===this._options.followRedirects||s<300||s>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(N(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new h;var u=this._options.beforeRedirect;u&&(o=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==s&&302!==s||"POST"!==this._options.method)&&(303!==s||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],S(/^content-/i,this._options.headers));var m=S(/^host$/i,this._options.headers),x=k(this._currentUrl),f=m||x.host,v=/^\w+:/.test(r)?this._currentUrl:n.format(Object.assign(x,{host:f})),b=l?new t(r,v):k(n.resolve(v,r));if(p("redirecting to",b.href),this._isRedirect=!0,E(b,this._options),(b.protocol===x.protocol||"https:"===b.protocol)&&(b.host===f||(c(R(a=b.host)&&R(f)),(i=a.length-f.length-1)>0&&"."===a[i]&&a.endsWith(f)))||S(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),O(u)){var g={headers:e.headers,statusCode:s},y={url:v,method:d,headers:o};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:s}),e.exports.wrap=w},54127:(e,a,i)=>{"use strict";var n=i(38726),t=i(21764),o=i(55315),s=i(32615),r=i(35240),c=i(17360).parse,p=i(92048),l=i(76162).Stream,u=i(70045),d=i(73930),m=i(60406),x=i(7238),f=i(6578);function h(e){if(!(this instanceof h))return new h(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[a]=e[a]}t.inherits(h,n),h.LINE_BREAK="\r\n",h.DEFAULT_CONTENT_TYPE="application/octet-stream",h.prototype.append=function(e,a,i){"string"==typeof(i=i||{})&&(i={filename:i});var t=n.prototype.append.bind(this);if(("number"==typeof a||null==a)&&(a=String(a)),Array.isArray(a)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,a,i),s=this._multiPartFooter();t(o),t(a),t(s),this._trackLength(o,a,i)},h.prototype._trackLength=function(e,a,i){var n=0;null!=i.knownLength?n+=Number(i.knownLength):Buffer.isBuffer(a)?n=a.length:"string"==typeof a&&(n=Buffer.byteLength(a)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+h.LINE_BREAK.length,a&&(a.path||a.readable&&x(a,"httpVersion")||a instanceof l)&&(i.knownLength||this._valuesToMeasure.push(a))},h.prototype._lengthRetriever=function(e,a){x(e,"fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(i,n){if(i){a(i);return}a(null,n.size-(e.start?e.start:0))}):x(e,"httpVersion")?a(null,Number(e.headers["content-length"])):x(e,"httpModule")?(e.on("response",function(i){e.pause(),a(null,Number(i.headers["content-length"]))}),e.resume()):a("Unknown stream")},h.prototype._multiPartHeader=function(e,a,i){if("string"==typeof i.header)return i.header;var n,t=this._getContentDisposition(a,i),o=this._getContentType(a,i),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(t||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof i.header&&f(r,i.header),r)if(x(r,c)){if(null==(n=r[c]))continue;Array.isArray(n)||(n=[n]),n.length&&(s+=c+": "+n.join("; ")+h.LINE_BREAK)}return"--"+this.getBoundary()+h.LINE_BREAK+s+h.LINE_BREAK},h.prototype._getContentDisposition=function(e,a){var i;if("string"==typeof a.filepath?i=o.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e&&(e.name||e.path)?i=o.basename(a.filename||e&&(e.name||e.path)):e&&e.readable&&x(e,"httpVersion")&&(i=o.basename(e.client._httpMessage.path||"")),i)return'filename="'+i+'"'},h.prototype._getContentType=function(e,a){var i=a.contentType;return!i&&e&&e.name&&(i=u.lookup(e.name)),!i&&e&&e.path&&(i=u.lookup(e.path)),!i&&e&&e.readable&&x(e,"httpVersion")&&(i=e.headers["content-type"]),!i&&(a.filepath||a.filename)&&(i=u.lookup(a.filepath||a.filename)),!i&&e&&"object"==typeof e&&(i=h.DEFAULT_CONTENT_TYPE),i},h.prototype._multiPartFooter=function(){return(function(e){var a=h.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},h.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+h.LINE_BREAK},h.prototype.getHeaders=function(e){var a,i={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)x(e,a)&&(i[a.toLowerCase()]=e[a]);return i},h.prototype.setBoundary=function(e){if("string"!=typeof e)throw TypeError("FormData boundary must be a string");this._boundary=e},h.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},h.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),i=0,n=this._streams.length;i<n;i++)"function"!=typeof this._streams[i]&&(Buffer.isBuffer(this._streams[i])?e=Buffer.concat([e,this._streams[i]]):e=Buffer.concat([e,Buffer.from(this._streams[i])]),("string"!=typeof this._streams[i]||this._streams[i].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(h.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},h.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},h.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},h.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},h.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(i,n){if(i){e(i);return}n.forEach(function(e){a+=e}),e(null,a)})},h.prototype.submit=function(e,a){var i,n,t={method:"post"};return"string"==typeof e?n=f({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},t):(n=f(e,t)).port||(n.port="https:"===n.protocol?443:80),n.headers=this.getHeaders(e.headers),i="https:"===n.protocol?r.request(n):s.request(n),this.getLength((function(e,n){if(e&&"Unknown stream"!==e){this._error(e);return}if(n&&i.setHeader("Content-Length",n),this.pipe(i),a){var t,o=function(e,n){return i.removeListener("error",o),i.removeListener("response",t),a.call(this,e,n)};t=o.bind(this,null),i.on("error",o),i.on("response",t)}}).bind(this)),i},h.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},h.prototype.toString=function(){return"[object FormData]"},m(h,"FormData"),e.exports=h},6578:e=>{"use strict";e.exports=function(e,a){return Object.keys(a).forEach(function(i){e[i]=e[i]||a[i]}),e}},14930:e=>{"use strict";var a=Object.prototype.toString,i=Math.max,n=function(e,a){for(var i=[],n=0;n<e.length;n+=1)i[n]=e[n];for(var t=0;t<a.length;t+=1)i[t+e.length]=a[t];return i},t=function(e,a){for(var i=[],n=a||0,t=0;n<e.length;n+=1,t+=1)i[t]=e[n];return i},o=function(e,a){for(var i="",n=0;n<e.length;n+=1)i+=e[n],n+1<e.length&&(i+=a);return i};e.exports=function(e){var s,r=this;if("function"!=typeof r||"[object Function]"!==a.apply(r))throw TypeError("Function.prototype.bind called on incompatible "+r);for(var c=t(arguments,1),p=i(0,r.length-c.length),l=[],u=0;u<p;u++)l[u]="$"+u;if(s=Function("binder","return function ("+o(l,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof s){var a=r.apply(this,n(c,arguments));return Object(a)===a?a:this}return r.apply(e,n(c,arguments))}),r.prototype){var d=function(){};d.prototype=r.prototype,s.prototype=new d,d.prototype=null}return s}},49759:(e,a,i)=>{"use strict";var n=i(14930);e.exports=Function.prototype.bind||n},88197:(e,a,i)=>{"use strict";var n,t=i(87713),o=i(88170),s=i(71073),r=i(45553),c=i(66861),p=i(37450),l=i(12823),u=i(64606),d=i(55135),m=i(13084),x=i(63626),f=i(61794),h=i(41887),v=i(99953),b=i(99944),g=Function,y=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},w=i(22924),j=i(5702),k=function(){throw new l},_=w?function(){try{return arguments.callee,k}catch(e){try{return w(arguments,"callee").get}catch(e){return k}}}():k,E=i(86514)(),S=i(58644),C=i(32230),N=i(21475),R=i(66802),O=i(77336),A={},T="undefined"!=typeof Uint8Array&&S?S(Uint8Array):n,F={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":E&&S?S([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":A,"%AsyncGenerator%":A,"%AsyncGeneratorFunction%":A,"%AsyncIteratorPrototype%":A,"%Atomics%":"undefined"==typeof Atomics?n:Atomics,"%BigInt%":"undefined"==typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":s,"%Float16Array%":"undefined"==typeof Float16Array?n:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":A,"%Int8Array%":"undefined"==typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":E&&S?S(S([][Symbol.iterator]())):n,"%JSON%":"object"==typeof JSON?JSON:n,"%Map%":"undefined"==typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&E&&S?S(new Map()[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":t,"%Object.getOwnPropertyDescriptor%":w,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?n:Promise,"%Proxy%":"undefined"==typeof Proxy?n:Proxy,"%RangeError%":r,"%ReferenceError%":c,"%Reflect%":"undefined"==typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&E&&S?S(new Set()[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":E&&S?S(""[Symbol.iterator]()):n,"%Symbol%":E?Symbol:n,"%SyntaxError%":p,"%ThrowTypeError%":_,"%TypedArray%":T,"%TypeError%":l,"%Uint8Array%":"undefined"==typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?n:Uint32Array,"%URIError%":u,"%WeakMap%":"undefined"==typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?n:WeakSet,"%Function.prototype.call%":O,"%Function.prototype.apply%":R,"%Object.defineProperty%":j,"%Object.getPrototypeOf%":C,"%Math.abs%":d,"%Math.floor%":m,"%Math.max%":x,"%Math.min%":f,"%Math.pow%":h,"%Math.round%":v,"%Math.sign%":b,"%Reflect.getPrototypeOf%":N};if(S)try{null.error}catch(e){var z=S(S(e));F["%Error.prototype%"]=z}var P=function e(a){var i;if("%AsyncFunction%"===a)i=y("async function () {}");else if("%GeneratorFunction%"===a)i=y("function* () {}");else if("%AsyncGeneratorFunction%"===a)i=y("async function* () {}");else if("%AsyncGenerator%"===a){var n=e("%AsyncGeneratorFunction%");n&&(i=n.prototype)}else if("%AsyncIteratorPrototype%"===a){var t=e("%AsyncGenerator%");t&&S&&(i=S(t.prototype))}return F[a]=i,i},L={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},B=i(49759),U=i(7238),q=B.call(O,Array.prototype.concat),D=B.call(R,Array.prototype.splice),I=B.call(O,String.prototype.replace),M=B.call(O,String.prototype.slice),$=B.call(O,RegExp.prototype.exec),H=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Z=/\\(\\)?/g,G=function(e){var a=M(e,0,1),i=M(e,-1);if("%"===a&&"%"!==i)throw new p("invalid intrinsic syntax, expected closing `%`");if("%"===i&&"%"!==a)throw new p("invalid intrinsic syntax, expected opening `%`");var n=[];return I(e,H,function(e,a,i,t){n[n.length]=i?I(t,Z,"$1"):a||e}),n},W=function(e,a){var i,n=e;if(U(L,n)&&(n="%"+(i=L[n])[0]+"%"),U(F,n)){var t=F[n];if(t===A&&(t=P(n)),void 0===t&&!a)throw new l("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:i,name:n,value:t}}throw new p("intrinsic "+e+" does not exist!")};e.exports=function(e,a){if("string"!=typeof e||0===e.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof a)throw new l('"allowMissing" argument must be a boolean');if(null===$(/^%?[^%]*%?$/,e))throw new p("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var i=G(e),n=i.length>0?i[0]:"",t=W("%"+n+"%",a),o=t.name,s=t.value,r=!1,c=t.alias;c&&(n=c[0],D(i,q([0,1],c)));for(var u=1,d=!0;u<i.length;u+=1){var m=i[u],x=M(m,0,1),f=M(m,-1);if(('"'===x||"'"===x||"`"===x||'"'===f||"'"===f||"`"===f)&&x!==f)throw new p("property names with quotes must have matching quotes");if("constructor"!==m&&d||(r=!0),n+="."+m,U(F,o="%"+n+"%"))s=F[o];else if(null!=s){if(!(m in s)){if(!a)throw new l("base intrinsic for "+e+" exists, but the property is not available.");return}if(w&&u+1>=i.length){var h=w(s,m);s=(d=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:s[m]}else d=U(s,m),s=s[m];d&&!r&&(F[o]=s)}}return s}},32230:(e,a,i)=>{"use strict";var n=i(87713);e.exports=n.getPrototypeOf||null},21475:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},58644:(e,a,i)=>{"use strict";var n=i(21475),t=i(32230),o=i(37186);e.exports=n?function(e){return n(e)}:t?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return t(e)}:o?function(e){return o(e)}:null},53534:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},22924:(e,a,i)=>{"use strict";var n=i(53534);if(n)try{n([],"length")}catch(e){n=null}e.exports=n},53285:e=>{"use strict";e.exports=(e,a=process.argv)=>{let i=e.startsWith("-")?"":1===e.length?"-":"--",n=a.indexOf(i+e),t=a.indexOf("--");return -1!==n&&(-1===t||n<t)}},86514:(e,a,i)=>{"use strict";var n="undefined"!=typeof Symbol&&Symbol,t=i(26799);e.exports=function(){return"function"==typeof n&&"function"==typeof Symbol&&"symbol"==typeof n("foo")&&"symbol"==typeof Symbol("bar")&&t()}},26799:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},a=Symbol("test"),i=Object(a);if("string"==typeof a||"[object Symbol]"!==Object.prototype.toString.call(a)||"[object Symbol]"!==Object.prototype.toString.call(i))return!1;for(var n in e[a]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var t=Object.getOwnPropertySymbols(e);if(1!==t.length||t[0]!==a||!Object.prototype.propertyIsEnumerable.call(e,a))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,a);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},85247:(e,a,i)=>{"use strict";var n=i(26799);e.exports=function(){return n()&&!!Symbol.toStringTag}},7238:(e,a,i)=>{"use strict";var n=Function.prototype.call,t=Object.prototype.hasOwnProperty,o=i(49759);e.exports=o.call(n,t)},37358:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},39183:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},96633:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},41137:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},77506:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},11019:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},83855:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88307:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},33734:(e,a,i)=>{"use strict";i.d(a,{Z:()=>n});let n=(0,i(62881).Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},55135:e=>{"use strict";e.exports=Math.abs},13084:e=>{"use strict";e.exports=Math.floor},1049:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},63626:e=>{"use strict";e.exports=Math.max},61794:e=>{"use strict";e.exports=Math.min},41887:e=>{"use strict";e.exports=Math.pow},99953:e=>{"use strict";e.exports=Math.round},99944:(e,a,i)=>{"use strict";var n=i(1049);e.exports=function(e){return n(e)||0===e?e:e<0?-1:1}},46866:(e,a,i)=>{e.exports=i(40572)},70045:(e,a,i)=>{"use strict";var n=i(46866),t=i(55315).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),i=a&&n[a[1].toLowerCase()];return i&&i.charset?i.charset:!!(a&&s.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var i=-1===e.indexOf("/")?a.lookup(e):e;if(!i)return!1;if(-1===i.indexOf("charset")){var n=a.charset(i);n&&(i+="; charset="+n.toLowerCase())}return i},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var i=o.exec(e),n=i&&a.extensions[i[1].toLowerCase()];return!!n&&!!n.length&&n[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var i=t("x."+e).toLowerCase().substr(1);return!!i&&(a.types[i]||!1)},a.types=Object.create(null),function(e,a){var i=["nginx","apache",void 0,"iana"];Object.keys(n).forEach(function(t){var o=n[t],s=o.extensions;if(s&&s.length){e[t]=s;for(var r=0;r<s.length;r++){var c=s[r];if(a[c]){var p=i.indexOf(n[a[c]].source),l=i.indexOf(o.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=t}}})}(a.extensions,a.types)},87914:e=>{function a(e,a,i,n){return Math.round(e/i)+" "+n+(a>=1.5*i?"s":"")}e.exports=function(e,i){i=i||{};var n,t,o=typeof e;if("string"===o&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var i=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*i;case"weeks":case"week":case"w":return 6048e5*i;case"days":case"day":case"d":return 864e5*i;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*i;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*i;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*i;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}(e);if("number"===o&&isFinite(e))return i.long?(n=Math.abs(e))>=864e5?a(e,n,864e5,"day"):n>=36e5?a(e,n,36e5,"hour"):n>=6e4?a(e,n,6e4,"minute"):n>=1e3?a(e,n,1e3,"second"):e+" ms":(t=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":t>=36e5?Math.round(e/36e5)+"h":t>=6e4?Math.round(e/6e4)+"m":t>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},933:(e,a,i)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"BailoutToCSR",{enumerable:!0,get:function(){return t}});let n=i(94129);function t(e){let{reason:a,children:i}=e;throw new n.BailoutToCSRError(a)}},46618:(e,a,i)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"PreloadCss",{enumerable:!0,get:function(){return o}});let n=i(10326),t=i(54580);function o(e){let{moduleIds:a}=e,i=(0,t.getExpectedRequestStore)("next/dynamic css"),o=[];if(i.reactLoadableManifest&&a){let e=i.reactLoadableManifest;for(let i of a){if(!e[i])continue;let a=e[i].files.filter(e=>e.endsWith(".css"));o.push(...a)}}return 0===o.length?null:(0,n.jsx)(n.Fragment,{children:o.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:i.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},13304:(e,a,i)=>{"use strict";var n=i(17360).parse,t={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,i,r,c="string"==typeof e?n(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p||(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),i=u=parseInt(u)||t[p]||0,!(!(r=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var n=e.match(/^(.+):(\d+)$/),t=n?n[1]:e,s=n?parseInt(n[2]):0;return!!s&&s!==i||(/^[.*]/.test(t)?("*"===t.charAt(0)&&(t=t.slice(1)),!o.call(a,t)):a!==t)}))))return"";var d=s("npm_config_"+p+"_proxy")||s(p+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},67495:(e,a,i)=>{"use strict";let n;let t=i(19801),o=i(74175),s=i(53285),{env:r}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,a){if(0===n)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!a&&void 0===n)return 0;let i=n||0;if("dumb"===r.TERM)return i;if("win32"===process.platform){let e=t.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:i;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:i}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?n=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(n=1),"FORCE_COLOR"in r&&(n="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,o.isatty(1))),stderr:c(p(!0,o.isatty(2)))}},89146:(e,a,i)=>{"use strict";let n,t,o,s,r,c,p;i.r(a),i.d(a,{EnhancedBlogList:()=>iv});var l,u,d,m={};i.r(m),i.d(m,{hasBrowserEnv:()=>eZ,hasStandardBrowserEnv:()=>eW,hasStandardBrowserWebWorkerEnv:()=>eV,navigator:()=>eG,origin:()=>eJ});var x=i(10326),f=i(17577),h=i(88307),v=i(41137),b=i(94019),g=i(33734),y=i(23201);function w({blogs:e,tags:a,onFilterChange:i,className:n=""}){let[t,o]=(0,f.useState)(!1),[s,r]=(0,f.useState)({search:"",selectedTags:[],selectedYears:[],sortBy:"date",sortOrder:"desc",showOnlyFeatured:!1}),c=(0,f.useMemo)(()=>{let a=new Set;return e.forEach(e=>{let i=new Date(e.display_date).getFullYear().toString();a.add(i)}),Array.from(a).sort((e,a)=>parseInt(a)-parseInt(e))},[e]),p=(0,f.useMemo)(()=>{let a=e.filter(e=>{if(s.search){let a=s.search.toLowerCase();if(!(e.title.toLowerCase().includes(a)||e.description?.toLowerCase().includes(a)||e.content?.toLowerCase().includes(a)||e.tags?.some(e=>e.name.toLowerCase().includes(a))))return!1}if(s.selectedTags.length>0){let a=e.tags?.map(e=>e.name)||[];if(!s.selectedTags.some(e=>a.includes(e)))return!1}if(s.selectedYears.length>0){let a=new Date(e.display_date).getFullYear().toString();if(!s.selectedYears.includes(a))return!1}return!s.showOnlyFeatured||!!e.is_featured});return a.sort((e,a)=>{let i=0;switch(s.sortBy){case"title":i=e.title.localeCompare(a.title);break;case"date":i=new Date(e.display_date).getTime()-new Date(a.display_date).getTime();break;case"views":i=(e.views||0)-(a.views||0);break;case"tag":let n=e.tags?.[0]?.name||"",t=a.tags?.[0]?.name||"";i=n.localeCompare(t)}return"desc"===s.sortOrder?-i:i}),a},[e,s]),l=(e,a)=>{r(i=>({...i,[e]:a}))},u=e=>{r(a=>({...a,selectedTags:a.selectedTags.includes(e)?a.selectedTags.filter(a=>a!==e):[...a.selectedTags,e]}))},d=e=>{r(a=>({...a,selectedYears:a.selectedYears.includes(e)?a.selectedYears.filter(a=>a!==e):[...a.selectedYears,e]}))},m=s.search||s.selectedTags.length>0||s.selectedYears.length>0||s.showOnlyFeatured;return(0,x.jsxs)("div",{className:`relative ${n}`,children:[x.jsx("div",{className:"relative mb-6 z-[1000]",children:(0,x.jsxs)("div",{className:"relative",children:[x.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",size:20}),x.jsx("input",{type:"text",placeholder:"Search articles...",value:s.search,onChange:e=>l("search",e.target.value),className:"w-full pl-10 pr-12 py-3 bg-background/60 border border-border/50 rounded-xl backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-300"}),x.jsx("button",{onClick:()=>o(!t),className:`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg transition-all duration-300 ${t?"bg-primary/10 text-primary":"hover:bg-muted"}`,children:x.jsx(v.Z,{size:16})})]})}),t&&x.jsx("div",{className:"absolute top-full left-0 right-0 z-[99999] mt-2 p-6 bg-background backdrop-blur-lg border border-border/50 rounded-2xl shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25)] animate-fade-in-up",children:(0,x.jsxs)("div",{className:"space-y-6",children:[(0,x.jsxs)("div",{className:"flex items-center justify-between",children:[x.jsx("h3",{className:"text-lg font-semibold",children:"Filter Articles"}),(0,x.jsxs)("div",{className:"flex items-center gap-2",children:[m&&x.jsx("button",{onClick:()=>{r({search:"",selectedTags:[],selectedYears:[],sortBy:"date",sortOrder:"desc",showOnlyFeatured:!1})},className:"text-sm text-muted-foreground hover:text-foreground transition-colors duration-200",children:"Clear all"}),x.jsx("button",{onClick:()=>o(!1),className:"p-1 rounded-lg hover:bg-muted transition-colors duration-200",children:x.jsx(b.Z,{size:16})})]})]}),(0,x.jsxs)("div",{children:[x.jsx("label",{className:"block text-sm font-medium mb-3",children:"Sort by"}),(0,x.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,x.jsxs)("select",{value:s.sortBy,onChange:e=>l("sortBy",e.target.value),className:"px-3 py-2 bg-background border border-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/50",children:[x.jsx("option",{value:"date",children:"Date"}),x.jsx("option",{value:"title",children:"Title"}),x.jsx("option",{value:"views",children:"Views"}),x.jsx("option",{value:"tag",children:"Tag"})]}),(0,x.jsxs)("select",{value:s.sortOrder,onChange:e=>l("sortOrder",e.target.value),className:"px-3 py-2 bg-background border border-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/50",children:[x.jsx("option",{value:"desc",children:"Descending"}),x.jsx("option",{value:"asc",children:"Ascending"})]})]})]}),(0,x.jsxs)("div",{children:[x.jsx("label",{className:"block text-sm font-medium mb-3",children:"Tags"}),x.jsx("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:a.map(e=>{let a=s.selectedTags.includes(e.name),i=y.K$.getTagStyle(e.color,"minimal"),n=y.K$.getTagClasses(e.color,"minimal");return x.jsx("button",{onClick:()=>u(e.name),className:`${n} ${a?"ring-2 ring-primary/50 scale-105 shadow-md":"hover:scale-105 hover:shadow-sm"} transition-all duration-200`,style:{...i,...a&&{backgroundColor:e.color||"hsl(var(--primary))",color:"white",borderColor:e.color||"hsl(var(--primary))"}},children:e.name},e.id)})})]}),(0,x.jsxs)("div",{children:[x.jsx("label",{className:"block text-sm font-medium mb-3",children:"Years"}),x.jsx("div",{className:"flex flex-wrap gap-2",children:c.map(e=>x.jsx("button",{onClick:()=>d(e),className:`px-3 py-1 rounded-full text-sm transition-all duration-200 ${s.selectedYears.includes(e)?"bg-primary text-primary-foreground":"bg-muted hover:bg-muted/80"}`,children:e},e))})]}),x.jsx("div",{children:(0,x.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[x.jsx("input",{type:"checkbox",checked:s.showOnlyFeatured,onChange:e=>l("showOnlyFeatured",e.target.checked),className:"w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary/50"}),(0,x.jsxs)("span",{className:"text-sm font-medium flex items-center gap-2",children:[x.jsx(g.Z,{size:16,className:"text-primary"}),"Show only featured articles"]})]})}),x.jsx("div",{className:"pt-4 border-t border-border/50",children:(0,x.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",p.length," of ",e.length," articles"]})})]})})]})}var j=i(90434),k=i(41135),_=i(39183);function E({as:e,className:a,children:i}){return x.jsx(e??"div",{className:(0,k.Z)(a,"group relative flex flex-col items-start"),children:i})}E.Link=function({children:e,...a}){return(0,x.jsxs)(x.Fragment,{children:[x.jsx("div",{className:"absolute -inset-x-4 -inset-y-6 z-0 scale-95 transition group-hover:scale-100 sm:-inset-x-6 sm:rounded-2xl group-hover:bg-muted/50 "}),(0,x.jsxs)(j.default,{...a,children:[x.jsx("span",{className:"absolute -inset-x-4 -inset-y-6 z-20 sm:-inset-x-6 sm:rounded-2xl"}),x.jsx("span",{className:"relative z-10",children:e})]})]})},E.Title=function({as:e,href:a,children:i}){return x.jsx(e??"h2",{className:"text-base font-semibold tracking-normal",children:a?x.jsx(E.Link,{href:a,children:i}):i})},E.Description=function({children:e}){return x.jsx("p",{className:"relative z-10 mt-2 text-sm text-muted-foreground",children:e})},E.Cta=function({children:e}){return(0,x.jsxs)("div",{"aria-hidden":"true",className:"relative z-10 mt-4 flex items-center text-sm font-medium text-primary",children:[e,x.jsx(_.Z,{className:"ml-1 h-4 w-4 stroke-current"})]})},E.Content=function({className:e,children:a,...i}){return x.jsx("div",{className:(0,k.Z)("relative z-10",e),...i,children:a})},E.Eyebrow=function({as:e,decorate:a=!1,className:i,children:n,...t}){return(0,x.jsxs)(e??"p",{className:(0,k.Z)(i,"relative z-10 order-first mb-3 flex items-center text-sm text-muted-foreground",a&&"pl-3.5"),...t,children:[a&&x.jsx("span",{className:"absolute inset-y-0 left-0 flex items-center","aria-hidden":"true",children:x.jsx("span",{className:"h-4 w-0.5 rounded-full bg-muted-foreground/30"})}),n]})};var S=i(22428),C=i(37358),N=i(24230),R=i(51223),O=i(35047),A=i(77506);function T({href:e,children:a,className:i,showLoadingState:n=!0,debounceMs:t=300,onClick:o}){let[s,r]=(0,f.useState)(!1),[c,p]=(0,f.useState)(!1),l=(0,O.useRouter)(),u=(0,f.useCallback)(a=>{if(s||c){a.preventDefault();return}o&&o(),p(!0),setTimeout(()=>p(!1),t),n&&(a.preventDefault(),r(!0),l.push(e),setTimeout(()=>{r(!1)},5e3))},[s,c,o,t,n,e,l]);return(0,x.jsxs)(j.default,{href:e,className:(0,R.cn)("relative transition-all duration-200",s&&"pointer-events-none opacity-75",i),onClick:u,children:[a,s&&n&&x.jsx("div",{className:"absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center rounded-lg z-10",children:(0,x.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[x.jsx(A.Z,{className:"w-4 h-4 animate-spin"}),x.jsx("span",{children:"Loading..."})]})})]})}function F({href:e,children:a,className:i}){return x.jsx(T,{href:e,className:i,showLoadingState:!0,debounceMs:500,children:a})}let z=({blog:e,index:a,className:i})=>{let n=e=>y.K$.getTagStyle(e,"minimal"),t=e=>y.K$.getTagClasses(e,"minimal"),o=e=>({...n(e),transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",transform:"perspective(100px) translateZ(0)",transformStyle:"preserve-3d"});return x.jsx(F,{href:`/blogs/${e.slug}`,className:"block",children:x.jsx("article",{className:"md:grid md:grid-cols-4 md:items-start group",children:(0,x.jsxs)(E,{className:(0,R.cn)("md:col-span-4 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/15 hover:-translate-y-3 hover:scale-[1.03] hover:border-primary/40 relative overflow-hidden group/card cursor-pointer",i),style:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d"},children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/8 to-primary/0 opacity-0 group-hover/card:opacity-100 transition-all duration-500"}),x.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/8 opacity-0 group-hover/card:opacity-100 transition-all duration-700"}),x.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-white/10 opacity-0 group-hover/card:opacity-100 transition-all duration-500"}),x.jsx("div",{className:"absolute -inset-1 bg-gradient-to-r from-primary/20 via-blue-500/20 to-primary/20 rounded-lg opacity-0 group-hover/card:opacity-100 transition-all duration-500 blur-sm"}),x.jsx("div",{className:"absolute inset-0 rounded-lg border border-white/20 opacity-0 group-hover/card:opacity-100 transition-all duration-300"}),(0,x.jsxs)(E.Content,{className:"relative z-10 p-6 space-y-6",style:{transform:"translateZ(20px)",transformStyle:"preserve-3d"},children:[x.jsx("div",{className:"pb-3 border-b border-border/30 group-hover/card:border-primary/20 transition-colors duration-300",children:(0,x.jsxs)(E.Eyebrow,{as:"time",dateTime:e.display_date,className:"flex items-center gap-2 text-sm font-medium text-muted-foreground group-hover/card:text-primary/80 transition-all duration-300",children:[x.jsx(C.Z,{className:"w-3.5 h-3.5 flex-shrink-0 group-hover/card:scale-110 group-hover/card:text-primary transition-all duration-300"}),(0,x.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:gap-3",children:[(0,x.jsxs)("span",{className:"relative",children:[(0,S.p)(e.display_date),x.jsx("div",{className:"absolute -bottom-0.5 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-primary/50 transition-all duration-500 group-hover/card:w-full"})]}),x.jsx("span",{className:"text-xs text-muted-foreground/80 mt-1 sm:mt-0 group-hover/card:text-primary/60 transition-colors duration-300",children:new Date(e.display_date).toLocaleDateString("en-US",{weekday:"short"})})]})]})}),(0,x.jsxs)("div",{className:"space-y-4",children:[(0,x.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.is_featured&&(0,x.jsxs)("span",{className:"inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 transition-all duration-200 cursor-default",children:[x.jsx(g.Z,{className:"w-3 h-3 fill-current animate-pulse-soft"}),"Featured"]}),e.is_major_change&&x.jsx("span",{className:"inline-flex items-center px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 transition-all duration-200 cursor-default",children:"Major Update"})]}),x.jsx(E.Title,{className:"text-2xl font-bold leading-tight tracking-tight group-hover/card:text-primary transition-all duration-300 hover:scale-[1.02] origin-left bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text hover:from-primary hover:to-primary/80 drop-shadow-sm group-hover/card:drop-shadow-md",style:{transform:"translateZ(10px)",transformStyle:"preserve-3d"},children:e.title})]}),e.tags&&e.tags.length>0&&x.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.map(e=>x.jsx(j.default,{href:`/blogs?tag=${e.slug||e.id}`,children:(0,x.jsxs)("span",{className:(0,R.cn)(t(e.color),"hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden"),style:o(e.color),children:[x.jsx("div",{className:"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm",style:{backgroundColor:e.color||"hsl(var(--primary))"}}),x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500"}),x.jsx("span",{className:"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300",children:e.name}),x.jsx("div",{className:"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200"})]})},e.id))}),x.jsx("p",{className:"relative z-10 mt-2 text-base leading-relaxed text-muted-foreground group-hover:text-foreground transition-colors duration-300 line-clamp-3",children:e.description}),e.category&&x.jsx("div",{className:"inline-flex items-center px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full border border-primary/20",children:e.category}),(0,x.jsxs)("div",{className:"group/indicator inline-flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-primary/10 to-primary/5 group-hover/card:from-primary/20 group-hover/card:to-primary/10 border border-primary/20 group-hover/card:border-primary/30 rounded-lg text-primary group-hover/card:text-primary/90 transition-all duration-300 group-hover/card:scale-105 group-hover/card:shadow-lg group-hover/card:shadow-primary/20 font-medium relative overflow-hidden group-hover/card:-translate-y-0.5 pointer-events-none",children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"}),x.jsx("span",{className:"relative z-10 group-hover/card:drop-shadow-sm transition-all duration-300",children:"Continue Reading"}),x.jsx(N.Z,{className:"w-4 h-4 group-hover/card:translate-x-1 group-hover/card:scale-110 transition-all duration-300 relative z-10 group-hover/card:drop-shadow-sm"}),x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/15 to-primary/0 translate-x-[-100%] group-hover/card:translate-x-[100%] transition-transform duration-500 rounded-lg"})]})]}),x.jsx("div",{className:"absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-primary/8 via-primary/3 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 blur-2xl"}),x.jsx("div",{className:"absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-secondary/8 via-secondary/3 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 blur-xl"}),x.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-radial from-primary/2 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-1000"})]})})})};var P=i(83855),L=i(11019),B=i(941),U=i(96633);function q(e,a){return function(){return e.apply(a,arguments)}}let{toString:D}=Object.prototype,{getPrototypeOf:I}=Object,{iterator:M,toStringTag:$}=Symbol,H=(n=Object.create(null),e=>{let a=D.call(e);return n[a]||(n[a]=a.slice(8,-1).toLowerCase())}),Z=e=>(e=e.toLowerCase(),a=>H(a)===e),G=e=>a=>typeof a===e,{isArray:W}=Array,V=G("undefined"),J=Z("ArrayBuffer"),K=G("string"),Y=G("function"),X=G("number"),Q=e=>null!==e&&"object"==typeof e,ee=e=>{if("object"!==H(e))return!1;let a=I(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!($ in e)&&!(M in e)},ea=Z("Date"),ei=Z("File"),en=Z("Blob"),et=Z("FileList"),eo=Z("URLSearchParams"),[es,er,ec,ep]=["ReadableStream","Request","Response","Headers"].map(Z);function el(e,a,{allOwnKeys:i=!1}={}){let n,t;if(null!=e){if("object"!=typeof e&&(e=[e]),W(e))for(n=0,t=e.length;n<t;n++)a.call(null,e[n],n,e);else{let t;let o=i?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(n=0;n<s;n++)t=o[n],a.call(null,e[t],t,e)}}}function eu(e,a){let i;a=a.toLowerCase();let n=Object.keys(e),t=n.length;for(;t-- >0;)if(a===(i=n[t]).toLowerCase())return i;return null}let ed="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,em=e=>!V(e)&&e!==ed,ex=(t="undefined"!=typeof Uint8Array&&I(Uint8Array),e=>t&&e instanceof t),ef=Z("HTMLFormElement"),eh=(({hasOwnProperty:e})=>(a,i)=>e.call(a,i))(Object.prototype),ev=Z("RegExp"),eb=(e,a)=>{let i=Object.getOwnPropertyDescriptors(e),n={};el(i,(i,t)=>{let o;!1!==(o=a(i,t,e))&&(n[t]=o||i)}),Object.defineProperties(e,n)},eg=Z("AsyncFunction"),ey=(l="function"==typeof setImmediate,u=Y(ed.postMessage),l?setImmediate:u?(c=`axios@${Math.random()}`,p=[],ed.addEventListener("message",({source:e,data:a})=>{e===ed&&a===c&&p.length&&p.shift()()},!1),e=>{p.push(e),ed.postMessage(c,"*")}):e=>setTimeout(e)),ew="undefined"!=typeof queueMicrotask?queueMicrotask.bind(ed):"undefined"!=typeof process&&process.nextTick||ey,ej={isArray:W,isArrayBuffer:J,isBuffer:function(e){return null!==e&&!V(e)&&null!==e.constructor&&!V(e.constructor)&&Y(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||Y(e.append)&&("formdata"===(a=H(e))||"object"===a&&Y(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&J(e.buffer)},isString:K,isNumber:X,isBoolean:e=>!0===e||!1===e,isObject:Q,isPlainObject:ee,isReadableStream:es,isRequest:er,isResponse:ec,isHeaders:ep,isUndefined:V,isDate:ea,isFile:ei,isBlob:en,isRegExp:ev,isFunction:Y,isStream:e=>Q(e)&&Y(e.pipe),isURLSearchParams:eo,isTypedArray:ex,isFileList:et,forEach:el,merge:function e(){let{caseless:a}=em(this)&&this||{},i={},n=(n,t)=>{let o=a&&eu(i,t)||t;ee(i[o])&&ee(n)?i[o]=e(i[o],n):ee(n)?i[o]=e({},n):W(n)?i[o]=n.slice():i[o]=n};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&el(arguments[e],n);return i},extend:(e,a,i,{allOwnKeys:n}={})=>(el(a,(a,n)=>{i&&Y(a)?e[n]=q(a,i):e[n]=a},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,i,n)=>{e.prototype=Object.create(a.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),i&&Object.assign(e.prototype,i)},toFlatObject:(e,a,i,n)=>{let t,o,s;let r={};if(a=a||{},null==e)return a;do{for(o=(t=Object.getOwnPropertyNames(e)).length;o-- >0;)s=t[o],(!n||n(s,e,a))&&!r[s]&&(a[s]=e[s],r[s]=!0);e=!1!==i&&I(e)}while(e&&(!i||i(e,a))&&e!==Object.prototype);return a},kindOf:H,kindOfTest:Z,endsWith:(e,a,i)=>{e=String(e),(void 0===i||i>e.length)&&(i=e.length),i-=a.length;let n=e.indexOf(a,i);return -1!==n&&n===i},toArray:e=>{if(!e)return null;if(W(e))return e;let a=e.length;if(!X(a))return null;let i=Array(a);for(;a-- >0;)i[a]=e[a];return i},forEachEntry:(e,a)=>{let i;let n=(e&&e[M]).call(e);for(;(i=n.next())&&!i.done;){let n=i.value;a.call(e,n[0],n[1])}},matchAll:(e,a)=>{let i;let n=[];for(;null!==(i=e.exec(a));)n.push(i);return n},isHTMLForm:ef,hasOwnProperty:eh,hasOwnProp:eh,reduceDescriptors:eb,freezeMethods:e=>{eb(e,(a,i)=>{if(Y(e)&&-1!==["arguments","caller","callee"].indexOf(i))return!1;if(Y(e[i])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+i+"'")})}})},toObjectSet:(e,a)=>{let i={};return(e=>{e.forEach(e=>{i[e]=!0})})(W(e)?e:String(e).split(a)),i},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,i){return a.toUpperCase()+i}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:eu,global:ed,isContextDefined:em,isSpecCompliantForm:function(e){return!!(e&&Y(e.append)&&"FormData"===e[$]&&e[M])},toJSONObject:e=>{let a=Array(10),i=(e,n)=>{if(Q(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[n]=e;let t=W(e)?[]:{};return el(e,(e,a)=>{let o=i(e,n+1);V(o)||(t[a]=o)}),a[n]=void 0,t}}return e};return i(e,0)},isAsyncFn:eg,isThenable:e=>e&&(Q(e)||Y(e))&&Y(e.then)&&Y(e.catch),setImmediate:ey,asap:ew,isIterable:e=>null!=e&&Y(e[M])};function ek(e,a,i,n,t){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),i&&(this.config=i),n&&(this.request=n),t&&(this.response=t,this.status=t.status?t.status:null)}ej.inherits(ek,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ej.toJSONObject(this.config),code:this.code,status:this.status}}});let e_=ek.prototype,eE={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{eE[e]={value:e}}),Object.defineProperties(ek,eE),Object.defineProperty(e_,"isAxiosError",{value:!0}),ek.from=(e,a,i,n,t,o)=>{let s=Object.create(e_);return ej.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),ek.call(s,e.message,a,i,n,t),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var eS=i(54127);function eC(e){return ej.isPlainObject(e)||ej.isArray(e)}function eN(e){return ej.endsWith(e,"[]")?e.slice(0,-2):e}function eR(e,a,i){return e?e.concat(a).map(function(e,a){return e=eN(e),!i&&a?"["+e+"]":e}).join(i?".":""):a}let eO=ej.toFlatObject(ej,{},null,function(e){return/^is[A-Z]/.test(e)}),eA=function(e,a,i){if(!ej.isObject(e))throw TypeError("target must be an object");a=a||new(eS||FormData);let n=(i=ej.toFlatObject(i,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!ej.isUndefined(a[e])})).metaTokens,t=i.visitor||p,o=i.dots,s=i.indexes,r=(i.Blob||"undefined"!=typeof Blob&&Blob)&&ej.isSpecCompliantForm(a);if(!ej.isFunction(t))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ej.isDate(e))return e.toISOString();if(ej.isBoolean(e))return e.toString();if(!r&&ej.isBlob(e))throw new ek("Blob is not supported. Use a Buffer instead.");return ej.isArrayBuffer(e)||ej.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,i,t){let r=e;if(e&&!t&&"object"==typeof e){if(ej.endsWith(i,"{}"))i=n?i:i.slice(0,-2),e=JSON.stringify(e);else{var p;if(ej.isArray(e)&&(p=e,ej.isArray(p)&&!p.some(eC))||(ej.isFileList(e)||ej.endsWith(i,"[]"))&&(r=ej.toArray(e)))return i=eN(i),r.forEach(function(e,n){ej.isUndefined(e)||null===e||a.append(!0===s?eR([i],n,o):null===s?i:i+"[]",c(e))}),!1}}return!!eC(e)||(a.append(eR(t,i,o),c(e)),!1)}let l=[],u=Object.assign(eO,{defaultVisitor:p,convertValue:c,isVisitable:eC});if(!ej.isObject(e))throw TypeError("data must be an object");return function e(i,n){if(!ej.isUndefined(i)){if(-1!==l.indexOf(i))throw Error("Circular reference detected in "+n.join("."));l.push(i),ej.forEach(i,function(i,o){!0===(!(ej.isUndefined(i)||null===i)&&t.call(a,i,ej.isString(o)?o.trim():o,n,u))&&e(i,n?n.concat(o):[o])}),l.pop()}}(e),a};function eT(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function eF(e,a){this._pairs=[],e&&eA(e,this,a)}let ez=eF.prototype;function eP(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eL(e,a,i){let n;if(!a)return e;let t=i&&i.encode||eP;ej.isFunction(i)&&(i={serialize:i});let o=i&&i.serialize;if(n=o?o(a,i):ej.isURLSearchParams(a)?a.toString():new eF(a,i).toString(t)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}ez.append=function(e,a){this._pairs.push([e,a])},ez.toString=function(e){let a=e?function(a){return e.call(this,a,eT)}:eT;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class eB{constructor(){this.handlers=[]}use(e,a,i){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!i&&i.synchronous,runWhen:i?i.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ej.forEach(this.handlers,function(a){null!==a&&e(a)})}}let eU={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var eq=i(84770);let eD=i(17360).URLSearchParams,eI="abcdefghijklmnopqrstuvwxyz",eM="0123456789",e$={DIGIT:eM,ALPHA:eI,ALPHA_DIGIT:eI+eI.toUpperCase()+eM},eH={isNode:!0,classes:{URLSearchParams:eD,FormData:eS,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:e$,generateString:(e=16,a=e$.ALPHA_DIGIT)=>{let i="",{length:n}=a,t=new Uint32Array(e);eq.randomFillSync(t);for(let o=0;o<e;o++)i+=a[t[o]%n];return i},protocols:["http","https","file","data"]},eZ="undefined"!=typeof window&&"undefined"!=typeof document,eG="object"==typeof navigator&&navigator||void 0,eW=eZ&&(!eG||0>["ReactNative","NativeScript","NS"].indexOf(eG.product)),eV="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eJ=eZ&&window.location.href||"http://localhost",eK={...m,...eH},eY=function(e){if(ej.isFormData(e)&&ej.isFunction(e.entries)){let a={};return ej.forEachEntry(e,(e,i)=>{!function e(a,i,n,t){let o=a[t++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),r=t>=a.length;return(o=!o&&ej.isArray(n)?n.length:o,r)?ej.hasOwnProp(n,o)?n[o]=[n[o],i]:n[o]=i:(n[o]&&ej.isObject(n[o])||(n[o]=[]),e(a,i,n[o],t)&&ej.isArray(n[o])&&(n[o]=function(e){let a,i;let n={},t=Object.keys(e),o=t.length;for(a=0;a<o;a++)n[i=t[a]]=e[i];return n}(n[o]))),!s}(ej.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),i,a,0)}),a}return null},eX={transitional:eU,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let i;let n=a.getContentType()||"",t=n.indexOf("application/json")>-1,o=ej.isObject(e);if(o&&ej.isHTMLForm(e)&&(e=new FormData(e)),ej.isFormData(e))return t?JSON.stringify(eY(e)):e;if(ej.isArrayBuffer(e)||ej.isBuffer(e)||ej.isStream(e)||ej.isFile(e)||ej.isBlob(e)||ej.isReadableStream(e))return e;if(ej.isArrayBufferView(e))return e.buffer;if(ej.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1){var s,r;return(s=e,r=this.formSerializer,eA(s,new eK.classes.URLSearchParams,Object.assign({visitor:function(e,a,i,n){return eK.isNode&&ej.isBuffer(e)?(this.append(a,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},r))).toString()}if((i=ej.isFileList(e))||n.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return eA(i?{"files[]":e}:e,a&&new a,this.formSerializer)}}return o||t?(a.setContentType("application/json",!1),function(e,a,i){if(ej.isString(e))try{return(0,JSON.parse)(e),ej.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||eX.transitional,i=a&&a.forcedJSONParsing,n="json"===this.responseType;if(ej.isResponse(e)||ej.isReadableStream(e))return e;if(e&&ej.isString(e)&&(i&&!this.responseType||n)){let i=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!i&&n){if("SyntaxError"===e.name)throw ek.from(e,ek.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eK.classes.FormData,Blob:eK.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ej.forEach(["delete","get","head","post","put","patch"],e=>{eX.headers[e]={}});let eQ=ej.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),e0=e=>{let a,i,n;let t={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),a=e.substring(0,n).trim().toLowerCase(),i=e.substring(n+1).trim(),!a||t[a]&&eQ[a]||("set-cookie"===a?t[a]?t[a].push(i):t[a]=[i]:t[a]=t[a]?t[a]+", "+i:i)}),t},e1=Symbol("internals");function e2(e){return e&&String(e).trim().toLowerCase()}function e3(e){return!1===e||null==e?e:ej.isArray(e)?e.map(e3):String(e)}let e5=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function e6(e,a,i,n,t){if(ej.isFunction(n))return n.call(this,a,i);if(t&&(a=i),ej.isString(a)){if(ej.isString(n))return -1!==a.indexOf(n);if(ej.isRegExp(n))return n.test(a)}}class e4{constructor(e){e&&this.set(e)}set(e,a,i){let n=this;function t(e,a,i){let t=e2(a);if(!t)throw Error("header name must be a non-empty string");let o=ej.findKey(n,t);o&&void 0!==n[o]&&!0!==i&&(void 0!==i||!1===n[o])||(n[o||a]=e3(e))}let o=(e,a)=>ej.forEach(e,(e,i)=>t(e,i,a));if(ej.isPlainObject(e)||e instanceof this.constructor)o(e,a);else if(ej.isString(e)&&(e=e.trim())&&!e5(e))o(e0(e),a);else if(ej.isObject(e)&&ej.isIterable(e)){let i={},n,t;for(let a of e){if(!ej.isArray(a))throw TypeError("Object iterator must return a key-value pair");i[t=a[0]]=(n=i[t])?ej.isArray(n)?[...n,a[1]]:[n,a[1]]:a[1]}o(i,a)}else null!=e&&t(a,e,i);return this}get(e,a){if(e=e2(e)){let i=ej.findKey(this,e);if(i){let e=this[i];if(!a)return e;if(!0===a)return function(e){let a;let i=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=n.exec(e);)i[a[1]]=a[2];return i}(e);if(ej.isFunction(a))return a.call(this,e,i);if(ej.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=e2(e)){let i=ej.findKey(this,e);return!!(i&&void 0!==this[i]&&(!a||e6(this,this[i],i,a)))}return!1}delete(e,a){let i=this,n=!1;function t(e){if(e=e2(e)){let t=ej.findKey(i,e);t&&(!a||e6(i,i[t],t,a))&&(delete i[t],n=!0)}}return ej.isArray(e)?e.forEach(t):t(e),n}clear(e){let a=Object.keys(this),i=a.length,n=!1;for(;i--;){let t=a[i];(!e||e6(this,this[t],t,e,!0))&&(delete this[t],n=!0)}return n}normalize(e){let a=this,i={};return ej.forEach(this,(n,t)=>{let o=ej.findKey(i,t);if(o){a[o]=e3(n),delete a[t];return}let s=e?t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,i)=>a.toUpperCase()+i):String(t).trim();s!==t&&delete a[t],a[s]=e3(n),i[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return ej.forEach(this,(i,n)=>{null!=i&&!1!==i&&(a[n]=e&&ej.isArray(i)?i.join(", "):i)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let i=new this(e);return a.forEach(e=>i.set(e)),i}static accessor(e){let a=(this[e1]=this[e1]={accessors:{}}).accessors,i=this.prototype;function n(e){let n=e2(e);a[n]||(function(e,a){let i=ej.toCamelCase(" "+a);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+i,{value:function(e,i,t){return this[n].call(this,a,e,i,t)},configurable:!0})})}(i,e),a[n]=!0)}return ej.isArray(e)?e.forEach(n):n(e),this}}function e8(e,a){let i=this||eX,n=a||i,t=e4.from(n.headers),o=n.data;return ej.forEach(e,function(e){o=e.call(i,o,t.normalize(),a?a.status:void 0)}),t.normalize(),o}function e7(e){return!!(e&&e.__CANCEL__)}function e9(e,a,i){ek.call(this,null==e?"canceled":e,ek.ERR_CANCELED,a,i),this.name="CanceledError"}function ae(e,a,i){let n=i.config.validateStatus;!i.status||!n||n(i.status)?e(i):a(new ek("Request failed with status code "+i.status,[ek.ERR_BAD_REQUEST,ek.ERR_BAD_RESPONSE][Math.floor(i.status/100)-4],i.config,i.request,i))}function aa(e,a,i){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a);return e&&(n||!1==i)?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}e4.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ej.reduceDescriptors(e4.prototype,({value:e},a)=>{let i=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[i]=e}}}),ej.freezeMethods(e4),ej.inherits(e9,ek,{__CANCEL__:!0});var ai=i(13304),an=i(32615),at=i(35240),ao=i(21764),as=i(19831),ar=i(71568);let ac="1.10.0";function ap(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let al=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var au=i(76162);let ad=Symbol("internals");class am extends au.Transform{constructor(e){super({readableHighWaterMark:(e=ej.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!ej.isUndefined(a[e]))).chunkSize});let a=this[ad]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[ad];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,i){let n=this[ad],t=n.maxRate,o=this.readableHighWaterMark,s=n.timeWindow,r=t/(1e3/s),c=!1!==n.minChunkSize?Math.max(n.minChunkSize,.01*r):0,p=(e,a)=>{let i=Buffer.byteLength(e);n.bytesSeen+=i,n.bytes+=i,n.isCaptured&&this.emit("progress",n.bytesSeen),this.push(e)?process.nextTick(a):n.onReadCallback=()=>{n.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let i;let l=Buffer.byteLength(e),u=null,d=o,m=0;if(t){let e=Date.now();(!n.ts||(m=e-n.ts)>=s)&&(n.ts=e,i=r-n.bytes,n.bytes=i<0?-i:0,m=0),i=r-n.bytes}if(t){if(i<=0)return setTimeout(()=>{a(null,e)},s-m);i<d&&(d=i)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,n){if(a)return i(a);n?l(n,e):i(null)})}}var ax=i(17702);let{asyncIterator:af}=Symbol,ah=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[af]?yield*e[af]():yield e},av=eK.ALPHABET.ALPHA_DIGIT+"-_",ab="function"==typeof TextEncoder?new TextEncoder:new ao.TextEncoder,ag=ab.encode("\r\n");class ay{constructor(e,a){let{escapeName:i}=this.constructor,n=ej.isString(a),t=`Content-Disposition: form-data; name="${i(e)}"${!n&&a.name?`; filename="${i(a.name)}"`:""}\r
`;n?a=ab.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):t+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=ab.encode(t+"\r\n"),this.contentLength=n?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;ej.isTypedArray(e)?yield e:yield*ah(e),yield ag}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let aw=(e,a,i)=>{let{tag:n="form-data-boundary",size:t=25,boundary:o=n+"-"+eK.generateString(t,av)}=i||{};if(!ej.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let s=ab.encode("--"+o+"\r\n"),r=ab.encode("--"+o+"--\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let i=new ay(e,a);return c+=i.size,i});c+=s.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c=ej.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),au.Readable.from(async function*(){for(let e of p)yield s,yield*e.encode();yield r}())};class aj extends au.Transform{__transform(e,a,i){this.push(e),i()}_transform(e,a,i){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,i)}}let ak=(e,a)=>ej.isAsyncFn(e)?function(...i){let n=i.pop();e.apply(this,i).then(e=>{try{a?n(null,...a(e)):n(null,e)}catch(e){n(e)}},n)}:e,a_=function(e,a){let i;let n=Array(e=e||10),t=Array(e),o=0,s=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=t[s];i||(i=c),n[o]=r,t[o]=c;let l=s,u=0;for(;l!==o;)u+=n[l++],l%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),c-i<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}},aE=function(e,a){let i,n,t=0,o=1e3/a,s=(a,o=Date.now())=>{t=o,i=null,n&&(clearTimeout(n),n=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-t;r>=o?s(e,a):(i=e,n||(n=setTimeout(()=>{n=null,s(i)},o-r)))},()=>i&&s(i)]},aS=(e,a,i=3)=>{let n=0,t=a_(50,250);return aE(i=>{let o=i.loaded,s=i.lengthComputable?i.total:void 0,r=o-n,c=t(r);n=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:i,lengthComputable:null!=s,[a?"download":"upload"]:!0})},i)},aC=(e,a)=>{let i=null!=e;return[n=>a[0]({lengthComputable:i,total:e,loaded:n}),a[1]]},aN=e=>(...a)=>ej.asap(()=>e(...a)),aR={flush:ar.constants.Z_SYNC_FLUSH,finishFlush:ar.constants.Z_SYNC_FLUSH},aO={flush:ar.constants.BROTLI_OPERATION_FLUSH,finishFlush:ar.constants.BROTLI_OPERATION_FLUSH},aA=ej.isFunction(ar.createBrotliDecompress),{http:aT,https:aF}=as,az=/https:?/,aP=eK.protocols.map(e=>e+":"),aL=(e,[a,i])=>(e.on("end",i).on("error",i),a);function aB(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let aU="undefined"!=typeof process&&"process"===ej.kindOf(process),aq=e=>new Promise((a,i)=>{let n,t;let o=(e,a)=>{!t&&(t=!0,n&&n(e,a))},s=e=>{o(e,!0),i(e)};e(e=>{o(e),a(e)},s,e=>n=e).catch(s)}),aD=({address:e,family:a})=>{if(!ej.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},aI=(e,a)=>aD(ej.isObject(e)?e:{address:e,family:a}),aM=aU&&function(e){return aq(async function(a,i,n){let t,o,s,r,c,p,l,{data:u,lookup:d,family:m}=e,{responseType:x,responseEncoding:f}=e,h=e.method.toUpperCase(),v=!1;if(d){let e=ak(d,e=>ej.isArray(e)?e:[e]);d=(a,i,n)=>{e(a,i,(e,a,t)=>{if(e)return n(e);let o=ej.isArray(a)?a.map(e=>aI(e)):[aI(a,t)];i.all?n(e,o):n(e,o[0].address,o[0].family)})}}let b=new ax.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new e9(null,e,c):a)}n((e,a)=>{r=!0,a&&(v=!0,g())}),b.once("abort",i),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(aa(e.baseURL,e.url,e.allowAbsoluteUrls),eK.hasBrowserEnv?eK.origin:void 0),j=w.protocol||aP[0];if("data:"===j){let n;if("GET"!==h)return ae(a,i,{status:405,statusText:"method not allowed",headers:{},config:e});try{n=function(e,a,i){let n=i&&i.Blob||eK.classes.Blob,t=ap(e);if(void 0===a&&n&&(a=!0),"data"===t){e=t.length?e.slice(t.length+1):e;let i=al.exec(e);if(!i)throw new ek("Invalid URL",ek.ERR_INVALID_URL);let o=i[1],s=i[2],r=i[3],c=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(a){if(!n)throw new ek("Blob is not supported",ek.ERR_NOT_SUPPORT);return new n([c],{type:o})}return c}throw new ek("Unsupported protocol "+t,ek.ERR_NOT_SUPPORT)}(e.url,"blob"===x,{Blob:e.env&&e.env.Blob})}catch(a){throw ek.from(a,ek.ERR_BAD_REQUEST,e)}return"text"===x?(n=n.toString(f),f&&"utf8"!==f||(n=ej.stripBOM(n))):"stream"===x&&(n=au.Readable.from(n)),ae(a,i,{data:n,status:200,statusText:"OK",headers:new e4,config:e})}if(-1===aP.indexOf(j))return i(new ek("Unsupported protocol "+j,ek.ERR_BAD_REQUEST,e));let k=e4.from(e.headers).normalize();k.set("User-Agent","axios/"+ac,!1);let{onUploadProgress:_,onDownloadProgress:E}=e,S=e.maxRate;if(ej.isSpecCompliantForm(u)){let e=k.getContentType(/boundary=([-_\w\d]{10,70})/i);u=aw(u,e=>{k.set(e)},{tag:`axios-${ac}-boundary`,boundary:e&&e[1]||void 0})}else if(ej.isFormData(u)&&ej.isFunction(u.getHeaders)){if(k.set(u.getHeaders()),!k.hasContentLength())try{let e=await ao.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&k.setContentLength(e)}catch(e){}}else if(ej.isBlob(u)||ej.isFile(u))u.size&&k.setContentType(u.type||"application/octet-stream"),k.setContentLength(u.size||0),u=au.Readable.from(ah(u));else if(u&&!ej.isStream(u)){if(Buffer.isBuffer(u));else if(ej.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!ej.isString(u))return i(new ek("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",ek.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(k.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return i(new ek("Request body larger than maxBodyLength limit",ek.ERR_BAD_REQUEST,e))}let C=ej.toFiniteNumber(k.getContentLength());ej.isArray(S)?(t=S[0],o=S[1]):t=o=S,u&&(_||t)&&(ej.isStream(u)||(u=au.Readable.from(u,{objectMode:!1})),u=au.pipeline([u,new am({maxRate:ej.toFiniteNumber(t)})],ej.noop),_&&u.on("progress",aL(u,aC(C,aS(aN(_),!1,3))))),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&w.username&&(s=w.username+":"+w.password),s&&k.delete("authorization");try{p=eL(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(n){let a=Error(n.message);return a.config=e,a.url=e.url,a.exists=!0,i(a)}k.set("Accept-Encoding","gzip, compress, deflate"+(aA?", br":""),!1);let N={path:p,method:h,headers:k.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:j,family:m,beforeRedirect:aB,beforeRedirects:{}};ej.isUndefined(d)||(N.lookup=d),e.socketPath?N.socketPath=e.socketPath:(N.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,N.port=w.port,function e(a,i,n){let t=i;if(!t&&!1!==t){let e=ai.getProxyForUrl(n);e&&(t=new URL(e))}if(t){if(t.username&&(t.auth=(t.username||"")+":"+(t.password||"")),t.auth){(t.auth.username||t.auth.password)&&(t.auth=(t.auth.username||"")+":"+(t.auth.password||""));let e=Buffer.from(t.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=t.hostname||t.host;a.hostname=e,a.host=e,a.port=t.port,a.path=n,t.protocol&&(a.protocol=t.protocol.includes(":")?t.protocol:`${t.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,i,a.href)}}(N,e.proxy,j+"//"+w.hostname+(w.port?":"+w.port:"")+N.path));let R=az.test(N.protocol);if(N.agent=R?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=R?at:an:(e.maxRedirects&&(N.maxRedirects=e.maxRedirects),e.beforeRedirect&&(N.beforeRedirects.config=e.beforeRedirect),l=R?aF:aT),e.maxBodyLength>-1?N.maxBodyLength=e.maxBodyLength:N.maxBodyLength=1/0,e.insecureHTTPParser&&(N.insecureHTTPParser=e.insecureHTTPParser),c=l.request(N,function(n){if(c.destroyed)return;let t=[n],s=+n.headers["content-length"];if(E||o){let e=new am({maxRate:ej.toFiniteNumber(o)});E&&e.on("progress",aL(e,aC(s,aS(aN(E),!0,3)))),t.push(e)}let r=n,p=n.req||c;if(!1!==e.decompress&&n.headers["content-encoding"])switch(("HEAD"===h||204===n.statusCode)&&delete n.headers["content-encoding"],(n.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":t.push(ar.createUnzip(aR)),delete n.headers["content-encoding"];break;case"deflate":t.push(new aj),t.push(ar.createUnzip(aR)),delete n.headers["content-encoding"];break;case"br":aA&&(t.push(ar.createBrotliDecompress(aO)),delete n.headers["content-encoding"])}r=t.length>1?au.pipeline(t,ej.noop):t[0];let l=au.finished(r,()=>{l(),g()}),u={status:n.statusCode,statusText:n.statusMessage,headers:new e4(n.headers),config:e,request:p};if("stream"===x)u.data=r,ae(a,i,u);else{let n=[],t=0;r.on("data",function(a){n.push(a),t+=a.length,e.maxContentLength>-1&&t>e.maxContentLength&&(v=!0,r.destroy(),i(new ek("maxContentLength size of "+e.maxContentLength+" exceeded",ek.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(v)return;let a=new ek("stream has been aborted",ek.ERR_BAD_RESPONSE,e,p);r.destroy(a),i(a)}),r.on("error",function(a){c.destroyed||i(ek.from(a,null,e,p))}),r.on("end",function(){try{let e=1===n.length?n[0]:Buffer.concat(n);"arraybuffer"===x||(e=e.toString(f),f&&"utf8"!==f||(e=ej.stripBOM(e))),u.data=e}catch(a){return i(ek.from(a,null,e,u.request,u))}ae(a,i,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{i(e),c.destroy(e)}),c.on("error",function(a){i(ek.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){i(new ek("error trying to parse `config.timeout` to int",ek.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||eU;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),i(new ek(a,n.clarifyTimeoutError?ek.ETIMEDOUT:ek.ECONNABORTED,e,c)),y()})}if(ej.isStream(u)){let a=!1,i=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{i=!0,c.destroy(e)}),u.on("close",()=>{a||i||y(new e9("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},a$=eK.hasStandardBrowserEnv?(o=new URL(eK.origin),s=eK.navigator&&/(msie|trident)/i.test(eK.navigator.userAgent),e=>(e=new URL(e,eK.origin),o.protocol===e.protocol&&o.host===e.host&&(s||o.port===e.port))):()=>!0,aH=eK.hasStandardBrowserEnv?{write(e,a,i,n,t,o){let s=[e+"="+encodeURIComponent(a)];ej.isNumber(i)&&s.push("expires="+new Date(i).toGMTString()),ej.isString(n)&&s.push("path="+n),ej.isString(t)&&s.push("domain="+t),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},aZ=e=>e instanceof e4?{...e}:e;function aG(e,a){a=a||{};let i={};function n(e,a,i,n){return ej.isPlainObject(e)&&ej.isPlainObject(a)?ej.merge.call({caseless:n},e,a):ej.isPlainObject(a)?ej.merge({},a):ej.isArray(a)?a.slice():a}function t(e,a,i,t){return ej.isUndefined(a)?ej.isUndefined(e)?void 0:n(void 0,e,i,t):n(e,a,i,t)}function o(e,a){if(!ej.isUndefined(a))return n(void 0,a)}function s(e,a){return ej.isUndefined(a)?ej.isUndefined(e)?void 0:n(void 0,e):n(void 0,a)}function r(i,t,o){return o in a?n(i,t):o in e?n(void 0,i):void 0}let c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,a,i)=>t(aZ(e),aZ(a),i,!0)};return ej.forEach(Object.keys(Object.assign({},e,a)),function(n){let o=c[n]||t,s=o(e[n],a[n],n);ej.isUndefined(s)&&o!==r||(i[n]=s)}),i}let aW=e=>{let a;let i=aG({},e),{data:n,withXSRFToken:t,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:c}=i;if(i.headers=r=e4.from(r),i.url=eL(aa(i.baseURL,i.url,i.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ej.isFormData(n)){if(eK.hasStandardBrowserEnv||eK.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...i]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...i].join("; "))}}if(eK.hasStandardBrowserEnv&&(t&&ej.isFunction(t)&&(t=t(i)),t||!1!==t&&a$(i.url))){let e=o&&s&&aH.read(s);e&&r.set(o,e)}return i},aV="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,i){let n,t,o,s,r;let c=aW(e),p=c.data,l=e4.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function x(){s&&s(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(n),c.signal&&c.signal.removeEventListener("abort",n)}let f=new XMLHttpRequest;function h(){if(!f)return;let n=e4.from("getAllResponseHeaders"in f&&f.getAllResponseHeaders());ae(function(e){a(e),x()},function(e){i(e),x()},{data:u&&"text"!==u&&"json"!==u?f.response:f.responseText,status:f.status,statusText:f.statusText,headers:n,config:e,request:f}),f=null}f.open(c.method.toUpperCase(),c.url,!0),f.timeout=c.timeout,"onloadend"in f?f.onloadend=h:f.onreadystatechange=function(){f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))&&setTimeout(h)},f.onabort=function(){f&&(i(new ek("Request aborted",ek.ECONNABORTED,e,f)),f=null)},f.onerror=function(){i(new ek("Network Error",ek.ERR_NETWORK,e,f)),f=null},f.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",n=c.transitional||eU;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),i(new ek(a,n.clarifyTimeoutError?ek.ETIMEDOUT:ek.ECONNABORTED,e,f)),f=null},void 0===p&&l.setContentType(null),"setRequestHeader"in f&&ej.forEach(l.toJSON(),function(e,a){f.setRequestHeader(a,e)}),ej.isUndefined(c.withCredentials)||(f.withCredentials=!!c.withCredentials),u&&"json"!==u&&(f.responseType=c.responseType),m&&([o,r]=aS(m,!0),f.addEventListener("progress",o)),d&&f.upload&&([t,s]=aS(d),f.upload.addEventListener("progress",t),f.upload.addEventListener("loadend",s)),(c.cancelToken||c.signal)&&(n=a=>{f&&(i(!a||a.type?new e9(null,e,f):a),f.abort(),f=null)},c.cancelToken&&c.cancelToken.subscribe(n),c.signal&&(c.signal.aborted?n():c.signal.addEventListener("abort",n)));let v=ap(c.url);if(v&&-1===eK.protocols.indexOf(v)){i(new ek("Unsupported protocol "+v+":",ek.ERR_BAD_REQUEST,e));return}f.send(p||null)})},aJ=(e,a)=>{let{length:i}=e=e?e.filter(Boolean):[];if(a||i){let i,n=new AbortController,t=function(e){if(!i){i=!0,s();let a=e instanceof Error?e:this.reason;n.abort(a instanceof ek?a:new e9(a instanceof Error?a.message:a))}},o=a&&setTimeout(()=>{o=null,t(new ek(`timeout ${a} of ms exceeded`,ek.ETIMEDOUT))},a),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(t):e.removeEventListener("abort",t)}),e=null)};e.forEach(e=>e.addEventListener("abort",t));let{signal:r}=n;return r.unsubscribe=()=>ej.asap(s),r}},aK=function*(e,a){let i,n=e.byteLength;if(!a||n<a){yield e;return}let t=0;for(;t<n;)i=t+a,yield e.slice(t,i),t=i},aY=async function*(e,a){for await(let i of aX(e))yield*aK(i,a)},aX=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:i}=await a.read();if(e)break;yield i}}finally{await a.cancel()}},aQ=(e,a,i,n)=>{let t;let o=aY(e,a),s=0,r=e=>{!t&&(t=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:a,value:n}=await o.next();if(a){r(),e.close();return}let t=n.byteLength;if(i){let e=s+=t;i(e)}e.enqueue(new Uint8Array(n))}catch(e){throw r(e),e}},cancel:e=>(r(e),o.return())},{highWaterMark:2})},a0="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,a1=a0&&"function"==typeof ReadableStream,a2=a0&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),a3=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},a5=a1&&a3(()=>{let e=!1,a=new Request(eK.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),a6=a1&&a3(()=>ej.isReadableStream(new Response("").body)),a4={stream:a6&&(e=>e.body)};a0&&(d=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{a4[e]||(a4[e]=ej.isFunction(d[e])?a=>a[e]():(a,i)=>{throw new ek(`Response type '${e}' is not supported`,ek.ERR_NOT_SUPPORT,i)})}));let a8=async e=>{if(null==e)return 0;if(ej.isBlob(e))return e.size;if(ej.isSpecCompliantForm(e)){let a=new Request(eK.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return ej.isArrayBufferView(e)||ej.isArrayBuffer(e)?e.byteLength:(ej.isURLSearchParams(e)&&(e+=""),ej.isString(e))?(await a2(e)).byteLength:void 0},a7=async(e,a)=>{let i=ej.toFiniteNumber(e.getContentLength());return null==i?a8(a):i},a9={http:aM,xhr:aV,fetch:a0&&(async e=>{let a,i,{url:n,method:t,data:o,signal:s,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:x}=aW(e);u=u?(u+"").toLowerCase():"text";let f=aJ([s,r&&r.toAbortSignal()],c),h=f&&f.unsubscribe&&(()=>{f.unsubscribe()});try{if(l&&a5&&"get"!==t&&"head"!==t&&0!==(i=await a7(d,o))){let e,a=new Request(n,{method:"POST",body:o,duplex:"half"});if(ej.isFormData(o)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,n]=aC(i,aS(aN(l)));o=aQ(a.body,65536,e,n)}}ej.isString(m)||(m=m?"include":"omit");let s="credentials"in Request.prototype;a=new Request(n,{...x,signal:f,method:t.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?m:void 0});let r=await fetch(a,x),c=a6&&("stream"===u||"response"===u);if(a6&&(p||c&&h)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=ej.toFiniteNumber(r.headers.get("content-length")),[i,n]=p&&aC(a,aS(aN(p),!0))||[];r=new Response(aQ(r.body,65536,i,()=>{n&&n(),h&&h()}),e)}u=u||"text";let v=await a4[ej.findKey(a4,u)||"text"](r,e);return!c&&h&&h(),await new Promise((i,n)=>{ae(i,n,{data:v,headers:e4.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(i){if(h&&h(),i&&"TypeError"===i.name&&/Load failed|fetch/i.test(i.message))throw Object.assign(new ek("Network Error",ek.ERR_NETWORK,e,a),{cause:i.cause||i});throw ek.from(i,i&&i.code,e,a)}})};ej.forEach(a9,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let ie=e=>`- ${e}`,ia=e=>ej.isFunction(e)||null===e||!1===e,ii={getAdapter:e=>{let a,i;let{length:n}=e=ej.isArray(e)?e:[e],t={};for(let o=0;o<n;o++){let n;if(i=a=e[o],!ia(a)&&void 0===(i=a9[(n=String(a)).toLowerCase()]))throw new ek(`Unknown adapter '${n}'`);if(i)break;t[n||"#"+o]=i}if(!i){let e=Object.entries(t).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new ek("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(ie).join("\n"):" "+ie(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return i}};function it(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new e9(null,e)}function io(e){return it(e),e.headers=e4.from(e.headers),e.data=e8.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ii.getAdapter(e.adapter||eX.adapter)(e).then(function(a){return it(e),a.data=e8.call(e,e.transformResponse,a),a.headers=e4.from(a.headers),a},function(a){return!e7(a)&&(it(e),a&&a.response&&(a.response.data=e8.call(e,e.transformResponse,a.response),a.response.headers=e4.from(a.response.headers))),Promise.reject(a)})}let is={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{is[e]=function(i){return typeof i===e||"a"+(a<1?"n ":" ")+e}});let ir={};is.transitional=function(e,a,i){function n(e,a){return"[Axios v"+ac+"] Transitional option '"+e+"'"+a+(i?". "+i:"")}return(i,t,o)=>{if(!1===e)throw new ek(n(t," has been removed"+(a?" in "+a:"")),ek.ERR_DEPRECATED);return a&&!ir[t]&&(ir[t]=!0,console.warn(n(t," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(i,t,o)}},is.spelling=function(e){return(a,i)=>(console.warn(`${i} is likely a misspelling of ${e}`),!0)};let ic={assertOptions:function(e,a,i){if("object"!=typeof e)throw new ek("options must be an object",ek.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),t=n.length;for(;t-- >0;){let o=n[t],s=a[o];if(s){let a=e[o],i=void 0===a||s(a,o,e);if(!0!==i)throw new ek("option "+o+" must be "+i,ek.ERR_BAD_OPTION_VALUE);continue}if(!0!==i)throw new ek("Unknown option "+o,ek.ERR_BAD_OPTION)}},validators:is},ip=ic.validators;class il{constructor(e){this.defaults=e||{},this.interceptors={request:new eB,response:new eB}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=Error();let i=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?i&&!String(e.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+i):e.stack=i}catch(e){}}throw e}}_request(e,a){let i,n;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:t,paramsSerializer:o,headers:s}=a=aG(this.defaults,a);void 0!==t&&ic.assertOptions(t,{silentJSONParsing:ip.transitional(ip.boolean),forcedJSONParsing:ip.transitional(ip.boolean),clarifyTimeoutError:ip.transitional(ip.boolean)},!1),null!=o&&(ej.isFunction(o)?a.paramsSerializer={serialize:o}:ic.assertOptions(o,{encode:ip.function,serialize:ip.function},!0)),void 0!==a.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),ic.assertOptions(a,{baseUrl:ip.spelling("baseURL"),withXsrfToken:ip.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=s&&ej.merge(s.common,s[a.method]);s&&ej.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),a.headers=e4.concat(r,s);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[io.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,l),n=e.length,i=Promise.resolve(a);u<n;)i=i.then(e[u++],e[u++]);return i}n=c.length;let d=a;for(u=0;u<n;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{i=io.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,n=l.length;u<n;)i=i.then(l[u++],l[u++]);return i}getUri(e){return eL(aa((e=aG(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}ej.forEach(["delete","get","head","options"],function(e){il.prototype[e]=function(a,i){return this.request(aG(i||{},{method:e,url:a,data:(i||{}).data}))}}),ej.forEach(["post","put","patch"],function(e){function a(a){return function(i,n,t){return this.request(aG(t||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:i,data:n}))}}il.prototype[e]=a(),il.prototype[e+"Form"]=a(!0)});class iu{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let i=this;this.promise.then(e=>{if(!i._listeners)return;let a=i._listeners.length;for(;a-- >0;)i._listeners[a](e);i._listeners=null}),this.promise.then=e=>{let a;let n=new Promise(e=>{i.subscribe(e),a=e}).then(e);return n.cancel=function(){i.unsubscribe(a)},n},e(function(e,n,t){i.reason||(i.reason=new e9(e,n,t),a(i.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new iu(function(a){e=a}),cancel:e}}}let id={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(id).forEach(([e,a])=>{id[a]=e});let im=function e(a){let i=new il(a),n=q(il.prototype.request,i);return ej.extend(n,il.prototype,i,{allOwnKeys:!0}),ej.extend(n,i,null,{allOwnKeys:!0}),n.create=function(i){return e(aG(a,i))},n}(eX);im.Axios=il,im.CanceledError=e9,im.CancelToken=iu,im.isCancel=e7,im.VERSION=ac,im.toFormData=eA,im.AxiosError=ek,im.Cancel=im.CanceledError,im.all=function(e){return Promise.all(e)},im.spread=function(e){return function(a){return e.apply(null,a)}},im.isAxiosError=function(e){return ej.isObject(e)&&!0===e.isAxiosError},im.mergeConfig=aG,im.AxiosHeaders=e4,im.formToJSON=e=>eY(ej.isHTMLForm(e)?new FormData(e):e),im.getAdapter=ii.getAdapter,im.HttpStatusCode=id,im.default=im;let ix="http://100.90.150.110:8000/api",ih=({className:e,filteredBlogs:a})=>{let[i,n]=(0,f.useState)(new Set),[t,o]=(0,f.useState)({}),s=!a||0===a.length,{timelineStructure:r,yearData:c,expandedYears:p,loadingYears:l,isInitialLoading:u,error:d,totalArticles:m,toggleYear:h,preloadYear:v}=function(e=!0){let[a,i]=(0,f.useState)({}),[n,t]=(0,f.useState)({}),[o,s]=(0,f.useState)(new Set),[r,c]=(0,f.useState)(new Set),[p,l]=(0,f.useState)(!0),[u,d]=(0,f.useState)(null),[m,x]=(0,f.useState)(0),h=(0,f.useCallback)(async()=>{l(!0),d(null);try{let e=await im.get(`${ix}/blogs/timeline-structure`);i(e.data.structure),x(e.data.total_articles);let a=Object.keys(e.data.structure).sort((e,a)=>parseInt(a)-parseInt(e));if(a.length>0){let e=a[0];s(new Set([e]));let i=v(e);setTimeout(()=>{a.slice(1).forEach((e,a)=>{setTimeout(()=>{v(e)},200*a)})},1e3),await i}}catch(e){d("获取时间线结构失败")}finally{l(!1)}},[]),v=(0,f.useCallback)(e=>a[e]?.loaded||n[e]||r.has(e)?Promise.resolve():(c(a=>new Set([...a,e])),d(null),im.get(`${ix}/blogs/year/${e}`).then(a=>{t(i=>({...i,[e]:a.data})),i(a=>({...a,[e]:{...a[e],loaded:!0}}))}).catch(a=>{d(`加载年份 ${e} 数据失败`)}).finally(()=>{c(a=>{let i=new Set(a);return i.delete(e),i})})),[a,n]),b=(0,f.useCallback)(e=>o.has(e)?(s(a=>{let i=new Set(a);return i.delete(e),i}),Promise.resolve()):(s(a=>new Set([...a,e])),a[e]?.loaded||n[e])?Promise.resolve():v(e),[o,a,n,v]),g=(0,f.useCallback)(e=>a[e]?.loaded||n[e]||r.has(e)?Promise.resolve():v(e),[a,n,r,v]);return{timelineStructure:a,yearData:n,expandedYears:o,loadingYears:r,isInitialLoading:p,error:u,totalArticles:m,toggleYear:b,preloadYear:g,refreshStructure:(0,f.useCallback)(()=>h(),[h])}}(s);(0,f.useEffect)(()=>{s&&c&&o(e=>({...e,...c}))},[s,c]),(0,f.useEffect)(()=>{if(s&&Object.keys(r).length>0){let e=Object.keys(r).sort((e,a)=>parseInt(a)-parseInt(e)),a=setTimeout(()=>{e.forEach((e,a)=>{setTimeout(()=>{v(e)},100*a)})},500);return()=>clearTimeout(a)}},[s,r,v]);let b=(0,f.useMemo)(()=>{if(!a||!(a.length>0))return Object.entries(r).map(([e,a])=>{let i=t[e]||c[e]||[],n=i.length>0;return{year:e,blogs:i.sort((e,a)=>new Date(a.display_date).getTime()-new Date(e.display_date).getTime()),count:a.count,loaded:a.loaded||n}}).sort((e,a)=>parseInt(a.year)-parseInt(e.year));{let e={};return a.forEach(a=>{let i=new Date(a.display_date).getFullYear().toString();e[i]||(e[i]=[]),e[i].push(a)}),Object.entries(e).map(([e,a])=>({year:e,blogs:a,count:a.length,loaded:!0})).sort((e,a)=>parseInt(a.year)-parseInt(e.year))}},[a,r,c,t]);(0,f.useEffect)(()=>{a&&a.length>0&&b.length>0&&n(new Set([b[0].year]))},[a,b]);let g=a?i:p,y=async e=>{a?n(a=>{let i=new Set(a);return i.has(e)?i.delete(e):i.add(e),i}):await h(e)};return u?x.jsx("div",{className:(0,R.cn)("relative flex items-center justify-center py-20",e),children:(0,x.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[x.jsx(A.Z,{className:"w-8 h-8 animate-spin text-primary"}),x.jsx("p",{className:"text-muted-foreground",children:"Loading timeline..."})]})}):d?x.jsx("div",{className:(0,R.cn)("relative flex items-center justify-center py-20",e),children:(0,x.jsxs)("div",{className:"flex flex-col items-center gap-4 text-center",children:[x.jsx("div",{className:"w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center",children:x.jsx("span",{className:"text-red-600 dark:text-red-400 text-xl",children:"⚠"})}),(0,x.jsxs)("div",{children:[x.jsx("p",{className:"text-red-600 dark:text-red-400 font-medium",children:"Failed to load timeline"}),x.jsx("p",{className:"text-muted-foreground text-sm mt-1",children:d})]})]})}):0===b.length?(0,x.jsxs)("div",{className:"text-center py-20",children:[(0,x.jsxs)("div",{className:"relative",children:[x.jsx("div",{className:"text-6xl mb-6 opacity-50",children:"\uD83D\uDCDD"}),x.jsx("div",{className:"absolute inset-0 bg-gradient-radial from-primary/5 via-transparent to-transparent rounded-full blur-3xl"})]}),x.jsx("h3",{className:"text-xl font-semibold mb-3",children:"No Articles Found"}),x.jsx("p",{className:"text-muted-foreground max-w-md mx-auto",children:"No articles available at the moment."})]}):(0,x.jsxs)("div",{className:(0,R.cn)("relative",e),children:[x.jsx("div",{className:"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/70 via-primary/40 via-border to-transparent shadow-sm"}),x.jsx("div",{className:"absolute left-[31px] top-0 bottom-0 w-px bg-gradient-to-b from-primary/20 via-transparent to-transparent"}),x.jsx("div",{className:"space-y-16",children:b.map((e,i)=>{let n=g.has(e.year),t=!a&&l.has(e.year);return(0,x.jsxs)("div",{className:"relative",children:[(0,x.jsxs)("div",{className:"relative mb-16",children:[x.jsx("button",{onClick:()=>y(e.year),onMouseEnter:()=>{s&&!e.loaded&&v&&v(e.year)},disabled:t,className:"absolute left-2 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-background border-4 border-primary shadow-xl shadow-primary/20 z-[10] flex items-center justify-center timeline-node hover:scale-110 hover:shadow-2xl hover:shadow-primary/30 transition-all duration-500 group/node cursor-pointer disabled:cursor-not-allowed disabled:opacity-70",style:{transform:`translateY(-50%) ${n?"rotateX(0deg)":"rotateX(180deg)"}`,transformStyle:"preserve-3d"},children:(0,x.jsxs)("div",{className:"relative w-full h-full rounded-full overflow-hidden",children:[x.jsx("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-primary via-primary/80 to-primary/60 animate-pulse-soft"}),x.jsx("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-white/30 via-transparent to-transparent"}),x.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:t?x.jsx(A.Z,{className:"w-4 h-4 text-white drop-shadow-sm animate-spin"}):n?x.jsx(L.Z,{className:"w-4 h-4 text-white drop-shadow-sm"}):x.jsx(P.Z,{className:"w-4 h-4 text-white drop-shadow-sm"})})]})}),(0,x.jsxs)("div",{className:"ml-24 p-8 rounded-2xl bg-gradient-to-br from-background via-muted/20 to-background border border-border/50 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-700 group/year cursor-pointer",onClick:()=>y(e.year),style:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d"},children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-0 group-hover/year:opacity-100 transition-opacity duration-700"}),x.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-blue-500/3 opacity-0 group-hover/year:opacity-100 transition-opacity duration-500"}),x.jsx("div",{className:"flex items-center justify-between flex-wrap gap-6 relative z-[5]",children:(0,x.jsxs)("div",{className:"flex items-center gap-6",children:[(0,x.jsxs)("h2",{className:"text-4xl font-bold text-foreground flex items-center gap-4 group",children:[x.jsx("div",{className:"p-2 rounded-xl bg-primary/10 text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300 shadow-lg",children:x.jsx(C.Z,{className:"w-8 h-8"})}),x.jsx("span",{className:"bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent drop-shadow-sm",children:e.year})]}),(0,x.jsxs)("div",{className:"px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium border border-primary/20 hover:bg-primary/20 hover:scale-105 transition-all duration-300 shadow-md",children:[e.count," articles",e.loaded&&e.blogs.length!==e.count&&(0,x.jsxs)("span",{className:"ml-1 text-xs opacity-70",children:["(",e.blogs.length," loaded)"]})]}),(0,x.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[t?x.jsx("span",{className:"text-xs font-medium",children:"Loading..."}):x.jsx("span",{className:"text-xs font-medium",children:n?"Collapse":"Expand"}),x.jsx("div",{className:"p-1 rounded-md hover:bg-muted/50 transition-colors",children:t?x.jsx(A.Z,{className:"w-4 h-4 animate-spin"}):n?x.jsx(U.Z,{className:"w-4 h-4 transition-transform duration-300"}):x.jsx(B.Z,{className:"w-4 h-4 transition-transform duration-300"})})]})]})})]})]}),x.jsx("div",{className:(0,R.cn)("space-y-12 transition-all duration-700 ease-in-out overflow-hidden",n?"max-h-[10000px] opacity-100 transform scale-100 translate-y-0":"max-h-0 opacity-0 transform scale-95 -translate-y-4"),style:{transformOrigin:"top center"},children:(0,x.jsxs)(x.Fragment,{children:[t&&x.jsx("div",{className:"flex items-center justify-center py-8",children:(0,x.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[x.jsx(A.Z,{className:"w-6 h-6 animate-spin text-primary"}),x.jsx("p",{className:"text-sm text-muted-foreground",children:"Loading articles..."})]})}),e.loaded&&e.blogs.length>0&&x.jsx("div",{className:"space-y-12",children:e.blogs.map((a,t)=>{let o=t===e.blogs.length-1,s=i===b.length-1;return(0,x.jsxs)("div",{className:"relative group/article",children:[(0,x.jsxs)("div",{className:"absolute left-5 top-8 w-6 h-6 rounded-full bg-background border-3 border-primary shadow-xl shadow-primary/20 z-10 group-hover/article:scale-125 group-hover/article:shadow-2xl group-hover/article:shadow-primary/30 transition-all duration-500 timeline-node",style:{transform:"perspective(500px) translateZ(0)",transformStyle:"preserve-3d"},children:[x.jsx("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-primary via-primary/80 to-primary/60 animate-pulse-soft"}),x.jsx("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-white/20 via-transparent to-transparent"}),x.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-br from-primary/30 to-transparent animate-glow-pulse"}),!o&&n&&x.jsx("div",{className:"absolute top-6 left-1/2 transform -translate-x-1/2 w-0.5 h-16 bg-gradient-to-b from-primary/50 via-primary/20 to-border shadow-sm"})]}),x.jsx("div",{className:"absolute left-16 top-6 px-3 py-1 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 text-primary text-xs font-medium border border-primary/20 shadow-md backdrop-blur-sm",children:new Date(a.display_date).toLocaleDateString("en-US",{month:"short"})}),x.jsx("div",{className:"ml-24 group-hover/article:translate-x-2 transition-transform duration-500",children:x.jsx(z,{blog:a,index:100*i+t,className:"shadow-lg hover:shadow-2xl transition-all duration-500 border-l-4 border-l-primary/30 hover:border-l-primary"})}),o&&!s&&n&&x.jsx("div",{className:"absolute left-8 top-full w-0.5 h-20 bg-gradient-to-b from-border via-border/50 to-transparent shadow-sm"})]},a.slug)})}),e.loaded&&0===e.blogs.length&&x.jsx("div",{className:"flex items-center justify-center py-8",children:(0,x.jsxs)("p",{className:"text-sm text-muted-foreground",children:["No articles found for ",e.year]})})]})})]},e.year)})}),(0,x.jsxs)("div",{className:"relative mt-20",children:[x.jsx("div",{className:"absolute left-4 w-8 h-8 rounded-full bg-gradient-to-br from-primary via-blue-500 to-secondary shadow-xl shadow-primary/20 flex items-center justify-center animate-glow-pulse",children:x.jsx("div",{className:"w-3 h-3 rounded-full bg-white shadow-sm"})}),(0,x.jsxs)("div",{className:"ml-24 p-4 rounded-xl bg-gradient-to-r from-muted/50 to-transparent border border-border/30 backdrop-blur-sm",children:[x.jsx("div",{className:"text-sm text-muted-foreground italic font-medium",children:"✨ End of timeline"}),x.jsx("div",{className:"text-xs text-muted-foreground/70 mt-1",children:"Thanks for reading through my journey"})]})]})]})},iv=({tags:e,blogs:a,searchTerm:i,tagSlug:n})=>{let t=(0,f.useMemo)(()=>a.length>0?[...a].sort((e,a)=>new Date(a.display_date).getTime()-new Date(e.display_date).getTime()):[],[a]),[o,s]=(0,f.useState)(t);return((0,f.useEffect)(()=>{s(t)},[t]),a&&0!==a.length)?(0,x.jsxs)("div",{className:"space-y-12",children:[x.jsx("div",{className:"space-y-8 relative z-[2000]",children:(0,x.jsxs)("div",{className:"relative",children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 rounded-2xl"}),(0,x.jsxs)("div",{className:"relative p-6 rounded-2xl border border-border/50 backdrop-blur-sm",children:[(0,x.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[x.jsx("div",{className:"p-2 rounded-lg bg-primary/10 text-primary",children:x.jsx(h.Z,{className:"w-5 h-5"})}),x.jsx("h3",{className:"text-lg font-semibold",children:"Search & Filter Articles"})]}),x.jsx(w,{blogs:a,tags:e,onFilterChange:e=>{s(e)},className:"w-full"})]})]})}),x.jsx(ih,{filteredBlogs:o})]}):x.jsx("div",{className:"space-y-12",children:(0,x.jsxs)("div",{className:"relative",children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 rounded-2xl"}),x.jsx("div",{className:"relative p-6 rounded-2xl border border-border/50 backdrop-blur-sm",children:(0,x.jsxs)("div",{className:"text-center py-8",children:[x.jsx("p",{className:"text-muted-foreground mb-2",children:"No articles found"}),i&&x.jsx("p",{className:"text-sm text-muted-foreground",children:"Try adjusting your search terms"})]})})]})})}},75621:(e,a,i)=>{"use strict";i.r(a),i.d(a,{AnimatedSection:()=>c,FloatingElement:()=>u,PageTransition:()=>r,PulseOnHover:()=>d,ScrollReveal:()=>l,StaggeredList:()=>p});var n=i(10326),t=i(17577),o=i(35047),s=i(51223);function r({children:e,className:a}){(0,o.usePathname)();let[i,r]=(0,t.useState)(!1),[c,p]=(0,t.useState)(e);return(0,n.jsxs)("div",{className:(0,s.cn)("relative",a),children:[i&&n.jsx("div",{className:"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-fade-in",children:n.jsx("div",{className:"flex items-center justify-center h-full",children:(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[n.jsx("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin-slow"}),n.jsx("p",{className:"text-sm text-muted-foreground animate-pulse-soft",children:"加载中..."})]})})}),n.jsx("div",{className:(0,s.cn)("transition-all duration-300 ease-smooth",i?"opacity-0 scale-95":"opacity-100 scale-100 animate-fade-in-up"),children:c})]})}function c({children:e,className:a,delay:i=0,direction:o="up"}){let[r,c]=(0,t.useState)(!1);return n.jsx("div",{className:(0,s.cn)("transition-all duration-500 ease-smooth",r?({up:"animate-fade-in-up",down:"animate-fade-in-down",left:"animate-slide-in-left",right:"animate-slide-in-right"})[o]:"opacity-0 translate-y-4",a),children:e})}function p({children:e,className:a,staggerDelay:i=100}){return n.jsx("div",{className:a,children:e.map((e,a)=>n.jsx(c,{delay:a*i,className:"mb-4 last:mb-0",children:e},a))})}function l({children:e,className:a,threshold:i=.1,rootMargin:o="0px 0px -50px 0px"}){let[r,c]=(0,t.useState)(!1),[p,l]=(0,t.useState)(null);return n.jsx("div",{ref:l,className:(0,s.cn)("transition-all duration-700 ease-smooth",r?"opacity-100 translate-y-0 scale-100":"opacity-0 translate-y-8 scale-95",a),children:e})}function u({children:e,className:a,intensity:i="normal"}){return n.jsx("div",{className:(0,s.cn)("transition-transform duration-300 ease-smooth",{subtle:"hover:translate-y-[-2px]",normal:"hover:translate-y-[-4px]",strong:"hover:translate-y-[-8px]"}[i],a),children:e})}function d({children:e,className:a}){return n.jsx("div",{className:(0,s.cn)("transition-all duration-300 ease-smooth hover:animate-pulse-soft",a),children:e})}},23201:(e,a,i)=>{"use strict";i.d(a,{AS:()=>s,K$:()=>o,WD:()=>r});let n=["#10B981","#059669","#22C55E","#16A34A","#84CC16","#60A5FA","#3B82F6","#0EA5E9","#38BDF8","#06B6D4","#A78BFA","#8B5CF6","#A855F7","#C084FC","#DDD6FE","#FB923C","#F97316","#FDBA74","#FED7AA","#FEF3C7","#F472B6","#EC4899","#F9A8D4","#FBCFE8","#FCE7F3","#22D3EE","#06B6D4","#67E8F9","#A5F3FC","#CFFAFE","#FDE047","#EAB308","#FEF08A","#FEFCE8","#FFFBEB","#94A3B8","#64748B","#CBD5E1","#E2E8F0","#F1F5F9"],t={getLuminance(e){let a=this.hexToRgb(e);if(!a)return 0;let{r:i,g:n,b:t}=a;return .2126*i+.7152*n+.0722*t},hexToRgb(e){let a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return a?{r:parseInt(a[1],16),g:parseInt(a[2],16),b:parseInt(a[3],16)}:null},getContrastColor(e){return this.getLuminance(e)>128?"#000000":"#FFFFFF"},withOpacity(e,a){let i=this.hexToRgb(e);return i?`rgba(${i.r}, ${i.g}, ${i.b}, ${a})`:e},getShadow(e,a=.25){return`0 4px 12px ${this.withOpacity(e,a)}`},getGradient(e,a="to right"){let i=this.withOpacity(e,.8),n=this.withOpacity(e,.6);return`linear-gradient(${a}, ${i}, ${n})`}},o={getTagStyle(e,a="default"){let i=e&&e.startsWith("#")?e:n[0];switch(a){case"minimal":return{backgroundColor:t.withOpacity(i,.08),color:t.withOpacity(i,.8),border:`1px solid ${t.withOpacity(i,.15)}`,boxShadow:"none"};case"soft":return{backgroundColor:t.withOpacity(i,.12),color:t.withOpacity(i,.9),border:`1px solid ${t.withOpacity(i,.2)}`,boxShadow:`0 1px 3px ${t.withOpacity(i,.1)}`};case"outline":return{backgroundColor:"transparent",color:t.withOpacity(i,.8),border:`1px solid ${t.withOpacity(i,.3)}`,boxShadow:`0 1px 2px ${t.withOpacity(i,.05)}`};default:return{backgroundColor:t.withOpacity(i,.15),color:t.withOpacity(i,.9),border:`1px solid ${t.withOpacity(i,.25)}`,boxShadow:`0 2px 4px ${t.withOpacity(i,.1)}`}}},getTagClasses(e,a="default"){let i="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 cursor-pointer relative overflow-hidden";return e&&e.startsWith("#")?i:`${i} bg-muted/50 text-muted-foreground border border-border/50`}},s={primary:{main:"#10B981",dark:"#059669",light:"#6EE7B7",deeper:"#047857",lighter:"#A7F3D0",subtle:"#D1FAE5"},gradients:{primary:"linear-gradient(135deg, #10B981 0%, #059669 100%)",subtle:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",card:"linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(5, 150, 105, 0.08) 100%)",glow:"radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%)",overlay:"linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%)"},shadows:{card:"0 4px 12px rgba(16, 185, 129, 0.15)",cardHover:"0 8px 25px rgba(16, 185, 129, 0.25)",glow:"0 0 20px rgba(16, 185, 129, 0.3)",glowIntense:"0 0 40px rgba(16, 185, 129, 0.4)"},animations:{duration:{fast:"200ms",normal:"300ms",slow:"500ms",slower:"700ms"},easing:{smooth:"cubic-bezier(0.4, 0, 0.2, 1)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)",elastic:"cubic-bezier(0.175, 0.885, 0.32, 1.275)"}}},r={getTimelineCardStyle:()=>({background:s.gradients.card,border:`1px solid ${t.withOpacity(s.primary.main,.2)}`,borderRadius:"1rem",boxShadow:s.shadows.card,backdropFilter:"blur(10px)",transition:`all ${s.animations.duration.normal} ${s.animations.easing.smooth}`}),getAlbumCardStyle:()=>({background:s.gradients.subtle,border:`1px solid ${t.withOpacity(s.primary.main,.15)}`,borderRadius:"1.5rem",boxShadow:s.shadows.card,backdropFilter:"blur(8px)",transition:`all ${s.animations.duration.slow} ${s.animations.easing.smooth}`}),getGridCardStyle:()=>({borderRadius:"0.75rem",overflow:"hidden",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:`all ${s.animations.duration.normal} ${s.animations.easing.smooth}`}),getHoverStyle:()=>({transform:"translateY(-8px) scale(1.02)",boxShadow:s.shadows.cardHover,borderColor:t.withOpacity(s.primary.main,.4)})};o.getTagStyle(),o.getTagClasses()},22428:(e,a,i)=>{"use strict";function n(e){if(!e)return"Unknown date";let a=new Date(e);return isNaN(a.getTime())?"Unknown date":a.toLocaleDateString("en-US",{day:"numeric",month:"long",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1})}i.d(a,{p:()=>n})},55782:(e,a,i)=>{"use strict";i.d(a,{default:()=>t.a});var n=i(34567),t=i.n(n)},34567:(e,a,i)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"default",{enumerable:!0,get:function(){return o}});let n=i(53370);i(19510),i(71159);let t=n._(i(26155));function o(e,a){var i;let n={loading:e=>{let{error:a,isLoading:i,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let o={...n,...a};return(0,t.default)({...o,modules:null==(i=o.loadableGenerated)?void 0:i.modules})}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},13689:(e,a,i)=>{"use strict";let{createProxy:n}=i(68570);e.exports=n("/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js")},26155:(e,a,i)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"default",{enumerable:!0,get:function(){return p}});let n=i(19510),t=i(71159),o=i(13689),s=i(44459);function r(e){return{default:e&&"default"in e?e.default:e}}let c={loader:()=>Promise.resolve(r(()=>null)),loading:null,ssr:!0},p=function(e){let a={...c,...e},i=(0,t.lazy)(()=>a.loader().then(r)),p=a.loading;function l(e){let r=p?(0,n.jsx)(p,{isLoading:!0,pastDelay:!0,error:null}):null,c=a.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.PreloadCss,{moduleIds:a.modules}),(0,n.jsx)(i,{...e})]}):(0,n.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(i,{...e})});return(0,n.jsx)(t.Suspense,{fallback:r,children:c})}return l.displayName="LoadableComponent",l}},44459:(e,a,i)=>{"use strict";let{createProxy:n}=i(68570);e.exports=n("/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js")},30532:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>g,generateMetadata:()=>b,revalidate:()=>v,runtime:()=>h});var n=i(19510),t=i(91925),o=i(64001),s=i(96917),r=i(27162);let c=(0,r.Z)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),p=(0,r.Z)("Tags",[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]]);function l({title:e="Blog",intro:a="Thoughts, insights, and stories from my journey in technology and design.",children:i,tags:r=[],totalArticles:l=0}){let u=r.length;return(0,n.jsxs)("div",{className:"min-h-screen",children:[n.jsx(t.W2,{className:"mt-16 sm:mt-24",children:(0,n.jsxs)("div",{className:"relative mb-16 rounded-3xl overflow-hidden",children:[n.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/12 via-blue-500/5 to-secondary/12"}),n.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-background/95 via-background/85 to-background/95"}),n.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent"}),n.jsx("div",{className:"absolute top-8 right-8 w-40 h-40 bg-gradient-radial from-primary/15 via-primary/5 to-transparent rounded-full blur-3xl animate-glow-pulse"}),n.jsx("div",{className:"absolute bottom-12 left-12 w-32 h-32 bg-gradient-radial from-secondary/15 via-secondary/5 to-transparent rounded-full blur-2xl animate-glow-pulse",style:{animationDelay:"1s"}}),n.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-radial from-blue-500/3 via-transparent to-transparent rounded-full blur-3xl animate-glow-pulse",style:{animationDelay:"2s"}}),(0,n.jsxs)("div",{className:"relative py-16 px-8 lg:py-20 lg:px-12 min-h-[500px]",children:[n.jsx("div",{className:"relative z-10 max-w-4xl",children:(0,n.jsxs)(o.Zu,{className:"space-y-8",children:[(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("h1",{className:"text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl",children:n.jsx(s.t1,{variant:"primary",animate:!0,children:e})}),n.jsx("p",{className:"text-lg text-muted-foreground leading-relaxed max-w-3xl",children:a})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-6 pt-8",children:[n.jsx("div",{className:"group",children:(0,n.jsxs)("div",{className:"flex items-center gap-3 p-5 rounded-2xl bg-background/60 border border-border/50 backdrop-blur-sm transition-all duration-500 hover:bg-background/90 hover:border-primary/30 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1 enhanced-hover relative overflow-hidden",children:[n.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),n.jsx("div",{className:"p-3 rounded-xl bg-primary/10 text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300 relative z-10",children:n.jsx(c,{className:"w-5 h-5"})}),(0,n.jsxs)("div",{className:"relative z-10",children:[n.jsx("div",{className:"text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300",children:l}),n.jsx("div",{className:"text-sm text-muted-foreground",children:"Articles"})]})]})}),n.jsx("div",{className:"group",children:(0,n.jsxs)("div",{className:"flex items-center gap-3 p-5 rounded-2xl bg-background/60 border border-border/50 backdrop-blur-sm transition-all duration-500 hover:bg-background/90 hover:border-green-500/30 hover:shadow-xl hover:shadow-green-500/10 hover:-translate-y-1 enhanced-hover relative overflow-hidden",children:[n.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),n.jsx("div",{className:"p-3 rounded-xl bg-green-500/10 text-green-500 group-hover:bg-green-500/20 group-hover:scale-110 transition-all duration-300 relative z-10",children:n.jsx(p,{className:"w-5 h-5"})}),(0,n.jsxs)("div",{className:"relative z-10",children:[n.jsx("div",{className:"text-2xl font-bold text-foreground group-hover:text-green-500 transition-colors duration-300",children:u}),n.jsx("div",{className:"text-sm text-muted-foreground",children:"Topics"})]})]})})]})]})}),n.jsx(o.Zu,{delay:200,direction:"right",className:"absolute top-1/2 right-8 transform -translate-y-1/2 hidden xl:block",children:(0,n.jsxs)("div",{className:"relative w-48 h-48",children:[n.jsx("div",{className:"absolute top-6 right-6 w-4 h-4 bg-primary/50 rounded-full blur-sm animate-pulse-soft shadow-lg shadow-primary/20"}),n.jsx("div",{className:"absolute bottom-12 left-8 w-3 h-3 bg-secondary/50 rounded-full blur-sm animate-pulse-soft shadow-lg shadow-secondary/20",style:{animationDelay:"1s"}}),n.jsx("div",{className:"absolute top-1/2 left-3 w-2 h-2 bg-blue-400/50 rounded-full blur-sm animate-pulse-soft shadow-lg shadow-blue-400/20",style:{animationDelay:"2s"}}),n.jsx("div",{className:"absolute bottom-6 right-12 w-3.5 h-3.5 bg-green-400/40 rounded-full blur-sm animate-pulse-soft shadow-lg shadow-green-400/20",style:{animationDelay:"3s"}}),n.jsx("div",{className:"absolute top-8 left-1/2 w-2.5 h-2.5 bg-purple-400/40 rounded-full blur-sm animate-pulse-soft shadow-lg shadow-purple-400/20",style:{animationDelay:"4s"}}),n.jsx("div",{className:"absolute inset-0 bg-gradient-radial from-primary/25 via-primary/8 to-transparent rounded-full animate-glow-pulse"}),n.jsx("div",{className:"absolute inset-4 bg-gradient-radial from-blue-500/15 via-transparent to-transparent rounded-full animate-glow-pulse",style:{animationDelay:"1s"}}),n.jsx("div",{className:"absolute inset-8 bg-gradient-radial from-secondary/20 via-transparent to-transparent rounded-full animate-glow-pulse",style:{animationDelay:"2s"}})]})})]})]})}),n.jsx(t.W2,{children:i})]})}var u=i(70687),d=i(53722),m=i(55782),x=i(59979);let f=(0,m.default)(()=>i.e(259).then(i.bind(i,11259)).then(e=>({default:e.EnhancedBlogList})),{loadableGenerated:{modules:["app/blogs/page.tsx -> @/components/blog/EnhancedBlogList"]},loading:()=>n.jsx("div",{className:"space-y-4",children:Array.from({length:6}).map((e,a)=>(0,n.jsxs)("div",{className:"animate-pulse",children:[n.jsx("div",{className:"bg-muted rounded-lg h-32 mb-4"}),(0,n.jsxs)("div",{className:"space-y-2",children:[n.jsx("div",{className:"bg-muted rounded h-4 w-3/4"}),n.jsx("div",{className:"bg-muted rounded h-4 w-1/2"})]})]},a))}),ssr:!0}),h="nodejs",v=30;async function b(){try{return await (0,x.ve)("blogs")}catch(e){return{title:"Blogs",description:u.Ul}}}async function g({searchParams:e}){let a=e?.search,i=e?.tag,[t,s,r]=await Promise.all([(0,u.hT)(),(0,u.L)(a,i),(0,d.$2)()]),c=r.blogs.title,p=r.blogs.description,m=null;if(i){let e=t.find(e=>(e.slug||e.id.toString())===i);m=e?e.name:i,c=`Tag: ${m}`,p=`Exploring articles tagged with "${m}".`}if(a){let e=m;c=`Search Results: "${a}"`,p=`Search results for "${a}"${e?` in "${e}"`:""}.`}return n.jsx(l,{title:c,intro:p,tags:t,totalArticles:s.length,children:n.jsx(o.Zu,{children:n.jsx(f,{tags:t,blogs:s,searchTerm:a,tagSlug:i})})})}},64001:(e,a,i)=>{"use strict";i.d(a,{$m:()=>o,Zu:()=>t});var n=i(68570);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#PageTransition`);let t=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#AnimatedSection`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#StaggeredList`);let o=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#ScrollReveal`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#FloatingElement`),(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#PulseOnHover`)},96917:(e,a,i)=>{"use strict";i.d(a,{t1:()=>c});var n=i(19510),t=i(71159),o=i(55761),s=i(62386);function r(...e){return(0,s.m6)((0,o.W)(e))}let c=(0,t.forwardRef)(({variant:e="primary",animate:a=!1,className:i,children:t,...o},s)=>n.jsx("span",{ref:s,className:r("bg-clip-text text-transparent font-semibold",{primary:"bg-gradient-to-r from-primary to-primary/70",secondary:"bg-gradient-to-r from-secondary to-muted",rainbow:"bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600",ocean:"bg-gradient-to-r from-blue-500 via-teal-500 to-green-500"}[e],a&&"animate-pulse-soft",i),...o,children:t}));c.displayName="GradientText",(0,t.forwardRef)(({intensity:e="medium",tint:a="none",className:i,children:t,...o},s)=>n.jsx("div",{ref:s,className:r("rounded-xl border backdrop-blur-md","dark:bg-black/10 dark:border-white/10",{light:"bg-white/5 backdrop-blur-sm border-white/10",medium:"bg-white/10 backdrop-blur-md border-white/20",strong:"bg-white/20 backdrop-blur-lg border-white/30"}[e],{none:"",primary:"bg-primary/5 border-primary/20",secondary:"bg-secondary/5 border-secondary/20"}[a],i),...o,children:t})).displayName="GlassCard",(0,t.forwardRef)(({variant:e="raised",size:a="md",className:i,children:t,...o},s)=>n.jsx("div",{ref:s,className:r("bg-background border-0 transition-all duration-300",{raised:"shadow-[8px_8px_16px_rgba(0,0,0,0.1),-8px_-8px_16px_rgba(255,255,255,0.1)]",inset:"shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)]",flat:"shadow-[0_0_0_1px_rgba(0,0,0,0.05)]"}[e],{sm:"p-4 rounded-lg",md:"p-6 rounded-xl",lg:"p-8 rounded-2xl"}[a],i),...o,children:t})).displayName="NeumorphismCard",(0,t.forwardRef)(({color:e="primary",intensity:a="medium",animate:i=!1,className:t,children:o,...s},c)=>n.jsx("div",{ref:c,className:r("transition-all duration-300",{primary:"shadow-primary/50",secondary:"shadow-secondary/50",success:"shadow-green-500/50",warning:"shadow-yellow-500/50",error:"shadow-red-500/50"}[e],{subtle:"shadow-lg",medium:"shadow-xl",strong:"shadow-2xl"}[a],i&&"animate-pulse-soft",t),...s,children:o})).displayName="GlowEffect",(0,t.forwardRef)(({pattern:e="dots",opacity:a=.1,className:i,children:t,...o},s)=>{let c={dots:{backgroundImage:`radial-gradient(circle, currentColor ${100*a}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},grid:{backgroundImage:`linear-gradient(currentColor ${100*a}% 1px, transparent 1px), linear-gradient(90deg, currentColor ${100*a}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},diagonal:{backgroundImage:`repeating-linear-gradient(45deg, transparent, transparent 10px, currentColor ${100*a}% 10px, currentColor ${100*a}% 11px)`},waves:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000' fill-opacity='${a}'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}};return n.jsx("div",{ref:s,className:r("relative",i),style:c[e],...o,children:t})}).displayName="PatternBackground",(0,t.forwardRef)(({gradient:e="rainbow",width:a=2,animate:i=!1,className:t,children:o,...s},c)=>n.jsx("div",{ref:c,className:r("relative rounded-xl overflow-hidden",i&&"animate-pulse-soft",t),...s,children:n.jsx("div",{className:r("absolute inset-0 bg-gradient-to-r",{rainbow:"from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"from-orange-500 via-pink-500 to-purple-600",ocean:"from-blue-500 via-teal-500 to-green-500",forest:"from-green-600 via-emerald-500 to-teal-500",custom:"from-primary via-secondary to-accent"}[e],i&&"animate-spin-slow"),style:{padding:`${a}px`},children:n.jsx("div",{className:"w-full h-full bg-background rounded-xl",children:o})})})).displayName="ColorfulBorder"},70687:(e,a,i)=>{"use strict";i.d(a,{L:()=>o,Ul:()=>t,hT:()=>c,rR:()=>r,v3:()=>s});var n=i(53722);let t="Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation.",o=async function(e,a,i){try{let t=new URLSearchParams;t.append("published_only","true"),t.append("limit","100"),i||t.append("article_type","blog"),e&&t.append("search",e),a&&t.append("tag",a);let o=`${n.CT}/blogs?${t.toString()}`,s=!e&&!a,r=await fetch(o,{...s?{next:{revalidate:60,tags:["blogs"]}}:{cache:"no-store"},headers:{"Cache-Control":s?"public, s-maxage=60, stale-while-revalidate=120":"no-cache"}});if(!r.ok)throw Error(`Failed to fetch blogs: ${r.status}`);return await r.json()||[]}catch(e){return[]}};async function s(){try{let e=`${n.CT}/blogs/homepage-content`,a=await fetch(e,{next:{revalidate:180,tags:["homepage-content","blogs","projects"]},headers:{"Cache-Control":"public, s-maxage=180, stale-while-revalidate=360"}});if(!a.ok)throw Error(`Failed to fetch homepage content: ${a.status}`);return await a.json()}catch(e){return{blogs:[],projects:[],total_blogs:0,total_projects:0}}}async function r(e){try{let a=`${n.CT}/blogs/${e}`,i=await fetch(a,{next:{revalidate:300,tags:[`blog-${e}`,"blogs"]},headers:{"Cache-Control":"public, s-maxage=300, stale-while-revalidate=600"}});if(!i.ok)throw Error(`Failed to fetch blog: ${i.status}`);let t=await i.json();if(t&&"blog"===t.article_type)return t;return null}catch(e){return null}}async function c(){try{let e=`${n.CT}/tags/with-blogs/`,a=await fetch(e,{next:{revalidate:240,tags:["tags","blogs"]},headers:{"Cache-Control":"public, s-maxage=240, stale-while-revalidate=480"}});if(!a.ok)throw Error(`Failed to fetch tags with blogs: ${a.status}`);return await a.json()||[]}catch(e){return[]}}},53370:(e,a,i)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}i.r(a),i.d(a,{_:()=>n,_interop_require_default:()=>n})},40572:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}};var a=require("../../webpack-runtime.js");a.C(e);var i=e=>a(a.s=e),n=a.X(0,[948,499,151,466,722],()=>i(61906));module.exports=n})();