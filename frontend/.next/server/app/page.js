(()=>{var e={};e.id=931,e.ids=[931],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},89552:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d}),a(28921),a(48109),a(27683);var r=a(23191),o=a(88716),n=a(37922),s=a.n(n),i=a(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,28921)),"/home/<USER>/Code/me/My-web/frontend/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,48109)),"/home/<USER>/Code/me/My-web/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,27683)),"/home/<USER>/Code/me/My-web/frontend/src/app/not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9873))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/Code/me/My-web/frontend/src/app/page.tsx"],m="/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10767:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,79404,23)),Promise.resolve().then(a.bind(a,72677)),Promise.resolve().then(a.bind(a,65567)),Promise.resolve().then(a.bind(a,80301)),Promise.resolve().then(a.bind(a,24822)),Promise.resolve().then(a.bind(a,62876)),Promise.resolve().then(a.bind(a,91456)),Promise.resolve().then(a.bind(a,61711)),Promise.resolve().then(a.bind(a,78357)),Promise.resolve().then(a.bind(a,77669)),Promise.resolve().then(a.bind(a,75621))},77148:(e,t,a)=>{"use strict";var r=Object.create?function(e,t,a,r){void 0===r&&(r=a);var o=Object.getOwnPropertyDescriptor(t,a);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[a]}}),Object.defineProperty(e,r,o)}:function(e,t,a,r){void 0===r&&(r=a),e[r]=t[a]},o=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},n=function(){var e=function(t){return(e=Object.getOwnPropertyNames||function(e){var t=[];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[t.length]=a);return t})(t)};return function(t){if(t&&t.__esModule)return t;var a={};if(null!=t)for(var n=e(t),s=0;s<n.length;s++)"default"!==n[s]&&r(a,t,n[s]);return o(a,t),a}}();Object.defineProperty(t,"__esModule",{value:!0});let s=a(80786),i=n(a(17577));t.default=({url:e,src:t,alt:a,size:r=32,className:o="",timeout:n=3e3,border:l=!1,padding:d=0,background:c="transparent",borderRadius:m=0,lazy:u=!1,preferFallback:h=!1,preferSrc:p=!0})=>{let g=(0,s.getDomain)(e),[x,f]=(0,i.useState)(""),[b,y]=(0,i.useState)(0),[v,j]=(0,i.useState)(!0),[w,N]=(0,i.useState)(!1),[M,A]=(0,i.useState)(!1),C=[`https://${g}/favicon.ico`,`https://${g}/logo.svg`,`https://${g}/logo.png`,`https://${g}/apple-touch-icon.png`,`https://${g}/apple-touch-icon-precomposed.png`,`https://${g}/static/img/favicon.ico`,`https://${g}/static/img/favicon.png`,`https://${g}/img/favicon.png`,`https://${g}/img/favicon.ico`,`https://${g}/static/img/logo.svg`,`https://${g}/apple-touch-icon-precomposed.png`],k=[`https://favicon.im/${g}?larger=true`,`https://favicon.im/${g}`,`https://www.google.com/s2/favicons?domain=https://${g}&sz=64`,`https://www.google.com/s2/favicons?domain=http://${g}&sz=64`],E=h?[...k,...C]:[...C,...k];(0,i.useEffect)(()=>{M||(t?j(!1):f(E[0]),A(!0))},[M,E,t]),(0,i.useEffect)(()=>{let e;return v&&x&&!t&&(e=setTimeout(()=>{Z()},n)),()=>{e&&clearTimeout(e)}},[x,v,n,t]);let Z=()=>{let e=b+1;e<E.length?(y(e),f(E[e]),j(!0)):(N(!0),j(!1))};return i.default.createElement("div",{className:`relative inline-block 
        ${o} 
        ${l?"border":""} 
        ${w?"opacity-0":""}
        ${d?`p-[${d}px]`:""}
        ${m?`rounded-[${m}px]`:""}
        `,style:{width:r,height:r,background:c,padding:d?`${d}px`:0,borderRadius:m?`${m}px`:0}},v&&i.default.createElement("div",{className:"absolute inset-0 animate-pulse"},i.default.createElement("div",{className:"w-full h-full rounded-md bg-gray-200/60"})),(t||x)&&i.default.createElement("img",{src:t||x,alt:a||`${g} logo`,width:r,height:r,loading:u?"lazy":"eager",onError:Z,onLoad:()=>{j(!1),N(!1)},className:`inline-block transition-opacity duration-300 ${v?"opacity-0":"opacity-100"}`,style:{objectFit:"contain",display:w?"none":"inline-block"}}),w&&i.default.createElement("div",{className:"w-full h-full flex items-center justify-center bg-gray-100 rounded-md",style:{fontSize:`${.5*r}px`}},g.charAt(0).toUpperCase()))}},19335:function(e,t,a){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Favicon=void 0;var o=a(77148);Object.defineProperty(t,"Favicon",{enumerable:!0,get:function(){return r(o).default}})},80786:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDomain=void 0,t.getDomain=e=>{try{let t=e.startsWith("http")?e:`https://${e}`;return new URL(t).hostname.replace(/^www\./,"")}catch(t){return e}}},72677:(e,t,a)=>{"use strict";a.d(t,{BlogPreloader:()=>i});var r=a(10326),o=a(17577);let n=new Set,s=new Map;function i({slug:e,children:t}){let i=(0,o.useRef)(null),l=(0,o.useRef)(),d=(0,o.useCallback)(async()=>{if(!n.has(e)){if(s.has(e))return s.get(e);n.add(e);try{let t=fetch(`/api/blogs/${e}`,{method:"GET",headers:{"Cache-Control":"public, max-age=300, stale-while-revalidate=600"}}).then(e=>{if(e.ok)return e.json();throw Error(`Failed to prefetch blog: ${e.status}`)});s.set(e,t);let r=await t;return r?.content&&a.e(396).then(a.bind(a,13396)).then(({processMarkdownFast:e})=>{try{e(r.content)}catch(e){}}),r}catch(t){throw n.delete(e),s.delete(e),t}}},[e]);return(0,o.useCallback)(()=>{l.current&&clearTimeout(l.current),l.current=setTimeout(()=>{d().catch(()=>{})},100)},[d]),(0,o.useCallback)(()=>{l.current&&(clearTimeout(l.current),l.current=void 0)},[]),(0,o.useCallback)(()=>{d().catch(()=>{})},[d]),r.jsx("div",{ref:i,"data-blog-slug":e,className:"w-full h-full",children:t})}},65567:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var r=a(10326),o=a(17577),n=a(13153);let s=new Map([["bold",o.createElement(o.Fragment,null,o.createElement("path",{d:"M100,100a12,12,0,0,1,12-12h32a12,12,0,0,1,0,24H112A12,12,0,0,1,100,100ZM236,68V196a20,20,0,0,1-20,20H40a20,20,0,0,1-20-20V68A20,20,0,0,1,40,48H76V40a28,28,0,0,1,28-28h48a28,28,0,0,1,28,28v8h36A20,20,0,0,1,236,68ZM100,48h56V40a4,4,0,0,0-4-4H104a4,4,0,0,0-4,4ZM44,72v35.23A180.06,180.06,0,0,0,128,128a180,180,0,0,0,84-20.78V72ZM212,192V133.94A204.27,204.27,0,0,1,128,152a204.21,204.21,0,0,1-84-18.06V192Z"}))],["duotone",o.createElement(o.Fragment,null,o.createElement("path",{d:"M224,118.31V200a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V118.31h0A191.14,191.14,0,0,0,128,144,191.08,191.08,0,0,0,224,118.31Z",opacity:"0.2"}),o.createElement("path",{d:"M104,112a8,8,0,0,1,8-8h32a8,8,0,0,1,0,16H112A8,8,0,0,1,104,112ZM232,72V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V72A16,16,0,0,1,40,56H80V48a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24v8h40A16,16,0,0,1,232,72ZM96,56h64V48a8,8,0,0,0-8-8H104a8,8,0,0,0-8,8ZM40,72v41.62A184.07,184.07,0,0,0,128,136a184,184,0,0,0,88-22.39V72ZM216,200V131.63A200.25,200.25,0,0,1,128,152a200.19,200.19,0,0,1-88-20.36V200H216Z"}))],["fill",o.createElement(o.Fragment,null,o.createElement("path",{d:"M152,112a8,8,0,0,1-8,8H112a8,8,0,0,1,0-16h32A8,8,0,0,1,152,112Zm80-40V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V72A16,16,0,0,1,40,56H80V48a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24v8h40A16,16,0,0,1,232,72ZM96,56h64V48a8,8,0,0,0-8-8H104a8,8,0,0,0-8,8Zm120,57.61V72H40v41.61A184,184,0,0,0,128,136,184,184,0,0,0,216,113.61Z"}))],["light",o.createElement(o.Fragment,null,o.createElement("path",{d:"M106,112a6,6,0,0,1,6-6h32a6,6,0,0,1,0,12H112A6,6,0,0,1,106,112ZM230,72V200a14,14,0,0,1-14,14H40a14,14,0,0,1-14-14V72A14,14,0,0,1,40,58H82V48a22,22,0,0,1,22-22h48a22,22,0,0,1,22,22V58h42A14,14,0,0,1,230,72ZM94,58h68V48a10,10,0,0,0-10-10H104A10,10,0,0,0,94,48ZM38,72v42.79A186,186,0,0,0,128,138a185.91,185.91,0,0,0,90-23.22V72a2,2,0,0,0-2-2H40A2,2,0,0,0,38,72ZM218,200V128.37A198.12,198.12,0,0,1,128,150a198.05,198.05,0,0,1-90-21.62V200a2,2,0,0,0,2,2H216A2,2,0,0,0,218,200Z"}))],["regular",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216,56H176V48a24,24,0,0,0-24-24H104A24,24,0,0,0,80,48v8H40A16,16,0,0,0,24,72V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V72A16,16,0,0,0,216,56ZM96,48a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96ZM216,72v41.61A184,184,0,0,1,128,136a184.07,184.07,0,0,1-88-22.38V72Zm0,128H40V131.64A200.19,200.19,0,0,0,128,152a200.25,200.25,0,0,0,88-20.37V200ZM104,112a8,8,0,0,1,8-8h32a8,8,0,0,1,0,16H112A8,8,0,0,1,104,112Z"}))],["thin",o.createElement(o.Fragment,null,o.createElement("path",{d:"M108,112a4,4,0,0,1,4-4h32a4,4,0,0,1,0,8H112A4,4,0,0,1,108,112ZM228,72V200a12,12,0,0,1-12,12H40a12,12,0,0,1-12-12V72A12,12,0,0,1,40,60H84V48a20,20,0,0,1,20-20h48a20,20,0,0,1,20,20V60h44A12,12,0,0,1,228,72ZM92,60h72V48a12,12,0,0,0-12-12H104A12,12,0,0,0,92,48ZM36,72v44a188,188,0,0,0,92,24,188,188,0,0,0,92-24V72a4,4,0,0,0-4-4H40A4,4,0,0,0,36,72ZM220,200V125.1A196.06,196.06,0,0,1,128,148a196,196,0,0,1-92-22.9V200a4,4,0,0,0,4,4H216A4,4,0,0,0,220,200Z"}))]]),i=o.forwardRef((e,t)=>o.createElement(n.Z,{ref:t,...e,weights:s}));i.displayName="BriefcaseIcon";var l=a(78357);function d({careerItem:e}){return(0,r.jsxs)("li",{className:"group/item flex gap-4 p-3 rounded-lg transition-all duration-300 hover:bg-muted/20 hover:scale-[1.02]",children:[(0,r.jsxs)("div",{className:"relative mt-1 flex h-10 w-10 flex-none items-center justify-center rounded-full shadow-md border border-muted bg-background transition-all duration-300 group-hover/item:shadow-lg group-hover/item:border-primary/30 group-hover/item:bg-primary/5",children:[r.jsx("div",{className:"absolute inset-0 rounded-full bg-primary/20 blur opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"}),e.logo?r.jsx(l.CustomIcon,{name:e.logo}):r.jsx(i,{size:20,weight:"duotone",className:"relative text-primary transition-all duration-300 group-hover/item:scale-110"})]}),(0,r.jsxs)("dl",{className:"flex flex-auto flex-wrap gap-x-2",children:[r.jsx("dt",{className:"sr-only",children:"Company"}),(0,r.jsxs)("dd",{className:"w-full flex-none text-sm font-medium transition-colors duration-300 group-hover/item:text-primary",children:[e.company,e.is_current&&r.jsx("span",{className:"ml-2 text-green-500 text-xs",children:"(Current)"})]}),r.jsx("dt",{className:"sr-only",children:"Position"}),r.jsx("dd",{className:"text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/80",children:e.position}),e.location&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("dt",{className:"sr-only",children:"Location"}),(0,r.jsxs)("dd",{className:"text-xs text-muted-foreground",children:["• ",e.location]})]}),r.jsx("dt",{className:"sr-only",children:"Date"}),(0,r.jsxs)("dd",{className:"ml-auto text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/70","aria-label":`${e.start_date} until ${e.end_date||"Present"}`,children:[e.start_date," - ",e.end_date||"Present"]}),e.description&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("dt",{className:"sr-only",children:"Description"}),r.jsx("dd",{className:"w-full text-xs text-muted-foreground mt-1",children:e.description})]})]})]})}function c(){let[e,t]=(0,o.useState)([]),[a,n]=(0,o.useState)(!0);return a||0!==e.length?(0,r.jsxs)("div",{className:"rounded-2xl border border-muted shadow-sm p-6",children:[(0,r.jsxs)("h2",{className:"flex text-sm font-semibold",children:[r.jsx(i,{size:24,weight:"duotone"}),r.jsx("span",{className:"ml-3",children:"Work"})]}),a?r.jsx("div",{className:"mt-6 space-y-4",children:[1,2].map(e=>(0,r.jsxs)("div",{className:"animate-pulse flex gap-4 p-3",children:[r.jsx("div",{className:"w-10 h-10 bg-muted rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[r.jsx("div",{className:"h-4 bg-muted rounded w-3/4"}),r.jsx("div",{className:"h-3 bg-muted rounded w-1/2"})]})]},e))}):r.jsx("ol",{className:"mt-6 space-y-4",children:e.map(e=>r.jsx(d,{careerItem:e},e.id))})]}):null}},80301:(e,t,a)=>{"use strict";a.d(t,{default:()=>u});var r=a(10326),o=a(77328),n=a(17577),s=a(13153);let i=new Map([["bold",n.createElement(n.Fragment,null,n.createElement("path",{d:"M227.79,52.62l-96-32a11.85,11.85,0,0,0-7.58,0l-96,32A12,12,0,0,0,20,63.37,6.05,6.05,0,0,0,20,64v80a12,12,0,0,0,24,0V80.65l23.71,7.9a67.92,67.92,0,0,0,18.42,85A100.36,100.36,0,0,0,46,209.44a12,12,0,1,0,20.1,13.11C80.37,200.59,103,188,128,188s47.63,12.59,61.95,34.55a12,12,0,1,0,20.1-13.11,100.36,100.36,0,0,0-40.18-35.92,67.92,67.92,0,0,0,18.42-85l39.5-13.17a12,12,0,0,0,0-22.76Zm-99.79-8L186.05,64,128,83.35,70,64ZM172,120A44,44,0,1,1,90.94,96.29l33.27,11.09a11.89,11.89,0,0,0,7.58,0l33.27-11.09A43.85,43.85,0,0,1,172,120Z"}))],["duotone",n.createElement(n.Fragment,null,n.createElement("path",{d:"M224,64,128,96,32,64l96-32Z",opacity:"0.2"}),n.createElement("path",{d:"M226.53,56.41l-96-32a8,8,0,0,0-5.06,0l-96,32a8,8,0,0,0-5.4,6.75A5,5,0,0,0,24,64v80a8,8,0,0,0,16,0V75.1L73.59,86.29a64,64,0,0,0,20.65,88.05c-18,7.06-33.56,19.83-44.94,37.29a8,8,0,1,0,13.4,8.74C77.77,197.25,101.57,184,128,184s50.23,13.25,65.3,36.37a8,8,0,0,0,13.4-8.74c-11.38-17.46-27-30.23-44.94-37.29a64,64,0,0,0,20.65-88l44.12-14.7a8,8,0,0,0,0-15.18ZM176,120A48,48,0,1,1,89.35,91.55l36.12,12a8,8,0,0,0,5.06,0l36.12-12A47.89,47.89,0,0,1,176,120ZM128,87.57,57.3,64,128,40.43,198.7,64Z"}))],["fill",n.createElement(n.Fragment,null,n.createElement("path",{d:"M226.53,56.41l-96-32a8,8,0,0,0-5.06,0l-96,32A8,8,0,0,0,24,64v80a8,8,0,0,0,16,0V75.1L73.59,86.29a64,64,0,0,0,20.65,88.05c-18,7.06-33.56,19.83-44.94,37.29a8,8,0,1,0,13.4,8.74C77.77,197.25,101.57,184,128,184s50.23,13.25,65.3,36.37a8,8,0,0,0,13.4-8.74c-11.38-17.46-27-30.23-44.94-37.29a64,64,0,0,0,20.65-88l44.12-14.7a8,8,0,0,0,0-15.18ZM176,120A48,48,0,1,1,89.35,91.55l36.12,12a8,8,0,0,0,5.06,0l36.12-12A47.89,47.89,0,0,1,176,120Z"}))],["light",n.createElement(n.Fragment,null,n.createElement("path",{d:"M225.9,58.31l-96-32a6,6,0,0,0-3.8,0l-96,32A6,6,0,0,0,26,64v80a6,6,0,0,0,12,0V72.32l38.68,12.9A62,62,0,0,0,99,174.75c-19.25,6.53-36,19.59-48,38A6,6,0,0,0,61,219.28C76.47,195.59,100.88,182,128,182s51.53,13.59,67,37.28A6,6,0,0,0,205,212.72c-12-18.38-28.73-31.44-48-38a62,62,0,0,0,22.27-89.53L225.9,69.69a6,6,0,0,0,0-11.38ZM178,120A50,50,0,1,1,88.63,89.2l37.47,12.49a6,6,0,0,0,3.8,0L167.37,89.2A49.78,49.78,0,0,1,178,120ZM128,89.68,51,64l77-25.68L205,64Z"}))],["regular",n.createElement(n.Fragment,null,n.createElement("path",{d:"M226.53,56.41l-96-32a8,8,0,0,0-5.06,0l-96,32A8,8,0,0,0,24,64v80a8,8,0,0,0,16,0V75.1L73.59,86.29a64,64,0,0,0,20.65,88.05c-18,7.06-33.56,19.83-44.94,37.29a8,8,0,1,0,13.4,8.74C77.77,197.25,101.57,184,128,184s50.23,13.25,65.3,36.37a8,8,0,0,0,13.4-8.74c-11.38-17.46-27-30.23-44.94-37.29a64,64,0,0,0,20.65-88l44.12-14.7a8,8,0,0,0,0-15.18ZM176,120A48,48,0,1,1,89.35,91.55l36.12,12a8,8,0,0,0,5.06,0l36.12-12A47.89,47.89,0,0,1,176,120ZM128,87.57,57.3,64,128,40.43,198.7,64Z"}))],["thin",n.createElement(n.Fragment,null,n.createElement("path",{d:"M225.27,60.21l-96-32a4,4,0,0,0-2.54,0l-96,32A4,4,0,0,0,28,64v80a4,4,0,0,0,8,0V69.55L79.88,84.18a60,60,0,0,0,24.54,91c-20.86,5.74-39,19.13-51.77,38.65a4,4,0,0,0,6.7,4.36C75.17,193.92,100.2,180,128,180s52.83,13.92,68.65,38.18a4,4,0,0,0,6.7-4.36c-12.72-19.52-30.91-32.91-51.77-38.65a60,60,0,0,0,24.54-91l49.15-16.39a4,4,0,0,0,0-7.58ZM180,120A52,52,0,1,1,87.93,86.86l38.8,12.93a3.95,3.95,0,0,0,2.54,0l38.8-12.93A51.85,51.85,0,0,1,180,120ZM128,91.78,44.65,64,128,36.22,211.35,64Z"}))]]),l=n.forwardRef((e,t)=>n.createElement(s.Z,{ref:t,...e,weights:i}));l.displayName="StudentIcon";var d=a(78357);let c={bachelor:{label:"Bachelor",color:"text-gray-700 dark:text-gray-300",iconColor:"text-gray-900 dark:text-gray-200",hoverColor:"group-hover/item:border-secondary/30 group-hover/item:bg-secondary/10"},master:{label:"Master",color:"text-blue-500",iconColor:"text-blue-500",hoverColor:"group-hover/item:border-blue-500/30 group-hover/item:bg-blue-50 dark:group-hover/item:bg-blue-950/20"},phd:{label:"PhD",color:"text-purple-500",iconColor:"text-purple-500",hoverColor:"group-hover/item:border-purple-500/30 group-hover/item:bg-purple-50 dark:group-hover/item:bg-purple-950/20"},diploma:{label:"Diploma",color:"text-orange-500",iconColor:"text-orange-500",hoverColor:"group-hover/item:border-orange-500/30 group-hover/item:bg-orange-50 dark:group-hover/item:bg-orange-950/20"},certificate:{label:"Certificate",color:"text-green-500",iconColor:"text-green-500",hoverColor:"group-hover/item:border-green-500/30 group-hover/item:bg-green-50 dark:group-hover/item:bg-green-950/20"}};function m({educationItem:e}){let t=c[e.degree_type]||c.bachelor;return(0,r.jsxs)("li",{className:"group/item flex gap-4 p-3 rounded-lg transition-all duration-300 hover:bg-muted/20 hover:scale-[1.02]",children:[(0,r.jsxs)("div",{className:`relative mt-1 flex h-10 w-10 flex-none items-center justify-center rounded-full shadow-md border border-muted bg-background transition-all duration-300 group-hover/item:shadow-lg ${t.hoverColor}`,children:[r.jsx("div",{className:`absolute inset-0 rounded-full bg-${"master"===e.degree_type?"blue":"phd"===e.degree_type?"purple":"diploma"===e.degree_type?"orange":"certificate"===e.degree_type?"green":"secondary"}-500/20 blur opacity-0 group-hover/item:opacity-100 transition-opacity duration-300`}),e.logo?r.jsx(d.CustomIcon,{name:e.logo}):r.jsx(o.X,{size:20,weight:"duotone",className:`relative ${t.iconColor} transition-all duration-300 group-hover/item:scale-110`})]}),(0,r.jsxs)("dl",{className:"flex flex-auto flex-wrap gap-x-2",children:[r.jsx("dt",{className:"sr-only",children:"学位类型"}),(0,r.jsxs)("dd",{className:`w-full flex-none text-xs font-medium ${t.color} mb-1 transition-colors duration-300`,children:[t.label,e.is_current&&r.jsx("span",{className:"ml-2 text-green-500",children:"(Current)"})]}),r.jsx("dt",{className:"sr-only",children:"School"}),r.jsx("dd",{className:"w-full flex-none text-sm font-medium transition-colors duration-300 group-hover/item:text-primary",children:e.school}),r.jsx("dt",{className:"sr-only",children:"Major"}),r.jsx("dd",{className:"text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/80",children:e.major}),r.jsx("dt",{className:"sr-only",children:"Date"}),(0,r.jsxs)("dd",{className:"ml-auto text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/70","aria-label":`${e.start_year} until ${e.end_year}`,children:[e.start_year," - ",e.end_year]}),e.gpa&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("dt",{className:"sr-only",children:"GPA"}),(0,r.jsxs)("dd",{className:"w-full text-xs text-muted-foreground",children:["GPA: ",e.gpa]})]})]})]})}function u(){let[e,t]=(0,n.useState)([]),[a,o]=(0,n.useState)(!0);if(!a&&0===e.length)return null;let s=e.filter(e=>"master"===e.degree_type),i=e.filter(e=>"bachelor"===e.degree_type),d=e.filter(e=>"phd"===e.degree_type),c=e.filter(e=>!["master","bachelor","phd"].includes(e.degree_type));return(0,r.jsxs)("div",{className:"group relative rounded-2xl border border-muted-foreground/20 shadow-sm p-6 bg-background/80 backdrop-blur-sm transition-all duration-500 hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5",children:[r.jsx("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx("div",{className:"absolute top-4 right-4 w-1.5 h-1.5 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("h2",{className:"flex items-center text-sm font-semibold mb-6 group-hover:text-primary transition-colors duration-300",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(l,{size:24,weight:"duotone",className:"transition-all duration-300 group-hover:scale-110 group-hover:text-primary"}),r.jsx("div",{className:"absolute inset-0 bg-primary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,r.jsxs)("span",{className:"ml-3 relative",children:["Education",r.jsx("div",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"})]})]}),a?r.jsx("div",{className:"space-y-4",children:[1,2].map(e=>(0,r.jsxs)("div",{className:"animate-pulse flex gap-4 p-3",children:[r.jsx("div",{className:"w-10 h-10 bg-muted rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[r.jsx("div",{className:"h-4 bg-muted rounded w-3/4"}),r.jsx("div",{className:"h-3 bg-muted rounded w-1/2"})]})]},e))}):(0,r.jsxs)("ol",{className:"space-y-6",children:[d.map((e,t)=>r.jsx(m,{educationItem:e},`phd-${e.id}`)),s.map((e,t)=>r.jsx(m,{educationItem:e},`master-${e.id}`)),i.map((e,t)=>r.jsx(m,{educationItem:e},`bachelor-${e.id}`)),c.map((e,t)=>r.jsx(m,{educationItem:e},`other-${e.id}`))]})]})]})}},24822:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var r=a(10326);function o(){return(0,r.jsxs)("div",{className:"group relative w-full overflow-hidden max-h-36 flex items-center justify-center py-1",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),r.jsx("div",{className:"absolute inset-0 border-t border-b border-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx("div",{className:"absolute left-4 top-1/2 w-1 h-1 bg-primary/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),r.jsx("div",{className:"absolute right-4 top-1/2 w-1 h-1 bg-secondary/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft",style:{animationDelay:"1s"}}),(0,r.jsxs)("div",{className:"dark:hidden w-full relative group/light",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 rounded-lg blur opacity-0 group-hover/light:opacity-100 transition-opacity duration-500"}),r.jsx("img",{src:"/github-contribution-snake/github-contribution-grid-snake.svg",alt:"github-contribution",className:"relative w-full h-auto transition-all duration-500 group-hover:scale-[1.02] group-hover:brightness-110 group-hover:contrast-110"})]}),(0,r.jsxs)("div",{className:"hidden dark:block w-full relative group/dark",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 rounded-lg blur opacity-0 group-hover/dark:opacity-100 transition-opacity duration-500"}),r.jsx("img",{src:"/github-contribution-snake/github-contribution-grid-snake-dark.svg",alt:"github-contribution",className:"relative w-full h-auto transition-all duration-500 group-hover:scale-[1.02] group-hover:brightness-110 group-hover:contrast-110"})]}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-foreground/90 text-background text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none",children:["GitHub Contribution Activity",r.jsx("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-foreground/90"})]})]})}},91456:(e,t,a)=>{"use strict";a.d(t,{ProjectCard:()=>N});var r=a(10326),o=a(17577),n=a(62881);(0,n.Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]),(0,n.Z)("Container",[["path",{d:"M22 7.7c0-.6-.4-1.2-.8-1.5l-6.3-3.9a1.72 1.72 0 0 0-1.7 0l-10.3 6c-.5.2-.9.8-.9 1.4v6.6c0 .5.4 1.2.8 1.5l6.3 3.9a1.72 1.72 0 0 0 1.7 0l10.3-6c.5-.3.9-1 .9-1.5Z",key:"1t2lqe"}],["path",{d:"M10 21.9V14L2.1 9.1",key:"o7czzq"}],["path",{d:"m10 14 11.9-6.9",key:"zm5e20"}],["path",{d:"M14 19.8v-8.1",key:"159ecu"}],["path",{d:"M18 17.5V9.4",key:"11uown"}]]);let s=(0,n.Z)("Puzzle",[["path",{d:"M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z",key:"w46dr5"}]]);var i=a(24230),l=a(10629),d=a(13153);let c=new Map([["bold",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216.49,79.51l-56-56A12,12,0,0,0,152,20H56A20,20,0,0,0,36,40v68a12,12,0,0,0,24,0V44h76V92a12,12,0,0,0,12,12h48V212H180a12,12,0,0,0,0,24h20a20,20,0,0,0,20-20V88A12,12,0,0,0,216.49,79.51ZM160,57l23,23H160Zm-4.22,139.85a24.75,24.75,0,0,1-10.95,18.06c-6,4-13.27,5.15-19.73,5.15a63.75,63.75,0,0,1-16.23-2.21,12,12,0,0,1,6.46-23.12c6.81,1.86,15,1.61,16.39.06a2.48,2.48,0,0,0,.21-.71c-1.94-1.23-6.83-2.64-9.88-3.52-5.39-1.56-11-3.18-15.75-6.27-7.62-4.92-11.21-12.45-10.11-21.2a24.45,24.45,0,0,1,10.69-17.75c6.06-4.09,14.17-5.84,24.1-5.18A68.53,68.53,0,0,1,143,142a12,12,0,0,1-6.1,23.21c-6.36-1.63-13.62-1.51-16.07-.33a79.5,79.5,0,0,0,7.91,2.59c5.48,1.58,11.68,3.37,16.8,6.82C153.33,179.55,157,187.55,155.78,196.82ZM84,152v38a30,30,0,0,1-60,0,12,12,0,0,1,24,0,6,6,0,0,0,12,0V152a12,12,0,0,1,24,0Z"}))],["duotone",o.createElement(o.Fragment,null,o.createElement("path",{d:"M208,88H152V32Z",opacity:"0.2"}),o.createElement("path",{d:"M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40v72a8,8,0,0,0,16,0V40h88V88a8,8,0,0,0,8,8h48V216H176a8,8,0,0,0,0,16h24a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160Zm-12.19,145a20.82,20.82,0,0,1-9.19,15.23C133.43,215,127,216,121.13,216A61.14,61.14,0,0,1,106,214a8,8,0,1,1,4.3-15.41c4.38,1.2,15,2.7,19.55-.36.88-.59,1.83-1.52,2.14-3.93.35-2.67-.71-4.1-12.78-7.59-9.35-2.7-25-7.23-23-23.11a20.56,20.56,0,0,1,9-14.95c11.84-8,30.71-3.31,32.83-2.76a8,8,0,0,1-4.07,15.48c-4.49-1.17-15.23-2.56-19.83.56a4.54,4.54,0,0,0-2,3.67c-.12.9-.14,1.09,1.11,1.9,2.31,1.49,6.45,2.68,10.45,3.84C133.49,174.17,150.05,179,147.81,196.31ZM80,152v38a26,26,0,0,1-52,0,8,8,0,0,1,16,0,10,10,0,0,0,20,0V152a8,8,0,0,1,16,0Z"}))],["fill",o.createElement(o.Fragment,null,o.createElement("path",{d:"M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40v76a4,4,0,0,0,4,4H164a4,4,0,0,1,4,4V228a4,4,0,0,0,4,4h28a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM152,88V44l44,44Zm-4.19,108.31a20.82,20.82,0,0,1-9.19,15.23C133.43,215,127,216,121.13,216a61.34,61.34,0,0,1-15.19-2,8,8,0,0,1,4.31-15.41c4.38,1.2,15,2.7,19.55-.36.88-.59,1.83-1.52,2.14-3.93.34-2.67-.72-4.1-12.78-7.59-9.35-2.7-25-7.23-23-23.11a20.58,20.58,0,0,1,9-14.95c11.85-8,30.72-3.31,32.84-2.76a8,8,0,0,1-4.07,15.48c-4.49-1.17-15.23-2.56-19.83.56a4.57,4.57,0,0,0-2,3.67c-.11.9-.13,1.09,1.12,1.9,2.31,1.49,6.45,2.68,10.45,3.84C133.49,174.17,150,179,147.81,196.31ZM80,152v37.41c0,14.22-11.18,26.26-25.41,26.58A26,26,0,0,1,28,190.37,8.17,8.17,0,0,1,35.31,182,8,8,0,0,1,44,190.22a8.89,8.89,0,0,0,4,8c7.85,4.82,16-.75,16-8.2V152.27A8.17,8.17,0,0,1,71.47,144,8,8,0,0,1,80,152Z"}))],["light",o.createElement(o.Fragment,null,o.createElement("path",{d:"M212.24,83.76l-56-56A6,6,0,0,0,152,26H56A14,14,0,0,0,42,40v72a6,6,0,0,0,12,0V40a2,2,0,0,1,2-2h90V88a6,6,0,0,0,6,6h50V216a2,2,0,0,1-2,2H176a6,6,0,0,0,0,12h24a14,14,0,0,0,14-14V88A6,6,0,0,0,212.24,83.76ZM158,46.48,193.52,82H158ZM145.83,196.06a18.89,18.89,0,0,1-8.31,13.81c-4.82,3.19-10.87,4.14-16.36,4.14a58.89,58.89,0,0,1-14.68-2,6,6,0,0,1,3.23-11.56c3.71,1,15.58,3.11,21.19-.62a6.85,6.85,0,0,0,3-5.34c.58-4.43-2.08-6.26-14.2-9.76-9.31-2.69-23.37-6.75-21.57-20.94a18.61,18.61,0,0,1,8.08-13.54c11.11-7.49,29.18-3,31.21-2.48a6,6,0,0,1-3.06,11.6c-3.78-1-15.85-3-21.45.84a6.59,6.59,0,0,0-2.88,5.08c-.41,3.22,2.14,4.78,13,7.91C132.92,176.09,147.84,180.4,145.83,196.06ZM78,152v38a24,24,0,0,1-48,0,6,6,0,0,1,12,0,12,12,0,0,0,24,0V152a6,6,0,0,1,12,0Z"}))],["regular",o.createElement(o.Fragment,null,o.createElement("path",{d:"M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40v72a8,8,0,0,0,16,0V40h88V88a8,8,0,0,0,8,8h48V216H176a8,8,0,0,0,0,16h24a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160Zm-12.19,145a20.82,20.82,0,0,1-9.19,15.23C133.43,215,127,216,121.13,216a61.34,61.34,0,0,1-15.19-2,8,8,0,0,1,4.31-15.41c4.38,1.2,15,2.7,19.55-.36.88-.59,1.83-1.52,2.14-3.93.34-2.67-.71-4.1-12.78-7.59-9.35-2.7-25-7.23-23-23.11a20.56,20.56,0,0,1,9-14.95c11.84-8,30.71-3.31,32.83-2.76a8,8,0,0,1-4.07,15.48c-4.49-1.17-15.23-2.56-19.83.56a4.54,4.54,0,0,0-2,3.67c-.12.9-.14,1.09,1.11,1.9,2.31,1.49,6.45,2.68,10.45,3.84C133.49,174.17,150.05,179,147.81,196.31ZM80,152v38a26,26,0,0,1-52,0,8,8,0,0,1,16,0,10,10,0,0,0,20,0V152a8,8,0,0,1,16,0Z"}))],["thin",o.createElement(o.Fragment,null,o.createElement("path",{d:"M210.83,85.17l-56-56A4,4,0,0,0,152,28H56A12,12,0,0,0,44,40v72a4,4,0,0,0,8,0V40a4,4,0,0,1,4-4h92V88a4,4,0,0,0,4,4h52V216a4,4,0,0,1-4,4H176a4,4,0,0,0,0,8h24a12,12,0,0,0,12-12V88A4,4,0,0,0,210.83,85.17ZM156,41.65,198.34,84H156ZM143.84,195.8a17,17,0,0,1-7.43,12.41c-4.39,2.91-10,3.77-15.22,3.77A57.89,57.89,0,0,1,107,210.11a4,4,0,0,1,2.15-7.7c4.22,1.17,16.56,3.29,22.83-.88a8.94,8.94,0,0,0,3.91-6.75c.83-6.45-4.38-8.69-15.64-11.94-9.68-2.8-21.72-6.28-20.14-18.77a16.66,16.66,0,0,1,7.22-12.13c4.56-3.07,11-4.36,19.1-3.82a61.33,61.33,0,0,1,10.48,1.61,4,4,0,0,1-2.05,7.74c-4.29-1.13-16.81-3.12-23.06,1.11a8.51,8.51,0,0,0-3.75,6.49c-.66,5.17,3.89,7,14.42,10.08C132.26,178,145.64,181.84,143.84,195.8ZM76,152v38a22,22,0,0,1-44,0,4,4,0,0,1,8,0,14,14,0,0,0,28,0V152a4,4,0,0,1,8,0Z"}))]]),m=o.forwardRef((e,t)=>o.createElement(d.Z,{ref:t,...e,weights:c}));m.displayName="FileJsIcon";let u=new Map([["bold",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216,36H40A20,20,0,0,0,20,56V200a20,20,0,0,0,20,20H216a20,20,0,0,0,20-20V56A20,20,0,0,0,216,36Zm-4,160H44V60H212ZM68,92A12,12,0,0,1,80,80h96a12,12,0,0,1,0,24H80A12,12,0,0,1,68,92Zm0,36a12,12,0,0,1,12-12h96a12,12,0,0,1,0,24H80A12,12,0,0,1,68,128Zm0,36a12,12,0,0,1,12-12h96a12,12,0,0,1,0,24H80A12,12,0,0,1,68,164Z"}))],["duotone",o.createElement(o.Fragment,null,o.createElement("path",{d:"M224,56V200a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216A8,8,0,0,1,224,56Z",opacity:"0.2"}),o.createElement("path",{d:"M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,160H40V56H216V200ZM184,96a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h96A8,8,0,0,1,184,96Zm0,32a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h96A8,8,0,0,1,184,128Zm0,32a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h96A8,8,0,0,1,184,160Z"}))],["fill",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM176,168H80a8,8,0,0,1,0-16h96a8,8,0,0,1,0,16Zm0-32H80a8,8,0,0,1,0-16h96a8,8,0,0,1,0,16Zm0-32H80a8,8,0,0,1,0-16h96a8,8,0,0,1,0,16Z"}))],["light",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216,42H40A14,14,0,0,0,26,56V200a14,14,0,0,0,14,14H216a14,14,0,0,0,14-14V56A14,14,0,0,0,216,42Zm2,158a2,2,0,0,1-2,2H40a2,2,0,0,1-2-2V56a2,2,0,0,1,2-2H216a2,2,0,0,1,2,2ZM182,96a6,6,0,0,1-6,6H80a6,6,0,0,1,0-12h96A6,6,0,0,1,182,96Zm0,32a6,6,0,0,1-6,6H80a6,6,0,0,1,0-12h96A6,6,0,0,1,182,128Zm0,32a6,6,0,0,1-6,6H80a6,6,0,0,1,0-12h96A6,6,0,0,1,182,160Z"}))],["regular",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,160H40V56H216V200ZM184,96a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h96A8,8,0,0,1,184,96Zm0,32a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h96A8,8,0,0,1,184,128Zm0,32a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h96A8,8,0,0,1,184,160Z"}))],["thin",o.createElement(o.Fragment,null,o.createElement("path",{d:"M216,44H40A12,12,0,0,0,28,56V200a12,12,0,0,0,12,12H216a12,12,0,0,0,12-12V56A12,12,0,0,0,216,44Zm4,156a4,4,0,0,1-4,4H40a4,4,0,0,1-4-4V56a4,4,0,0,1,4-4H216a4,4,0,0,1,4,4ZM180,96a4,4,0,0,1-4,4H80a4,4,0,0,1,0-8h96A4,4,0,0,1,180,96Zm0,32a4,4,0,0,1-4,4H80a4,4,0,0,1,0-8h96A4,4,0,0,1,180,128Zm0,32a4,4,0,0,1-4,4H80a4,4,0,0,1,0-8h96A4,4,0,0,1,180,160Z"}))]]),h=o.forwardRef((e,t)=>o.createElement(d.Z,{ref:t,...e,weights:u}));h.displayName="ArticleIcon";let p=new Map([["bold",o.createElement(o.Fragment,null,o.createElement("path",{d:"M204,64V168a12,12,0,0,1-24,0V93L72.49,200.49a12,12,0,0,1-17-17L163,76H88a12,12,0,0,1,0-24H192A12,12,0,0,1,204,64Z"}))],["duotone",o.createElement(o.Fragment,null,o.createElement("path",{d:"M192,64V168L88,64Z",opacity:"0.2"}),o.createElement("path",{d:"M192,56H88a8,8,0,0,0-5.66,13.66L128.69,116,58.34,186.34a8,8,0,0,0,11.32,11.32L140,127.31l46.34,46.35A8,8,0,0,0,200,168V64A8,8,0,0,0,192,56Zm-8,92.69-38.34-38.34h0L107.31,72H184Z"}))],["fill",o.createElement(o.Fragment,null,o.createElement("path",{d:"M200,64V168a8,8,0,0,1-13.66,5.66L140,127.31,69.66,197.66a8,8,0,0,1-11.32-11.32L128.69,116,82.34,69.66A8,8,0,0,1,88,56H192A8,8,0,0,1,200,64Z"}))],["light",o.createElement(o.Fragment,null,o.createElement("path",{d:"M198,64V168a6,6,0,0,1-12,0V78.48L68.24,196.24a6,6,0,0,1-8.48-8.48L177.52,70H88a6,6,0,0,1,0-12H192A6,6,0,0,1,198,64Z"}))],["regular",o.createElement(o.Fragment,null,o.createElement("path",{d:"M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"}))],["thin",o.createElement(o.Fragment,null,o.createElement("path",{d:"M196,64V168a4,4,0,0,1-8,0V73.66L66.83,194.83a4,4,0,0,1-5.66-5.66L182.34,68H88a4,4,0,0,1,0-8H192A4,4,0,0,1,196,64Z"}))]]),g=o.forwardRef((e,t)=>o.createElement(d.Z,{ref:t,...e,weights:p}));g.displayName="ArrowUpRightIcon";var x=a(95470),f=a(90434),b=a(19335),y=a(78357),v=a(61711);let j={github:e=>r.jsx(l.b,{...e,size:14,weight:"duotone"}),npm:e=>r.jsx(m,{...e,size:14,weight:"duotone"}),pypi:e=>r.jsx(m,{...e,size:14,weight:"duotone"}),python:e=>r.jsx(y.CustomIcon,{...e,name:"python",size:14}),huggingface:()=>r.jsx("div",{className:"text-xs font-bold",children:"\uD83E\uDD17"}),kaggle:()=>r.jsx("div",{className:"text-xs font-bold",children:"K"}),docker:()=>r.jsx("div",{className:"text-xs font-bold",children:"\uD83D\uDC33"}),llm:()=>r.jsx("div",{className:"text-xs font-bold",children:"\uD83E\uDD16"}),knowledge_graph:()=>r.jsx("div",{className:"text-xs font-bold",children:"\uD83D\uDD0D"})},w=e=>r.jsx(s,{...e,size:20,className:"text-muted-foreground"});function N({project:e,titleAs:t,className:a,style:o}){var n;let s="#",d=e=>e.startsWith("http://")||e.startsWith("https://")?e.replace(/^https?:\/\//,""):e;if(e.has_detail_page)s=`/projects/${e.slug}`;else if(e.link){let t="string"==typeof e.link?e.link:e.link.href;s=t.startsWith("http://")||t.startsWith("https://")?x.fI?`${t}?utm_source=${x.fI}`:t:x.fI?`https://${t}?utm_source=${x.fI}`:`https://${t}`}let c=!e.has_detail_page&&e.link,m=()=>"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium text-white",u=e=>{if(!e)return null;if(e.includes(":"))return r.jsx(y.CustomIcon,{name:e,size:14});let t=e.toLowerCase();return j[t]?j[t]({}):r.jsx(v.AntdIcon,{iconName:e,size:14})};return(0,r.jsxs)("li",{className:`group relative flex flex-col items-start h-full ${a||""}`,style:o,children:[r.jsx("div",{className:"absolute -inset-1 bg-gradient-to-r from-primary/10 via-secondary/5 to-primary/10 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse"}),(0,r.jsxs)("div",{className:"relative flex flex-col justify-between h-full w-full p-6 rounded-2xl border border-muted-foreground/20 shadow-sm bg-background/80 backdrop-blur-sm transition-all duration-500 ease-smooth group-hover:shadow-2xl group-hover:shadow-primary/10 group-hover:-translate-y-3 group-hover:scale-[1.03] group-hover:border-primary/30 group-hover:bg-gradient-to-br group-hover:from-background group-hover:to-primary/5 group-hover:rotate-1",children:[r.jsx("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx("div",{className:"absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),r.jsx("div",{className:"absolute bottom-6 left-4 w-1 h-1 bg-secondary/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft",style:{animationDelay:"0.5s"}}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center sm:justify-start items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"relative flex h-12 w-12 items-center justify-center",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-glow-pulse"}),r.jsx("div",{className:"relative flex h-12 w-12 items-center justify-center rounded-full bg-muted/30 border border-muted-foreground/10 backdrop-blur-sm transition-all duration-300 group-hover:bg-primary/10 group-hover:border-primary/20 group-hover:scale-110 group-hover:rotate-12",children:r.jsx("div",{className:"transition-all duration-300 group-hover:scale-110 group-hover:text-primary",children:(()=>{if(e.icon)return e.icon.includes(":")?r.jsx(y.CustomIcon,{name:e.icon,size:24}):r.jsx(v.AntdIcon,{iconName:e.icon,size:24});if(e.has_detail_page)return r.jsx(h,{size:24,weight:"duotone"});if("opensource"===e.type)return r.jsx(l.b,{size:24,weight:"duotone"});if(e.link?.href)try{return r.jsx(b.Favicon,{url:d("string"==typeof e.link?e.link:e.link.href)})}catch(e){}return r.jsx(w,{})})()})}),r.jsx("div",{className:"absolute inset-0 rounded-full border border-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"})]}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(t??"h2",{className:"text-base font-semibold transition-colors duration-300 group-hover:text-primary",children:e.name}),r.jsx("div",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary transition-all duration-300 group-hover:w-full"})]})]}),(0,r.jsxs)("div",{className:"relative mt-4 ml-2",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-muted/20 to-transparent rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx("p",{className:"relative text-sm text-muted-foreground h-[4.5rem] line-clamp-3 overflow-hidden text-ellipsis leading-relaxed transition-colors duration-300 group-hover:text-foreground/80",children:(n=e.description)?n:""})]})]}),r.jsx("div",{className:"relative z-10 mt-auto pt-4 ml-1",children:(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap items-center gap-2",children:[e.category&&!e.categories&&(0,r.jsxs)("span",{className:m(),style:{backgroundColor:"#6b7280"},children:[(()=>{if(e.category_icon)return u(e.category_icon);if(e.categories&&e.categories.length>0){let t=e.categories[0];if(t.icon)return u(t.icon)}return e.category?u(e.category):null})(),r.jsx("span",{children:e.category})]}),e.categories&&e.categories.length>0&&e.categories.map((e,t)=>(0,r.jsxs)("span",{className:m(),style:{backgroundColor:e.color||"#6b7280"},children:[u(e.icon),r.jsx("span",{children:e.name})]},`cat-${t}`)),e.module_categories&&e.module_categories.length>0&&e.module_categories.map((e,t)=>!1!==e.is_active?(0,r.jsxs)("span",{className:m()+" !bg-gradient-to-r from-blue-500 to-indigo-600",children:[u(e.icon),r.jsx("span",{children:e.name})]},`module-${t}`):null)]})}),c?r.jsx("a",{href:s,target:"_blank",rel:"noopener noreferrer",className:"absolute inset-0 z-20",children:r.jsx(g,{size:32,weight:"duotone",className:"absolute top-4 right-4 h-4 w-4 group-hover:text-primary group-hover:cursor-pointer"})}):r.jsx(f.default,{href:s,className:"absolute inset-0 z-20",children:r.jsx(i.Z,{className:"absolute top-4 right-4 h-4 w-4 group-hover:text-primary group-hover:cursor-pointer"})})]})]})}},61711:(e,t,a)=>{"use strict";a.d(t,{AntdIcon:()=>n});var r=a(10326),o=a(17577);let n=({iconName:e,size:t=20,className:n=""})=>{let[s,i]=(0,o.useState)(null),[l,d]=(0,o.useState)(!0),[c,m]=(0,o.useState)(!1),[u,h]=(0,o.useState)(!1),[p,g]=(0,o.useState)("");(0,o.useEffect)(()=>{if(!e){d(!1),m(!0);return}if(e.startsWith("emoji:")){h(!0),g(e.split(":")[1]),d(!1),m(!1);return}let t=!0;return d(!0),h(!1),a.e(990).then(a.bind(a,24990)).then(a=>{if(!t)return;let r=a[e];if(!r)throw Error(`Icon "${e}" not found in @ant-design/icons`);i(()=>r),m(!1)}).catch(e=>{t&&m(!0)}).finally(()=>{t&&d(!1)}),()=>{t=!1}},[e]);let x={fontSize:`${t}px`},f={display:"inline-flex",alignItems:"center",justifyContent:"center",fontSize:`${t}px`,width:`${t}px`,height:`${t}px`},b={display:"inline-flex",alignItems:"center",justifyContent:"center",width:`${t}px`,height:`${t}px`,fontSize:`${t/2}px`,fontWeight:"bold",color:"#ffffff",backgroundColor:c?"#ff4d4f":"#1890ff",borderRadius:"50%"};if(u)return r.jsx("span",{className:`antd-icon-emoji ${n}`,style:f,children:p});if(l)return r.jsx("span",{className:`antd-icon-loading ${n}`,style:b,children:"•••"});if(c||!s){let t=e?e.charAt(0).toUpperCase():"?";return r.jsx("span",{className:`antd-icon-error ${n}`,style:b,children:t})}return r.jsx(s,{className:n,style:x})}},77669:(e,t,a)=>{"use strict";a.d(t,{GlowOnHover:()=>i,RippleEffect:()=>s,ShimmerEffect:()=>l});var r=a(10326),o=a(17577),n=a(51223);function s({children:e,className:t,color:a="rgba(255, 255, 255, 0.6)",duration:s=600}){let[i,l]=(0,o.useState)([]),d=(0,o.useRef)(0);return(0,r.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden",t),onMouseDown:e=>{let t=e.currentTarget.getBoundingClientRect(),a={x:e.clientX-t.left,y:e.clientY-t.top,id:d.current++};l(e=>[...e,a]),setTimeout(()=>{l(e=>e.filter(e=>e.id!==a.id))},s)},children:[e,i.map(e=>r.jsx("span",{className:"absolute pointer-events-none animate-ping",style:{left:e.x-10,top:e.y-10,width:20,height:20,borderRadius:"50%",backgroundColor:a,animationDuration:`${s}ms`}},e.id))]})}function i({children:e,className:t,glowColor:a="hsl(var(--primary))",intensity:o="medium"}){return r.jsx("div",{className:(0,n.cn)("transition-all duration-300 hover:scale-105",t),style:{"--glow-color":a,"--glow-intensity":{low:"0 0 10px",medium:"0 0 20px",high:"0 0 30px"}[o]},onMouseEnter:e=>{e.currentTarget.style.boxShadow="var(--glow-intensity) var(--glow-color)"},onMouseLeave:e=>{e.currentTarget.style.boxShadow="none"},children:e})}function l({children:e,className:t,direction:a="left-to-right",duration:o=1500}){return(0,r.jsxs)("div",{className:(0,n.cn)("relative overflow-hidden group",t),children:[e,r.jsx("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:`linear-gradient(${"left-to-right"===a?"to right":"to bottom"}, transparent, rgba(255,255,255,0.4), transparent)`,animation:`shimmer ${o}ms ease-in-out infinite`}})]})}let d=`
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(var(--float-amplitude, -10px)); }
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  
  .animate-float {
    animation: float var(--float-duration, 3s) ease-in-out infinite;
    animation-delay: var(--float-delay, 0s);
  }
`;if("undefined"!=typeof document){let e=document.createElement("style");e.textContent=d,document.head.appendChild(e)}},75621:(e,t,a)=>{"use strict";a.r(t),a.d(t,{AnimatedSection:()=>l,FloatingElement:()=>m,PageTransition:()=>i,PulseOnHover:()=>u,ScrollReveal:()=>c,StaggeredList:()=>d});var r=a(10326),o=a(17577),n=a(35047),s=a(51223);function i({children:e,className:t}){(0,n.usePathname)();let[a,i]=(0,o.useState)(!1),[l,d]=(0,o.useState)(e);return(0,r.jsxs)("div",{className:(0,s.cn)("relative",t),children:[a&&r.jsx("div",{className:"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-fade-in",children:r.jsx("div",{className:"flex items-center justify-center h-full",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[r.jsx("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin-slow"}),r.jsx("p",{className:"text-sm text-muted-foreground animate-pulse-soft",children:"加载中..."})]})})}),r.jsx("div",{className:(0,s.cn)("transition-all duration-300 ease-smooth",a?"opacity-0 scale-95":"opacity-100 scale-100 animate-fade-in-up"),children:l})]})}function l({children:e,className:t,delay:a=0,direction:n="up"}){let[i,l]=(0,o.useState)(!1);return r.jsx("div",{className:(0,s.cn)("transition-all duration-500 ease-smooth",i?({up:"animate-fade-in-up",down:"animate-fade-in-down",left:"animate-slide-in-left",right:"animate-slide-in-right"})[n]:"opacity-0 translate-y-4",t),children:e})}function d({children:e,className:t,staggerDelay:a=100}){return r.jsx("div",{className:t,children:e.map((e,t)=>r.jsx(l,{delay:t*a,className:"mb-4 last:mb-0",children:e},t))})}function c({children:e,className:t,threshold:a=.1,rootMargin:n="0px 0px -50px 0px"}){let[i,l]=(0,o.useState)(!1),[d,c]=(0,o.useState)(null);return r.jsx("div",{ref:c,className:(0,s.cn)("transition-all duration-700 ease-smooth",i?"opacity-100 translate-y-0 scale-100":"opacity-0 translate-y-8 scale-95",t),children:e})}function m({children:e,className:t,intensity:a="normal"}){return r.jsx("div",{className:(0,s.cn)("transition-transform duration-300 ease-smooth",{subtle:"hover:translate-y-[-2px]",normal:"hover:translate-y-[-4px]",strong:"hover:translate-y-[-8px]"}[a],t),children:e})}function u({children:e,className:t}){return r.jsx("div",{className:(0,s.cn)("transition-all duration-300 ease-smooth hover:animate-pulse-soft",t),children:e})}},82688:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(27162).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},28921:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S,revalidate:()=>P});var r=a(19510),o=a(91925),n=a(68570);let s=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>"ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function g({as:e,className:t,children:a}){return r.jsx(e??"div",{className:(0,u.Z)(t,"group relative flex flex-col items-start"),children:a})}g.Link=function({children:e,...t}){return(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"absolute -inset-x-4 -inset-y-6 z-0 scale-95 transition group-hover:scale-100 sm:-inset-x-6 sm:rounded-2xl group-hover:bg-muted/50 "}),(0,r.jsxs)(m.default,{...t,children:[r.jsx("span",{className:"absolute -inset-x-4 -inset-y-6 z-20 sm:-inset-x-6 sm:rounded-2xl"}),r.jsx("span",{className:"relative z-10",children:e})]})]})},g.Title=function({as:e,href:t,children:a}){return r.jsx(e??"h2",{className:"text-base font-semibold tracking-normal",children:t?r.jsx(g.Link,{href:t,children:a}):a})},g.Description=function({children:e}){return r.jsx("p",{className:"relative z-10 mt-2 text-sm text-muted-foreground",children:e})},g.Cta=function({children:e}){return(0,r.jsxs)("div",{"aria-hidden":"true",className:"relative z-10 mt-4 flex items-center text-sm font-medium text-primary",children:[e,r.jsx(p,{className:"ml-1 h-4 w-4 stroke-current"})]})},g.Content=function({className:e,children:t,...a}){return r.jsx("div",{className:(0,u.Z)("relative z-10",e),...a,children:t})},g.Eyebrow=function({as:e,decorate:t=!1,className:a,children:o,...n}){return(0,r.jsxs)(e??"p",{className:(0,u.Z)(a,"relative z-10 order-first mb-3 flex items-center text-sm text-muted-foreground",t&&"pl-3.5"),...n,children:[t&&r.jsx("span",{className:"absolute inset-y-0 left-0 flex items-center","aria-hidden":"true",children:r.jsx("span",{className:"h-4 w-0.5 rounded-full bg-muted-foreground/30"})}),o]})};var x=a(82688);let f=(0,h.Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),b=(0,h.Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),y=["#10B981","#059669","#22C55E","#16A34A","#84CC16","#60A5FA","#3B82F6","#0EA5E9","#38BDF8","#06B6D4","#A78BFA","#8B5CF6","#A855F7","#C084FC","#DDD6FE","#FB923C","#F97316","#FDBA74","#FED7AA","#FEF3C7","#F472B6","#EC4899","#F9A8D4","#FBCFE8","#FCE7F3","#22D3EE","#06B6D4","#67E8F9","#A5F3FC","#CFFAFE","#FDE047","#EAB308","#FEF08A","#FEFCE8","#FFFBEB","#94A3B8","#64748B","#CBD5E1","#E2E8F0","#F1F5F9"],v={getLuminance(e){let t=this.hexToRgb(e);if(!t)return 0;let{r:a,g:r,b:o}=t;return .2126*a+.7152*r+.0722*o},hexToRgb(e){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},getContrastColor(e){return this.getLuminance(e)>128?"#000000":"#FFFFFF"},withOpacity(e,t){let a=this.hexToRgb(e);return a?`rgba(${a.r}, ${a.g}, ${a.b}, ${t})`:e},getShadow(e,t=.25){return`0 4px 12px ${this.withOpacity(e,t)}`},getGradient(e,t="to right"){let a=this.withOpacity(e,.8),r=this.withOpacity(e,.6);return`linear-gradient(${t}, ${a}, ${r})`}},j={getTagStyle(e,t="default"){let a=e&&e.startsWith("#")?e:y[0];switch(t){case"minimal":return{backgroundColor:v.withOpacity(a,.08),color:v.withOpacity(a,.8),border:`1px solid ${v.withOpacity(a,.15)}`,boxShadow:"none"};case"soft":return{backgroundColor:v.withOpacity(a,.12),color:v.withOpacity(a,.9),border:`1px solid ${v.withOpacity(a,.2)}`,boxShadow:`0 1px 3px ${v.withOpacity(a,.1)}`};case"outline":return{backgroundColor:"transparent",color:v.withOpacity(a,.8),border:`1px solid ${v.withOpacity(a,.3)}`,boxShadow:`0 1px 2px ${v.withOpacity(a,.05)}`};default:return{backgroundColor:v.withOpacity(a,.15),color:v.withOpacity(a,.9),border:`1px solid ${v.withOpacity(a,.25)}`,boxShadow:`0 2px 4px ${v.withOpacity(a,.1)}`}}},getTagClasses(e,t="default"){let a="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 cursor-pointer relative overflow-hidden";return e&&e.startsWith("#")?a:`${a} bg-muted/50 text-muted-foreground border border-border/50`}};j.getTagStyle(),j.getTagClasses();let w=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx#RippleEffect`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx#MagneticButton`),(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx#FloatingElement`),(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx#ParallaxElement`);let N=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx#GlowOnHover`),M=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx#ShimmerEffect`),A=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/blog/BlogPreloader.tsx#BlogPreloader`);function C({blog:e,titleAs:t}){let a=e=>j.getTagStyle(e,"minimal"),o=e=>j.getTagClasses(e,"minimal");return r.jsx(A,{slug:e.slug,children:(0,r.jsxs)(N,{className:"group relative animate-fade-in-up",intensity:"medium",children:[r.jsx("div",{className:"absolute -inset-2 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse"}),r.jsx("div",{className:"absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx(M,{children:(0,r.jsxs)(g,{as:"article",className:"relative bg-background/80 backdrop-blur-sm border border-muted-foreground/10 rounded-2xl p-6 transition-all duration-500 ease-smooth hover:shadow-2xl hover:shadow-primary/5 hover:-translate-y-2 hover:scale-[1.02] hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5",children:[r.jsx("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx("div",{className:"absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),(0,r.jsxs)("div",{className:"relative z-10 space-y-5",children:[r.jsx("div",{className:"pb-3 border-b border-border/20",children:(0,r.jsxs)(g.Eyebrow,{as:"time",dateTime:e.display_date,className:"flex items-center gap-2 text-sm font-medium text-muted-foreground group-hover:text-primary/70 transition-colors duration-300",children:[r.jsx(x.Z,{className:"w-3.5 h-3.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:gap-3",children:[(0,r.jsxs)("span",{className:"relative",children:[function(e){if(!e)return"Unknown date";let t=new Date(e);return isNaN(t.getTime())?"Unknown date":t.toLocaleDateString("en-US",{day:"numeric",month:"long",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1})}(e.display_date),r.jsx("div",{className:"absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary/50 transition-all duration-300 group-hover:w-full"})]}),r.jsx("span",{className:"text-xs text-muted-foreground/80 mt-1 sm:mt-0",children:new Date(e.display_date).toLocaleDateString("en-US",{weekday:"short"})})]})]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[e.is_featured&&(0,r.jsxs)("div",{className:"inline-flex items-center gap-1.5 px-3 py-1.5 bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 text-xs font-medium",children:[r.jsx(f,{className:"w-3 h-3 fill-current"}),r.jsx("span",{children:"Featured"})]}),r.jsx(g.Title,{as:t??"h2",href:`/blogs/${e.slug}`,className:"text-xl font-bold leading-tight tracking-tight group-hover:text-primary transition-all duration-300 hover:scale-[1.01] origin-left bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text",children:e.title})]}),e.tags&&e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.tags.slice(0,3).map(e=>r.jsx(m.default,{href:`/blogs?tag=${e.slug||e.id}`,children:r.jsx(w,{children:r.jsx("span",{className:o(e.color),style:a(e.color),children:e.name})})},e.id)),e.tags.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1.5 text-xs font-medium bg-muted/50 text-muted-foreground rounded-full border border-muted",children:["+",e.tags.length-3]})]}),r.jsx("p",{className:"text-muted-foreground group-hover:text-foreground transition-colors duration-300 leading-relaxed line-clamp-3",children:e.description}),r.jsx(w,{children:(0,r.jsxs)("div",{className:"group/cta inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 border border-primary/20 hover:border-primary/30 rounded-lg text-primary hover:text-primary/90 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary/20 font-medium relative",children:[r.jsx("span",{className:"relative",children:"Continue Reading"}),r.jsx(b,{className:"w-4 h-4 group-hover/cta:translate-x-0.5 group-hover/cta:-translate-y-0.5 transition-transform duration-300"}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 translate-x-[-100%] group-hover/cta:translate-x-[100%] transition-transform duration-500 rounded-lg"})]})})]})]})})]})})}(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/blog/BlogPreloader.tsx#useBlogBatchPreloader`),(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/blog/BlogPreloader.tsx#clearPreloadCache`),(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/blog/BlogPreloader.tsx#getPreloadStatus`);var k=a(70687);let E=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/project/ProjectCard.tsx#ProjectCard`);var Z=a(64001),H=a(96917);let V=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>/home/<USER>/Code/me/My-web/frontend/src/components/shared/CustomIcon.tsx#CustomIcon`),$=(0,h.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),F=(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/AntdIcon.tsx#AntdIcon`);(0,n.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/shared/AntdIcon.tsx#default`);let P=60;async function S(){let[e,t,a,n]=await Promise.allSettled([(0,l.HG)().catch(e=>({name:"Jay-Yao",headline:"Master's candidate in Information Science",introduction:"I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support."})),(0,l.QF)(),(0,l.rW)("homepage"),(0,k.v3)()]).then(e=>e.map(e=>"fulfilled"===e.status?e.value:null)),d=e=>{if(a&&"blocks"in a&&Array.isArray(a.blocks)){let t=a.blocks.find(t=>t.block_id===e);return!t||t.enabled}return!0},m=n&&"blogs"in n?n.blogs:[],u=n&&"projects"in n?n.projects:[],h=u.length>0?[{categoryName:"Featured Projects",name:"Featured Projects",icon:"star",description:"Projects displayed on homepage",projects:u.map(e=>({...e,name:e.title,description:e.description,link:e.project_url?{href:e.project_url}:null,has_detail_page:!0,show_on_homepage:!0,github_url:e.github_url,demo_url:e.demo_url,logo_url:e.logo_url,icon:e.icon,project_status:e.project_status,is_github_project:e.is_github_project,featured:e.featured,display_order:e.display_order,homepage_order:e.homepage_order}))}]:[];return r.jsx(r.Fragment,{children:(0,r.jsxs)(o.W2,{className:"mt-6 mb-0",children:[d("hero")&&(0,r.jsxs)("div",{className:"relative mb-4 rounded-2xl overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-2xl"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-32 h-32 bg-primary/10 rounded-full blur-3xl animate-pulse-soft"}),r.jsx("div",{className:"absolute bottom-1/4 right-1/4 w-24 h-24 bg-secondary/10 rounded-full blur-2xl animate-pulse-soft",style:{animationDelay:"1s"}}),r.jsx("div",{className:"relative p-8 py-16",children:r.jsx("div",{className:"max-w-4xl mx-auto text-center",children:(0,r.jsxs)(Z.Zu,{children:[r.jsx("div",{className:"relative mb-8",children:(0,r.jsxs)("h2",{className:"text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl mb-4 relative",children:[r.jsx(H.t1,{variant:"primary",animate:!0,className:"relative z-10",children:e&&"headline"in e?e.headline:"Master's candidate in Information Science"}),r.jsx("div",{className:"absolute inset-0 text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary/20 blur-sm -z-10",children:e&&"headline"in e?e.headline:"Master's candidate in Information Science"})]})}),r.jsx("div",{className:"relative mb-12",children:r.jsx("p",{className:"text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto transition-all duration-300 hover:text-foreground/80",children:e&&"introduction"in e?e.introduction:"I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support."})}),r.jsx("div",{className:"relative flex justify-center",children:r.jsx(c,{className:"animate-fade-in-up",style:{animationDelay:"300ms"}})})]})})})]})]}),d("github-contributions")&&r.jsx("div",{className:"mt-0 border-t border-zinc-100 py-2 dark:border-zinc-700/40",children:r.jsx(V,{})}),d("featured-projects")&&h.length>0&&(0,r.jsxs)(Z.$m,{className:"mx-auto flex flex-col max-w-xl gap-3 lg:max-w-none mt-1 mb-2 pt-4 pb-5 border-t border-muted",children:[r.jsx("h2",{className:"text-3xl font-semibold tracking-tight md:text-5xl mb-2",children:r.jsx(H.t1,{variant:"ocean",animate:!0,children:t&&"projectHeadLine"in t?t.projectHeadLine:"My Projects"})}),r.jsx("p",{className:"text-base text-muted-foreground max-w-2xl mb-6 leading-relaxed",children:t&&"projectIntro"in t?t.projectIntro:"Explore my collection of projects showcasing various technologies and skills."}),h[0].projects.length>0&&r.jsx("ul",{role:"list",className:"grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 md:grid-cols-3",children:h[0].projects.map((e,t)=>r.jsx(E,{project:e,titleAs:"h3",className:"animate-fade-in-up",style:{animationDelay:`${150*t}ms`}},e.name))})]}),h.slice(1).map(e=>(0,r.jsxs)("div",{className:"mx-auto flex flex-col max-w-xl gap-4 lg:max-w-none my-2 py-6 border-t border-muted",children:[(0,r.jsxs)("h2",{className:"flex flex-row items-center justify-start gap-2 text-xl font-semibold tracking-tight md:text-3xl mb-4 group",children:[r.jsx("span",{className:"transition-transform duration-300 group-hover:scale-110 group-hover:text-primary",children:e.icon?r.jsx(F,{iconName:e.icon||"",size:28}):e.categoryName.toLowerCase().includes("open source")||e.categoryName.toLowerCase().includes("开源")?r.jsx(_,{name:"github",size:28}):r.jsx($,{size:28})}),r.jsx(H.t1,{variant:"secondary",className:"group-hover:text-primary transition-colors duration-300",children:e.categoryName})]}),e&&e.description?r.jsx("p",{className:"text-base text-muted-foreground max-w-2xl mb-6",children:e.description}):null,r.jsx("ul",{role:"list",className:"grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 md:grid-cols-3",children:e.projects.map(e=>r.jsx(E,{project:e,titleAs:"h3"},e.name))})]},e.categoryName)),d("recent-blogs")&&(0,r.jsxs)(Z.$m,{className:"mx-auto flex flex-col max-w-xl gap-4 py-6 my-6 lg:max-w-none border-t border-muted",children:[r.jsx("h2",{className:"text-3xl font-semibold tracking-tight md:text-5xl mb-2",children:r.jsx(H.t1,{variant:"ocean",animate:!0,children:t&&"blogHeadLine"in t?t.blogHeadLine:"Recent Blogs"})}),r.jsx("p",{className:"text-base text-muted-foreground max-w-2xl mb-6 leading-relaxed",children:t&&"blogIntro"in t?t.blogIntro:"Latest thoughts and insights"})]}),d("recent-blogs")&&(0,r.jsxs)("div",{className:"mx-auto grid max-w-xl grid-cols-1 gap-y-14 lg:max-w-none lg:grid-cols-2",children:[r.jsx("div",{className:"flex flex-col gap-12",children:m.map((e,t)=>r.jsx("div",{className:"animate-fade-in-up",style:{animationDelay:`${150*t}ms`},children:r.jsx(C,{blog:e,titleAs:"h3"})},e.slug))}),(0,r.jsxs)("div",{className:"space-y-8 lg:pl-16 xl:pl-24",children:[d("career")&&r.jsx(Z.Zu,{delay:300,children:r.jsx(s,{})}),d("education")&&r.jsx(Z.Zu,{delay:400,children:r.jsx(i,{})})]})]})]})})}},64001:(e,t,a)=>{"use strict";a.d(t,{$m:()=>n,Zu:()=>o});var r=a(68570);(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#PageTransition`);let o=(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#AnimatedSection`);(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#StaggeredList`);let n=(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#ScrollReveal`);(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#FloatingElement`),(0,r.createProxy)(String.raw`/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx#PulseOnHover`)},96917:(e,t,a)=>{"use strict";a.d(t,{t1:()=>l});var r=a(19510),o=a(71159),n=a(55761),s=a(62386);function i(...e){return(0,s.m6)((0,n.W)(e))}let l=(0,o.forwardRef)(({variant:e="primary",animate:t=!1,className:a,children:o,...n},s)=>r.jsx("span",{ref:s,className:i("bg-clip-text text-transparent font-semibold",{primary:"bg-gradient-to-r from-primary to-primary/70",secondary:"bg-gradient-to-r from-secondary to-muted",rainbow:"bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600",ocean:"bg-gradient-to-r from-blue-500 via-teal-500 to-green-500"}[e],t&&"animate-pulse-soft",a),...n,children:o}));l.displayName="GradientText",(0,o.forwardRef)(({intensity:e="medium",tint:t="none",className:a,children:o,...n},s)=>r.jsx("div",{ref:s,className:i("rounded-xl border backdrop-blur-md","dark:bg-black/10 dark:border-white/10",{light:"bg-white/5 backdrop-blur-sm border-white/10",medium:"bg-white/10 backdrop-blur-md border-white/20",strong:"bg-white/20 backdrop-blur-lg border-white/30"}[e],{none:"",primary:"bg-primary/5 border-primary/20",secondary:"bg-secondary/5 border-secondary/20"}[t],a),...n,children:o})).displayName="GlassCard",(0,o.forwardRef)(({variant:e="raised",size:t="md",className:a,children:o,...n},s)=>r.jsx("div",{ref:s,className:i("bg-background border-0 transition-all duration-300",{raised:"shadow-[8px_8px_16px_rgba(0,0,0,0.1),-8px_-8px_16px_rgba(255,255,255,0.1)]",inset:"shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)]",flat:"shadow-[0_0_0_1px_rgba(0,0,0,0.05)]"}[e],{sm:"p-4 rounded-lg",md:"p-6 rounded-xl",lg:"p-8 rounded-2xl"}[t],a),...n,children:o})).displayName="NeumorphismCard",(0,o.forwardRef)(({color:e="primary",intensity:t="medium",animate:a=!1,className:o,children:n,...s},l)=>r.jsx("div",{ref:l,className:i("transition-all duration-300",{primary:"shadow-primary/50",secondary:"shadow-secondary/50",success:"shadow-green-500/50",warning:"shadow-yellow-500/50",error:"shadow-red-500/50"}[e],{subtle:"shadow-lg",medium:"shadow-xl",strong:"shadow-2xl"}[t],a&&"animate-pulse-soft",o),...s,children:n})).displayName="GlowEffect",(0,o.forwardRef)(({pattern:e="dots",opacity:t=.1,className:a,children:o,...n},s)=>{let l={dots:{backgroundImage:`radial-gradient(circle, currentColor ${100*t}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},grid:{backgroundImage:`linear-gradient(currentColor ${100*t}% 1px, transparent 1px), linear-gradient(90deg, currentColor ${100*t}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},diagonal:{backgroundImage:`repeating-linear-gradient(45deg, transparent, transparent 10px, currentColor ${100*t}% 10px, currentColor ${100*t}% 11px)`},waves:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000' fill-opacity='${t}'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}};return r.jsx("div",{ref:s,className:i("relative",a),style:l[e],...n,children:o})}).displayName="PatternBackground",(0,o.forwardRef)(({gradient:e="rainbow",width:t=2,animate:a=!1,className:o,children:n,...s},l)=>r.jsx("div",{ref:l,className:i("relative rounded-xl overflow-hidden",a&&"animate-pulse-soft",o),...s,children:r.jsx("div",{className:i("absolute inset-0 bg-gradient-to-r",{rainbow:"from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"from-orange-500 via-pink-500 to-purple-600",ocean:"from-blue-500 via-teal-500 to-green-500",forest:"from-green-600 via-emerald-500 to-teal-500",custom:"from-primary via-secondary to-accent"}[e],a&&"animate-spin-slow"),style:{padding:`${t}px`},children:r.jsx("div",{className:"w-full h-full bg-background rounded-xl",children:n})})})).displayName="ColorfulBorder"},70687:(e,t,a)=>{"use strict";a.d(t,{L:()=>n,Ul:()=>o,hT:()=>l,rR:()=>i,v3:()=>s});var r=a(53722);let o="Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation.",n=async function(e,t,a){try{let o=new URLSearchParams;o.append("published_only","true"),o.append("limit","100"),a||o.append("article_type","blog"),e&&o.append("search",e),t&&o.append("tag",t);let n=`${r.CT}/blogs?${o.toString()}`,s=!e&&!t,i=await fetch(n,{...s?{next:{revalidate:60,tags:["blogs"]}}:{cache:"no-store"},headers:{"Cache-Control":s?"public, s-maxage=60, stale-while-revalidate=120":"no-cache"}});if(!i.ok)throw Error(`Failed to fetch blogs: ${i.status}`);return await i.json()||[]}catch(e){return[]}};async function s(){try{let e=`${r.CT}/blogs/homepage-content`,t=await fetch(e,{next:{revalidate:180,tags:["homepage-content","blogs","projects"]},headers:{"Cache-Control":"public, s-maxage=180, stale-while-revalidate=360"}});if(!t.ok)throw Error(`Failed to fetch homepage content: ${t.status}`);return await t.json()}catch(e){return{blogs:[],projects:[],total_blogs:0,total_projects:0}}}async function i(e){try{let t=`${r.CT}/blogs/${e}`,a=await fetch(t,{next:{revalidate:300,tags:[`blog-${e}`,"blogs"]},headers:{"Cache-Control":"public, s-maxage=300, stale-while-revalidate=600"}});if(!a.ok)throw Error(`Failed to fetch blog: ${a.status}`);let o=await a.json();if(o&&"blog"===o.article_type)return o;return null}catch(e){return null}}async function l(){try{let e=`${r.CT}/tags/with-blogs/`,t=await fetch(e,{next:{revalidate:240,tags:["tags","blogs"]},headers:{"Cache-Control":"public, s-maxage=240, stale-while-revalidate=480"}});if(!t.ok)throw Error(`Failed to fetch tags with blogs: ${t.status}`);return await t.json()||[]}catch(e){return[]}}}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[948,499,151,466,722],()=>a(89552));module.exports=r})();