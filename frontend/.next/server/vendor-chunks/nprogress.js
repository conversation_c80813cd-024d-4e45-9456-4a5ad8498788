/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nprogress";
exports.ids = ["vendor-chunks/nprogress"];
exports.modules = {

/***/ "(ssr)/./node_modules/nprogress/nprogress.js":
/*!*********************************************!*\
  !*** ./node_modules/nprogress/nprogress.js ***!
  \*********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__;/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress\n * @license MIT */\n\n;(function(root, factory) {\n\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.call(exports, __webpack_require__, exports, module)) :\n\t\t__WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n\n})(this, function() {\n  var NProgress = {};\n\n  NProgress.version = '0.2.0';\n\n  var Settings = NProgress.settings = {\n    minimum: 0.08,\n    easing: 'ease',\n    positionUsing: '',\n    speed: 200,\n    trickle: true,\n    trickleRate: 0.02,\n    trickleSpeed: 800,\n    showSpinner: true,\n    barSelector: '[role=\"bar\"]',\n    spinnerSelector: '[role=\"spinner\"]',\n    parent: 'body',\n    template: '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n  };\n\n  /**\n   * Updates configuration.\n   *\n   *     NProgress.configure({\n   *       minimum: 0.1\n   *     });\n   */\n  NProgress.configure = function(options) {\n    var key, value;\n    for (key in options) {\n      value = options[key];\n      if (value !== undefined && options.hasOwnProperty(key)) Settings[key] = value;\n    }\n\n    return this;\n  };\n\n  /**\n   * Last number.\n   */\n\n  NProgress.status = null;\n\n  /**\n   * Sets the progress bar status, where `n` is a number from `0.0` to `1.0`.\n   *\n   *     NProgress.set(0.4);\n   *     NProgress.set(1.0);\n   */\n\n  NProgress.set = function(n) {\n    var started = NProgress.isStarted();\n\n    n = clamp(n, Settings.minimum, 1);\n    NProgress.status = (n === 1 ? null : n);\n\n    var progress = NProgress.render(!started),\n        bar      = progress.querySelector(Settings.barSelector),\n        speed    = Settings.speed,\n        ease     = Settings.easing;\n\n    progress.offsetWidth; /* Repaint */\n\n    queue(function(next) {\n      // Set positionUsing if it hasn't already been set\n      if (Settings.positionUsing === '') Settings.positionUsing = NProgress.getPositioningCSS();\n\n      // Add transition\n      css(bar, barPositionCSS(n, speed, ease));\n\n      if (n === 1) {\n        // Fade out\n        css(progress, { \n          transition: 'none', \n          opacity: 1 \n        });\n        progress.offsetWidth; /* Repaint */\n\n        setTimeout(function() {\n          css(progress, { \n            transition: 'all ' + speed + 'ms linear', \n            opacity: 0 \n          });\n          setTimeout(function() {\n            NProgress.remove();\n            next();\n          }, speed);\n        }, speed);\n      } else {\n        setTimeout(next, speed);\n      }\n    });\n\n    return this;\n  };\n\n  NProgress.isStarted = function() {\n    return typeof NProgress.status === 'number';\n  };\n\n  /**\n   * Shows the progress bar.\n   * This is the same as setting the status to 0%, except that it doesn't go backwards.\n   *\n   *     NProgress.start();\n   *\n   */\n  NProgress.start = function() {\n    if (!NProgress.status) NProgress.set(0);\n\n    var work = function() {\n      setTimeout(function() {\n        if (!NProgress.status) return;\n        NProgress.trickle();\n        work();\n      }, Settings.trickleSpeed);\n    };\n\n    if (Settings.trickle) work();\n\n    return this;\n  };\n\n  /**\n   * Hides the progress bar.\n   * This is the *sort of* the same as setting the status to 100%, with the\n   * difference being `done()` makes some placebo effect of some realistic motion.\n   *\n   *     NProgress.done();\n   *\n   * If `true` is passed, it will show the progress bar even if its hidden.\n   *\n   *     NProgress.done(true);\n   */\n\n  NProgress.done = function(force) {\n    if (!force && !NProgress.status) return this;\n\n    return NProgress.inc(0.3 + 0.5 * Math.random()).set(1);\n  };\n\n  /**\n   * Increments by a random amount.\n   */\n\n  NProgress.inc = function(amount) {\n    var n = NProgress.status;\n\n    if (!n) {\n      return NProgress.start();\n    } else {\n      if (typeof amount !== 'number') {\n        amount = (1 - n) * clamp(Math.random() * n, 0.1, 0.95);\n      }\n\n      n = clamp(n + amount, 0, 0.994);\n      return NProgress.set(n);\n    }\n  };\n\n  NProgress.trickle = function() {\n    return NProgress.inc(Math.random() * Settings.trickleRate);\n  };\n\n  /**\n   * Waits for all supplied jQuery promises and\n   * increases the progress as the promises resolve.\n   *\n   * @param $promise jQUery Promise\n   */\n  (function() {\n    var initial = 0, current = 0;\n\n    NProgress.promise = function($promise) {\n      if (!$promise || $promise.state() === \"resolved\") {\n        return this;\n      }\n\n      if (current === 0) {\n        NProgress.start();\n      }\n\n      initial++;\n      current++;\n\n      $promise.always(function() {\n        current--;\n        if (current === 0) {\n            initial = 0;\n            NProgress.done();\n        } else {\n            NProgress.set((initial - current) / initial);\n        }\n      });\n\n      return this;\n    };\n\n  })();\n\n  /**\n   * (Internal) renders the progress bar markup based on the `template`\n   * setting.\n   */\n\n  NProgress.render = function(fromStart) {\n    if (NProgress.isRendered()) return document.getElementById('nprogress');\n\n    addClass(document.documentElement, 'nprogress-busy');\n    \n    var progress = document.createElement('div');\n    progress.id = 'nprogress';\n    progress.innerHTML = Settings.template;\n\n    var bar      = progress.querySelector(Settings.barSelector),\n        perc     = fromStart ? '-100' : toBarPerc(NProgress.status || 0),\n        parent   = document.querySelector(Settings.parent),\n        spinner;\n    \n    css(bar, {\n      transition: 'all 0 linear',\n      transform: 'translate3d(' + perc + '%,0,0)'\n    });\n\n    if (!Settings.showSpinner) {\n      spinner = progress.querySelector(Settings.spinnerSelector);\n      spinner && removeElement(spinner);\n    }\n\n    if (parent != document.body) {\n      addClass(parent, 'nprogress-custom-parent');\n    }\n\n    parent.appendChild(progress);\n    return progress;\n  };\n\n  /**\n   * Removes the element. Opposite of render().\n   */\n\n  NProgress.remove = function() {\n    removeClass(document.documentElement, 'nprogress-busy');\n    removeClass(document.querySelector(Settings.parent), 'nprogress-custom-parent');\n    var progress = document.getElementById('nprogress');\n    progress && removeElement(progress);\n  };\n\n  /**\n   * Checks if the progress bar is rendered.\n   */\n\n  NProgress.isRendered = function() {\n    return !!document.getElementById('nprogress');\n  };\n\n  /**\n   * Determine which positioning CSS rule to use.\n   */\n\n  NProgress.getPositioningCSS = function() {\n    // Sniff on document.body.style\n    var bodyStyle = document.body.style;\n\n    // Sniff prefixes\n    var vendorPrefix = ('WebkitTransform' in bodyStyle) ? 'Webkit' :\n                       ('MozTransform' in bodyStyle) ? 'Moz' :\n                       ('msTransform' in bodyStyle) ? 'ms' :\n                       ('OTransform' in bodyStyle) ? 'O' : '';\n\n    if (vendorPrefix + 'Perspective' in bodyStyle) {\n      // Modern browsers with 3D support, e.g. Webkit, IE10\n      return 'translate3d';\n    } else if (vendorPrefix + 'Transform' in bodyStyle) {\n      // Browsers without 3D support, e.g. IE9\n      return 'translate';\n    } else {\n      // Browsers without translate() support, e.g. IE7-8\n      return 'margin';\n    }\n  };\n\n  /**\n   * Helpers\n   */\n\n  function clamp(n, min, max) {\n    if (n < min) return min;\n    if (n > max) return max;\n    return n;\n  }\n\n  /**\n   * (Internal) converts a percentage (`0..1`) to a bar translateX\n   * percentage (`-100%..0%`).\n   */\n\n  function toBarPerc(n) {\n    return (-1 + n) * 100;\n  }\n\n\n  /**\n   * (Internal) returns the correct CSS for changing the bar's\n   * position given an n percentage, and speed and ease from Settings\n   */\n\n  function barPositionCSS(n, speed, ease) {\n    var barCSS;\n\n    if (Settings.positionUsing === 'translate3d') {\n      barCSS = { transform: 'translate3d('+toBarPerc(n)+'%,0,0)' };\n    } else if (Settings.positionUsing === 'translate') {\n      barCSS = { transform: 'translate('+toBarPerc(n)+'%,0)' };\n    } else {\n      barCSS = { 'margin-left': toBarPerc(n)+'%' };\n    }\n\n    barCSS.transition = 'all '+speed+'ms '+ease;\n\n    return barCSS;\n  }\n\n  /**\n   * (Internal) Queues a function to be executed.\n   */\n\n  var queue = (function() {\n    var pending = [];\n    \n    function next() {\n      var fn = pending.shift();\n      if (fn) {\n        fn(next);\n      }\n    }\n\n    return function(fn) {\n      pending.push(fn);\n      if (pending.length == 1) next();\n    };\n  })();\n\n  /**\n   * (Internal) Applies css properties to an element, similar to the jQuery \n   * css method.\n   *\n   * While this helper does assist with vendor prefixed property names, it \n   * does not perform any manipulation of values prior to setting styles.\n   */\n\n  var css = (function() {\n    var cssPrefixes = [ 'Webkit', 'O', 'Moz', 'ms' ],\n        cssProps    = {};\n\n    function camelCase(string) {\n      return string.replace(/^-ms-/, 'ms-').replace(/-([\\da-z])/gi, function(match, letter) {\n        return letter.toUpperCase();\n      });\n    }\n\n    function getVendorProp(name) {\n      var style = document.body.style;\n      if (name in style) return name;\n\n      var i = cssPrefixes.length,\n          capName = name.charAt(0).toUpperCase() + name.slice(1),\n          vendorName;\n      while (i--) {\n        vendorName = cssPrefixes[i] + capName;\n        if (vendorName in style) return vendorName;\n      }\n\n      return name;\n    }\n\n    function getStyleProp(name) {\n      name = camelCase(name);\n      return cssProps[name] || (cssProps[name] = getVendorProp(name));\n    }\n\n    function applyCss(element, prop, value) {\n      prop = getStyleProp(prop);\n      element.style[prop] = value;\n    }\n\n    return function(element, properties) {\n      var args = arguments,\n          prop, \n          value;\n\n      if (args.length == 2) {\n        for (prop in properties) {\n          value = properties[prop];\n          if (value !== undefined && properties.hasOwnProperty(prop)) applyCss(element, prop, value);\n        }\n      } else {\n        applyCss(element, args[1], args[2]);\n      }\n    }\n  })();\n\n  /**\n   * (Internal) Determines if an element or space separated list of class names contains a class name.\n   */\n\n  function hasClass(element, name) {\n    var list = typeof element == 'string' ? element : classList(element);\n    return list.indexOf(' ' + name + ' ') >= 0;\n  }\n\n  /**\n   * (Internal) Adds a class to an element.\n   */\n\n  function addClass(element, name) {\n    var oldList = classList(element),\n        newList = oldList + name;\n\n    if (hasClass(oldList, name)) return; \n\n    // Trim the opening space.\n    element.className = newList.substring(1);\n  }\n\n  /**\n   * (Internal) Removes a class from an element.\n   */\n\n  function removeClass(element, name) {\n    var oldList = classList(element),\n        newList;\n\n    if (!hasClass(element, name)) return;\n\n    // Replace the class name.\n    newList = oldList.replace(' ' + name + ' ', ' ');\n\n    // Trim the opening and closing spaces.\n    element.className = newList.substring(1, newList.length - 1);\n  }\n\n  /**\n   * (Internal) Gets a space separated list of the class names on the element. \n   * The list is wrapped with a single space on each end to facilitate finding \n   * matches within the list.\n   */\n\n  function classList(element) {\n    return (' ' + (element.className || '') + ' ').replace(/\\s+/gi, ' ');\n  }\n\n  /**\n   * (Internal) Removes an element from the DOM.\n   */\n\n  function removeElement(element) {\n    element && element.parentNode && element.parentNode.removeChild(element);\n  }\n\n  return NProgress;\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nprogress/nprogress.js\n");

/***/ })

};
;