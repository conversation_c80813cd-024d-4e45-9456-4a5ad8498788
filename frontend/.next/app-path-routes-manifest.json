{"/_not-found/page": "/_not-found", "/api/revalidate/route": "/api/revalidate", "/api/performance/route": "/api/performance", "/api/system-config/waline/config/route": "/api/system-config/waline/config", "/api/visit-stats/route": "/api/visit-stats", "/about/page": "/about", "/api/waline/article/pageview/increment/route": "/api/waline/article/pageview/increment", "/api/waline/article/pageview/route": "/api/waline/article/pageview", "/api/waline/pageview/route": "/api/waline/pageview", "/feed/route": "/feed", "/api/waline/comment/count/route": "/api/waline/comment/count", "/demo/waline-design/page": "/demo/waline-design", "/api/waline/reaction/route": "/api/waline/reaction", "/robots.txt/route": "/robots.txt", "/sitemap.xml/route": "/sitemap.xml", "/favicon.ico/route": "/favicon.ico", "/blogs/page": "/blogs", "/blogs/[slug]/page": "/blogs/[slug]", "/project-demo/page": "/project-demo", "/gallery/[slug]/page": "/gallery/[slug]", "/gallery/album/[id]/page": "/gallery/album/[id]", "/projects/[slug]/page": "/projects/[slug]", "/version-history/[id]/page": "/version-history/[id]", "/page": "/", "/gallery/page": "/gallery", "/projects/page": "/projects", "/version-history/page": "/version-history"}