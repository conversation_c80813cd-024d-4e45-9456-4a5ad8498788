"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loading-test/page",{

/***/ "(app-pages-browser)/./src/app/loading-test/page.tsx":
/*!***************************************!*\
  !*** ./src/app/loading-test/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadingTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/PageLoadingOverlay */ \"(app-pages-browser)/./src/components/ui/PageLoadingOverlay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LoadingTestPage() {\n    _s();\n    const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading)();\n    const { withLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading)();\n    const { withLoading: withDataLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading)();\n    const { startLoading, stopLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 模拟API调用\n    const simulateApiCall = async (delay = 2000)=>{\n        return new Promise((resolve)=>{\n            setTimeout(()=>{\n                resolve(\"API调用成功！\");\n            }, delay);\n        });\n    };\n    const testPageLoading = ()=>{\n        showPageLoading(\"测试页面加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"页面加载测试完成\");\n        }, 3000);\n    };\n    const testContentLoading = ()=>{\n        showContentLoading(\"测试内容加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"内容加载测试完成\");\n        }, 2000);\n    };\n    const testApiLoading = async ()=>{\n        try {\n            const result = await withLoading(()=>simulateApiCall(2500), \"正在调用API...\");\n            setTestResult(`API测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"API测试失败\");\n        }\n    };\n    const testDataLoading = async ()=>{\n        try {\n            const result = await withDataLoading(()=>simulateApiCall(2000), {\n                message: \"正在加载数据...\",\n                type: \"content\",\n                minLoadingTime: 1000\n            });\n            setTestResult(`数据加载测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"数据加载测试失败\");\n        }\n    };\n    const testManualPageLoading = ()=>{\n        const cleanup = startLoading(\"手动页面加载测试...\");\n        setTimeout(()=>{\n            stopLoading();\n            setTestResult(\"手动页面加载测试完成\");\n            cleanup?.();\n        }, 2500);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"全局加载体验测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 dark:text-gray-300\",\n                            children: \"测试各种加载状态和指示器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"页面加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testPageLoading,\n                                            className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"测试页面加载遮罩\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testManualPageLoading,\n                                            className: \"w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\",\n                                            children: \"测试手动页面加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"内容加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testContentLoading,\n                                            className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            children: \"测试内容加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testDataLoading,\n                                            className: \"w-full px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors\",\n                                            children: \"测试数据加载Hook\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"API加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testApiLoading,\n                                    className: \"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"测试API加载Hook\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"加载组件示例\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"简单加载器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.SimpleLoadingSpinner, {\n                                                    size: \"md\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"内联加载指示器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.InlineLoadingIndicator, {\n                                                    text: \"正在处理...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                            children: \"测试结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600 dark:text-green-400\",\n                            children: testResult\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"路由加载测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                            children: \"点击以下链接测试路由变化时的加载指示器：\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/blogs\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"博客\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"项目\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/about\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"关于\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadingTestPage, \"zui9M2OwY1VgehzKeabt037FBRM=\", false, function() {\n    return [\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading\n    ];\n});\n_c = LoadingTestPage;\nvar _c;\n$RefreshReg$(_c, \"LoadingTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/loading-test/page.tsx\n"));

/***/ })

});