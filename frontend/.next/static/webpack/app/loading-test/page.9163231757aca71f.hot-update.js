"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loading-test/page",{

/***/ "(app-pages-browser)/./src/app/loading-test/page.tsx":
/*!***************************************!*\
  !*** ./src/app/loading-test/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadingTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/PageLoadingOverlay */ \"(app-pages-browser)/./src/components/ui/PageLoadingOverlay.tsx\");\n/* harmony import */ var _lib_loading_performance_test__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/loading-performance-test */ \"(app-pages-browser)/./src/lib/loading-performance-test.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LoadingTestPage() {\n    _s();\n    const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading)();\n    const { withLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading)();\n    const { withLoading: withDataLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading)();\n    const { startLoading, stopLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [performanceResult, setPerformanceResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模拟API调用\n    const simulateApiCall = async (delay = 2000)=>{\n        return new Promise((resolve)=>{\n            setTimeout(()=>{\n                resolve(\"API调用成功！\");\n            }, delay);\n        });\n    };\n    const testPageLoading = ()=>{\n        showPageLoading(\"测试页面加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"页面加载测试完成\");\n        }, 3000);\n    };\n    const testContentLoading = ()=>{\n        showContentLoading(\"测试内容加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"内容加载测试完成\");\n        }, 2000);\n    };\n    const testApiLoading = async ()=>{\n        try {\n            const result = await withLoading(()=>simulateApiCall(2500), \"正在调用API...\");\n            setTestResult(`API测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"API测试失败\");\n        }\n    };\n    const testDataLoading = async ()=>{\n        try {\n            const result = await withDataLoading(()=>simulateApiCall(2000), {\n                message: \"正在加载数据...\",\n                type: \"content\",\n                minLoadingTime: 1000\n            });\n            setTestResult(`数据加载测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"数据加载测试失败\");\n        }\n    };\n    const testManualPageLoading = ()=>{\n        const cleanup = startLoading(\"手动页面加载测试...\");\n        setTimeout(()=>{\n            stopLoading();\n            setTestResult(\"手动页面加载测试完成\");\n            cleanup?.();\n        }, 2500);\n    };\n    const runPerformanceTest = async ()=>{\n        try {\n            setTestResult(\"正在运行性能测试...\");\n            const result = await (0,_lib_loading_performance_test__WEBPACK_IMPORTED_MODULE_5__.quickLoadingTest)();\n            setPerformanceResult(result);\n            setTestResult(`性能测试完成 - 评分: ${result.evaluation.score}/100 (${result.evaluation.grade})`);\n        } catch (error) {\n            setTestResult(\"性能测试失败\");\n            console.error(\"Performance test error:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"全局加载体验测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 dark:text-gray-300\",\n                            children: \"测试各种加载状态和指示器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"页面加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testPageLoading,\n                                            className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"测试页面加载遮罩\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testManualPageLoading,\n                                            className: \"w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\",\n                                            children: \"测试手动页面加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"内容加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testContentLoading,\n                                            className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            children: \"测试内容加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testDataLoading,\n                                            className: \"w-full px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors\",\n                                            children: \"测试数据加载Hook\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"API加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testApiLoading,\n                                    className: \"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"测试API加载Hook\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"加载组件示例\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"简单加载器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.SimpleLoadingSpinner, {\n                                                    size: \"md\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"内联加载指示器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.InlineLoadingIndicator, {\n                                                    text: \"正在处理...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                            children: \"测试结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600 dark:text-green-400\",\n                            children: testResult\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"路由加载测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                            children: \"点击以下链接测试路由变化时的加载指示器：\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/blogs\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"博客\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"项目\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/about\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"关于\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadingTestPage, \"v6F/9GZJ0puKm9M/wA8qSsjdDsw=\", false, function() {\n    return [\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading\n    ];\n});\n_c = LoadingTestPage;\nvar _c;\n$RefreshReg$(_c, \"LoadingTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/loading-test/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/loading-performance-test.ts":
/*!*********************************************!*\
  !*** ./src/lib/loading-performance-test.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadingPerformanceTracker: function() { return /* binding */ loadingPerformanceTracker; },\n/* harmony export */   quickLoadingTest: function() { return /* binding */ quickLoadingTest; }\n/* harmony export */ });\n/**\n * 全局加载体验性能测试工具\n * 用于测试和验证加载指示器的性能表现\n */ class LoadingPerformanceTracker {\n    // 开始性能测试\n    startTest(testName) {\n        this.startTimes.set(testName, performance.now());\n        console.log(`🚀 开始测试: ${testName}`);\n    }\n    // 结束性能测试\n    endTest(testName) {\n        const startTime = this.startTimes.get(testName);\n        if (!startTime) {\n            console.warn(`⚠️ 未找到测试 ${testName} 的开始时间`);\n            return 0;\n        }\n        const duration = performance.now() - startTime;\n        this.startTimes.delete(testName);\n        console.log(`✅ 测试完成: ${testName} - ${duration.toFixed(2)}ms`);\n        return duration;\n    }\n    // 测试路由加载性能\n    async testRouteLoadingPerformance() {\n        const metrics = {};\n        // 测试首次反馈时间\n        this.startTest(\"firstFeedback\");\n        // 模拟路由变化触发\n        const feedbackTime = this.endTest(\"firstFeedback\");\n        metrics.timeToFirstFeedback = feedbackTime;\n        // 测试组件渲染时间\n        this.startTest(\"componentRender\");\n        await new Promise((resolve)=>setTimeout(resolve, 50)) // 模拟渲染时间\n        ;\n        metrics.componentRenderTime = this.endTest(\"componentRender\");\n        // 测试总加载时间\n        this.startTest(\"totalLoading\");\n        await new Promise((resolve)=>setTimeout(resolve, 1000)) // 模拟完整加载\n        ;\n        metrics.totalLoadingTime = this.endTest(\"totalLoading\");\n        // 计算用户感知延迟\n        metrics.userPerceivedDelay = Math.max(0, metrics.totalLoadingTime - metrics.timeToFirstFeedback);\n        return metrics;\n    }\n    // 测试加载状态管理性能\n    async testLoadingStatePerformance() {\n        const metrics = {};\n        // 测试上下文更新时间\n        this.startTest(\"contextUpdate\");\n        // 模拟状态更新\n        await new Promise((resolve)=>setTimeout(resolve, 10));\n        metrics.contextUpdateTime = this.endTest(\"contextUpdate\");\n        // 测试Hook执行时间\n        this.startTest(\"hookExecution\");\n        // 模拟Hook执行\n        await new Promise((resolve)=>setTimeout(resolve, 5));\n        metrics.hookExecutionTime = this.endTest(\"hookExecution\");\n        return metrics;\n    }\n    // 评估加载体验质量\n    evaluateLoadingExperience(metrics) {\n        const recommendations = [];\n        let score = 100;\n        // 评估首次反馈时间 (目标: <200ms)\n        if (metrics.timeToFirstFeedback > 200) {\n            score -= 20;\n            recommendations.push(\"优化首次反馈时间，建议使用预加载或骨架屏\");\n        }\n        // 评估用户感知延迟 (目标: <1000ms)\n        if (metrics.userPerceivedDelay > 1000) {\n            score -= 15;\n            recommendations.push(\"减少用户感知延迟，考虑分步加载或进度指示\");\n        }\n        // 评估组件渲染性能 (目标: <100ms)\n        if (metrics.componentRenderTime > 100) {\n            score -= 10;\n            recommendations.push(\"优化组件渲染性能，考虑使用React.memo或懒加载\");\n        }\n        // 评估总加载时间 (目标: <2000ms)\n        if (metrics.totalLoadingTime > 2000) {\n            score -= 25;\n            recommendations.push(\"优化总加载时间，检查网络请求和数据处理\");\n        }\n        // 确定等级\n        let grade;\n        if (score >= 90) grade = \"A+\";\n        else if (score >= 80) grade = \"A\";\n        else if (score >= 70) grade = \"B\";\n        else if (score >= 60) grade = \"C\";\n        else grade = \"D\";\n        return {\n            score,\n            grade,\n            recommendations\n        };\n    }\n    // 运行完整的性能测试套件\n    async runFullPerformanceTest() {\n        console.log(\"\\uD83D\\uDD0D 开始全局加载体验性能测试...\");\n        const routeMetrics = await this.testRouteLoadingPerformance();\n        const stateMetrics = await this.testLoadingStatePerformance();\n        const combinedMetrics = {\n            ...routeMetrics,\n            ...stateMetrics\n        };\n        const evaluation = this.evaluateLoadingExperience(combinedMetrics);\n        // 保存测试结果\n        this.testResults.push({\n            testName: \"fullPerformanceTest\",\n            metrics: combinedMetrics,\n            timestamp: Date.now()\n        });\n        console.log(\"\\uD83D\\uDCCA 性能测试结果:\");\n        console.log(`评分: ${evaluation.score}/100 (${evaluation.grade})`);\n        console.log(\"指标详情:\", combinedMetrics);\n        if (evaluation.recommendations.length > 0) {\n            console.log(\"优化建议:\", evaluation.recommendations);\n        }\n        return {\n            routeMetrics,\n            stateMetrics,\n            evaluation\n        };\n    }\n    // 获取测试历史\n    getTestHistory() {\n        return this.testResults;\n    }\n    // 清除测试历史\n    clearTestHistory() {\n        this.testResults = [];\n    }\n    // 导出测试报告\n    exportTestReport() {\n        const report = {\n            timestamp: new Date().toISOString(),\n            testCount: this.testResults.length,\n            results: this.testResults,\n            summary: this.generateSummary()\n        };\n        return JSON.stringify(report, null, 2);\n    }\n    generateSummary() {\n        if (this.testResults.length === 0) {\n            return {\n                message: \"暂无测试数据\"\n            };\n        }\n        const latestResult = this.testResults[this.testResults.length - 1];\n        return {\n            latestTest: latestResult.testName,\n            latestTimestamp: new Date(latestResult.timestamp).toLocaleString(),\n            keyMetrics: latestResult.metrics\n        };\n    }\n    constructor(){\n        this.metrics = {};\n        this.startTimes = new Map();\n        this.testResults = [];\n    }\n}\n// 导出单例实例\nconst loadingPerformanceTracker = new LoadingPerformanceTracker();\n// 便捷的测试函数\nasync function quickLoadingTest() {\n    return await loadingPerformanceTracker.runFullPerformanceTest();\n}\n// 在浏览器控制台中使用的全局函数\nif (true) {\n    window.testLoadingPerformance = quickLoadingTest(window).loadingTracker = loadingPerformanceTracker;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/loading-performance-test.ts\n"));

/***/ })

});