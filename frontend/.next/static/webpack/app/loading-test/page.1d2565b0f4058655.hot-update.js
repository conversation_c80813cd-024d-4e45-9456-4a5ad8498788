"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loading-test/page",{

/***/ "(app-pages-browser)/./src/app/loading-test/page.tsx":
/*!***************************************!*\
  !*** ./src/app/loading-test/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadingTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/PageLoadingOverlay */ \"(app-pages-browser)/./src/components/ui/PageLoadingOverlay.tsx\");\n/* harmony import */ var _lib_loading_performance_test__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/loading-performance-test */ \"(app-pages-browser)/./src/lib/loading-performance-test.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LoadingTestPage() {\n    _s();\n    const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading)();\n    const { withLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading)();\n    const { withLoading: withDataLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading)();\n    const { startLoading, stopLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [performanceResult, setPerformanceResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模拟API调用\n    const simulateApiCall = async (delay = 2000)=>{\n        return new Promise((resolve)=>{\n            setTimeout(()=>{\n                resolve(\"API调用成功！\");\n            }, delay);\n        });\n    };\n    const testPageLoading = ()=>{\n        showPageLoading(\"测试页面加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"页面加载测试完成\");\n        }, 3000);\n    };\n    const testContentLoading = ()=>{\n        showContentLoading(\"测试内容加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"内容加载测试完成\");\n        }, 2000);\n    };\n    const testApiLoading = async ()=>{\n        try {\n            const result = await withLoading(()=>simulateApiCall(2500), \"正在调用API...\");\n            setTestResult(`API测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"API测试失败\");\n        }\n    };\n    const testDataLoading = async ()=>{\n        try {\n            const result = await withDataLoading(()=>simulateApiCall(2000), {\n                message: \"正在加载数据...\",\n                type: \"content\",\n                minLoadingTime: 1000\n            });\n            setTestResult(`数据加载测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"数据加载测试失败\");\n        }\n    };\n    const testManualPageLoading = ()=>{\n        const cleanup = startLoading(\"手动页面加载测试...\");\n        setTimeout(()=>{\n            stopLoading();\n            setTestResult(\"手动页面加载测试完成\");\n            cleanup?.();\n        }, 2500);\n    };\n    const runPerformanceTest = async ()=>{\n        try {\n            setTestResult(\"正在运行性能测试...\");\n            const result = await (0,_lib_loading_performance_test__WEBPACK_IMPORTED_MODULE_5__.quickLoadingTest)();\n            setPerformanceResult(result);\n            setTestResult(`性能测试完成 - 评分: ${result.evaluation.score}/100 (${result.evaluation.grade})`);\n        } catch (error) {\n            setTestResult(\"性能测试失败\");\n            console.error(\"Performance test error:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"全局加载体验测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 dark:text-gray-300\",\n                            children: \"测试各种加载状态和指示器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"页面加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testPageLoading,\n                                            className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"测试页面加载遮罩\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testManualPageLoading,\n                                            className: \"w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\",\n                                            children: \"测试手动页面加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"内容加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testContentLoading,\n                                            className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            children: \"测试内容加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testDataLoading,\n                                            className: \"w-full px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors\",\n                                            children: \"测试数据加载Hook\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"API加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testApiLoading,\n                                    className: \"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"测试API加载Hook\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"加载组件示例\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"简单加载器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.SimpleLoadingSpinner, {\n                                                    size: \"md\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"内联加载指示器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.InlineLoadingIndicator, {\n                                                    text: \"正在处理...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"性能测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: runPerformanceTest,\n                                    className: \"w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                    children: \"运行完整性能测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                performanceResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"性能测试详细结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"评估结果\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"评分: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono text-blue-600\",\n                                                            children: [\n                                                                performanceResult.evaluation.score,\n                                                                \"/100\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"等级: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono text-green-600\",\n                                                            children: performanceResult.evaluation.grade\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"关键指标\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"首次反馈: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono\",\n                                                            children: [\n                                                                performanceResult.routeMetrics.timeToFirstFeedback?.toFixed(2),\n                                                                \"ms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 28\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"总加载时间: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono\",\n                                                            children: [\n                                                                performanceResult.routeMetrics.totalLoadingTime?.toFixed(2),\n                                                                \"ms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"用户感知延迟: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono\",\n                                                            children: [\n                                                                performanceResult.routeMetrics.userPerceivedDelay?.toFixed(2),\n                                                                \"ms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 30\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this),\n                        performanceResult.evaluation.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: \"优化建议\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: performanceResult.evaluation.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: rec\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, this),\n                testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                            children: \"测试结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600 dark:text-green-400\",\n                            children: testResult\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"路由加载测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                            children: \"点击以下链接测试路由变化时的加载指示器：\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/blogs\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"博客\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"项目\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/about\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"关于\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadingTestPage, \"v6F/9GZJ0puKm9M/wA8qSsjdDsw=\", false, function() {\n    return [\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading\n    ];\n});\n_c = LoadingTestPage;\nvar _c;\n$RefreshReg$(_c, \"LoadingTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/loading-test/page.tsx\n"));

/***/ })

});