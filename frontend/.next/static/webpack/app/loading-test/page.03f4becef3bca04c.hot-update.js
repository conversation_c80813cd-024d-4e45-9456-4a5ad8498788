"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loading-test/page",{

/***/ "(app-pages-browser)/./src/app/loading-test/page.tsx":
/*!***************************************!*\
  !*** ./src/app/loading-test/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoadingTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/PageLoadingOverlay */ \"(app-pages-browser)/./src/components/ui/PageLoadingOverlay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LoadingTestPage() {\n    _s();\n    const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading)();\n    const { withLoading } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading)();\n    const { withLoading: withDataLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading)();\n    const { startLoading, stopLoading } = (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [performanceResult, setPerformanceResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模拟API调用\n    const simulateApiCall = async (delay = 2000)=>{\n        return new Promise((resolve)=>{\n            setTimeout(()=>{\n                resolve(\"API调用成功！\");\n            }, delay);\n        });\n    };\n    const testPageLoading = ()=>{\n        showPageLoading(\"测试页面加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"页面加载测试完成\");\n        }, 3000);\n    };\n    const testContentLoading = ()=>{\n        showContentLoading(\"测试内容加载...\");\n        setTimeout(()=>{\n            hideLoading();\n            setTestResult(\"内容加载测试完成\");\n        }, 2000);\n    };\n    const testApiLoading = async ()=>{\n        try {\n            const result = await withLoading(()=>simulateApiCall(2500), \"正在调用API...\");\n            setTestResult(`API测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"API测试失败\");\n        }\n    };\n    const testDataLoading = async ()=>{\n        try {\n            const result = await withDataLoading(()=>simulateApiCall(2000), {\n                message: \"正在加载数据...\",\n                type: \"content\",\n                minLoadingTime: 1000\n            });\n            setTestResult(`数据加载测试完成: ${result}`);\n        } catch (error) {\n            setTestResult(\"数据加载测试失败\");\n        }\n    };\n    const testManualPageLoading = ()=>{\n        const cleanup = startLoading(\"手动页面加载测试...\");\n        setTimeout(()=>{\n            stopLoading();\n            setTestResult(\"手动页面加载测试完成\");\n            cleanup?.();\n        }, 2500);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"全局加载体验测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 dark:text-gray-300\",\n                            children: \"测试各种加载状态和指示器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"页面加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testPageLoading,\n                                            className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"测试页面加载遮罩\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testManualPageLoading,\n                                            className: \"w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\",\n                                            children: \"测试手动页面加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"内容加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testContentLoading,\n                                            className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            children: \"测试内容加载\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: testDataLoading,\n                                            className: \"w-full px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors\",\n                                            children: \"测试数据加载Hook\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"API加载测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testApiLoading,\n                                    className: \"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"测试API加载Hook\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"加载组件示例\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"简单加载器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.SimpleLoadingSpinner, {\n                                                    size: \"md\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: \"内联加载指示器:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoadingOverlay__WEBPACK_IMPORTED_MODULE_4__.InlineLoadingIndicator, {\n                                                    text: \"正在处理...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                            children: \"测试结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600 dark:text-green-400\",\n                            children: testResult\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"路由加载测试\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                            children: \"点击以下链接测试路由变化时的加载指示器：\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/blogs\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"博客\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"项目\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/about\",\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"关于\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/app/loading-test/page.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(LoadingTestPage, \"v6F/9GZJ0puKm9M/wA8qSsjdDsw=\", false, function() {\n    return [\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useLoading,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.useApiLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.useDataLoading,\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_3__.usePageLoading\n    ];\n});\n_c = LoadingTestPage;\nvar _c;\n$RefreshReg$(_c, \"LoadingTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbG9hZGluZy10ZXN0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUNnQztBQUNxQztBQUNFO0FBQzBCO0FBR2xGLFNBQVNPOztJQUN0QixNQUFNLEVBQUVDLGVBQWUsRUFBRUMsa0JBQWtCLEVBQUVDLGNBQWMsRUFBRUMsV0FBVyxFQUFFLEdBQUdWLG9FQUFVQTtJQUN2RixNQUFNLEVBQUVXLFdBQVcsRUFBRSxHQUFHVix1RUFBYUE7SUFDckMsTUFBTSxFQUFFVSxhQUFhQyxlQUFlLEVBQUUsR0FBR1QscUVBQWNBO0lBQ3ZELE1BQU0sRUFBRVUsWUFBWSxFQUFFQyxXQUFXLEVBQUUsR0FBR1oscUVBQWNBO0lBQ3BELE1BQU0sQ0FBQ2EsWUFBWUMsY0FBYyxHQUFHakIsK0NBQVFBLENBQVM7SUFDckQsTUFBTSxDQUFDa0IsbUJBQW1CQyxxQkFBcUIsR0FBR25CLCtDQUFRQSxDQUFNO0lBRWhFLFVBQVU7SUFDVixNQUFNb0Isa0JBQWtCLE9BQU9DLFFBQWdCLElBQUk7UUFDakQsT0FBTyxJQUFJQyxRQUFRQyxDQUFBQTtZQUNqQkMsV0FBVztnQkFDVEQsUUFBUTtZQUNWLEdBQUdGO1FBQ0w7SUFDRjtJQUVBLE1BQU1JLGtCQUFrQjtRQUN0QmpCLGdCQUFnQjtRQUNoQmdCLFdBQVc7WUFDVGI7WUFDQU0sY0FBYztRQUNoQixHQUFHO0lBQ0w7SUFFQSxNQUFNUyxxQkFBcUI7UUFDekJqQixtQkFBbUI7UUFDbkJlLFdBQVc7WUFDVGI7WUFDQU0sY0FBYztRQUNoQixHQUFHO0lBQ0w7SUFFQSxNQUFNVSxpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU1DLFNBQVMsTUFBTWhCLFlBQ25CLElBQU1RLGdCQUFnQixPQUN0QjtZQUVGSCxjQUFjLENBQUMsU0FBUyxFQUFFVyxPQUFPLENBQUM7UUFDcEMsRUFBRSxPQUFPQyxPQUFPO1lBQ2RaLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU1hLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTUYsU0FBUyxNQUFNZixnQkFDbkIsSUFBTU8sZ0JBQWdCLE9BQ3RCO2dCQUNFVyxTQUFTO2dCQUNUQyxNQUFNO2dCQUNOQyxnQkFBZ0I7WUFDbEI7WUFFRmhCLGNBQWMsQ0FBQyxVQUFVLEVBQUVXLE9BQU8sQ0FBQztRQUNyQyxFQUFFLE9BQU9DLE9BQU87WUFDZFosY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTWlCLHdCQUF3QjtRQUM1QixNQUFNQyxVQUFVckIsYUFBYTtRQUM3QlUsV0FBVztZQUNUVDtZQUNBRSxjQUFjO1lBQ2RrQjtRQUNGLEdBQUc7SUFDTDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBd0Q7Ozs7OztzQ0FHdEUsOERBQUNFOzRCQUFFRixXQUFVO3NDQUEyQzs7Ozs7Ozs7Ozs7OzhCQUsxRCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFHSCxXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6RSw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FDQ0MsU0FBU2pCOzRDQUNUWSxXQUFVO3NEQUNYOzs7Ozs7c0RBR0QsOERBQUNJOzRDQUNDQyxTQUFTUjs0Q0FDVEcsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFHSCxXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6RSw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FDQ0MsU0FBU2hCOzRDQUNUVyxXQUFVO3NEQUNYOzs7Ozs7c0RBR0QsOERBQUNJOzRDQUNDQyxTQUFTWjs0Q0FDVE8sV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFHSCxXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6RSw4REFBQ0k7b0NBQ0NDLFNBQVNmO29DQUNUVSxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7c0NBTUgsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQUdILFdBQVU7OENBQTJEOzs7Ozs7OENBR3pFLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzs4REFDQyw4REFBQ0c7b0RBQUVGLFdBQVU7OERBQWdEOzs7Ozs7OERBQzdELDhEQUFDL0IsbUZBQW9CQTtvREFBQ3FDLE1BQUs7Ozs7Ozs7Ozs7OztzREFFN0IsOERBQUNQOzs4REFDQyw4REFBQ0c7b0RBQUVGLFdBQVU7OERBQWdEOzs7Ozs7OERBQzdELDhEQUFDaEMscUZBQXNCQTtvREFBQ3VDLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFPcEM1Qiw0QkFDQyw4REFBQ29CO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1E7NEJBQUdSLFdBQVU7c0NBQTJEOzs7Ozs7c0NBR3pFLDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBc0NyQjs7Ozs7Ozs7Ozs7OzhCQUt2RCw4REFBQ29CO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1E7NEJBQUdSLFdBQVU7c0NBQTJEOzs7Ozs7c0NBR3pFLDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBd0M7Ozs7OztzQ0FHckQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1M7b0NBQ0NDLE1BQUs7b0NBQ0xWLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ1M7b0NBQ0NDLE1BQUs7b0NBQ0xWLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ1M7b0NBQ0NDLE1BQUs7b0NBQ0xWLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ1M7b0NBQ0NDLE1BQUs7b0NBQ0xWLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0E1TXdCOUI7O1FBQ3VETixnRUFBVUE7UUFDL0RDLG1FQUFhQTtRQUNJRSxpRUFBY0E7UUFDakJELGlFQUFjQTs7O0tBSjlCSSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xvYWRpbmctdGVzdC9wYWdlLnRzeD9jOTM3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUxvYWRpbmcsIHVzZUFwaUxvYWRpbmcgfSBmcm9tICdAL2NvbnRleHRzL0xvYWRpbmdDb250ZXh0J1xuaW1wb3J0IHsgdXNlUGFnZUxvYWRpbmcsIHVzZURhdGFMb2FkaW5nIH0gZnJvbSAnQC9ob29rcy91c2VQYWdlTG9hZGluZydcbmltcG9ydCB7IElubGluZUxvYWRpbmdJbmRpY2F0b3IsIFNpbXBsZUxvYWRpbmdTcGlubmVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1BhZ2VMb2FkaW5nT3ZlcmxheSdcbmltcG9ydCB7IHF1aWNrTG9hZGluZ1Rlc3QsIGxvYWRpbmdQZXJmb3JtYW5jZVRyYWNrZXIgfSBmcm9tICdAL2xpYi9sb2FkaW5nLXBlcmZvcm1hbmNlLXRlc3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdUZXN0UGFnZSgpIHtcbiAgY29uc3QgeyBzaG93UGFnZUxvYWRpbmcsIHNob3dDb250ZW50TG9hZGluZywgc2hvd0FwaUxvYWRpbmcsIGhpZGVMb2FkaW5nIH0gPSB1c2VMb2FkaW5nKClcbiAgY29uc3QgeyB3aXRoTG9hZGluZyB9ID0gdXNlQXBpTG9hZGluZygpXG4gIGNvbnN0IHsgd2l0aExvYWRpbmc6IHdpdGhEYXRhTG9hZGluZyB9ID0gdXNlRGF0YUxvYWRpbmcoKVxuICBjb25zdCB7IHN0YXJ0TG9hZGluZywgc3RvcExvYWRpbmcgfSA9IHVzZVBhZ2VMb2FkaW5nKClcbiAgY29uc3QgW3Rlc3RSZXN1bHQsIHNldFRlc3RSZXN1bHRdID0gdXNlU3RhdGU8c3RyaW5nPignJylcbiAgY29uc3QgW3BlcmZvcm1hbmNlUmVzdWx0LCBzZXRQZXJmb3JtYW5jZVJlc3VsdF0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG5cbiAgLy8g5qih5oufQVBJ6LCD55SoXG4gIGNvbnN0IHNpbXVsYXRlQXBpQ2FsbCA9IGFzeW5jIChkZWxheTogbnVtYmVyID0gMjAwMCkgPT4ge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICByZXNvbHZlKCdBUEnosIPnlKjmiJDlip/vvIEnKVxuICAgICAgfSwgZGVsYXkpXG4gICAgfSlcbiAgfVxuXG4gIGNvbnN0IHRlc3RQYWdlTG9hZGluZyA9ICgpID0+IHtcbiAgICBzaG93UGFnZUxvYWRpbmcoJ+a1i+ivlemhtemdouWKoOi9vS4uLicpXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBoaWRlTG9hZGluZygpXG4gICAgICBzZXRUZXN0UmVzdWx0KCfpobXpnaLliqDovb3mtYvor5XlrozmiJAnKVxuICAgIH0sIDMwMDApXG4gIH1cblxuICBjb25zdCB0ZXN0Q29udGVudExvYWRpbmcgPSAoKSA9PiB7XG4gICAgc2hvd0NvbnRlbnRMb2FkaW5nKCfmtYvor5XlhoXlrrnliqDovb0uLi4nKVxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaGlkZUxvYWRpbmcoKVxuICAgICAgc2V0VGVzdFJlc3VsdCgn5YaF5a655Yqg6L295rWL6K+V5a6M5oiQJylcbiAgICB9LCAyMDAwKVxuICB9XG5cbiAgY29uc3QgdGVzdEFwaUxvYWRpbmcgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHdpdGhMb2FkaW5nKFxuICAgICAgICAoKSA9PiBzaW11bGF0ZUFwaUNhbGwoMjUwMCksXG4gICAgICAgICfmraPlnKjosIPnlKhBUEkuLi4nXG4gICAgICApXG4gICAgICBzZXRUZXN0UmVzdWx0KGBBUEnmtYvor5XlrozmiJA6ICR7cmVzdWx0fWApXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldFRlc3RSZXN1bHQoJ0FQSea1i+ivleWksei0pScpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdGVzdERhdGFMb2FkaW5nID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB3aXRoRGF0YUxvYWRpbmcoXG4gICAgICAgICgpID0+IHNpbXVsYXRlQXBpQ2FsbCgyMDAwKSxcbiAgICAgICAge1xuICAgICAgICAgIG1lc3NhZ2U6ICfmraPlnKjliqDovb3mlbDmja4uLi4nLFxuICAgICAgICAgIHR5cGU6ICdjb250ZW50JyxcbiAgICAgICAgICBtaW5Mb2FkaW5nVGltZTogMTAwMFxuICAgICAgICB9XG4gICAgICApXG4gICAgICBzZXRUZXN0UmVzdWx0KGDmlbDmja7liqDovb3mtYvor5XlrozmiJA6ICR7cmVzdWx0fWApXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldFRlc3RSZXN1bHQoJ+aVsOaNruWKoOi9vea1i+ivleWksei0pScpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdGVzdE1hbnVhbFBhZ2VMb2FkaW5nID0gKCkgPT4ge1xuICAgIGNvbnN0IGNsZWFudXAgPSBzdGFydExvYWRpbmcoJ+aJi+WKqOmhtemdouWKoOi9vea1i+ivlS4uLicpXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzdG9wTG9hZGluZygpXG4gICAgICBzZXRUZXN0UmVzdWx0KCfmiYvliqjpobXpnaLliqDovb3mtYvor5XlrozmiJAnKVxuICAgICAgY2xlYW51cD8uKClcbiAgICB9LCAyNTAwKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMCBkYXJrOmZyb20tZ3JheS05MDAgZGFyazp0by1ncmF5LTgwMCBweS0xMlwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBweC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgIOWFqOWxgOWKoOi9veS9k+mqjOa1i+ivlVxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAg5rWL6K+V5ZCE56eN5Yqg6L2954q25oCB5ZKM5oyH56S65ZmoXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgIHsvKiDpobXpnaLliqDovb3mtYvor5UgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgIOmhtemdouWKoOi9vea1i+ivlVxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0UGFnZUxvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5rWL6K+V6aG16Z2i5Yqg6L296YGu572pXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17dGVzdE1hbnVhbFBhZ2VMb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTIgYmctaW5kaWdvLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctaW5kaWdvLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmtYvor5XmiYvliqjpobXpnaLliqDovb1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDlhoXlrrnliqDovb3mtYvor5UgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgIOWGheWuueWKoOi9vea1i+ivlVxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0Q29udGVudExvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMiBiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWdyZWVuLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmtYvor5XlhoXlrrnliqDovb1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0RGF0YUxvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMiBiZy10ZWFsLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctdGVhbC03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5rWL6K+V5pWw5o2u5Yqg6L29SG9va1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFQSeWKoOi9vea1i+ivlSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgICAgQVBJ5Yqg6L295rWL6K+VXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0QXBpTG9hZGluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMiBiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1wdXJwbGUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5rWL6K+VQVBJ5Yqg6L29SG9va1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog57uE5Lu256S65L6LICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgICAgICDliqDovb3nu4Tku7bnpLrkvotcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMlwiPueugOWNleWKoOi9veWZqDo8L3A+XG4gICAgICAgICAgICAgICAgPFNpbXBsZUxvYWRpbmdTcGlubmVyIHNpemU9XCJtZFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMlwiPuWGheiBlOWKoOi9veaMh+ekuuWZqDo8L3A+XG4gICAgICAgICAgICAgICAgPElubGluZUxvYWRpbmdJbmRpY2F0b3IgdGV4dD1cIuato+WcqOWkhOeQhi4uLlwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDmtYvor5Xnu5PmnpwgKi99XG4gICAgICAgIHt0ZXN0UmVzdWx0ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC02XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTJcIj5cbiAgICAgICAgICAgICAg5rWL6K+V57uT5p6cXG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgZGFyazp0ZXh0LWdyZWVuLTQwMFwiPnt0ZXN0UmVzdWx0fTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7Lyog6Lev55Sx5rWL6K+V6ZO+5o6lICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgIOi3r+eUseWKoOi9vea1i+ivlVxuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItNFwiPlxuICAgICAgICAgICAg54K55Ye75Lul5LiL6ZO+5o6l5rWL6K+V6Lev55Sx5Y+Y5YyW5pe255qE5Yqg6L295oyH56S65Zmo77yaXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgIGhyZWY9XCIvXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyYXktNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOmmlumhtVxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgaHJlZj1cIi9ibG9nc1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1ncmF5LTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDljZrlrqJcbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgIGhyZWY9XCIvcHJvamVjdHNcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctZ3JheS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg6aG555uuXG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8YVxuICAgICAgICAgICAgICBocmVmPVwiL2Fib3V0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyYXktNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOWFs+S6jlxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VMb2FkaW5nIiwidXNlQXBpTG9hZGluZyIsInVzZVBhZ2VMb2FkaW5nIiwidXNlRGF0YUxvYWRpbmciLCJJbmxpbmVMb2FkaW5nSW5kaWNhdG9yIiwiU2ltcGxlTG9hZGluZ1NwaW5uZXIiLCJMb2FkaW5nVGVzdFBhZ2UiLCJzaG93UGFnZUxvYWRpbmciLCJzaG93Q29udGVudExvYWRpbmciLCJzaG93QXBpTG9hZGluZyIsImhpZGVMb2FkaW5nIiwid2l0aExvYWRpbmciLCJ3aXRoRGF0YUxvYWRpbmciLCJzdGFydExvYWRpbmciLCJzdG9wTG9hZGluZyIsInRlc3RSZXN1bHQiLCJzZXRUZXN0UmVzdWx0IiwicGVyZm9ybWFuY2VSZXN1bHQiLCJzZXRQZXJmb3JtYW5jZVJlc3VsdCIsInNpbXVsYXRlQXBpQ2FsbCIsImRlbGF5IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwidGVzdFBhZ2VMb2FkaW5nIiwidGVzdENvbnRlbnRMb2FkaW5nIiwidGVzdEFwaUxvYWRpbmciLCJyZXN1bHQiLCJlcnJvciIsInRlc3REYXRhTG9hZGluZyIsIm1lc3NhZ2UiLCJ0eXBlIiwibWluTG9hZGluZ1RpbWUiLCJ0ZXN0TWFudWFsUGFnZUxvYWRpbmciLCJjbGVhbnVwIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwic2l6ZSIsInRleHQiLCJoMyIsImEiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/loading-test/page.tsx\n"));

/***/ })

});