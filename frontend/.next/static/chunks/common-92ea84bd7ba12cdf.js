"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[592],{46021:function(e,t,r){r.d(t,{I:function(){return m},Providers:function(){return p}});var n=r(57437),a=r(2265),s=r(99376),i=r(78298);function o({children:e}){return(0,a.useEffect)(()=>{},[]),(0,n.jsx)(n.Fragment,{children:e})}let l=(0,a.createContext)(null);function c({children:e}){let t=(0,a.useRef)(new Map),r=(0,a.useRef)(new Map),s=(0,a.useCallback)((e,r)=>{let n=e.toString();return(...a)=>{let s=t.current.get(n);s&&clearTimeout(s);let i=setTimeout(()=>{e(...a),t.current.delete(n)},r);t.current.set(n,i)}},[]),i=(0,a.useCallback)((e,t)=>{let n=e.toString();return(...a)=>{let s=Date.now(),i=r.current.get(n);(!i||s-i.lastRun>=t)&&(e(...a),r.current.set(n,{timer:setTimeout(()=>{r.current.delete(n)},t),lastRun:s}))}},[]),o=(0,a.useCallback)(e=>{a.startTransition(()=>{e.forEach(e=>e())})},[]),c=(0,a.useCallback)((e,t)=>{performance.now(),t(),performance.now()},[]),d=(0,a.useMemo)(()=>({debounce:s,throttle:i,batchUpdate:o,measurePerformance:c}),[s,i,o,c]);return(0,n.jsx)(l.Provider,{value:d,children:e})}var d=r(59211);function u(){let{resolvedTheme:e,setTheme:t}=(0,i.F)();return(0,a.useEffect)(()=>{let r=window.matchMedia("(prefers-color-scheme: dark)");function n(){e===(r.matches?"dark":"light")&&t("system")}return n(),r.addEventListener("change",n),()=>{r.removeEventListener("change",n)}},[e,t]),null}let m=(0,a.createContext)({});function p({children:e}){var t;let r;let l=(t=(0,s.usePathname)(),r=(0,a.useRef)(),(0,a.useEffect)(()=>{r.current=t},[t]),r.current);return(0,n.jsx)(m.Provider,{value:{previousPathname:l},children:(0,n.jsx)(d.PK,{children:(0,n.jsx)(c,{children:(0,n.jsx)(i.f,{attribute:"class",disableTransitionOnChange:!0,children:(0,n.jsxs)(o,{children:[(0,n.jsx)(u,{}),e]})})})})})}},6603:function(e,t,r){r.d(t,{d:function(){return f}});var n=r(57437),a=r(2265),s=r(86574),i=r(9880),o=r(1479),l=r(78298),c=r(74440),d=r(42208),u=r(30401),m=r(78867),p=r(92735),h=r(47416),g=r(93448);function f({children:e,className:t,language:r}){let[f,x]=(0,a.useState)(!1),[b,y]=(0,a.useState)(!1),[v,w]=(0,a.useState)(!1),[j,k]=(0,a.useState)(!1),{theme:N}=(0,l.F)(),C=(0,a.useRef)(null);(0,a.useEffect)(()=>{y(!0)},[]);let S=(()=>{if(r)return r;if(t){let e=t.match(/language-(\w+)/);return e?e[1]:"text"}return"text"})(),E=e.trim(),L=async()=>{try{await navigator.clipboard.writeText(E),x(!0),setTimeout(()=>x(!1),2e3)}catch(t){let e=document.createElement("textarea");e.value=E,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),x(!0),setTimeout(()=>x(!1),2e3)}},$=()=>{w(!v)},F=e=>({js:"JavaScript",jsx:"React JSX",ts:"TypeScript",tsx:"React TSX",py:"Python",python:"Python",java:"Java",cpp:"C++",c:"C",cs:"C#",php:"PHP",rb:"Ruby",go:"Go",rs:"Rust",swift:"Swift",kt:"Kotlin",scala:"Scala",sh:"Shell",bash:"Bash",zsh:"Zsh",fish:"Fish",ps1:"PowerShell",sql:"SQL",html:"HTML",css:"CSS",scss:"SCSS",sass:"Sass",less:"Less",json:"JSON",xml:"XML",yaml:"YAML",yml:"YAML",toml:"TOML",ini:"INI",dockerfile:"Dockerfile",makefile:"Makefile",md:"Markdown",markdown:"Markdown",text:"Plain Text",txt:"Plain Text"})[e.toLowerCase()]||e.toUpperCase();return b?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:(0,g.cn)("relative group my-8 transition-all duration-500 hover:shadow-xl",j&&"scale-[1.02]"),ref:C,onMouseEnter:()=>k(!0),onMouseLeave:()=>k(!1),style:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d"},children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl"}),(0,n.jsxs)("div",{className:"relative flex items-center justify-between bg-gradient-to-r from-muted/90 to-muted/70 backdrop-blur-sm px-4 py-3 rounded-t-xl border border-border/50 border-b-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("span",{className:"inline-flex items-center gap-2 px-3 py-1.5 bg-primary/10 text-primary text-sm font-semibold rounded-full border border-primary/20 hover:bg-primary/20 hover:scale-105 transition-all duration-200",children:[(0,n.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),F(S)]}),(0,n.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,n.jsxs)("span",{className:"flex items-center gap-1",children:[(0,n.jsx)(d.Z,{className:"w-3 h-3"}),E.split("\n").length," lines"]}),(0,n.jsxs)("span",{className:"flex items-center gap-1",children:["\uD83D\uDCCA ",E.length," chars"]})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("button",{onClick:L,className:(0,g.cn)("flex items-center gap-2 px-3 py-1.5 text-xs bg-background/80 hover:bg-background border border-border/50 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md group/btn",f&&"bg-green-500/10 border-green-500/20 text-green-600"),style:{transform:"perspective(100px) translateZ(5px)",transformStyle:"preserve-3d"},children:f?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.Z,{className:"w-3 h-3 group-hover/btn:scale-110 transition-transform"}),(0,n.jsx)("span",{children:"已复制"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(m.Z,{className:"w-3 h-3 group-hover/btn:scale-110 transition-transform"}),(0,n.jsx)("span",{children:"复制"})]})}),(0,n.jsxs)("button",{onClick:()=>{let e=new Blob([E],{type:"text/plain"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download=`code.${S}`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t)},className:"flex items-center gap-2 px-3 py-1.5 text-xs bg-background/80 hover:bg-background border border-border/50 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md group/btn",style:{transform:"perspective(100px) translateZ(5px)",transformStyle:"preserve-3d"},children:[(0,n.jsx)(p.Z,{className:"w-3 h-3 group-hover/btn:scale-110 transition-transform"}),(0,n.jsx)("span",{children:"下载"})]}),(0,n.jsxs)("button",{onClick:$,className:"flex items-center gap-2 px-3 py-1.5 text-xs bg-background/80 hover:bg-background border border-border/50 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md group/btn",style:{transform:"perspective(100px) translateZ(5px)",transformStyle:"preserve-3d"},children:[(0,n.jsx)(h.Z,{className:"w-3 h-3 group-hover/btn:scale-110 transition-transform"}),(0,n.jsx)("span",{children:"展开"})]})]})]}),(0,n.jsxs)("div",{className:"relative border border-border/50 border-t-0 rounded-b-xl overflow-hidden",children:[(0,n.jsx)(s.Z,{language:S,style:"dark"===N?i.Z:o.Z,customStyle:{margin:0,background:"transparent",fontSize:"14px",lineHeight:"1.6",padding:"1.5rem"},showLineNumbers:E.split("\n").length>5,lineNumberStyle:{minWidth:"3em",paddingRight:"1em",color:"dark"===N?"#6b7280":"#9ca3af",borderRight:`1px solid ${"dark"===N?"#374151":"#e5e7eb"}`,marginRight:"1em"},wrapLines:!0,wrapLongLines:!0,children:E}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"})]})]}),v&&(0,n.jsx)(()=>(0,n.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 animate-fade-in",children:(0,n.jsxs)("div",{className:"w-full max-w-7xl max-h-full bg-card rounded-xl border border-border/50 shadow-2xl overflow-hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border/30 bg-muted/50",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("span",{className:"inline-flex items-center gap-2 px-3 py-1.5 bg-primary/10 text-primary text-sm font-semibold rounded-full border border-primary/20",children:[(0,n.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),F(S)]}),(0,n.jsxs)("span",{className:"text-sm text-muted-foreground",children:[E.split("\n").length," lines"]})]}),(0,n.jsx)("button",{onClick:$,className:"p-2 hover:bg-muted/50 rounded-md transition-colors",children:(0,n.jsx)(c.Z,{className:"w-4 h-4"})})]}),(0,n.jsx)("div",{className:"p-6 overflow-auto max-h-[80vh] bg-muted/20",children:(0,n.jsx)(s.Z,{language:S,style:"dark"===N?i.Z:o.Z,customStyle:{margin:0,background:"transparent",fontSize:"14px",lineHeight:"1.6"},showLineNumbers:!0,wrapLines:!0,wrapLongLines:!0,children:E})})]})}),{})]}):(0,n.jsx)("div",{className:"relative group",children:(0,n.jsx)("pre",{className:"bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto",children:(0,n.jsx)("code",{className:t,children:e})})})}},40341:function(e,t,r){r.d(t,{OptimizedBlogContent:function(){return c}});var n=r(57437),a=r(2265),s=r(6603),i=r(93448);function o({src:e,alt:t,className:r,priority:s=!1,placeholder:o,onLoad:l,onError:c,onClick:d}){let[u,m]=(0,a.useState)(!0),[p,h]=(0,a.useState)(!1),[g,f]=(0,a.useState)(s),x=(0,a.useRef)(null),b=(0,a.useRef)(null);return(0,a.useEffect)(()=>{if(s)return;let e=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(f(!0),e.unobserve(t.target))})},{rootMargin:"50px"});return b.current&&e.observe(b.current),()=>e.disconnect()},[s]),(0,n.jsxs)("div",{ref:b,className:(0,i.cn)("relative overflow-hidden bg-muted rounded-lg",r),onClick:d,style:{cursor:d?"pointer":"default"},children:[u&&(0,n.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,n.jsx)("img",{src:o||`data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)" />
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="system-ui" font-size="14">Loading...</text>
      </svg>
    `)}`,alt:"",className:"w-full h-full object-cover opacity-50"}),(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsx)("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"})})]}),p&&(0,n.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-muted text-muted-foreground",children:[(0,n.jsx)("svg",{className:"w-12 h-12 mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,n.jsx)("span",{className:"text-sm",children:"Failed to load image"})]}),g&&!p&&(0,n.jsx)("img",{ref:x,src:e,alt:t,onLoad:()=>{m(!1),h(!1),l?.()},onError:()=>{m(!1),h(!0),c?.(Error(`Failed to load image: ${e}`))},className:(0,i.cn)("w-full h-full object-cover transition-opacity duration-500",u?"opacity-0":"opacity-100"),loading:s?"eager":"lazy",decoding:"async"}),!u&&!p&&(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"})]})}function l({src:e,alt:t,onClose:r}){return(0,a.useEffect)(()=>{let e=e=>{"Escape"===e.key&&r()};return document.addEventListener("keydown",e),document.body.style.overflow="hidden",()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[r]),(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",onClick:r,children:(0,n.jsxs)("div",{className:"relative max-w-[90vw] max-h-[90vh] p-4",children:[(0,n.jsx)("button",{onClick:r,className:"absolute -top-2 -right-2 z-10 w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors",children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,n.jsx)(o,{src:e,alt:t,priority:!0,className:"max-w-full max-h-full rounded-lg shadow-2xl"})]})})}function c({content:e,className:t=""}){let i=(0,a.useRef)(null),[o,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(!1);!function({enabled:e=!0,onMetricsCollected:t,debug:r=!1}={}){let n=(0,a.useRef)(null),s=(0,a.useRef)([]);(0,a.useEffect)(()=>{if(!e)return;let r=()=>{try{let e=performance.getEntriesByType("navigation")[0],r=performance.getEntriesByType("paint"),a={pageLoadTime:e.loadEventEnd-e.fetchStart,domContentLoadedTime:e.domContentLoadedEventEnd-e.fetchStart},i=r.find(e=>"first-contentful-paint"===e.name);if(i&&(a.firstContentfulPaint=i.startTime),n.current=a,"PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),r=t[t.length-1];r&&n.current&&(n.current.largestContentfulPaint=r.startTime)});e.observe({entryTypes:["largest-contentful-paint"]}),s.current.push(e);let t=0,r=new PerformanceObserver(e=>{for(let r of e.getEntries())r.hadRecentInput||(t+=r.value);n.current&&(n.current.cumulativeLayoutShift=t)});r.observe({entryTypes:["layout-shift"]}),s.current.push(r);let a=new PerformanceObserver(e=>{for(let t of e.getEntries()){let e=t.processingStart-t.startTime;n.current&&(n.current.firstInputDelay=e)}});a.observe({entryTypes:["first-input"]}),s.current.push(a);let i=new PerformanceObserver(e=>{let t=e.getEntries();if(t.length>0&&n.current){let e=t[t.length-1].startTime;n.current.timeToInteractive=e}});try{i.observe({entryTypes:["longtask"]}),s.current.push(i)}catch(e){}}setTimeout(()=>{n.current&&t&&t(n.current)},5e3)}catch(e){}};return"complete"===document.readyState?setTimeout(r,100):window.addEventListener("load",r),()=>{window.removeEventListener("load",r),s.current.forEach(e=>e.disconnect()),s.current=[]}},[e,t,r])}({enabled:!1,debug:!1,onMetricsCollected:e=>{}});let m=(0,a.useMemo)(()=>e.replace(/<h([1-6])>(.*?)<\/h[1-6]>/g,(e,t,r)=>{let n=r.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g,"").replace(/\s+/g,"-").trim()||`heading-${Math.random().toString(36).substr(2,9)}`;return`<h${t} id="${n}" class="${({1:"text-4xl font-bold mb-6 mt-12 text-foreground tracking-tight leading-tight scroll-mt-20 relative group",2:"text-3xl font-bold mb-4 mt-10 text-foreground tracking-tight leading-tight scroll-mt-20 relative group border-l-4 border-primary/30 pl-4 hover:border-primary/60 transition-colors duration-300",3:"text-2xl font-semibold mb-3 mt-8 text-foreground/90 tracking-tight leading-tight scroll-mt-20 relative group",4:"text-xl font-semibold mb-2 mt-6 text-foreground/85 tracking-tight leading-tight scroll-mt-20 relative group",5:"text-lg font-medium mb-2 mt-4 text-foreground/80 tracking-tight leading-tight scroll-mt-20 relative group",6:"text-base font-medium mb-2 mt-4 text-foreground/75 tracking-tight leading-tight scroll-mt-20 relative group"})[t]||""}">${r}</h${t}>`}).replace(/<p>/g,'<p class="mb-4 leading-8 text-foreground/85 text-lg tracking-wide hover:text-foreground transition-colors duration-300 relative group px-2 py-1 rounded-lg hover:bg-primary/5">').replace(/<blockquote>/g,'<blockquote class="relative pl-8 pr-6 py-6 my-8 bg-gradient-to-r from-primary/8 via-primary/5 to-transparent border-l-4 border-primary rounded-r-2xl italic text-foreground/80 text-lg leading-8 hover:from-primary/12 hover:via-primary/8 hover:to-primary/5 transition-all duration-500 group overflow-hidden">').replace(/<ul>/g,'<ul class="mb-8 space-y-3 text-foreground/85">').replace(/<ol>/g,'<ol class="mb-8 space-y-3 text-foreground/85 list-decimal list-inside">').replace(/<li>/g,'<li class="flex items-start gap-3 leading-7 hover:text-foreground transition-colors duration-300">').replace(/<table>/g,'<div class="overflow-x-auto my-8 rounded-lg border border-border"><table class="w-full border-collapse bg-background">').replace(/<\/table>/g,"</table></div>").replace(/<code class="inline-code">/g,'<code class="px-1.5 py-0.5 bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 rounded text-sm font-mono">'),[e]);return(0,a.useEffect)(()=>{if(!i.current||d)return;let e=e=>{e.style.columnCount="1",e.style.columns="1",e.style.columnFill="auto",e.style.columnGap="0",e.style.columnRule="none",e.style.columnSpan="none",e.style.columnWidth="auto",e.style.webkitColumnCount="1",e.style.mozColumnCount="1"};e(i.current),i.current.querySelectorAll("*").forEach(t=>{t instanceof HTMLElement&&e(t)});let t=i.current,n=()=>{let e=Array.from(t.querySelectorAll('[data-code-block="true"]')).map(e=>{let t=e.getAttribute("data-language")||"text",n=e.textContent||"",i=document.createElement("div");return e.parentNode?.replaceChild(i,e),Promise.resolve().then(r.t.bind(r,34040,19)).then(({createRoot:e})=>{e(i).render(a.createElement(s.d,{language:t,children:n}))})});t.querySelectorAll("img").forEach((e,t)=>{e.style.cursor="pointer",e.addEventListener("click",()=>{c({src:e.src,alt:e.alt})}),t>=2&&(e.loading="lazy")}),t.querySelectorAll("[data-icon-placeholder]").forEach(e=>{let t=e.getAttribute("data-src"),r=e.getAttribute("data-alt");if(t){let n=document.createElement("img");n.src=t,n.alt=r||"",n.className="inline-block w-6 h-6 mx-1",n.loading="lazy",e.parentNode?.replaceChild(n,e)}});let n=t.querySelectorAll("p");if(n.length>0){let e=n[n.length-1];e.classList.remove("mb-4"),e.classList.add("mb-0")}let i=t.querySelectorAll("h1, h2, h3, h4, h5, h6, blockquote, ul, ol, pre, div");if(i.length>0){let e=i[i.length-1];e.classList.contains("mb-8")&&(e.classList.remove("mb-8"),e.classList.add("mb-2")),e.classList.contains("mb-6")&&(e.classList.remove("mb-6"),e.classList.add("mb-2"))}return Promise.all(e)};"requestIdleCallback"in window?requestIdleCallback(()=>{n().then(()=>u(!0))}):setTimeout(()=>{n().then(()=>u(!0))},0)},[m,d]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{ref:i,className:`prose prose-zinc dark:prose-invert max-w-none ${t}`,style:{columnCount:"unset",columns:"unset",columnFill:"unset",columnGap:"unset"},dangerouslySetInnerHTML:{__html:m}}),o&&(0,n.jsx)(l,{src:o.src,alt:o.alt,onClose:()=>c(null)})]})}},29901:function(e,t,r){r.d(t,{r:function(){return c}});var n=r(57437),a=r(2265),s=r(93448),i=r(71769),o=r(22135),l=r(73247);function c({content:e,className:t}){let[r,c]=(0,a.useState)([]),[d,u]=(0,a.useState)(""),[m,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)(""),[f,x]=(0,a.useState)(!1),[b,y]=(0,a.useState)(!1),[v,w]=(0,a.useState)(!1),[j,k]=(0,a.useState)(0),[N,C]=(0,a.useState)(null),[S,E]=(0,a.useState)(""),[L,$]=(0,a.useState)({right:0,left:0}),F=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=()=>{let e=document.querySelector(".article-content[data-mdx-content]");if(e||(e=document.querySelector("[data-mdx-content]")),!e)return;let t=e.querySelectorAll("h1, h2, h3, h4, h5, h6"),r=[];t.forEach((n,a)=>{if(!e?.classList.contains("article-content")&&n.closest("[data-comment-section]"))return;let s=n.id,i=n.textContent||"",o=parseInt(n.tagName.charAt(1));if(s&&i){let e="",l=Array.from(t)[a+1],c=n.nextElementSibling;for(;c&&c!==l&&e.length<150;){let t=c.textContent||"";t.trim()&&(e+=t.trim()+" "),c=c.nextElementSibling}e.length>150&&(e=e.substring(0,150)+"..."),r.push({id:s,text:i,level:o,preview:e.trim()})}}),c(r),p(r.length>1)};e();let t=setTimeout(e,500),r=new MutationObserver(()=>{e()}),n=document.querySelector("article");n&&r.observe(n,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["id"]});let a=()=>{e()};return window.addEventListener("tocRefresh",a),()=>{clearTimeout(t),r.disconnect(),window.removeEventListener("tocRefresh",a)}},[e]),(0,a.useEffect)(()=>{let e=()=>{w(window.innerWidth>=1024);let e=document.querySelector(".article-content[data-mdx-content]");if(e||(e=document.querySelector("[data-mdx-content]")),e){let t=e.getBoundingClientRect();$({right:t.right,left:t.left})}};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{if(0===r.length)return;let e=()=>{let e=document.querySelector(".article-content[data-mdx-content]");if(e||(e=document.querySelector("[data-mdx-content]")),!e)return;let t=e.getBoundingClientRect(),n=window.scrollY;$({right:t.right,left:t.left});let a=t.top+n,s=Array.from(e.children).pop(),i=t.bottom+n;s&&(i=s.getBoundingClientRect().bottom+n);let o=i-a,l=n+200,c=0;o>0&&(c=l>=i-100?100:Math.min(100,Math.max(0,(l-a)/o*100))),k(c);let d=r.map(e=>document.getElementById(e.id)).filter(Boolean),m="";for(let e=0;e<d.length;e++){let t=d[e];if(t){let e=t.getBoundingClientRect(),r=e.top+n;r>=a&&r<=i&&e.top<=300&&(m=t.id)}}if(!m)for(let e=d.length-1;e>=0;e--){let t=d[e];if(t){let e=t.getBoundingClientRect(),r=e.top+n;if(r>=a&&r<=i&&e.top<=300){m=t.id;break}}}!m&&n+200<a+500&&d.length>0&&(m=d[0]?.id||""),u(m)},t=!1,n=()=>{t||(requestAnimationFrame(()=>{e(),t=!1}),t=!0)};return e(),window.addEventListener("scroll",n,{passive:!0}),()=>window.removeEventListener("scroll",n)},[r]);let B=(0,a.useCallback)(e=>{let t=document.getElementById(e);if(t){let e=t.getBoundingClientRect().top+window.pageYOffset+-180,r=document.documentElement.scrollHeight-window.innerHeight;e>r&&(e=Math.max(r,0)),e=Math.max(e,0),window.scrollTo({top:e,behavior:"smooth"}),y(!1)}},[]),A=r.filter(e=>e.text.toLowerCase().includes(h.toLowerCase())),R=({progress:e})=>{let t=2*Math.PI*20;return(0,n.jsxs)("div",{className:"relative w-12 h-12 group/progress",children:[(0,n.jsxs)("svg",{className:"w-12 h-12 transform -rotate-90",viewBox:"0 0 48 48",children:[(0,n.jsx)("circle",{cx:"24",cy:"24",r:20,stroke:"hsl(var(--muted))",strokeWidth:"3",fill:"none",className:"opacity-20"}),(0,n.jsx)("circle",{cx:"24",cy:"24",r:20,stroke:"hsl(var(--primary))",strokeWidth:"3",fill:"none",strokeDasharray:t,strokeDashoffset:t-e/100*t,className:"transition-all duration-300 ease-out drop-shadow-sm",strokeLinecap:"round"})]}),(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsxs)("span",{className:"text-xs font-semibold text-primary group-hover/progress:scale-110 transition-transform duration-200",children:[Math.round(e),"%"]})}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover/progress:opacity-100 transition-opacity duration-300 scale-110"})]})},T=e=>{let t=d===e.id,r=N===e.id,a=(e.level-1)*12+8;return(0,n.jsxs)("div",{className:"relative group/item",children:[(0,n.jsxs)("button",{onClick:()=>B(e.id),onMouseEnter:()=>{C(e.id),E(e.preview||"")},onMouseLeave:()=>{C(null),E("")},className:(0,s.cn)("w-full text-left text-sm transition-all duration-300 py-3 px-3 rounded-lg hover:bg-muted/50 group relative overflow-hidden hover:scale-105 hover:-translate-y-0.5 hover:shadow-lg",{"text-primary font-medium bg-gradient-to-r from-primary/15 to-primary/5 border-l-4 border-primary shadow-md scale-105":t,"text-muted-foreground hover:text-foreground":!t}),style:{paddingLeft:`${a}px`,transform:"perspective(200px) translateZ(0)",transformStyle:"preserve-3d"},children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),t&&(0,n.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary via-primary/80 to-primary/60 rounded-r-sm shadow-sm"}),(0,n.jsx)("div",{className:"relative z-10 space-y-1",children:(0,n.jsx)("span",{className:"block group-hover:text-foreground transition-colors font-medium",children:e.text})}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"})]}),r&&e.preview&&(0,n.jsxs)("div",{className:"absolute left-full top-0 ml-4 w-80 p-4 bg-card border border-border/50 rounded-xl shadow-xl backdrop-blur-sm z-50 animate-fade-in-up",style:{transform:"perspective(300px) translateZ(20px)",transformStyle:"preserve-3d"},children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-primary",children:[(0,n.jsx)(i.Z,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:"章节预览"})]}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:e.preview})]}),(0,n.jsx)("div",{className:"absolute left-0 top-4 transform -translate-x-1 w-2 h-2 bg-card border-l border-t border-border/50 rotate-45"})]})]},e.id)};return m?v?(0,n.jsx)("div",{className:(0,s.cn)("transition-all duration-700 ease-in-out transform-gpu",f?"w-16":"w-full max-h-[80vh]"),style:{position:"relative"},children:(0,n.jsxs)("div",{className:(0,s.cn)("bg-card/95 backdrop-blur-md border border-border/50 shadow-xl hover:shadow-2xl group/toc overflow-hidden flex flex-col transform-gpu",f?"rounded-full w-16 h-16 items-center justify-center cursor-pointer hover:scale-110 hover:shadow-primary/20 absolute transition-all duration-500 ease-out":"rounded-2xl max-h-[80vh] relative transition-all duration-500 ease-out"),style:f?{transform:"perspective(1000px) translateZ(0) translateX(calc(100% - 2rem))",transformStyle:"preserve-3d",willChange:"transform",right:"-2rem",width:"4rem",height:"4rem"}:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d",willChange:"transform",width:"100%",height:"auto"},ref:F,onClick:f?()=>x(!1):void 0,children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover/toc:opacity-100 transition-opacity duration-500 rounded-inherit"}),(0,n.jsxs)("div",{className:(0,s.cn)("absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300",f&&"opacity-100"),children:[(0,n.jsx)("div",{className:"transform transition-all duration-500 ease-out hover:scale-105",children:(0,n.jsx)(R,{progress:j})}),(0,n.jsxs)("div",{className:"absolute -top-12 -left-16 bg-card border rounded-lg px-3 py-1 text-xs whitespace-nowrap opacity-0 group-hover/toc:opacity-100 transition-all duration-300 pointer-events-none z-50 shadow-lg",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-medium",children:"目录导航"}),(0,n.jsxs)("div",{className:"text-muted-foreground",children:[Math.round(j),"% 已读"]})]}),(0,n.jsx)("div",{className:"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45"})]})]}),(0,n.jsx)("div",{className:(0,s.cn)("opacity-0 transition-opacity duration-300",!f&&"opacity-100"),children:!f&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"relative flex items-center justify-between p-4 border-b border-border/30",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)(R,{progress:j}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-sm font-semibold text-foreground",children:"目录导航"}),(0,n.jsxs)("p",{className:"text-xs text-muted-foreground",children:[A.length," 个章节"]})]})]}),(0,n.jsx)("button",{onClick:()=>x(!f),className:"p-2 rounded-lg hover:bg-muted/50 transition-all duration-300 hover:scale-110 group/btn",style:{transform:"perspective(100px) translateZ(5px)",transformStyle:"preserve-3d"},children:(0,n.jsx)(o.Z,{className:"w-4 h-4 group-hover/btn:scale-110 transition-transform duration-300"})})]}),(0,n.jsxs)("div",{className:"relative z-10 flex flex-col flex-1 min-h-0",children:[(0,n.jsx)("div",{className:"p-4 pb-2 flex-shrink-0",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"}),(0,n.jsx)("input",{type:"text",placeholder:"搜索章节...",value:h,onChange:e=>g(e.target.value),className:"w-full pl-10 pr-4 py-2 text-sm border border-border/50 rounded-lg bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 transition-all duration-200"})]})}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto space-y-2 px-4 pb-4 custom-scrollbar min-h-0",children:A.length>0?A.map(T):(0,n.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,n.jsx)(l.Z,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,n.jsx)("p",{className:"text-sm",children:"未找到匹配的章节"})]})})]})]})})]})}):(0,n.jsxs)("div",{className:"md:hidden",children:[(0,n.jsx)("button",{onClick:()=>y(!0),className:"fixed bottom-6 right-6 z-40 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110",children:"\uD83D\uDCCB"}),b&&(0,n.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:()=>y(!1)}),(0,n.jsx)("div",{className:(0,s.cn)("fixed bottom-0 left-0 right-0 z-50 bg-card border-t rounded-t-xl transition-transform duration-300",b?"translate-y-0":"translate-y-full"),children:(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold",children:"目录导航"}),(0,n.jsx)("button",{onClick:()=>y(!1),className:"p-2 rounded-md hover:bg-muted/50",children:"✕"})]}),(0,n.jsx)("div",{className:"mb-3",children:(0,n.jsx)("input",{type:"text",placeholder:"搜索章节...",value:h,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 text-sm border border-muted rounded-md bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20"})}),(0,n.jsx)("div",{className:"max-h-80 overflow-y-auto space-y-2 custom-scrollbar",children:A.map(T)})]})})]}):null}},45539:function(e,t,r){r.d(t,{CK:function(){return o},o5:function(){return i}});var n=r(57437),a=r(2265),s=r(93448);function i({content:e,className:t}){let[r,i]=(0,a.useState)([]),[o,l]=(0,a.useState)(""),[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),[p,h]=(0,a.useState)(!1),[g,f]=(0,a.useState)({top:0,isFixed:!1}),[x,b]=(0,a.useState)(""),[y,v]=(0,a.useState)([]),[w,j]=(0,a.useState)(new Set),[k,N]=(0,a.useState)({right:0,left:0}),C=(0,a.useRef)(null);(0,a.useRef)(null);let S=e=>{let t=[],r=[];return e.forEach(e=>{let n={...e,children:[],isCollapsed:w.has(e.id)};for(;r.length>0&&r[r.length-1].level>=n.level;)r.pop();if(0===r.length)t.push(n);else{let e=r[r.length-1];e.children||(e.children=[]),e.children.push(n)}r.push(n)}),t},E=(e,t)=>e.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g,"").replace(/\s+/g,"-").trim()||`heading-${t}`;(0,a.useEffect)(()=>{let e=setTimeout(()=>{let e=document.querySelector(".article-content[data-mdx-content]");if(!e){let e=document.querySelector("[data-mdx-content]");if(!e)return;let t=e.querySelectorAll("h1, h2, h3, h4, h5, h6"),r=[];t.forEach((e,t)=>{if(e.closest("[data-comment-section]"))return;let n=parseInt(e.tagName.charAt(1)),a=e.textContent||"",s=e.id;s||(s=E(a,t),e.id=s),r.push({id:s,text:a,level:n})});let n=S(r);i(n),d(n.length>0);return}let t=e.querySelectorAll("h1, h2, h3, h4, h5, h6"),r=[];t.forEach((e,t)=>{let n=parseInt(e.tagName.charAt(1)),a=e.textContent||"",s=e.id;s||(s=E(a,t),e.id=s),r.push({id:s,text:a,level:n})});let n=S(r);i(n),d(n.length>0)},100);return()=>clearTimeout(e)},[e,w]);let L=(e,t)=>{if(!t.trim())return e;let r=[],n=e=>{e.forEach(e=>{e.text.toLowerCase().includes(t.toLowerCase())&&r.push({...e,children:e.children||[]}),e.children&&e.children.length>0&&n(e.children)})};return n(e),r};(0,a.useEffect)(()=>{v(L(r,x))},[r,x]);let $=e=>{j(t=>{let r=new Set(t);return r.has(e)?r.delete(e):r.add(e),r})},F=e=>{let t=[],r=e=>{e.forEach(e=>{t.push(e.id),e.children&&e.children.length>0&&r(e.children)})};return r(e),t};(0,a.useEffect)(()=>{if(0===r.length)return;let e=F(r).map(e=>document.getElementById(e)).filter(Boolean);if(0!==e.length)return C.current=new IntersectionObserver(e=>{let t=e.filter(e=>e.isIntersecting);t.length>0&&l(t.reduce((e,t)=>t.boundingClientRect.top<e.boundingClientRect.top?t:e).target.id)},{rootMargin:"-180px 0% -50% 0%",threshold:0}),e.forEach(e=>{C.current?.observe(e)}),()=>{C.current?.disconnect()}},[r]),(0,a.useEffect)(()=>{let e=e=>{(e.ctrlKey||e.metaKey)&&e.shiftKey&&"T"===e.key&&(e.preventDefault(),m(!u))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[u]),(0,a.useEffect)(()=>{let e=()=>{let e=window.innerWidth;e<1400&&e>=1280?(h(!0),u||m(!0)):e>=1400&&h(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{let e,t=!1,r=()=>{let e=document.querySelector("article header");if(!e)return;let r=e.getBoundingClientRect(),n=window.scrollY,a=r.top+n;r.bottom,window.innerHeight;let s=a,i=!1;r.top<-r.height?(s=n+96,i=!0):r.top>=0&&(s=a,i=!1);let o=document.querySelector("article");if(o){let e=o.getBoundingClientRect().bottom+n,t=u?100:400;s+t>e&&(s=Math.max(e-t,a))}let l=document.querySelector("[data-mdx-content]");if(l){let e=l.getBoundingClientRect();N({right:e.right,left:e.left})}f({top:s,isFixed:i}),t=!1};r();let n=()=>{t||(requestAnimationFrame(r),t=!0)},a=()=>{clearTimeout(e),e=setTimeout(r,100)};return window.addEventListener("scroll",n,{passive:!0}),window.addEventListener("resize",a),()=>{window.removeEventListener("scroll",n),window.removeEventListener("resize",a),clearTimeout(e)}},[r,u]);let B=e=>{let t=document.getElementById(e);if(!t){for(let r of document.querySelectorAll("h1, h2, h3, h4, h5, h6"))if(r.textContent?.trim()===e||r.id===e){t=r;break}}if(t||(t=document.querySelector(`[data-id="${e}"]`)),t){let e=t.getBoundingClientRect().top+window.pageYOffset+-180,r=document.documentElement.scrollHeight-window.innerHeight;e>r&&(e=Math.max(r,0)),e=Math.max(e,0),window.scrollTo({top:e,behavior:"smooth"})}},A=(e,t=0)=>{let r=e.children&&e.children.length>0,a=w.has(e.id),i=o===e.id;return(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex items-center group",children:[r&&(0,n.jsx)("button",{onClick:()=>$(e.id),className:"flex items-center justify-center w-4 h-4 mr-1 rounded hover:bg-muted/50 transition-colors",title:a?"展开子章节":"折叠子章节",children:(0,n.jsx)("svg",{className:(0,s.cn)("w-3 h-3 transition-transform",a?"":"rotate-90"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),(0,n.jsx)("button",{onClick:()=>B(e.id),className:(0,s.cn)("flex-1 text-left text-sm transition-colors hover:text-foreground py-1 px-2 rounded-md hover:bg-muted/50",{"text-primary font-medium bg-muted":i,"text-muted-foreground":!i}),style:{paddingLeft:`${12*t+8}px`},children:(0,n.jsx)("span",{className:"truncate",children:e.text})})]}),r&&!a&&(0,n.jsx)("div",{className:"space-y-1",children:e.children.map(e=>A(e,t+1))})]},e.id)};return!c||r.length<2?null:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:(0,s.cn)("hidden md:block lg:hidden",t),children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:(0,s.cn)("absolute border bg-card shadow-sm cursor-pointer hover:scale-110 hover:shadow-lg transition-all duration-500 ease-out",u?"w-16 h-16 rounded-full flex items-center justify-center opacity-100 z-50":"w-0 h-0 opacity-0 pointer-events-none"),style:{right:"-2rem",top:"0"},onClick:u?()=>m(!1):void 0,children:(0,n.jsxs)("div",{className:"relative flex items-center justify-center w-full h-full group",children:[(0,n.jsxs)("div",{className:"relative w-12 h-12",children:[(0,n.jsxs)("svg",{className:"w-12 h-12 transform -rotate-90",viewBox:"0 0 48 48",children:[(0,n.jsx)("circle",{cx:"24",cy:"24",r:"18",stroke:"hsl(var(--muted))",strokeWidth:"3",fill:"none",className:"opacity-20"}),(0,n.jsx)("circle",{cx:"24",cy:"24",r:"18",stroke:"hsl(var(--primary))",strokeWidth:"3",fill:"none",strokeDasharray:2*Math.PI*18,strokeDashoffset:2*Math.PI*18-2*Math.PI*18*Math.min(100,Math.max(0,(F(r).findIndex(e=>e===o)+1)/F(r).length*100))/100,className:"transition-all duration-300 ease-out drop-shadow-sm",strokeLinecap:"round"})]}),(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsxs)("span",{className:"text-xs font-semibold text-primary group-hover:scale-110 transition-transform duration-200",children:[Math.min(100,Math.max(0,Math.round((F(r).findIndex(e=>e===o)+1)/F(r).length*100))),"%"]})}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-110"})]}),(0,n.jsxs)("div",{className:"absolute -top-16 -left-24 bg-card border rounded-lg px-3 py-2 text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50 shadow-lg",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-medium",children:"目录导航"}),(0,n.jsxs)("div",{className:"text-muted-foreground mt-1",children:[F(r).findIndex(e=>e===o)+1,"/",F(r).length," 章节"]}),o&&(0,n.jsx)("div",{className:"text-xs text-foreground mt-1 max-w-32 truncate",children:(()=>{let e=(t,r)=>{for(let n of t){if(n.id===r)return n;if(n.children){let t=e(n.children,r);if(t)return t}}return null},t=e(r,o);return t?.text||""})()})]}),(0,n.jsx)("div",{className:"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45"})]})]})}),(0,n.jsx)("div",{className:(0,s.cn)("transition-all duration-500 ease-out",u?"opacity-0 pointer-events-none":"opacity-100"),children:!u&&(0,n.jsx)("div",{className:"max-h-[calc(100vh-6rem)] overflow-y-auto transition-all duration-200 ease-out absolute",style:{top:`${g.top}px`,right:"0",transform:g.isFixed?"none":"translateY(0)",zIndex:10,willChange:"transform, top"},children:(0,n.jsx)("div",{className:"w-64 rounded-lg border bg-card shadow-sm transition-all duration-300 relative",children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"p-4 border-b space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("h3",{className:"text-sm font-semibold text-foreground",children:"目录"}),(0,n.jsx)("div",{className:(0,s.cn)("w-2 h-2 rounded-full transition-colors",g.isFixed?"bg-blue-500":"bg-green-500"),title:g.isFixed?"固定模式":"跟随模式"})]}),(0,n.jsx)("button",{onClick:()=>m(!u),className:"flex items-center justify-center w-6 h-6 rounded-md hover:bg-muted/50 transition-colors",title:"折叠目录 (Ctrl+Shift+T)",children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{type:"text",placeholder:"搜索章节...",value:x,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 text-sm border border-muted rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"}),x&&(0,n.jsx)("button",{onClick:()=>b(""),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,n.jsx)("nav",{className:"p-4 space-y-1 max-h-[calc(100vh-16rem)] overflow-y-auto",children:x?y.length>0?(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"text-xs text-muted-foreground mb-2",children:["找到 ",y.length," 个结果"]}),y.map(e=>(0,n.jsx)("button",{onClick:()=>B(e.id),className:(0,s.cn)("block w-full text-left text-sm transition-colors hover:text-foreground","py-2 px-2 rounded-md hover:bg-muted/50",{"text-primary font-medium bg-muted":o===e.id,"text-muted-foreground":o!==e.id}),children:(0,n.jsx)("span",{className:"truncate",children:e.text})},e.id))]}):(0,n.jsx)("div",{className:"text-sm text-muted-foreground text-center py-4",children:"未找到匹配的章节"}):(0,n.jsx)("div",{className:"space-y-1",children:r.map(e=>A(e,0))})})]})})})})]})}),p&&(0,n.jsx)("div",{className:"hidden xl:block fixed bottom-6 right-6 z-40",children:(0,n.jsxs)("div",{className:"bg-card border rounded-lg shadow-lg p-3 text-sm text-muted-foreground",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsx)("span",{children:"屏幕较窄，目录已自动折叠"})]}),(0,n.jsx)("div",{className:"text-xs mt-1 opacity-75",children:"按 Ctrl+Shift+T 切换目录"})]})})]})}function o(){let[e,t]=(0,a.useState)(0);return(0,a.useEffect)(()=>{let e=()=>{let e=document.querySelector(".article-content[data-mdx-content]");if(e||(e=document.querySelector("[data-mdx-content]")),!e)return;let r=window.scrollY,n=e.getBoundingClientRect(),a=n.top+r,s=n.bottom+r;if(e.classList.contains("article-content")){let t=Array.from(e.children).pop();t&&(s=t.getBoundingClientRect().bottom+r)}else{let e=document.querySelector("[data-comment-section]");e&&(s=Math.min(s,e.getBoundingClientRect().top+r-20))}let i=s-a,o=r+200,l=0;i>0&&(l=o>=s-50?100:Math.min(100,Math.max(0,(o-a)/i*100))),t(l)};return window.addEventListener("scroll",e),e(),()=>window.removeEventListener("scroll",e)},[]),(0,n.jsx)("div",{className:"fixed top-0 left-0 right-0 z-50 h-1 bg-muted",children:(0,n.jsx)("div",{className:"h-full bg-primary transition-all duration-150 ease-out",style:{width:`${e}%`}})})}},94258:function(e,t,r){r.d(t,{CommentStats:function(){return l}});var n=r(57437),a=r(2265),s=r(82718),i=r(42208),o=r(88997);function l({path:e,className:t="",showIcons:r=!0}){let[l,c]=(0,a.useState)({commentCount:0,pageViews:0,likes:0}),[d,u]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{let t=new AbortController,r=async()=>{try{u(!0);let[r,n]=await Promise.all([fetch(`/api/waline/comment/count?path=${encodeURIComponent(e)}`,{signal:t.signal,headers:{Accept:"application/json"}}),fetch(`/api/waline/pageview?path=${encodeURIComponent(e)}`,{signal:t.signal,headers:{Accept:"application/json"}})]);if(t.signal.aborted)return;let[a,s]=await Promise.all([r.json(),n.json()]);if(t.signal.aborted)return;let i=a.success?a.data:0,o=s.success?s.time:0,l=s.success&&s.reaction?.[0]||0;c({commentCount:i,pageViews:o,likes:l})}catch(e){e instanceof Error&&"AbortError"!==e.name&&!t.signal.aborted&&c({commentCount:0,pageViews:0,likes:0})}finally{t.signal.aborted||u(!1)}};if(e){let e=setTimeout(r,100);return()=>{clearTimeout(e),t.abort()}}return u(!1),()=>{t.abort()}},[e]),d)?(0,n.jsxs)("div",{className:`flex flex-row items-center justify-center gap-6 ${t}`,children:[(0,n.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground",children:[r&&(0,n.jsx)(s.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"animate-pulse",children:"-"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground",children:[r&&(0,n.jsx)(i.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"animate-pulse",children:"-"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground",children:[r&&(0,n.jsx)(o.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"animate-pulse",children:"-"})]})]}):(0,n.jsxs)("div",{className:`flex flex-row items-center justify-center gap-6 ${t}`,children:[(0,n.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[r&&(0,n.jsx)(s.Z,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:[l.commentCount," comments"]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[r&&(0,n.jsx)(i.Z,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:[l.pageViews," views"]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[r&&(0,n.jsx)(o.Z,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:[l.likes," likes"]})]})]})}},83275:function(e,t,r){r.d(t,{WalineComment:function(){return m}});var n=r(57437),a=r(74621),s=r(2265),i=r(78298);let o=null,l=null,c=0;async function d(e=0){try{let e=new AbortController,t=setTimeout(()=>e.abort(),5e3),r=await fetch("/api/system-config/waline/config",{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal,cache:"default"});if(clearTimeout(t),!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let n=await r.json(),a={serverURL:n.server_url,lang:n.lang||"zh-CN"};if(!a.serverURL||""===a.serverURL.trim())throw Error("Invalid server URL received");return a}catch(r){if(e<3){var t;return await (t=1e3*Math.pow(2,e),new Promise(e=>setTimeout(e,t))),d(e+1)}return{serverURL:"https://waline.jyaochen.cn",lang:"zh-CN"}}}async function u(){let e=Date.now();if(o&&e-c<3e5)return o;if(l)return l;l=d();try{let t=await l;return o=t,c=e,t}catch(e){if(o)return o;throw e}finally{l=null}}function m({path:e,title:t,className:r=""}){let o=(0,s.useRef)(null),{resolvedTheme:l}=(0,i.F)(),[c,d]=(0,s.useState)(null),[m,p]=(0,s.useState)(!1),[h,g]=(0,s.useState)(!0),[f,x]=(0,s.useState)(null),b=(0,s.useRef)(null),y=(0,s.useRef)(null),v=(0,s.useCallback)(e=>{if(e&&"function"==typeof e.destroy)try{e.destroy()}catch(e){}},[]),w=(0,s.useCallback)(()=>{if(!o.current)return;let e=o.current;e.querySelectorAll("[data-waline] .wl-action").forEach(t=>{(t.textContent?.includes("\uD83D\uDE00")||t.getAttribute("title")?.includes("emoji"))&&t.addEventListener("click",t=>{setTimeout(()=>{let t=e.querySelector("[data-waline] .wl-emoji-popup");if(t){if(!t.querySelector(".wl-popup-header")){let e=document.createElement("div");e.className="wl-popup-header",e.innerHTML=`
                  <div class="wl-popup-title">选择表情</div>
                  <button class="wl-popup-close" type="button">\xd7</button>
                `,t.insertBefore(e,t.firstChild);let r=e.querySelector(".wl-popup-close");r&&r.addEventListener("click",()=>{t.classList.remove("display")})}let e=r=>{r.target===t&&(t.classList.remove("display"),document.removeEventListener("click",e))};setTimeout(()=>{document.addEventListener("click",e)},100);let r=e=>{"Escape"===e.key&&(t.classList.remove("display"),document.removeEventListener("keydown",r))};document.addEventListener("keydown",r)}},50)})}),e.querySelectorAll("[data-waline] .wl-action").forEach(t=>{(t.textContent?.includes("GIF")||t.getAttribute("title")?.includes("gif"))&&t.addEventListener("click",t=>{setTimeout(()=>{let t=e.querySelector("[data-waline] .wl-gif-popup");if(t&&!t.querySelector(".wl-popup-header")){let e=document.createElement("div");e.className="wl-popup-header",e.innerHTML=`
                  <div class="wl-popup-title">选择GIF</div>
                  <button class="wl-popup-close" type="button">\xd7</button>
                `,t.insertBefore(e,t.firstChild);let r=e.querySelector(".wl-popup-close");r&&r.addEventListener("click",()=>{t.classList.remove("display")})}},50)})})},[]),j=(0,s.useCallback)(()=>{if(!o.current)return;let e=o.current;e.querySelector(".wl-login"),e.querySelector(".wl-logout"),e.querySelector(".wl-panel")&&setTimeout(()=>{k()},100)},[]),k=(0,s.useCallback)(()=>{if(!o.current)return;let e=o.current;if(e.querySelector(".wl-login"),e.querySelector(".wl-logout"),!e.querySelector(".wl-panel"))return;let t={nick:"用户",avatar:"",link:""};try{let e=localStorage.getItem("WALINE_USER")||localStorage.getItem("waline-user");if(e){let r=JSON.parse(e);(r.display_name||r.nick)&&(t.nick=r.display_name||r.nick),r.avatar&&(t.avatar=r.avatar),(r.url||r.link)&&(t.link=r.url||r.link)}}catch(e){}for(let r of e.querySelectorAll(".wl-user")){if(r.closest(".wl-card")||r.closest(".wl-item")||r.closest(".wl-comment"))continue;let e=r.querySelector(".wl-nick");e&&e.textContent?.trim()&&(t.nick=e.textContent.trim());let n=r.querySelector(".wl-avatar");if(n){let e=n.querySelector("img");if(e&&e.src&&!e.src.includes("data:")&&!e.src.includes("blob:"))t.avatar=e.src;else{let e=window.getComputedStyle(n).backgroundImage;if(e&&"none"!==e&&e.includes("url(")){let r=e.slice(5,-2);r.includes("data:")||r.includes("blob:")||(t.avatar=r)}}}let a=r.querySelector("a[href]")||r.closest("a[href]");if(a&&a.href&&!a.href.includes("javascript:")&&(t.link=a.href),"用户"!==t.nick||t.avatar)break}if("用户"===t.nick){let r=e.querySelector('input[placeholder*="nick"], input[name="nick"]');r&&r.value?.trim()&&(t.nick=r.value.trim())}let r=()=>{e.querySelectorAll(".wl-user").forEach(e=>{if(!(e.closest(".wl-card")||e.closest(".wl-item")||e.closest(".wl-comment"))){let t=e.querySelector(".wl-avatar");t&&!t.hasAttribute("data-click-handler")&&(t.setAttribute("data-click-handler","true"),t.style.cursor="pointer",t.addEventListener("click",async()=>{let e=(await u()).serverURL,t="";try{let e=localStorage.getItem("WALINE_USER")||localStorage.getItem("waline-user");e&&(t=JSON.parse(e).token||"")}catch(e){}let r=t?`${e}/ui/profile?lng=en-US&token=${t}`:`${e}/ui/login?lng=en-US`;window.open(r,"_blank")}))}})};r();let n=setInterval(r,500);setTimeout(()=>clearInterval(n),1e4);let a=e.querySelector(".wl-user-status-enhanced");a&&a.remove();let s=e.querySelector(".wl-meta, .wl-panel");s&&s.querySelectorAll("button").forEach(e=>{e.addEventListener("mouseenter",()=>{e.classList.contains("wl-login-enhanced")?e.style.background="hsl(var(--primary) / 0.9)":(e.style.background="hsl(var(--primary) / 0.08)",e.style.borderColor="hsl(var(--primary) / 0.3)",e.style.color="hsl(var(--primary))"),e.style.transform="translateY(-1px)"}),e.addEventListener("mouseleave",()=>{e.classList.contains("wl-login-enhanced")?e.style.background="hsl(var(--primary))":(e.style.background="transparent",e.style.borderColor="hsl(var(--border))",e.style.color="hsl(var(--muted-foreground))"),e.style.transform="translateY(0)"})})},[]),N=(0,s.useCallback)(async()=>{if(o.current){y.current&&y.current.abort(),y.current=new AbortController;try{if(g(!0),x(null),await new Promise(e=>setTimeout(e,100)),!o.current||y.current?.signal.aborted)return;let t=await u();o.current.innerHTML="";let r=(0,a.S1)({el:o.current,serverURL:t.serverURL,path:e,dark:"dark"===l,locale:{placeholder:"Share your thoughts and join the discussion...",admin:"Admin",level0:"Newcomer",level1:"Explorer",level2:"Contributor",level3:"Expert",level4:"Master",level5:"Legend",anonymous:"Anonymous",login:"Sign In",logout:"Sign Out",profile:"Profile",nickError:"Nickname must be at least 3 characters",mailError:"Please enter a valid email address",wordHint:"Please enter your comment",sofa:"Be the first to share your thoughts!",submit:"Publish Comment",reply:"Reply",cancelReply:"Cancel Reply",comment:"Comment",refresh:"Refresh",more:"Load More Comments...",preview:"Preview",emoji:"Emoji",uploadImage:"Upload Image",seconds:"seconds ago",minutes:"minutes ago",hours:"hours ago",days:"days ago",now:"just now"},emoji:["//unpkg.com/@waline/emojis@1.2.0/weibo","//unpkg.com/@waline/emojis@1.2.0/alus","//unpkg.com/@waline/emojis@1.2.0/bilibili"],meta:["nick","mail","link"],requiredMeta:["nick"],login:"enable",wordLimit:[0,1e3],pageSize:10,lang:"en-US",reaction:!0,imageUploader:!1,texRenderer:!1,search:!1});y.current?.signal.aborted||(d(r),p(!0),g(!1),setTimeout(()=>{o.current&&(o.current.classList.add("waline-loaded"),w(),j())},300))}catch(e){e instanceof Error&&"AbortError"!==e.name&&(x("Failed to load comments. Please refresh the page."),g(!1))}}},[e,l,v]);return(0,s.useEffect)(()=>(p(!1),b.current=setTimeout(()=>{N()},50),()=>{b.current&&clearTimeout(b.current),y.current&&y.current.abort(),c&&v(c),p(!1),d(null),g(!0),x(null)}),[e,l,N,v]),(0,n.jsxs)("div",{className:`waline-container-premium ${r}`,children:[f&&(0,n.jsxs)("div",{className:"mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center",children:[(0,n.jsx)("p",{children:f}),(0,n.jsx)("button",{onClick:()=>{x(null),N()},className:"mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors",children:"Retry"})]}),(0,n.jsx)("div",{className:"waline-container-premium",children:(0,n.jsx)("div",{ref:o,className:`waline-wrapper-premium transition-all duration-500 ${m?"opacity-100":"opacity-0"}`,style:{minHeight:m?"auto":"300px"}})}),h&&!f&&(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 space-y-4",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"w-12 h-12 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin"}),(0,n.jsx)("div",{className:"absolute inset-0 w-12 h-12 border-4 border-transparent border-r-primary/50 rounded-full animate-spin",style:{animationDirection:"reverse",animationDuration:"0.8s"}})]}),(0,n.jsxs)("div",{className:"text-center space-y-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-foreground",children:"Loading Discussion"}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Preparing comment system..."})]})]})]})}},79059:function(e,t,r){r.d(t,{O1:function(){return l},Uf:function(){return m},Vj:function(){return u},ay:function(){return o},dA:function(){return d},oX:function(){return c}});var n=r(57437),a=r(2265),s=r(93448),i=r(58608);function o({children:e,delay:t=0,className:r}){let[i,o]=(0,a.useState)(!1),l=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let e=l.current;if(!e)return;let r=new IntersectionObserver(([e])=>{e.isIntersecting&&setTimeout(()=>o(!0),t)},{threshold:.1});return r.observe(e),()=>r.unobserve(e)},[t]),(0,n.jsx)("div",{ref:l,className:(0,s.cn)("transition-all duration-700 ease-out",i?"opacity-100 translate-y-0":"opacity-0 translate-y-8",r),children:e})}function l({children:e,delay:t=0,className:r}){let[i,o]=(0,a.useState)(!1),l=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let e=l.current;if(!e)return;let r=new IntersectionObserver(([e])=>{e.isIntersecting&&setTimeout(()=>o(!0),t)},{threshold:.1});return r.observe(e),()=>r.unobserve(e)},[t]),(0,n.jsx)("div",{ref:l,className:(0,s.cn)("transition-all duration-500 ease-out",i?"opacity-100 scale-100":"opacity-0 scale-95",r),children:e})}function c({children:e,direction:t="left",delay:r=0,className:i}){let[o,l]=(0,a.useState)(!1),c=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let e=c.current;if(!e)return;let t=new IntersectionObserver(([e])=>{e.isIntersecting&&setTimeout(()=>l(!0),r)},{threshold:.1});return t.observe(e),()=>t.unobserve(e)},[r]),(0,n.jsx)("div",{ref:c,className:(0,s.cn)("transition-all duration-600 ease-out",o?"opacity-100":"opacity-0",(()=>{if(o)return"translate-x-0 translate-y-0";switch(t){case"left":default:return"-translate-x-8";case"right":return"translate-x-8";case"up":return"-translate-y-8";case"down":return"translate-y-8"}})(),i),children:e})}function d({children:e,delay:t=0,staggerDelay:r=100,className:a}){return(0,n.jsx)("div",{className:a,children:e.map((e,a)=>(0,n.jsx)(o,{delay:t+a*r,children:e},a))})}function u({children:e,color:t=i.AS.primary.main,intensity:r="normal",className:a}){return(0,n.jsx)("div",{className:(0,s.cn)("transition-all duration-300",a),style:{filter:`drop-shadow(0 0 10px ${t}${Math.round(255*({subtle:.2,normal:.3,intense:.5})[r]).toString(16)})`},children:e})}function m({children:e,color:t=i.AS.primary.main,className:r}){let[o,l]=(0,a.useState)([]);return(0,n.jsxs)("div",{className:(0,s.cn)("relative overflow-hidden",r),onClick:e=>{let t=e.currentTarget.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top,a=Date.now();l(e=>[...e,{x:r,y:n,id:a}]),setTimeout(()=>{l(e=>e.filter(e=>e.id!==a))},600)},children:[e,o.map(e=>(0,n.jsx)("span",{className:"absolute animate-ping rounded-full opacity-75",style:{left:e.x-10,top:e.y-10,width:20,height:20,backgroundColor:t,animationDuration:"600ms"}},e.id))]})}},52043:function(e,t,r){r.d(t,{default:function(){return l}});var n=r(57437),a=r(24721),s=r(27648),i=r(18191),o=r(93448);function l({className:e,style:t,socialLinks:r}){return r&&0!==r.length?(0,n.jsx)("div",{className:(0,o.cn)("mt-6 flex items-center gap-2",e),style:t,children:r.map((e,t)=>(0,n.jsxs)("div",{className:"group relative",style:{animationDelay:`${100*t}ms`},children:[(0,n.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300 animate-glow-pulse"}),(0,o.xf)(e.href)?(0,n.jsxs)("a",{href:(0,o.h1)(e.href,a.fI),target:"_blank",rel:"noreferrer","aria-label":`Follow on ${e.name}`,className:"relative inline-flex h-10 w-10 items-center justify-center rounded-lg border border-muted-foreground/20 bg-background/50 backdrop-blur-sm transition-all duration-300 ease-smooth hover:bg-primary/10 hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg group-hover:text-primary animate-float-gentle",children:[(0,n.jsx)(i.CustomIcon,{name:e.icon}),(0,n.jsx)("span",{className:"sr-only",children:e.name})]}):(0,n.jsxs)(s.default,{href:(0,o.h1)(e.href,a.fI),target:"_blank",rel:"noreferrer","aria-label":`Follow on ${e.name}`,className:"relative inline-flex h-10 w-10 items-center justify-center rounded-lg border border-muted-foreground/20 bg-background/50 backdrop-blur-sm transition-all duration-300 ease-smooth hover:bg-primary/10 hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg group-hover:text-primary animate-float-gentle",children:[(0,n.jsx)(i.CustomIcon,{name:e.icon}),(0,n.jsx)("span",{className:"sr-only",children:e.name})]})]},e.name))}):(0,n.jsx)("div",{className:(0,o.cn)("mt-6 flex items-center",e),style:t})}},90648:function(e,t,r){r.d(t,{W2:function(){return l},Zb:function(){return i},le:function(){return o}});var n=r(57437),a=r(2265),s=r(61994);let i=(0,a.forwardRef)(function({className:e,children:t,...r},a){return(0,n.jsx)("div",{ref:a,className:(0,s.Z)("sm:px-8",e),...r,children:(0,n.jsx)("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:t})})}),o=(0,a.forwardRef)(function({className:e,children:t,...r},a){return(0,n.jsx)("div",{ref:a,className:(0,s.Z)("relative px-4 sm:px-8 lg:px-12",e),...r,children:(0,n.jsx)("div",{className:"mx-auto max-w-2xl lg:max-w-5xl",children:t})})}),l=(0,a.forwardRef)(function({children:e,...t},r){return(0,n.jsx)(i,{ref:r,...t,children:(0,n.jsx)(o,{children:e})})})},36629:function(e,t,r){r.d(t,{s:function(){return S}});var n=r(57437),a=r(2265),s=r(48736),i=r(82023),o=r(76858),l=r(42208),c=r(18191),d=r(92331);function u({isActive:e,color:t="#3B82F6",particleCount:r=20,className:s=""}){let i=(0,a.useRef)(null),o=(0,a.useRef)(),l=(0,a.useRef)([]),[c,d]=(0,a.useState)({width:0,height:0}),u=(e,r)=>({x:e,y:r,vx:(Math.random()-.5)*2,vy:(Math.random()-.5)*2,life:0,maxLife:60+60*Math.random(),size:1+3*Math.random(),color:t,opacity:.8}),m=e=>(e.x+=e.vx,e.y+=e.vy,e.life++,e.opacity=.8*(1-e.life/e.maxLife),e.life<e.maxLife&&e.x>0&&e.x<c.width&&e.y>0&&e.y<c.height),p=()=>{let e=i.current;if(!e)return;let t=e.getContext("2d");t&&(t.clearRect(0,0,c.width,c.height),l.current.forEach(e=>{t.save(),t.globalAlpha=e.opacity,t.fillStyle=e.color,t.beginPath(),t.arc(e.x,e.y,e.size,0,2*Math.PI),t.fill(),t.restore()}))},h=()=>{l.current=l.current.filter(m),p(),(e||l.current.length>0)&&(o.current=requestAnimationFrame(h))},g=(e,t,r=5)=>{for(let n=0;n<r;n++)l.current.push(u(e,t))};return(0,a.useEffect)(()=>{let e=()=>{let e=i.current;if(!e||!e.parentElement)return;let t=e.parentElement.getBoundingClientRect();d({width:t.width,height:t.height}),e.width=t.width,e.height=t.height};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>(e&&h(),()=>{o.current&&cancelAnimationFrame(o.current)}),[e,c]),(0,n.jsx)("canvas",{ref:i,className:`absolute inset-0 pointer-events-none ${s}`,onMouseMove:t=>{if(!e)return;let r=i.current?.getBoundingClientRect();if(!r)return;let n=t.clientX-r.left,a=t.clientY-r.top;.3>Math.random()&&g(n,a,2)},onClick:e=>{let t=i.current?.getBoundingClientRect();t&&g(e.clientX-t.left,e.clientY-t.top,15)},style:{width:"100%",height:"100%",pointerEvents:e?"auto":"none"}})}function m({children:e,className:t="",color:r="rgba(255, 255, 255, 0.3)"}){let[s,i]=(0,a.useState)([]);return(0,n.jsxs)("div",{className:`relative overflow-hidden ${t}`,onClick:e=>{let t=e.currentTarget.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top,a=Date.now();i(e=>[...e,{x:r,y:n,id:a}]),setTimeout(()=>{i(e=>e.filter(e=>e.id!==a))},600)},children:[e,s.map(e=>(0,n.jsx)("span",{className:"absolute rounded-full animate-ping",style:{left:e.x-10,top:e.y-10,width:20,height:20,backgroundColor:r,animationDuration:"0.6s"}},e.id))]})}var p=r(33276),h=r(41671),g=r(91723),f=r(89227),x=r(61094);let b={active:{icon:p.Z,color:"#10B981",bgColor:"#10B98120",label:"Active",description:"Currently active and maintained"},completed:{icon:h.Z,color:"#059669",bgColor:"#05966920",label:"Completed",description:"Project completed successfully"},"in-progress":{icon:g.Z,color:"#F59E0B",bgColor:"#F59E0B20",label:"In Progress",description:"Currently under development"},paused:{icon:f.Z,color:"#6B7280",bgColor:"#6B728020",label:"Paused",description:"Development temporarily paused"},archived:{icon:x.Z,color:"#EF4444",bgColor:"#EF444420",label:"Archived",description:"No longer maintained"}};function y({status:e,className:t="",showLabel:r=!1}){let a=b[e],s=a.icon;return(0,n.jsxs)("div",{className:`inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 ${t}`,style:{backgroundColor:a.bgColor,color:a.color,border:`1px solid ${a.color}40`},title:a.description,children:[(0,n.jsx)(s,{size:12}),r&&(0,n.jsx)("span",{children:a.label}),(0,n.jsx)("div",{className:"w-2 h-2 rounded-full animate-pulse",style:{backgroundColor:a.color}})]})}let v={React:"#61DAFB",Vue:"#4FC08D",Angular:"#DD0031","Next.js":"#000000","Nuxt.js":"#00DC82",TypeScript:"#3178C6",JavaScript:"#F7DF1E",HTML:"#E34F26",CSS:"#1572B6",Tailwind:"#06B6D4",SCSS:"#CC6699","Node.js":"#339933",Python:"#3776AB",Java:"#007396",Go:"#00ADD8",Rust:"#000000",PHP:"#777BB4","C#":"#239120",Ruby:"#CC342D",MySQL:"#4479A1",PostgreSQL:"#336791",MongoDB:"#47A248",Redis:"#DC382D",SQLite:"#003B57",AWS:"#FF9900",Azure:"#0078D4",GCP:"#4285F4",Docker:"#2496ED",Kubernetes:"#326CE5",Vercel:"#000000",Netlify:"#00C7B7",Git:"#F05032",GitHub:"#181717",GitLab:"#FC6D26",Figma:"#F24E1E",Sketch:"#F7B500"};function w({techStack:e,maxVisible:t=5,className:r=""}){let a=e.slice(0,t),s=e.length-t;return(0,n.jsxs)("div",{className:`flex items-center gap-2 ${r}`,children:[(0,n.jsxs)("div",{className:"flex -space-x-2",children:[a.map((e,t)=>(0,n.jsx)("div",{className:"relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-bold text-white shadow-lg transition-all duration-300 hover:scale-110 hover:z-10",style:{backgroundColor:v[e]||"#6B7280",zIndex:a.length-t},title:e,children:e.charAt(0).toUpperCase()},e)),s>0&&(0,n.jsxs)("div",{className:"relative w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-500 flex items-center justify-center text-xs font-bold text-white shadow-lg",title:`+${s} more technologies`,children:["+",s]})]}),(0,n.jsxs)("div",{className:"flex-1 max-w-20",children:[(0,n.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:(0,n.jsx)("div",{className:"h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500",style:{width:`${Math.min(e.length/10*100,100)}%`}})}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.length," tech",1!==e.length?"s":""]})]})]})}function j({stats:e,className:t=""}){let r=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),a=[{key:"stars",label:"Stars",icon:"⭐",value:e.stars},{key:"forks",label:"Forks",icon:"\uD83C\uDF74",value:e.forks},{key:"views",label:"Views",icon:"\uD83D\uDC41️",value:e.views},{key:"downloads",label:"Downloads",icon:"⬇️",value:e.downloads},{key:"commits",label:"Commits",icon:"\uD83D\uDCDD",value:e.commits},{key:"contributors",label:"Contributors",icon:"\uD83D\uDC65",value:e.contributors}].filter(e=>void 0!==e.value&&e.value>0);return 0===a.length?null:(0,n.jsx)("div",{className:`grid grid-cols-2 gap-2 ${t}`,children:a.slice(0,4).map(e=>(0,n.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs transition-all duration-300 hover:scale-105",children:[(0,n.jsx)("span",{className:"text-xs",children:e.icon}),(0,n.jsx)("span",{className:"font-medium",children:r(e.value)}),(0,n.jsx)("span",{className:"text-gray-500 dark:text-gray-400 truncate",children:e.label})]},e.key))})}function k({priority:e,className:t=""}){let r={high:{color:"#EF4444",label:"High Priority",intensity:3},medium:{color:"#F59E0B",label:"Medium Priority",intensity:2},low:{color:"#10B981",label:"Low Priority",intensity:1}}[e];return(0,n.jsx)("div",{className:`flex items-center gap-1 ${t}`,title:r.label,children:Array.from({length:3}).map((e,t)=>(0,n.jsx)("div",{className:`w-1 h-3 rounded-full transition-all duration-300 ${t<r.intensity?"opacity-100":"opacity-30"}`,style:{backgroundColor:r.color}},t))})}let N=()=>({measureText:(0,a.useCallback)((e,t,r=14,n=1.5)=>{if("undefined"==typeof document)return{lines:1,height:r*n};let a=document.createElement("canvas").getContext("2d");if(!a)return{lines:1,height:r*n};a.font=`${r}px system-ui, -apple-system, sans-serif`;let s=e.split(" "),i=[],o="";for(let e of s){let r=o?`${o} ${e}`:e;a.measureText(r).width>t&&o?(i.push(o),o=e):o=r}return o&&i.push(o),{lines:i.length,height:i.length*r*n,wrappedText:i}},[])}),C=e=>({web:{primary:"#10B981",secondary:"#059669",accent:"#6EE7B7",background:"linear-gradient(135deg, #10B981 0%, #059669 100%)",cardBg:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",shadow:"rgba(16, 185, 129, 0.25)",glow:"rgba(16, 185, 129, 0.4)"},mobile:{primary:"#14B8A6",secondary:"#0D9488",accent:"#5EEAD4",background:"linear-gradient(135deg, #14B8A6 0%, #0D9488 100%)",cardBg:"linear-gradient(135deg, rgba(20, 184, 166, 0.08) 0%, rgba(13, 148, 136, 0.05) 100%)",shadow:"rgba(20, 184, 166, 0.25)",glow:"rgba(20, 184, 166, 0.4)"},ai:{primary:"#84CC16",secondary:"#65A30D",accent:"#BEF264",background:"linear-gradient(135deg, #84CC16 0%, #65A30D 100%)",cardBg:"linear-gradient(135deg, rgba(132, 204, 22, 0.08) 0%, rgba(101, 163, 13, 0.05) 100%)",shadow:"rgba(132, 204, 22, 0.25)",glow:"rgba(132, 204, 22, 0.4)"},opensource:{primary:"#16A34A",secondary:"#15803D",accent:"#86EFAC",background:"linear-gradient(135deg, #16A34A 0%, #15803D 100%)",cardBg:"linear-gradient(135deg, rgba(22, 163, 74, 0.08) 0%, rgba(21, 128, 61, 0.05) 100%)",shadow:"rgba(22, 163, 74, 0.25)",glow:"rgba(22, 163, 74, 0.4)"},backend:{primary:"#047857",secondary:"#065F46",accent:"#A7F3D0",background:"linear-gradient(135deg, #047857 0%, #065F46 100%)",cardBg:"linear-gradient(135deg, rgba(4, 120, 87, 0.08) 0%, rgba(6, 95, 70, 0.05) 100%)",shadow:"rgba(4, 120, 87, 0.25)",glow:"rgba(4, 120, 87, 0.4)"},design:{primary:"#06B6D4",secondary:"#0891B2",accent:"#67E8F9",background:"linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)",cardBg:"linear-gradient(135deg, rgba(6, 182, 212, 0.08) 0%, rgba(8, 145, 178, 0.05) 100%)",shadow:"rgba(6, 182, 212, 0.25)",glow:"rgba(6, 182, 212, 0.4)"},game:{primary:"#22C55E",secondary:"#16A34A",accent:"#BBF7D0",background:"linear-gradient(135deg, #22C55E 0%, #16A34A 100%)",cardBg:"linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(22, 163, 74, 0.05) 100%)",shadow:"rgba(34, 197, 94, 0.25)",glow:"rgba(34, 197, 94, 0.4)"},default:{primary:"#10B981",secondary:"#059669",accent:"#6EE7B7",background:"linear-gradient(135deg, #10B981 0%, #059669 100%)",cardBg:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",shadow:"rgba(16, 185, 129, 0.25)",glow:"rgba(16, 185, 129, 0.4)"}})[(()=>{let t=e.categories?.[0]?.name?.toLowerCase()||"",r=e.name.toLowerCase(),n=e.description?.toLowerCase()||"";return t.includes("web")||r.includes("web")||n.includes("website")?"web":t.includes("mobile")||t.includes("app")||r.includes("app")?"mobile":t.includes("ai")||t.includes("ml")||n.includes("machine learning")?"ai":t.includes("open")||"opensource"===e.type?"opensource":t.includes("backend")||t.includes("api")||n.includes("server")?"backend":t.includes("design")||t.includes("ui")||n.includes("design")?"design":t.includes("game")||n.includes("game")?"game":"default"})()];function S({project:e,index:t,categoryIndex:r,onPreview:p}){let[h,g]=(0,a.useState)(!1),[f,x]=(0,a.useState)(320),[b,v]=(0,a.useState)({x:0,y:0}),[S,E]=(0,a.useState)(!1),L=(0,a.useRef)(null),$=(0,a.useRef)(null),{measureText:F}=N();!function(e,t=.3){(0,a.useEffect)(()=>{let r=e.current;if(!r)return;let n=e=>{let n=r.getBoundingClientRect(),a=e.clientX-n.left-n.width/2,s=e.clientY-n.top-n.height/2,i=Math.sqrt(a*a+s*s),o=Math.max(n.width,n.height)/2;if(i<o){let e=(o-i)/o,n=a*e*t,l=s*e*t;r.style.transform=`translate(${n}px, ${l}px)`}},a=()=>{r.style.transform="translate(0px, 0px)"};return r.addEventListener("mousemove",n),r.addEventListener("mouseleave",a),()=>{r.removeEventListener("mousemove",n),r.removeEventListener("mouseleave",a)}},[t])}($,.2),function(e,t=15){(0,a.useEffect)(()=>{let r=e.current;if(!r)return;let n=e=>{let n=r.getBoundingClientRect(),a=e.clientX-n.left,s=e.clientY-n.top,i=n.width/2,o=n.height/2,l=(s-o)/o*t,c=(i-a)/i*t;r.style.transform=`perspective(1000px) rotateX(${l}deg) rotateY(${c}deg) scale3d(1.05, 1.05, 1.05)`},a=()=>{r.style.transform="perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)"};return r.addEventListener("mousemove",n),r.addEventListener("mouseleave",a),()=>{r.removeEventListener("mousemove",n),r.removeEventListener("mouseleave",a)}},[t])}(L,8);let B=(0,a.useCallback)(()=>!!e.description&&!!f&&F(e.description,f-48,14,1.5).lines>4,[e.description,f,F])(),A=C(e);return(0,a.useEffect)(()=>{let e=()=>{L.current&&x(L.current.getBoundingClientRect().width)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,n.jsxs)("li",{ref:L,className:"group relative flex flex-col items-start animate-fade-in-up focus-within:outline-none w-full",style:{animationDelay:`${200*r+100*t}ms`,height:"480px",minWidth:"320px",maxWidth:"100%"},onMouseEnter:()=>{g(!0),E(!0)},onMouseLeave:()=>{g(!1),E(!1)},onMouseMove:e=>{if(L.current){let t=L.current.getBoundingClientRect();v({x:e.clientX-t.left,y:e.clientY-t.top})}},role:"article","aria-label":`Project: ${e.name}`,tabIndex:0,children:[(0,n.jsx)("div",{className:"absolute -inset-2 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse-slow",style:{background:`radial-gradient(circle at ${b.x}px ${b.y}px, ${A.glow}, transparent 70%)`}}),(0,n.jsx)("div",{className:"absolute -inset-1 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500",style:{background:A.cardBg}}),(0,n.jsxs)("div",{className:"relative w-full h-full backdrop-blur-xl rounded-2xl border shadow-2xl overflow-hidden project-card-hover focus-within:outline-none",style:{background:`linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(255, 255, 255, 0.90) 50%,
            rgba(255, 255, 255, 0.85) 100%)`,borderColor:`${A.primary}40`,boxShadow:`0 25px 50px -12px ${A.shadow}, 0 0 0 1px ${A.primary}20`},children:[(0,n.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none",style:{boxShadow:`0 0 0 3px ${A.primary}60, 0 0 0 6px ${A.primary}20`}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300",style:{background:`linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.90) 50%,
              rgba(51, 65, 85, 0.85) 100%)`}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-0 dark:opacity-100 transition-opacity duration-300",style:{background:`linear-gradient(135deg,
                 rgba(15, 23, 42, 0.95) 0%,
                 rgba(30, 41, 59, 0.90) 50%,
                 rgba(51, 65, 85, 0.85) 100%)`}}),(0,n.jsx)(u,{isActive:S,color:A.primary,particleCount:15,className:"z-0"}),(0,n.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1 opacity-80 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none",style:{background:A.background}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none",style:{background:`radial-gradient(circle at 50% 0%, ${A.primary}20, transparent 50%)`}}),(0,n.jsxs)("div",{className:"relative z-10 h-full p-3 sm:p-4 lg:p-6 flex flex-col justify-between",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-3",children:[(0,n.jsxs)("div",{ref:$,className:"relative flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 flex-shrink-0",style:{background:A.background,boxShadow:`0 6px 24px ${A.shadow}, inset 0 1px 0 rgba(255, 255, 255, 0.2)`},role:"img","aria-label":`${e.name} project icon`,children:[e.icon?e.icon.includes(":")?(0,n.jsx)(c.CustomIcon,{name:e.icon,size:24}):(0,n.jsx)(d.AntdIcon,{iconName:e.icon,size:24,className:"text-white"}):e.has_detail_page?(0,n.jsx)(s.Z,{size:24,className:"text-white"}):(0,n.jsx)(i.Z,{size:24,className:"text-white"}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur-md",style:{background:`radial-gradient(circle, ${A.glow}, transparent 70%)`}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-xl border-2 opacity-0 group-hover:opacity-40 transition-opacity duration-300",style:{borderImage:`linear-gradient(45deg, ${A.primary}, ${A.secondary}, ${A.accent}) 1`,animation:h?"spin 3s linear infinite":"none"}}),(0,n.jsx)("div",{className:"absolute inset-1 rounded-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300",style:{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent 50%)"}})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,n.jsx)(y,{status:e.status||"active",showLabel:!1}),e.priority&&(0,n.jsx)(k,{priority:e.priority})]})]}),(0,n.jsxs)("div",{className:"mb-2",children:[(0,n.jsx)("h3",{className:"text-sm sm:text-base font-bold text-gray-900 dark:text-white leading-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300 line-clamp-2",style:{backgroundImage:h?`linear-gradient(135deg, ${A.primary}, ${A.secondary})`:void 0},id:`project-title-${e.name.replace(/\s+/g,"-").toLowerCase()}`,title:e.name,children:e.name}),(0,n.jsx)("div",{className:"h-0.5 transition-all duration-500 group-hover:w-full rounded-full",style:{width:h?"100%":"0%",background:A.background,boxShadow:`0 0 8px ${A.glow}`}})]}),(0,n.jsxs)("div",{className:"flex-1 mb-3 min-h-[80px] flex flex-col",children:[(0,n.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-300 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 line-clamp-4","aria-describedby":`project-title-${e.name.replace(/\s+/g,"-").toLowerCase()}`,title:e.description,children:e.description}),B&&(0,n.jsxs)("div",{className:"mt-2 text-xs opacity-60 group-hover:opacity-100 transition-opacity duration-300 flex items-center gap-1",style:{color:A.primary},children:[(0,n.jsx)("span",{className:"inline-block animate-pulse",children:"\uD83D\uDCD6"}),(0,n.jsx)("span",{children:"Click to read more"})]}),(0,n.jsx)("div",{className:"flex-1"})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[e.tech_stack&&e.tech_stack.length>0&&(0,n.jsx)(w,{techStack:e.tech_stack,maxVisible:3}),e.stats&&(0,n.jsx)(j,{stats:e.stats})]}),e.categories&&e.categories.length>0&&(0,n.jsxs)("div",{className:"flex flex-wrap gap-1.5",children:[e.categories.slice(0,2).map((e,t)=>(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300 hover:scale-105",style:{backgroundColor:`${e.color||A.primary}15`,borderColor:`${e.color||A.primary}30`,color:e.color||A.primary},children:e.name},t)),e.categories.length>2&&(0,n.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full border border-gray-200 dark:border-gray-700",children:["+",e.categories.length-2]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between gap-2 relative z-30 mt-auto",children:[(0,n.jsx)(m,{className:"rounded-lg flex-1",children:(0,n.jsxs)("a",{href:e.has_detail_page&&e.slug?`/projects/${e.slug}`:"string"==typeof e.link?e.link:e.link?.href?e.link.href:"#",className:"inline-flex items-center justify-center gap-1.5 px-3 py-2 text-xs font-medium text-white rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 magnetic-effect w-full relative overflow-hidden z-30",style:{background:A.background,boxShadow:`0 4px 20px ${A.shadow}`,minHeight:"36px"},target:e.has_detail_page?"_self":"_blank",rel:e.has_detail_page?void 0:"noopener noreferrer","aria-label":`${e.has_detail_page?"View details for":"Visit"} ${e.name}`,children:[(0,n.jsx)("div",{className:"absolute inset-0 opacity-0 hover:opacity-20 transition-opacity duration-300 pointer-events-none",style:{background:"linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent)"}}),(0,n.jsx)("span",{className:"relative z-10 truncate",children:e.has_detail_page?"Details":"Visit"}),(0,n.jsx)(o.Z,{size:12,className:"relative z-10 transition-transform duration-300 group-hover:translate-x-1 flex-shrink-0"})]})}),p&&(0,n.jsx)(m,{className:"rounded-full flex-shrink-0",children:(0,n.jsx)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),p(e)},className:"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 hover:scale-110 magnetic-effect relative z-30 rounded-full",style:{minWidth:"36px",minHeight:"36px"},"aria-label":`Preview ${e.name}`,children:(0,n.jsx)(l.Z,{size:14})})})]})]})]}),(0,n.jsx)("div",{className:"absolute pointer-events-none opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-full blur-xl",style:{width:"120px",height:"120px",background:`radial-gradient(circle, ${A.glow}, ${A.primary}30, transparent)`,left:b.x-60,top:b.y-60,transform:"translate3d(0, 0, 0)"}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none",style:{background:`linear-gradient(135deg, transparent, ${A.primary}20, transparent)`,filter:"blur(1px)"}})]})]})}},92331:function(e,t,r){r.d(t,{AntdIcon:function(){return s}});var n=r(57437),a=r(2265);let s=({iconName:e,size:t=20,className:s=""})=>{let[i,o]=(0,a.useState)(null),[l,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(!1),[m,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)("");(0,a.useEffect)(()=>{if(!e){c(!1),u(!0);return}if(e.startsWith("emoji:")){p(!0),g(e.split(":")[1]),c(!1),u(!1);return}let t=!0;return c(!0),p(!1),Promise.all([r.e(446),r.e(216)]).then(r.bind(r,60778)).then(r=>{if(!t)return;let n=r[e];if(!n)throw Error(`Icon "${e}" not found in @ant-design/icons`);o(()=>n),u(!1)}).catch(e=>{t&&u(!0)}).finally(()=>{t&&c(!1)}),()=>{t=!1}},[e]);let f={fontSize:`${t}px`},x={display:"inline-flex",alignItems:"center",justifyContent:"center",fontSize:`${t}px`,width:`${t}px`,height:`${t}px`},b={display:"inline-flex",alignItems:"center",justifyContent:"center",width:`${t}px`,height:`${t}px`,fontSize:`${t/2}px`,fontWeight:"bold",color:"#ffffff",backgroundColor:d?"#ff4d4f":"#1890ff",borderRadius:"50%"};if(m)return(0,n.jsx)("span",{className:`antd-icon-emoji ${s}`,style:x,children:h});if(l)return(0,n.jsx)("span",{className:`antd-icon-loading ${s}`,style:b,children:"•••"});if(d||!i){let t=e?e.charAt(0).toUpperCase():"?";return(0,n.jsx)("span",{className:`antd-icon-error ${s}`,style:b,children:t})}return(0,n.jsx)(i,{className:s,style:f})}},18191:function(e,t,r){r.d(t,{CustomIcon:function(){return g}});var n=r(57437),a=r(12305),s=r(73278),i=r(42076),o=r(24625),l=r(56699),c=r(74646),d=r(60303),u=r(18899),m=r(5490),p=r(64875),h=r(63531);function g({name:e,size:t=20}){if(!e)return(0,n.jsx)("span",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t,fontSize:`${.6*t}px`,color:"#999",background:"#f5f5f5",borderRadius:"2px",border:"1px solid #ddd"},children:"?"});if(e.includes(":")){let[r,a]=e.split(":");if(!r||!a)return(0,n.jsx)("span",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t,fontSize:`${.6*t}px`,color:"#f44336",background:"#ffebee",borderRadius:"2px",border:"1px solid #f44336"},children:"!"});if("custom"===r||"iconic"===r&&["cursor","claude","anthropic","deepseek","doubao","gemini","github","google","huggingface","hunyuan","langchain","meta","mistral","modelscope","ollama","openai","openrouter","qwen","zhipu"].includes(a)){let e=`/images/icons/${a}.svg`;return(0,n.jsx)("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t},children:(0,n.jsx)("img",{src:e,alt:`${a} icon`,width:t,height:t,style:{display:"block",maxWidth:"100%",maxHeight:"100%"},onError:e=>{let r=e.target.parentElement;r&&(r.innerHTML=`
                  <span style="
                    font-size: ${.6*t}px;
                    color: #f44336;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: ${t}px;
                    height: ${t}px;
                    background: #ffebee;
                    border-radius: 2px;
                    border: 1px solid #f44336;
                  ">!</span>
                `)},onLoad:()=>{}})})}let s=`http://**************:8000/api/icons/render/${r}/${a}?size=${t}&theme=light`;return(0,n.jsx)("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:t,height:t,verticalAlign:"middle"},children:(0,n.jsx)("img",{src:s,alt:`${r} ${a} icon`,width:t,height:t,style:{display:"block",maxWidth:"100%",maxHeight:"100%"},onError:e=>{let r=e.target.parentElement;r&&(r.innerHTML=`
                <span style="
                  font-size: ${.6*t}px;
                  color: #f44336;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: ${t}px;
                  height: ${t}px;
                  background: #ffebee;
                  border-radius: 2px;
                  border: 1px solid #f44336;
                ">!</span>
              `)},onLoad:()=>{}})})}switch(e){case"bank":return(0,n.jsx)(a.B,{size:t,weight:"duotone"});case"github":return(0,n.jsx)(s.b,{size:t,weight:"duotone"});case"x":return(0,n.jsx)(i.S,{size:t,weight:"duotone"});case"instagram":return(0,n.jsx)(o.C,{size:t,weight:"duotone"});case"bsky":return(0,n.jsx)(l.C,{size:t,weight:"duotone"});case"email":return(0,n.jsx)(c.w,{size:t,weight:"duotone"});case"college":return(0,n.jsx)(d.X,{size:t,weight:"duotone"});case"coffee":return(0,n.jsx)(u.n,{size:t,weight:"duotone"});case"pill":return(0,n.jsx)(m.D,{size:t,weight:"duotone"});case"wechat":return(0,n.jsx)(p.F,{size:t,weight:"duotone"});case"discord":return(0,n.jsx)(h.$,{size:t,weight:"duotone"});case"huggingface":return(0,n.jsx)("div",{className:"flex items-center justify-center",style:{width:`${t}px`,height:`${t}px`},children:(0,n.jsx)("span",{style:{fontSize:`${.85*t}px`},children:"\uD83E\uDD17"})});default:return null}}},78394:function(e,t,r){r.d(t,{M:function(){return s}});var n=r(57437),a=r(61994);function s({className:e,...t}){return(0,n.jsx)("div",{className:(0,a.Z)(e,"prose prose-lg dark:prose-invert rich-text-content","prose-headings:font-semibold prose-h1:text-4xl prose-h2:text-3xl prose-h3:text-2xl prose-h4:text-xl prose-h5:text-lg prose-h6:text-base","prose-p:my-3 prose-p:leading-relaxed prose-p:text-lg sm:prose-p:text-xl","prose-a:text-primary prose-a:font-semibold prose-a:decoration-1 prose-a:underline-offset-2 hover:prose-a:decoration-2","prose-strong:font-semibold prose-strong:text-gray-900 dark:prose-strong:text-gray-100","prose-ul:list-disc prose-ul:pl-6 prose-ol:list-decimal prose-ol:pl-6","prose-li:my-1.5 prose-li:pl-2 prose-li:text-lg sm:prose-li:text-xl prose-li:leading-relaxed","prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-700 prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:text-lg prose-blockquote:my-4","prose-img:rounded-lg prose-img:mx-auto prose-img:shadow-lg","prose-code:px-2 prose-code:py-1 prose-code:rounded-md prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:text-base prose-code:font-medium","prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:rounded-lg prose-pre:p-6 prose-pre:overflow-auto prose-pre:text-base prose-pre:leading-relaxed","prose-table:border-collapse prose-table:w-full prose-table:text-base prose-table:my-6","prose-thead:bg-gray-50 dark:prose-thead:bg-gray-800","prose-th:p-3 prose-th:border prose-th:border-gray-200 dark:prose-th:border-gray-700 prose-th:font-semibold prose-th:text-left","prose-td:p-3 prose-td:border prose-td:border-gray-200 dark:prose-td:border-gray-700 prose-td:text-base","[&_.project-card]:my-4 [&_.project-card]:bg-white [&_.project-card]:dark:bg-zinc-800 [&_.project-card]:border [&_.project-card]:border-zinc-200 [&_.project-card]:dark:border-zinc-700 [&_.project-card]:rounded-lg [&_.project-card]:overflow-hidden [&_.project-card]:transition-all [&_.project-card]:duration-300","[&_.project-card:hover]:transform [&_.project-card:hover]:-translate-y-1 [&_.project-card:hover]:shadow-lg","[&_.project-card-content]:p-6","[&_.project-card-title]:text-xl [&_.project-card-title]:font-semibold [&_.project-card-title]:text-zinc-900 [&_.project-card-title]:dark:text-zinc-100 [&_.project-card-title]:mb-2","[&_.project-card-desc]:text-sm [&_.project-card-desc]:text-zinc-600 [&_.project-card-desc]:dark:text-zinc-400","[&_.project-card-link]:inline-flex [&_.project-card-link]:items-center [&_.project-card-link]:text-primary [&_.project-card-link]:text-sm [&_.project-card-link]:font-medium [&_.project-card-link]:no-underline [&_.project-card-link:hover]:text-primary-dark","[&_.project-card-link-text]:mr-2","[&_.project-card-link-icon]:w-4 [&_.project-card-link-icon]:h-4","max-w-none"),style:{columnCount:"unset",columns:"unset",columnFill:"unset",columnGap:"unset"},...t})}},21832:function(e,t,r){r.r(t),r.d(t,{AnimatedSection:function(){return l},FloatingElement:function(){return u},PageTransition:function(){return o},PulseOnHover:function(){return m},ScrollReveal:function(){return d},StaggeredList:function(){return c}});var n=r(57437),a=r(2265),s=r(99376),i=r(93448);function o({children:e,className:t}){let r=(0,s.usePathname)(),[o,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(e);return(0,a.useEffect)(()=>{l(!0);let t=setTimeout(()=>{d(e),l(!1)},150);return()=>clearTimeout(t)},[r,e]),(0,n.jsxs)("div",{className:(0,i.cn)("relative",t),children:[o&&(0,n.jsx)("div",{className:"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-fade-in",children:(0,n.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,n.jsx)("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin-slow"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground animate-pulse-soft",children:"加载中..."})]})})}),(0,n.jsx)("div",{className:(0,i.cn)("transition-all duration-300 ease-smooth",o?"opacity-0 scale-95":"opacity-100 scale-100 animate-fade-in-up"),children:c})]})}function l({children:e,className:t,delay:r=0,direction:s="up"}){let[o,l]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{l(!0)},r);return()=>clearTimeout(e)},[r]),(0,n.jsx)("div",{className:(0,i.cn)("transition-all duration-500 ease-smooth",o?({up:"animate-fade-in-up",down:"animate-fade-in-down",left:"animate-slide-in-left",right:"animate-slide-in-right"})[s]:"opacity-0 translate-y-4",t),children:e})}function c({children:e,className:t,staggerDelay:r=100}){return(0,n.jsx)("div",{className:t,children:e.map((e,t)=>(0,n.jsx)(l,{delay:t*r,className:"mb-4 last:mb-0",children:e},t))})}function d({children:e,className:t,threshold:r=.1,rootMargin:s="0px 0px -50px 0px"}){let[o,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null);return(0,a.useEffect)(()=>{if(!c)return;let e=new IntersectionObserver(([t])=>{t.isIntersecting&&(l(!0),e.unobserve(c))},{threshold:r,rootMargin:s});return e.observe(c),()=>{c&&e.unobserve(c)}},[c,r,s]),(0,n.jsx)("div",{ref:d,className:(0,i.cn)("transition-all duration-700 ease-smooth",o?"opacity-100 translate-y-0 scale-100":"opacity-0 translate-y-8 scale-95",t),children:e})}function u({children:e,className:t,intensity:r="normal"}){return(0,n.jsx)("div",{className:(0,i.cn)("transition-transform duration-300 ease-smooth",{subtle:"hover:translate-y-[-2px]",normal:"hover:translate-y-[-4px]",strong:"hover:translate-y-[-8px]"}[r],t),children:e})}function m({children:e,className:t}){return(0,n.jsx)("div",{className:(0,i.cn)("transition-all duration-300 ease-smooth hover:animate-pulse-soft",t),children:e})}},24721:function(e,t,r){r.d(t,{fI:function(){return n}});let n=r(40257).env.NEXT_PUBLIC_UTM_SOURCE},59211:function(e,t,r){r.d(t,{PK:function(){return i},r$:function(){return o}});var n=r(57437),a=r(2265);let s=(0,a.createContext)(void 0);function i({children:e}){let[t,r]=(0,a.useState)({isLoading:!1,message:"Loading...",type:"page"}),i=(0,a.useCallback)((e,t="Loading...",n="page")=>{r({isLoading:e,message:t,type:n})},[]),o=(0,a.useCallback)((e="Loading page...")=>{i(!0,e,"page")},[i]),l=(0,a.useCallback)((e="Loading content...")=>{i(!0,e,"content")},[i]),c=(0,a.useCallback)((e="Loading data...")=>{i(!0,e,"api")},[i]),d=(0,a.useCallback)(()=>{i(!1)},[i]);return(0,n.jsx)(s.Provider,{value:{loadingState:t,setLoading:i,showPageLoading:o,showContentLoading:l,showApiLoading:c,hideLoading:d},children:e})}function o(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useLoading must be used within a LoadingProvider");return e}},79879:function(e,t,r){r.d(t,{$p:function(){return o},Lv:function(){return a},o1:function(){return i},wb:function(){return s}});var n=r(2265);function a(e=.3){let t=(0,n.useRef)(null),[r,a]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let n=t.current;if(!n)return;let s=t=>{if(!r)return;let a=n.getBoundingClientRect(),s=a.left+a.width/2,i=a.top+a.height/2,o=(t.clientX-s)*e,l=(t.clientY-i)*e;n.style.transform=`translate(${o}px, ${l}px)`},i=()=>{a(!0),n.style.transition="transform 0.1s ease-out"},o=()=>{a(!1),n.style.transform="translate(0px, 0px)",n.style.transition="transform 0.3s ease-out"};return n.addEventListener("mouseenter",i),n.addEventListener("mouseleave",o),document.addEventListener("mousemove",s),()=>{n.removeEventListener("mouseenter",i),n.removeEventListener("mouseleave",o),document.removeEventListener("mousemove",s)}},[e,r]),t}function s(e=10){let t=(0,n.useRef)(null);return(0,n.useEffect)(()=>{let r=t.current;if(!r)return;let n=t=>{let n=r.getBoundingClientRect(),a=n.left+n.width/2,s=n.top+n.height/2,i=(t.clientX-a)/(n.width/2),o=(t.clientY-s)/(n.height/2)*e,l=-i*e;r.style.transform=`perspective(1000px) rotateX(${o}deg) rotateY(${l}deg) scale3d(1.05, 1.05, 1.05)`},a=()=>{r.style.transition="transform 0.1s ease-out"},s=()=>{r.style.transform="perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)",r.style.transition="transform 0.3s ease-out"};return r.addEventListener("mouseenter",a),r.addEventListener("mouseleave",s),r.addEventListener("mousemove",n),()=>{r.removeEventListener("mouseenter",a),r.removeEventListener("mouseleave",s),r.removeEventListener("mousemove",n)}},[e]),t}function i(){let[e,t]=(0,n.useState)(new Set),[r,a]=(0,n.useState)(new Set),s=(0,n.useCallback)(n=>new Promise((s,i)=>{if(e.has(n)){s();return}if(r.has(n)){let t=()=>{e.has(n)?s():setTimeout(t,100)};t();return}a(e=>new Set(e).add(n));let o=new Image;o.onload=()=>{t(e=>new Set(e).add(n)),a(e=>{let t=new Set(e);return t.delete(n),t}),s()},o.onerror=()=>{a(e=>{let t=new Set(e);return t.delete(n),t}),i(Error(`Failed to load image: ${n}`))},o.src=n}),[e,r]),i=(0,n.useCallback)(e=>Promise.allSettled(e.map(e=>s(e))),[s]);return{preloadImage:s,preloadImages:i,isImageLoaded:(0,n.useCallback)(t=>e.has(t),[e]),isImageLoading:(0,n.useCallback)(e=>r.has(e),[r]),loadedImages:Array.from(e),loadingImages:Array.from(r)}}function o(e=.1){let t=(0,n.useRef)(null),[r,a]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let r=t.current;if(!r)return;let n=new IntersectionObserver(([e])=>{a(e.isIntersecting)},{threshold:e});return n.observe(r),()=>{n.unobserve(r)}},[e]),{elementRef:t,isVisible:r}}},71837:function(e,t,r){r.d(t,{CT:function(){return n},HG:function(){return a},Y8:function(){return l},dv:function(){return s},hQ:function(){return i},kX:function(){return o},sK:function(){return c}});let n="http://**************:8000/api",a=async()=>{try{let e=await fetch(`${n}/site-settings/personal-info/config`,{next:{revalidate:30,tags:["personal-info"]},headers:{"Cache-Control":"public, s-maxage=30, stale-while-revalidate=60"}});if(!e.ok)throw Error(`Failed to fetch personal info: ${e.status} ${e.statusText}`);return(await e.json()).personal_info}catch(e){throw Error(`Failed to fetch personal info: ${e}`)}},s=async()=>{try{let e=await fetch(`${n}/site-settings/social-links/config`,{next:{revalidate:300,tags:["social-links"]},headers:{"Cache-Control":"public, s-maxage=300, stale-while-revalidate=600"}});if(!e.ok)throw Error(`Failed to fetch social links: ${e.status} ${e.statusText}`);return(await e.json()).social_links}catch(e){return[{name:"Github",icon:"github",href:"https://github.com/JYao-Chen"},{name:"HuggingFace",icon:"huggingface",href:"https://huggingface.co/JYaooo"},{name:"Email",icon:"email",href:"mailto:<EMAIL>"}]}},i=async()=>{try{let e=await fetch(`${n}/my/public/education`,{next:{revalidate:1800,tags:["education"]},headers:{"Cache-Control":"public, s-maxage=1800, stale-while-revalidate=3600"}});if(!e.ok)throw Error("Failed to fetch education");return await e.json()}catch(e){return[{id:1,school:"China Academy of Chinese Medical Sciences",major:"Information Science",degree_type:"master",start_year:"2023",end_year:"2026",logo:"college",description:"Master's candidate focusing on applications of LLM and KG in Traditional Chinese Medicine",is_current:!0},{id:2,school:"North China Institute of Science and Technology",major:"Information Management and Information Systems",degree_type:"bachelor",start_year:"2019",end_year:"2023",logo:"college",description:"Bachelor's degree in Information Management and Information Systems",is_current:!1}]}},o=async()=>{try{let e=await fetch(`${n}/my/public/career`,{next:{revalidate:1800,tags:["career"]},headers:{"Cache-Control":"public, s-maxage=1800, stale-while-revalidate=3600"}});if(!e.ok)throw Error("Failed to fetch career");return await e.json()}catch(e){return[{id:1,company:"Somewhere Financial Inc.",position:"Software Engineer",start_date:"2020",end_date:"Present",logo:"bank",description:"Software development and system maintenance",location:"Beijing",is_current:!0}]}},l=async()=>{try{let e=await fetch(`${n}/my/public/navigation/header`,{next:{revalidate:1800,tags:["navigation"]},headers:{"Cache-Control":"public, s-maxage=1800, stale-while-revalidate=3600"}});if(!e.ok)throw Error("Failed to fetch header navigation");return await e.json()}catch(e){return[{name:"Home",href:"/",is_external:!1,target:"_self"},{name:"About",href:"/about",is_external:!1,target:"_self"},{name:"Projects",href:"/projects",is_external:!1,target:"_self"},{name:"Blogs",href:"/blogs",is_external:!1,target:"_self"},{name:"Gallery",href:"/gallery",is_external:!1,target:"_self"}]}},c=async()=>{try{let e=await fetch(`${n}/my/public/navigation/footer`,{next:{revalidate:1800,tags:["navigation"]},headers:{"Cache-Control":"public, s-maxage=1800, stale-while-revalidate=3600"}});if(!e.ok)throw Error("Failed to fetch footer navigation");return await e.json()}catch(e){return[{name:"Home",href:"/",is_external:!1,target:"_self"},{name:"About",href:"/about",is_external:!1,target:"_self"},{name:"Projects",href:"/projects",is_external:!1,target:"_self"},{name:"Blogs",href:"/blogs",is_external:!1,target:"_self"},{name:"Gallery",href:"/gallery",is_external:!1,target:"_self"}]}}},58608:function(e,t,r){r.d(t,{AS:function(){return i},K$:function(){return s},WD:function(){return o}});let n=["#10B981","#059669","#22C55E","#16A34A","#84CC16","#60A5FA","#3B82F6","#0EA5E9","#38BDF8","#06B6D4","#A78BFA","#8B5CF6","#A855F7","#C084FC","#DDD6FE","#FB923C","#F97316","#FDBA74","#FED7AA","#FEF3C7","#F472B6","#EC4899","#F9A8D4","#FBCFE8","#FCE7F3","#22D3EE","#06B6D4","#67E8F9","#A5F3FC","#CFFAFE","#FDE047","#EAB308","#FEF08A","#FEFCE8","#FFFBEB","#94A3B8","#64748B","#CBD5E1","#E2E8F0","#F1F5F9"],a={getLuminance(e){let t=this.hexToRgb(e);if(!t)return 0;let{r,g:n,b:a}=t;return .2126*r+.7152*n+.0722*a},hexToRgb(e){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},getContrastColor(e){return this.getLuminance(e)>128?"#000000":"#FFFFFF"},withOpacity(e,t){let r=this.hexToRgb(e);return r?`rgba(${r.r}, ${r.g}, ${r.b}, ${t})`:e},getShadow(e,t=.25){return`0 4px 12px ${this.withOpacity(e,t)}`},getGradient(e,t="to right"){let r=this.withOpacity(e,.8),n=this.withOpacity(e,.6);return`linear-gradient(${t}, ${r}, ${n})`}},s={getTagStyle(e,t="default"){let r=e&&e.startsWith("#")?e:n[0];switch(t){case"minimal":return{backgroundColor:a.withOpacity(r,.08),color:a.withOpacity(r,.8),border:`1px solid ${a.withOpacity(r,.15)}`,boxShadow:"none"};case"soft":return{backgroundColor:a.withOpacity(r,.12),color:a.withOpacity(r,.9),border:`1px solid ${a.withOpacity(r,.2)}`,boxShadow:`0 1px 3px ${a.withOpacity(r,.1)}`};case"outline":return{backgroundColor:"transparent",color:a.withOpacity(r,.8),border:`1px solid ${a.withOpacity(r,.3)}`,boxShadow:`0 1px 2px ${a.withOpacity(r,.05)}`};default:return{backgroundColor:a.withOpacity(r,.15),color:a.withOpacity(r,.9),border:`1px solid ${a.withOpacity(r,.25)}`,boxShadow:`0 2px 4px ${a.withOpacity(r,.1)}`}}},getTagClasses(e,t="default"){let r="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 cursor-pointer relative overflow-hidden";return e&&e.startsWith("#")?r:`${r} bg-muted/50 text-muted-foreground border border-border/50`}},i={primary:{main:"#10B981",dark:"#059669",light:"#6EE7B7",deeper:"#047857",lighter:"#A7F3D0",subtle:"#D1FAE5"},gradients:{primary:"linear-gradient(135deg, #10B981 0%, #059669 100%)",subtle:"linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.05) 100%)",card:"linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(5, 150, 105, 0.08) 100%)",glow:"radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%)",overlay:"linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%)"},shadows:{card:"0 4px 12px rgba(16, 185, 129, 0.15)",cardHover:"0 8px 25px rgba(16, 185, 129, 0.25)",glow:"0 0 20px rgba(16, 185, 129, 0.3)",glowIntense:"0 0 40px rgba(16, 185, 129, 0.4)"},animations:{duration:{fast:"200ms",normal:"300ms",slow:"500ms",slower:"700ms"},easing:{smooth:"cubic-bezier(0.4, 0, 0.2, 1)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)",elastic:"cubic-bezier(0.175, 0.885, 0.32, 1.275)"}}},o={getTimelineCardStyle:()=>({background:i.gradients.card,border:`1px solid ${a.withOpacity(i.primary.main,.2)}`,borderRadius:"1rem",boxShadow:i.shadows.card,backdropFilter:"blur(10px)",transition:`all ${i.animations.duration.normal} ${i.animations.easing.smooth}`}),getAlbumCardStyle:()=>({background:i.gradients.subtle,border:`1px solid ${a.withOpacity(i.primary.main,.15)}`,borderRadius:"1.5rem",boxShadow:i.shadows.card,backdropFilter:"blur(8px)",transition:`all ${i.animations.duration.slow} ${i.animations.easing.smooth}`}),getGridCardStyle:()=>({borderRadius:"0.75rem",overflow:"hidden",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:`all ${i.animations.duration.normal} ${i.animations.easing.smooth}`}),getHoverStyle:()=>({transform:"translateY(-8px) scale(1.02)",boxShadow:i.shadows.cardHover,borderColor:a.withOpacity(i.primary.main,.4)})};s.getTagStyle(),s.getTagClasses()},57741:function(e,t,r){r.d(t,{p:function(){return n}});function n(e){if(!e)return"Unknown date";let t=new Date(e);return isNaN(t.getTime())?"Unknown date":t.toLocaleDateString("en-US",{day:"numeric",month:"long",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1})}},93448:function(e,t,r){r.d(t,{cn:function(){return s},h1:function(){return o},xf:function(){return l}});var n=r(61994),a=r(53335);function s(...e){return(0,a.m6)((0,n.W)(e))}function i(e){return!e||e.startsWith("mailto:")||e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/")?e:`https://${e}`}function o(e,t){if(!e||!t||e.startsWith("mailto:"))return e;try{let r=i(e),n=new URL(r);return n.searchParams.set("utm_source",t),n.toString()}catch(t){return i(e)}}function l(e){return!!e&&(!!e.startsWith("mailto:")||!e.startsWith("/")&&(!!(e.startsWith("http://")||e.startsWith("https://"))||/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)))}},81201:function(e,t,r){r.d(t,{cn:function(){return s}});var n=r(61994),a=r(53335);function s(...e){return(0,a.m6)((0,n.W)(e))}}}]);