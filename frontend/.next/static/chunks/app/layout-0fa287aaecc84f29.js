(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{78923:function(e,t,r){Promise.resolve().then(r.t.bind(r,88003,23)),Promise.resolve().then(r.bind(r,46021)),Promise.resolve().then(r.bind(r,60827)),Promise.resolve().then(r.bind(r,42545)),Promise.resolve().then(r.bind(r,93285)),Promise.resolve().then(r.bind(r,18908)),Promise.resolve().then(r.bind(r,3864)),Promise.resolve().then(r.bind(r,11816)),Promise.resolve().then(r.bind(r,83258)),Promise.resolve().then(r.bind(r,62989)),Promise.resolve().then(r.bind(r,59183)),Promise.resolve().then(r.bind(r,20686)),Promise.resolve().then(r.t.bind(r,79763,23))},93285:function(e,t,r){"use strict";var a=r(57437),n=r(2265);class s extends n.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-red-100 dark:bg-red-900/20 rounded-full",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"出现了一些问题"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"页面遇到了意外错误，请尝试刷新页面或稍后再试。"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200",children:"刷新页面"}),(0,a.jsx)("button",{onClick:()=>window.history.back(),className:"w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-md transition-colors duration-200",children:"返回上一页"})]}),!1]})}):this.props.children}}t.default=s},60827:function(e,t,r){"use strict";r.d(t,{default:function(){return i}});var a=r(57437),n=r(13786),s=r(40257);function i(){let e=s.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;return e?(0,a.jsx)(n.GoogleAnalytics,{gaId:e}):null}},42545:function(e,t,r){"use strict";r.d(t,{PlausibleAnalytics:function(){return l}});var a=r(57437),n=r(48667),s=r(40257);let i=s.env.NEXT_PUBLIC_PLAUSIBLE_URL,o=s.env.NEXT_PUBLIC_PLAUSIBLE_SRC;function l(){return(0,a.jsx)(n.default,{defer:!0,type:"text/javascript","data-domain":i,src:o})}},18908:function(e,t,r){"use strict";r.d(t,{Footer:function(){return f}});var a=r(57437),n=r(27648),s=r(2265),i=r(90648),o=r(71837),l=r(94420),c=r(52043);function d({className:e,style:t}){let[r,n]=(0,s.useState)([]),[i,l]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{(0,o.dv)().then(e=>{n(e)}).catch(e=>{n([])}).finally(()=>{l(!1)})},[]),i)?(0,a.jsx)("div",{className:e,style:t,children:"Loading..."}):(0,a.jsx)(c.default,{className:e,style:t,socialLinks:r})}var m=r(75921);function u(){let[e,t]=(0,s.useState)({totalUV:"-",dailyUV:"-"});return(0,s.useEffect)(()=>{let e=()=>{fetch("/api/visit-stats").then(e=>e.json()).then(e=>{t(e)}).catch(e=>{})};e();let r=setInterval(e,3e5);return()=>clearInterval(r)},[]),(0,a.jsxs)("div",{className:"flex flex-row items-center justify-center gap-2 text-sm text-gray-500 mt-2",children:[(0,a.jsx)(m.b,{size:16,weight:"duotone"}),"Total Visits: ",e.totalUV," / Today Visits: ",e.dailyUV]})}function h({href:e,children:t}){return(0,a.jsx)(n.default,{href:e,className:"transition hover:text-primary",children:t})}function f(){let[e,t]=(0,s.useState)([{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Blogs",href:"/blogs"},{name:"Gallery",href:"/gallery"}]);return(0,s.useEffect)(()=>{(0,o.sK)().then(e=>{t(e.map(e=>({name:e.name,href:e.href,icon:e.icon})))}).catch(e=>{})},[]),(0,a.jsx)("footer",{className:"mt-16 flex-none",children:(0,a.jsx)(i.Zb,{children:(0,a.jsx)("div",{className:"border-t border-muted pb-16 pt-10",children:(0,a.jsx)(i.le,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-between gap-6 sm:flex-row sm:items-start",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-x-6 gap-y-1 text-sm font-medium",children:e.map(e=>(0,a.jsx)(h,{href:e.href,children:e.name},e.name))}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground text-center",children:["Frontend template from ",(0,a.jsx)("a",{href:"https://github.com/iAmCorey/coreychiu-portfolio-template",target:"_blank",rel:"noopener noreferrer",className:"underline hover:text-primary",children:"coreychiu-portfolio-template"}),(0,a.jsx)("br",{}),"Full-stack personal website based on Next.js + MySQL + FastAPI"]})]}),(0,a.jsxs)("div",{className:"flex flex-col justify-center items-start",children:[(0,a.jsxs)("div",{className:"flex flex-row justify-end items-center gap-2",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," ","Jay-Yao",". All rights reserved."]}),(0,a.jsx)(n.default,{href:"/version-history",className:"text-xs text-muted-foreground hover:text-primary transition-colors duration-200 underline",title:"查看网站版本历史",children:"Version"}),(0,a.jsx)(l.T,{})]}),(0,a.jsx)(d,{className:"mt-0"}),(0,a.jsx)(u,{})]})]})})})})})}},3864:function(e,t,r){"use strict";r.d(t,{Header:function(){return k}});var a=r(57437),n=r(2265),s=r(33145),i=r(27648),o=r(99376),l=r(54138),c=r(74931),d=r(61994),m=r(90648),u={src:"/_next/static/media/avatar.700ad1ea.jpg",height:902,width:902,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAP/xAAVAQEBAAAAAAAAAAAAAAAAAAADBP/aAAwDAQACEAMQAAABqJ3/AP/EABcQAQADAAAAAAAAAAAAAAAAAAEAAgP/2gAIAQEAAQUCcm8//8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAS/9oACAEDAQE/AdJf/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAgEBPwF//8QAFxAAAwEAAAAAAAAAAAAAAAAAAAECEv/aAAgBAQAGPwJ6cuj/xAAYEAACAwAAAAAAAAAAAAAAAAABEQAhof/aAAgBAQABPyGv5QBeT//aAAwDAQACAAMAAAAQB//EABgRAQADAQAAAAAAAAAAAAAAAAEAESGB/9oACAEDAQE/EAWNcJ//xAAWEQADAAAAAAAAAAAAAAAAAAAAARH/2gAIAQIBAT8QcP/EABgQAQEBAQEAAAAAAAAAAAAAAAERIQBh/9oACAEBAAE/EF25JU0tPumvf//Z",blurWidth:8,blurHeight:8},h=r(71837),f=r(94420),x=r(40875),g=r(32489),A=r(93448);function p({text:e,duration:t=200,className:r}){let[s,i]=(0,n.useState)(""),[o,l]=(0,n.useState)(0);return(0,n.useEffect)(()=>{let r=setInterval(()=>{o<e.length?(i(e.substring(0,o+1)),l(o+1)):clearInterval(r)},t);return()=>{clearInterval(r)}},[t,o]),(0,a.jsx)("h1",{className:(0,A.cn)("font-display text-center text-4xl font-bold leading-[5rem] tracking-[-0.02em] drop-shadow-sm",r),children:s||e})}function b({href:e,children:t}){return(0,a.jsx)("li",{children:(0,a.jsx)(l.J.Button,{as:i.default,href:e,prefetch:!0,className:"block py-2",children:t})})}function v({navItems:e,...t}){return(0,a.jsxs)(l.J,{...t,children:[(0,a.jsxs)(l.J.Button,{className:"group flex items-center rounded-full px-4 py-2 text-sm font-medium shadow-lg ring-1 ring-border backdrop-blur-md bg-card/95 transition-all duration-300 hover:shadow-xl hover:ring-primary/20 hover:bg-primary/5",children:["Menu",(0,a.jsx)(x.Z,{className:"ml-3 h-auto w-2 transition-transform duration-200 group-hover:rotate-180"})]}),(0,a.jsxs)(c.u.Root,{children:[(0,a.jsx)(c.u.Child,{as:n.Fragment,enter:"duration-150 ease-out",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"duration-150 ease-in",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,a.jsx)(l.J.Overlay,{className:"fixed inset-0 z-50 backdrop-blur-sm dark:bg-background/80"})}),(0,a.jsx)(c.u.Child,{as:n.Fragment,enter:"duration-150 ease-out",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"duration-150 ease-in",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.J.Panel,{focus:!0,className:"fixed inset-x-4 top-8 z-50 origin-top rounded-3xl p-8 ring-1 ring-muted bg-card",children:[(0,a.jsxs)("div",{className:"flex flex-row-reverse items-center justify-between",children:[(0,a.jsx)(l.J.Button,{"aria-label":"Close menu",className:"-m-1 p-1",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-muted-foreground"})}),(0,a.jsx)("h2",{className:"text-sm font-medium text-muted-foreground",children:"Navigation"})]}),(0,a.jsx)("nav",{className:"mt-6",children:(0,a.jsx)("ul",{className:"-my-2 divide-y divide-zinc-100 text-base dark:divide-zinc-100/5",children:e.map(e=>(0,a.jsx)(b,{href:e.href,children:e.name},e.name))})})]})})]})]})}function y({href:e,children:t}){let r=(0,o.usePathname)()===e;return(0,a.jsx)("li",{children:(0,a.jsxs)(i.default,{href:e,prefetch:!0,className:(0,d.Z)("relative block px-3 py-2 transition-all duration-300 rounded-md",r?"text-primary bg-primary/10":"opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5"),children:[t,r&&(0,a.jsx)("span",{className:"absolute inset-x-1 -bottom-px h-[2px] bg-gradient-to-r from-primary/0 via-primary/60 to-primary/0 rounded-full animate-pulse-soft"})]})})}function j({navItems:e,...t}){return(0,a.jsx)("nav",{...t,children:(0,a.jsx)("ul",{className:"flex rounded-full px-3 text-sm font-medium bg-card/95 ring-1 ring-border shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-xl hover:ring-primary/20",children:e.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:[t>0&&(0,a.jsx)("li",{className:"flex items-center",children:(0,a.jsx)("div",{className:"h-4 w-px bg-muted-foreground/30"})}),(0,a.jsx)(y,{href:e.href,children:e.name})]},e.name))})})}function w(e,t,r){return Math.min(Math.max(e,Math.min(t,r)),Math.max(t,r))}function N({showName:e=!1,personalName:t="Jay-Yao",className:r,...n}){return(0,a.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,a.jsx)("div",{className:(0,d.Z)(r,"h-10 w-10 rounded-full bg-white/90 p-0.5 shadow-lg shadow-zinc-800/5 ring-1 ring-zinc-900/5 backdrop-blur dark:bg-zinc-800/90 dark:ring-white/10"),...n}),e&&(0,a.jsx)(i.default,{href:"/","aria-label":"Home",className:"pointer-events-auto",children:(0,a.jsx)("div",{className:"text-md font-semibold capitalize",children:t})})]})}function E({large:e=!1,className:t,...r}){return(0,a.jsx)(i.default,{href:"/","aria-label":"Home",className:(0,d.Z)(t,"pointer-events-auto"),...r,children:(0,a.jsx)(s.default,{src:u,alt:"",sizes:e?"4rem":"2.25rem",className:(0,d.Z)("rounded-full bg-zinc-100 object-cover dark:bg-zinc-800",e?"h-16 w-16":"h-9 w-9"),priority:!0})})}function k(){let e="/"===(0,o.usePathname)(),[t,r]=(0,n.useState)([{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Blogs",href:"/blogs"},{name:"Gallery",href:"/gallery"}]),[s,i]=(0,n.useState)("Jay-Yao"),l=(0,n.useRef)(null),c=(0,n.useRef)(null),d=(0,n.useRef)(!0);return(0,n.useEffect)(()=>{(0,h.Y8)().then(e=>{r(e.map(e=>({name:e.name,href:e.href,icon:e.icon})))}).catch(e=>{}),(0,h.HG)().then(e=>{i(e.name)}).catch(e=>{})},[]),(0,n.useEffect)(()=>{let t=c.current?.offsetTop??0;function r(e,t){document.documentElement.style.setProperty(e,t)}function a(){(function(){if(!l.current)return;let{top:e,height:a}=l.current.getBoundingClientRect(),n=w(window.scrollY,0,document.body.scrollHeight-window.innerHeight);if(d.current&&r("--header-position","sticky"),r("--content-offset",`${t}px`),d.current||n<t)r("--header-height",`${t+a}px`),r("--header-mb",`${-t}px`);else if(e+a<-64){let e=Math.max(a,n-64);r("--header-height",`${e}px`),r("--header-mb",`${a-e}px`)}else 0===e&&(r("--header-height",`${n+a}px`),r("--header-mb",`${-n}px`))})(),function(){if(!e)return;let a=36/64,n=2/16,s=t-window.scrollY,i=s*(1-a)/t+a;i=w(i,1,a);let o=s*(0-n)/t+n;o=w(o,0,n),r("--avatar-image-transform",`translate3d(${o}rem, 0, 0) scale(${i})`),r("--avatar-hi-opacity",i===a?"0":"1")}(),e&&r("--avatar-hi-opacity",(window.scrollY<50?1:0).toString()),d.current=!1}return a(),window.addEventListener("scroll",a,{passive:!0}),window.addEventListener("resize",a),()=>{window.removeEventListener("scroll",a),window.removeEventListener("resize",a)}},[e]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:"pointer-events-none relative z-50 flex flex-none flex-col",style:{height:"var(--header-height)",marginBottom:"var(--header-mb)"},children:[e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{ref:c,className:"order-last mt-[calc(theme(spacing.16)-theme(spacing.3))]"}),(0,a.jsx)(m.W2,{className:"top-0 order-last -mb-3 pt-3",style:{position:"var(--header-position)"},children:(0,a.jsx)("div",{className:"top-[var(--avatar-top,theme(spacing.3))] w-full",style:{position:"var(--header-inner-position)"},children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N,{className:"absolute left-0 top-3 origin-left transition-opacity",style:{opacity:"var(--avatar-border-opacity, 0)",transform:"var(--avatar-border-transform)"}}),(0,a.jsxs)("div",{className:"flex flex-row items-center gap-4",children:[(0,a.jsx)(E,{large:!0,className:"block h-16 w-16 origin-left",style:{transform:"var(--avatar-image-transform)"}}),(0,a.jsxs)("div",{className:"text-3xl md:text-6xl font-bold tracking-tight flex flex-row",style:{opacity:"var(--avatar-hi-opacity, 0)",transform:"var(--avatar-hi-transform)"},children:["Hi,"," ",(0,a.jsx)(p,{className:"text-3xl md:text-6xl font-bold tracking-tight",text:`I'm ${s} `,duration:150}),"\uD83D\uDC4B"]})]})]})})})]}),(0,a.jsx)("div",{ref:l,className:"top-0 z-10 h-16 pt-6",style:{position:"var(--header-position)"},children:(0,a.jsx)(m.W2,{className:"top-[var(--header-top,theme(spacing.6))] w-full",style:{position:"var(--header-inner-position)"},children:(0,a.jsxs)("div",{className:"relative flex gap-4",children:[(0,a.jsx)("div",{className:"flex flex-1",children:!e&&(0,a.jsx)(N,{showName:!0,personalName:s,children:(0,a.jsx)(E,{})})}),(0,a.jsxs)("div",{className:"flex flex-1 justify-end md:justify-center",children:[(0,a.jsx)(v,{navItems:t,className:"pointer-events-auto md:hidden"}),(0,a.jsx)(j,{navItems:t,className:"pointer-events-auto hidden md:block"})]}),(0,a.jsx)("div",{className:"flex justify-end md:flex-1",children:(0,a.jsx)("div",{className:"pointer-events-auto flex flex-row items-center gap-2 md:mr-2",children:(0,a.jsx)(f.T,{})})})]})})})]}),e&&(0,a.jsx)("div",{className:"flex-none",style:{height:"var(--content-offset)"}})]})}},11816:function(e,t,r){"use strict";r.d(t,{PerformanceDebugger:function(){return s},PerformanceMonitor:function(){return n}});var a=r(2265);function n(){return(0,a.useEffect)(()=>{let e=()=>{try{let e=performance.getEntriesByType("navigation")[0],r=performance.getEntriesByType("paint"),a={url:window.location.href,userAgent:navigator.userAgent,timestamp:Date.now()};e&&(a.loadTime=e.loadEventEnd-e.fetchStart,a.renderTime=e.domContentLoadedEventEnd-e.fetchStart);let n=r.find(e=>"first-contentful-paint"===e.name);if(n&&(a.firstContentfulPaint=n.startTime),"PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),r=t[t.length-1];r&&(a.largestContentfulPaint=r.startTime)});e.observe({entryTypes:["largest-contentful-paint"]});let r=new PerformanceObserver(e=>{let t=0;for(let r of e.getEntries())r.hadRecentInput||(t+=r.value);a.cumulativeLayoutShift=t});r.observe({entryTypes:["layout-shift"]});let n=new PerformanceObserver(e=>{for(let t of e.getEntries())a.firstInputDelay=t.processingStart-t.startTime});n.observe({entryTypes:["first-input"]}),setTimeout(()=>{t(a),e.disconnect(),r.disconnect(),n.disconnect()},5e3)}else setTimeout(()=>t(a),2e3)}catch(e){}},t=async e=>{try{await fetch("/api/performance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(e){}};return"complete"===document.readyState?e():window.addEventListener("load",e),()=>{window.removeEventListener("load",e)}},[]),null}function s(){return(0,a.useEffect)(()=>{},[]),null}},94420:function(e,t,r){"use strict";r.d(t,{T:function(){return h}});var a=r(57437),n=r(2265),s=r(85929),i=r(97226),o=r(78298),l=r(37053),c=r(90535),d=r(93448);let m=(0,c.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...s},i)=>{let o=n?l.g7:"button";return(0,a.jsx)(o,{className:(0,d.cn)(m({variant:t,size:r,className:e})),ref:i,...s})});function h(){let{setTheme:e,theme:t,resolvedTheme:r}=(0,o.F)();return(0,a.jsxs)(u,{variant:"ghost",size:"icon",onClick:()=>{e("dark"===r?"light":"dark")},className:"relative",children:[(0,a.jsx)(s.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(i.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}u.displayName="Button"},83258:function(e,t,r){"use strict";r.d(t,{ThemeInitializer:function(){return s}});var a=r(2265);let n=()=>{let e=document.documentElement;["--theme-manager-primary","--theme-manager-primary-dark","--theme-manager-secondary","--theme-manager-accent","--theme-manager-bg-primary","--theme-manager-bg-secondary","--theme-manager-bg-tertiary","--theme-manager-text-primary","--theme-manager-text-secondary","--theme-manager-text-muted","--theme-manager-border-primary","--theme-manager-border-secondary","--theme-primary-hue","--theme-primary-saturation","--theme-primary-lightness","--theme-accent-hue","--theme-accent-saturation","--theme-accent-lightness"].forEach(t=>{e.style.removeProperty(t)})},s=()=>((0,a.useEffect)(()=>{n()},[]),null)},62989:function(e,t,r){"use strict";r.d(t,{GlobalLoadingManager:function(){return l}});var a=r(57437),n=r(59211),s=r(2265),i=r(99376);function o({isLoading:e=!1,message:t="Loading...",showOverlay:r=!0}){let[n,o]=(0,s.useState)(!1),[l,c]=(0,s.useState)(t),d=(0,i.usePathname)();return((0,s.useEffect)(()=>{if(e)o(!0),d.includes("/blogs/")?c("Loading article content..."):d.includes("/projects/")?c("Loading project details..."):d.includes("/gallery/")?c("Loading images..."):c(t);else{let e=setTimeout(()=>o(!1),300);return()=>clearTimeout(e)}},[e,t,d]),n&&r)?(0,a.jsx)("div",{className:"fixed inset-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm z-50 flex items-center justify-center transition-all duration-300",children:(0,a.jsxs)("div",{className:"text-center max-w-sm mx-auto px-6",children:[(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-4 border-gray-200 dark:border-gray-700 border-t-blue-600 dark:border-t-blue-400 mx-auto"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full animate-pulse"})}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-20 h-20 border-2 border-blue-200 dark:border-blue-800 rounded-full animate-ping opacity-20"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:l}),(0,a.jsxs)("div",{className:"flex justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-4",children:"Please wait, preparing amazing content for you"})]})}):null}function l(){let{loadingState:e}=(0,n.r$)();return"page"===e.type&&e.isLoading?(0,a.jsx)(o,{isLoading:e.isLoading,message:e.message,showOverlay:!0}):null}},59183:function(e,t,r){"use strict";r.d(t,{RouteProgressBar:function(){return m}});var a=r(57437),n=r(29),s=r.n(n),i=r(2265),o=r(99376),l=r(71318),c=r.n(l);function d(){let e=(0,o.usePathname)(),t=(0,o.useSearchParams)(),[r,n]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let e=()=>{n(!0),c().start()},t=()=>{n(!1),c().done()},a=a=>{let n=a.target.closest("a");if(n&&n.href&&!n.href.startsWith("#")&&!n.target&&!n.download)try{let a=new URL(n.href),s=new URL(window.location.href);if(a.origin!==s.origin)return;(a.pathname!==s.pathname||a.search!==s.search)&&(e(),setTimeout(()=>{r&&t()},1e4))}catch(e){}},s=()=>{e()},i=()=>{"visible"===document.visibilityState&&r&&t()};document.addEventListener("click",a,!0),window.addEventListener("popstate",s),document.addEventListener("visibilitychange",i);let o=setTimeout(t,100);return()=>{document.removeEventListener("click",a,!0),window.removeEventListener("popstate",s),document.removeEventListener("visibilitychange",i),clearTimeout(o),c().done()}},[e,t,r]),(0,i.useEffect)(()=>{n(!1),c().done()},[e,t]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(s(),{id:"da1a53aeab4aec9c",children:"#nprogress{pointer-events:none}#nprogress .bar{background:-webkit-linear-gradient(left,#3b82f6,#8b5cf6)!important;background:-moz-linear-gradient(left,#3b82f6,#8b5cf6)!important;background:-o-linear-gradient(left,#3b82f6,#8b5cf6)!important;background:linear-gradient(90deg,#3b82f6,#8b5cf6)!important;position:fixed;z-index:1031;top:0;left:0;width:100%;height:3px;-webkit-border-radius:0 0 2px 2px;-moz-border-radius:0 0 2px 2px;border-radius:0 0 2px 2px}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;-webkit-box-shadow:0 0 10px#3b82f6,0 0 5px#3b82f6;-moz-box-shadow:0 0 10px#3b82f6,0 0 5px#3b82f6;box-shadow:0 0 10px#3b82f6,0 0 5px#3b82f6;opacity:1;-webkit-transform:rotate(3deg)translate(0px,-4px);-moz-transform:rotate(3deg)translate(0px,-4px);-ms-transform:rotate(3deg)translate(0px,-4px);-o-transform:rotate(3deg)translate(0px,-4px);transform:rotate(3deg)translate(0px,-4px)}.dark #nprogress .bar{background:-webkit-linear-gradient(left,#60a5fa,#a78bfa)!important;background:-moz-linear-gradient(left,#60a5fa,#a78bfa)!important;background:-o-linear-gradient(left,#60a5fa,#a78bfa)!important;background:linear-gradient(90deg,#60a5fa,#a78bfa)!important}.dark #nprogress .peg{-webkit-box-shadow:0 0 10px#60a5fa,0 0 5px#60a5fa;-moz-box-shadow:0 0 10px#60a5fa,0 0 5px#60a5fa;box-shadow:0 0 10px#60a5fa,0 0 5px#60a5fa}@media(max-width:768px){#nprogress .bar{height:2px}}"})})}function m(){return(0,a.jsx)(i.Suspense,{fallback:null,children:(0,a.jsx)(d,{})})}c().configure({minimum:.3,easing:"ease",speed:500,showSpinner:!1})},20686:function(e,t,r){"use strict";r.d(t,{SmartPrefetch:function(){return s}});var a=r(2265),n=r(99376);function s({routes:e,priority:t="low",delay:r=2e3,checkOnline:s=!0}){return!function(e,t={}){let r=(0,n.useRouter)(),s=(0,a.useRef)(new Set),{priority:i="low",delay:o=2e3,condition:l}=t;(0,a.useEffect)(()=>{if(l&&!l())return;let t=()=>{e.forEach(e=>{if(!s.current.has(e))try{r.prefetch(e),s.current.add(e)}catch(e){}})};if("high"===i)t();else{let e=setTimeout(t,o);return()=>clearTimeout(e)}},[e,r,i,o,l])}(e,{priority:t,delay:r,condition:s?()=>!1!==navigator.onLine:void 0}),null}},79763:function(){}},function(e){e.O(0,[636,85,446,105,216,592,744],function(){return e(e.s=78923)}),_N_E=e.O()}]);