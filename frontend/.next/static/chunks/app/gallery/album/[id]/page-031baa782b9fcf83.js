(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[516],{27381:function(e,t,r){Promise.resolve().then(r.bind(r,62901))},62901:function(e,t,r){"use strict";r.d(t,{AlbumDetailFixed:function(){return f}});var s=r(57437),a=r(2265),l=r(33145),i=r(99376),n=r(32660),d=r(28124),o=r(41473),c=r(31047),m=r(83774),x=r(53581),h=r(42208),u=r(32489),g=r(92451),b=r(10407),p=r(81201),v=r(79059),w=r(83275),j=r(94258);function f({album:e}){var t;let r=(0,i.useRouter)(),[f,N]=(0,a.useState)({isOpen:!1,currentIndex:0,images:[]}),[y,k]=(0,a.useState)("masonry"),[I,C]=(0,a.useState)({}),[S,O]=(0,a.useState)(new Set),Z=(0,a.useCallback)((e,t)=>{let r=t.findIndex(t=>t.id===e.id);N({isOpen:!0,currentIndex:r>=0?r:0,images:t})},[]),z=(0,a.useCallback)(()=>{N({isOpen:!1,currentIndex:0,images:[]})},[]),E=(0,a.useCallback)(()=>{N(e=>({...e,currentIndex:(e.currentIndex+1)%e.images.length}))},[]),_=(0,a.useCallback)(()=>{N(e=>({...e,currentIndex:0===e.currentIndex?e.images.length-1:e.currentIndex-1}))},[]),A=(0,a.useCallback)((e,t)=>{if(!e)return;let r=new IntersectionObserver(([e])=>{e.isIntersecting&&O(e=>new Set([...e,t]))},{threshold:.1});return r.observe(e),()=>r.disconnect()},[]);return(0,a.useEffect)(()=>{let e=e=>{if(f.isOpen)switch(e.key){case"Escape":z();break;case"ArrowLeft":_();break;case"ArrowRight":E()}};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[f.isOpen,z,_,E]),(0,s.jsxs)("div",{className:"mt-16 sm:mt-32 sm:px-8",children:[(0,s.jsx)("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:(0,s.jsx)("div",{className:"relative px-4 sm:px-8 lg:px-12",children:(0,s.jsxs)("div",{className:"mx-auto max-w-2xl lg:max-w-5xl",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("button",{onClick:()=>r.push("/gallery?view=albums"),className:(0,p.cn)("group flex items-center gap-2 px-4 py-2 rounded-xl","bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700","text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700","transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md"),children:[(0,s.jsx)(n.Z,{className:"w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1"}),(0,s.jsx)("span",{className:"font-medium",children:"Back to Albums"})]})}),(0,s.jsxs)("div",{className:"relative h-64 md:h-80 lg:h-96 overflow-hidden rounded-2xl mb-12",children:[(0,s.jsxs)("div",{className:"absolute inset-0",children:[(0,s.jsx)(l.default,{src:e.images[0]?.url||e.coverImage.url,alt:e.title,fill:!0,className:"object-cover",priority:!0}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"})]}),(0,s.jsx)("div",{className:"absolute top-4 right-4 z-20",children:(0,s.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-1",children:[(0,s.jsx)("button",{onClick:()=>k("masonry"),className:(0,p.cn)("p-2 rounded-lg transition-all duration-300","masonry"===y?"bg-emerald-500 text-white":"text-white/70 hover:text-white hover:bg-white/10"),children:(0,s.jsx)(d.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>k("grid"),className:(0,p.cn)("p-2 rounded-lg transition-all duration-300","grid"===y?"bg-emerald-500 text-white":"text-white/70 hover:text-white hover:bg-white/10"),children:(0,s.jsx)(o.Z,{className:"w-4 h-4"})})]})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-10 p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 leading-tight",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-lg text-white/90 mb-4 max-w-2xl mx-auto",children:e.description}),(0,s.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3 text-white/90",children:[e.date&&(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:[(0,s.jsx)(c.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm",children:new Date(e.date).toLocaleDateString("zh-CN")})]}),e.location&&(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:[(0,s.jsx)(m.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm",children:e.location})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:[(0,s.jsx)(x.Z,{className:"w-4 h-4"}),(0,s.jsxs)("span",{className:"text-sm",children:[e.images.length," Photos"]})]}),(0,s.jsx)("div",{className:"bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20",children:(0,s.jsx)(j.CommentStats,{path:`/gallery/album/${e.id}`,showIcons:!0})})]})]})})]}),(0,s.jsxs)("div",{className:"py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2",children:"Photo Collection"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Explore every moment captured in this beautiful album"})]}),(0,s.jsx)("div",{className:(0,p.cn)("masonry"===y?"columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-4":`grid gap-4 ${1===(t=e.images.length)?"grid-cols-1":2===t?"grid-cols-1 md:grid-cols-2":t<=4?"grid-cols-2 md:grid-cols-2":"grid-cols-2 md:grid-cols-3 lg:grid-cols-4"}`),children:e.images.map((t,r)=>(0,s.jsx)(v.O1,{delay:100*r,children:(0,s.jsx)(v.Uf,{children:(0,s.jsxs)("div",{ref:e=>A(e,t.id),className:(0,p.cn)("group relative cursor-pointer overflow-hidden","bg-white dark:bg-gray-800 rounded-2xl","border border-gray-200 dark:border-gray-700","transition-all duration-500 hover:shadow-xl hover:shadow-emerald-500/10","hover:-translate-y-2 hover:scale-[1.02]","masonry"===y?"break-inside-avoid mb-4":1===e.images.length?"aspect-video":"aspect-square"),onClick:()=>Z(t,e.images),children:[S.has(t.id)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.default,{src:t.thumbnail_url||t.url,alt:t.alt,fill:"masonry"!==y,width:"masonry"===y?400:void 0,height:"masonry"===y?300:void 0,className:(0,p.cn)("object-cover transition-all duration-700","group-hover:scale-110 group-hover:brightness-110",I[t.id]?"opacity-100":"opacity-0","masonry"===y?"w-full h-auto":""),sizes:"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw",onLoad:()=>C(e=>({...e,[t.id]:!0})),loading:"lazy"}),(0,s.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300",children:[(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,s.jsx)("h3",{className:"text-white font-semibold text-sm mb-1 truncate",children:t.alt})}),(0,s.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center",children:(0,s.jsx)(h.Z,{className:"w-4 h-4 text-white"})})})]})]}),!S.has(t.id)&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-2xl flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-emerald-300 rounded-full animate-spin border-t-transparent"})})]})})},t.id))})]}),e.tags&&e.tags.length>0&&(0,s.jsx)("div",{className:"py-8 border-t border-gray-200 dark:border-gray-700 mt-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-2",children:e.tags.map((e,t)=>(0,s.jsx)("span",{className:(0,p.cn)("px-3 py-1.5 rounded-full text-sm font-medium","bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300","border border-emerald-200 dark:border-emerald-700","hover:bg-emerald-200 dark:hover:bg-emerald-800/50 transition-colors duration-200"),children:"string"==typeof e?e:e.name||JSON.stringify(e)},"string"==typeof e?e:e.id||t))})]})}),(0,s.jsx)("section",{className:"mt-20 mb-12",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"}),(0,s.jsx)("div",{className:"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent"}),(0,s.jsxs)("div",{className:"pt-12",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsxs)("div",{className:"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"p-2 rounded-xl bg-primary/10 text-primary",children:(0,s.jsx)(x.Z,{className:"w-5 h-5"})}),(0,s.jsx)("span",{className:"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase",children:"Album Discussion"})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3",children:"Share Your Memories"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed",children:"What story do these moments tell you? Share your thoughts and connect through shared experiences."})]}),(0,s.jsx)(w.WalineComment,{path:`/gallery/album/${e.id}`,title:e.title,className:"max-w-5xl mx-auto"})]})]})})]})})}),f.isOpen&&(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-black/95 backdrop-blur-sm",children:(0,s.jsxs)("div",{className:"w-full h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-6 text-white",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:f.images[f.currentIndex]?.alt}),(0,s.jsxs)("span",{className:"text-sm text-white/70",children:[f.currentIndex+1," / ",f.images.length]})]}),(0,s.jsx)("button",{onClick:z,className:"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors",children:(0,s.jsx)(u.Z,{className:"w-5 h-5"})})]}),(0,s.jsxs)("div",{className:"flex-grow flex items-center justify-center relative",children:[f.images.length>1&&(0,s.jsx)("button",{onClick:_,className:"absolute left-6 z-10 p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-300 hover:scale-110",children:(0,s.jsx)(g.Z,{className:"w-6 h-6"})}),(0,s.jsx)("div",{className:"relative max-w-[90vw] max-h-[80vh] flex items-center justify-center",children:(0,s.jsx)(l.default,{src:f.images[f.currentIndex]?.url,alt:f.images[f.currentIndex]?.alt||"",width:f.images[f.currentIndex]?.width||800,height:f.images[f.currentIndex]?.height||600,className:"max-w-full max-h-full object-contain rounded-lg select-none",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.5)"},unoptimized:!0,draggable:!1})}),f.images.length>1&&(0,s.jsx)("button",{onClick:E,className:"absolute right-6 z-10 p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-300 hover:scale-110",children:(0,s.jsx)(b.Z,{className:"w-6 h-6"})})]}),f.images.length>1&&(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex justify-center gap-2 overflow-x-auto max-w-full",children:f.images.map((e,t)=>(0,s.jsx)("button",{onClick:()=>N(e=>({...e,currentIndex:t})),className:(0,p.cn)("relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0","border-2 transition-all duration-300",t===f.currentIndex?"border-white scale-110":"border-transparent hover:border-white/50"),children:(0,s.jsx)(l.default,{src:e.thumbnail_url||e.url,alt:e.alt,fill:!0,className:"object-cover",sizes:"64px"})},e.id))})})]})})]})}}},function(e){e.O(0,[105,216,592,744],function(){return e(e.s=27381)}),_N_E=e.O()}]);