(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[928],{33889:function(e,t,a){Promise.resolve().then(a.bind(a,81523)),Promise.resolve().then(a.bind(a,70049)),Promise.resolve().then(a.bind(a,72502))},72502:function(e,t,a){"use strict";a.r(t),a.d(t,{EnhancedGalleryContainer:function(){return L}});var r=a(57437),s=a(2265),l=a(91723),i=a(14924),n=a(55736),o=a(53113),d=a(31047),c=a(82023),m=a(81201),h=a(58608),g=a(33145),x=a(40875),u=a(83774),p=a(53581),y=a(42208),b=a(22135),f=a(32489),v=a(79879),j=a(79059);function w({entries:e,highlightedEntryId:t}){let[a,l]=(0,s.useState)(null),[i,n]=(0,s.useState)(0),[o,c]=(0,s.useState)([]),[w,N]=(0,s.useState)({}),[k,S]=(0,s.useState)([]),[C,A]=(0,s.useState)(null),[_,Z]=(0,s.useState)(!1),[T,E]=(0,s.useState)(null),z=e=>{if(!e)return"";let t=e.thumbnail_url||e.url;return t?t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`):""},$=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch{return e}},D=(e,t)=>{let a=t.findIndex(t=>t.id===e.id);l(e),n(a),c(t)},L=()=>{l(null),n(0),c([])},W=()=>{if(0===o.length)return;let e=i>0?i-1:o.length-1;n(e),l(o[e])},I=()=>{if(0===o.length)return;let e=i<o.length-1?i+1:0;n(e),l(o[e])};(0,s.useEffect)(()=>{let e=e=>{if(a)switch(e.key){case"ArrowLeft":e.preventDefault(),W();break;case"ArrowRight":e.preventDefault(),I();break;case"Escape":e.preventDefault(),L();break;case"t":case"T":o.length>1&&o.length<=10&&(e.preventDefault(),Z(!_))}};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[a,i,o,_]),(0,s.useEffect)(()=>{let t=e.reduce((e,t)=>{let a=new Date(t.date).getFullYear().toString(),r=e.find(e=>e.year===a);return r?r.entries.push(t):e.push({year:a,entries:[t],isCollapsed:!1}),e},[]);t.sort((e,t)=>parseInt(t.year)-parseInt(e.year)),t.forEach(e=>{e.entries.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())}),S(t)},[e]),(0,s.useEffect)(()=>{if(!t)return;let a=e.find(e=>e.id===t);if(a){E(a);let e=new Date(a.date).getFullYear().toString();S(t=>t.map(t=>t.year===e?{...t,isCollapsed:!1}:t)),setTimeout(()=>{let e=document.getElementById(`timeline-entry-${t}`);e&&(e.scrollIntoView({behavior:"smooth",block:"center"}),e.classList.add("highlight-pulse"),setTimeout(()=>{e.classList.remove("highlight-pulse")},2e3))},500)}},[t,e]);let M=e=>{S(t=>t.map(t=>t.year===e?{...t,isCollapsed:!t.isCollapsed}:t))},P=s.memo(({year:e,isCollapsed:t,entryCount:a})=>{let l=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".year-node"),a=e.currentTarget.querySelector(".year-label");t&&(t.style.transform="scale(1.05) translateY(-2px)",t.style.boxShadow=`0 8px 25px ${h.AS.primary.main}40, ${h.AS.shadows.glow}`),a&&(a.style.transform="translateY(-50%) translateX(4px) scale(1.02)",a.style.boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)")},[]),i=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".year-node"),a=e.currentTarget.querySelector(".year-label");t&&(t.style.transform="scale(1) translateY(0)",t.style.boxShadow=h.AS.shadows.glow),a&&(a.style.transform="translateY(-50%) translateX(0) scale(1)",a.style.boxShadow="0 4px 6px rgba(0, 0, 0, 0.1)")},[]);return(0,r.jsxs)("div",{className:"relative group cursor-pointer",onClick:()=>M(e),style:{paddingLeft:"1rem",paddingRight:"1rem",paddingTop:"0.5rem",paddingBottom:"0.5rem"},onMouseEnter:l,onMouseLeave:i,children:[(0,r.jsxs)("div",{className:"year-node relative w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg transform-gpu transition-all duration-300",style:{background:h.AS.gradients.primary,boxShadow:h.AS.shadows.glow},children:[(0,r.jsx)(j.Vj,{intensity:"normal",children:(0,r.jsx)("span",{className:"relative z-10",children:e})}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-full animate-ping opacity-0 group-hover:opacity-30 transition-opacity duration-300",style:{backgroundColor:h.AS.primary.main}})]}),(0,r.jsx)("div",{className:"year-label absolute left-20 top-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transform-gpu transition-all duration-300",style:{boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:e}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",a," memories)"]}),(0,r.jsx)("div",{className:"transition-transform duration-200",style:{transform:t?"rotate(0deg)":"rotate(180deg)"},children:(0,r.jsx)(x.Z,{className:"w-4 h-4"})})]})})]})}),Y=s.memo(({entry:e,index:t})=>{let{elementRef:a,isVisible:l}=(0,v.$p)(.2),i=T?.id===e.id,n=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".timeline-card");t&&(t.style.transform="translateY(-8px) scale(1.02)",t.style.boxShadow="0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.1)")},[]),o=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".timeline-card");t&&(t.style.transform="translateY(0) scale(1)",t.style.boxShadow="0 10px 25px rgba(0, 0, 0, 0.1)")},[]);return(0,r.jsx)(j.ay,{delay:100*t,children:(0,r.jsxs)("div",{id:`timeline-entry-${e.id}`,ref:a,className:(0,m.cn)("relative ml-8 mb-8",i&&"highlight-entry"),style:{paddingLeft:"1rem",paddingRight:"1rem",paddingTop:"0.5rem",paddingBottom:"0.5rem",marginLeft:"2rem",marginRight:"1rem"},children:[(0,r.jsx)("div",{className:"absolute -left-12 top-8 w-8 h-0.5 bg-gradient-to-r from-gray-300 to-transparent dark:from-gray-600"}),(0,r.jsx)("div",{className:"absolute -left-14 top-7 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 shadow-sm z-10",style:{backgroundColor:h.AS.primary.main}}),(0,r.jsx)(j.Uf,{children:(0,r.jsxs)("div",{className:"timeline-card relative overflow-hidden rounded-2xl border cursor-pointer group transition-all duration-300",style:{...h.WD.getTimelineCardStyle(),boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)",borderColor:`${h.AS.primary.main}20`},onMouseEnter:n,onMouseLeave:o,children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:h.AS.gradients.glow}}),(0,r.jsxs)("div",{className:"relative z-10 p-6",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsx)("span",{children:$(e.date)})]}),e.location&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{children:[e.images.length," photos"]})]})]}),e.content&&(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e.content})}),e.images.length>0&&(0,r.jsxs)("div",{className:(0,m.cn)("grid gap-3 rounded-lg",1===e.images.length?"grid-cols-1":2===e.images.length?"grid-cols-2":3===e.images.length?"grid-cols-3":"grid-cols-2 sm:grid-cols-3"),style:{padding:"0.25rem",margin:"-0.25rem"},children:[e.images.slice(0,6).map((t,a)=>(0,r.jsx)(j.O1,{delay:50*a,children:(0,r.jsxs)("div",{className:(0,m.cn)("relative cursor-pointer overflow-hidden rounded-lg group/image transform-gpu",1===e.images.length?"aspect-video":"aspect-square"),onClick:()=>D(t,e.images),style:{transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",transformOrigin:"center center"},onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.02) translateY(-2px)",e.currentTarget.style.boxShadow=`0 8px 20px ${h.AS.primary.main}20, 0 4px 8px rgba(0, 0, 0, 0.1)`},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1) translateY(0)",e.currentTarget.style.boxShadow="none"},children:[(0,r.jsx)(g.default,{src:z(t),alt:t.alt||`Gallery image ${a+1}`,fill:!0,className:"object-cover transition-transform duration-300 group-hover/image:scale-110",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover/image:bg-black/20 transition-colors duration-300"}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-300",children:(0,r.jsx)(y.Z,{className:"w-6 h-6 text-white"})})]})},t.id)),e.images.length>6&&(0,r.jsx)("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-gray-500 dark:text-gray-400 font-medium",children:["+",e.images.length-6]})})]})]})]})})]})})});return(0,r.jsxs)("div",{className:"timeline-container relative",children:[(0,r.jsxs)("div",{className:"relative",style:{paddingLeft:"2rem",paddingRight:"2rem",paddingTop:"1rem",paddingBottom:"1rem"},children:[(0,r.jsx)("div",{className:"absolute top-0 bottom-0 w-0.5 opacity-30",style:{backgroundColor:h.AS.primary.main,left:"2.5rem"}}),k.map((e,t)=>(0,r.jsxs)("div",{className:"relative mb-12",children:[(0,r.jsx)(j.ay,{delay:200*t,children:(0,r.jsx)(P,{year:e.year,isCollapsed:e.isCollapsed,entryCount:e.entries.length})}),!e.isCollapsed&&(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)(j.dA,{staggerDelay:100,children:e.entries.map((e,t)=>(0,r.jsx)(Y,{entry:e,index:t},e.id))})})]},e.year))]}),a&&(0,r.jsxs)("div",{className:"fixed inset-0 z-[60] flex items-center justify-center backdrop-blur-md bg-white/95 dark:bg-gray-900/95",onClick:L,children:[(0,r.jsxs)("div",{className:"relative max-w-7xl max-h-[90vh] m-4 flex items-center justify-center",onClick:e=>e.stopPropagation(),children:[o.length>1&&(0,r.jsx)("button",{onClick:W,className:"absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:(0,r.jsx)(b.Z,{className:"w-6 h-6 rotate-[-90deg]"})}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(g.default,{src:(e=>{if(!e||!e.url)return"";let t=e.url;return t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`)})(a),alt:a.alt||"Gallery image",width:1200,height:800,className:(0,m.cn)("object-contain rounded-lg max-w-[85vw] transition-all duration-300",_?"max-h-[70vh]":"max-h-[85vh]"),quality:90})}),o.length>1&&(0,r.jsx)("button",{onClick:I,className:"absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:(0,r.jsx)(b.Z,{className:"w-6 h-6 rotate-90"})}),(0,r.jsx)("button",{onClick:L,className:"absolute top-4 right-4 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:(0,r.jsx)(f.Z,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"fixed bottom-6 left-1/2 -translate-x-1/2 z-20 pointer-events-none",children:(0,r.jsx)("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl px-6 py-4 text-gray-700 dark:text-gray-300 shadow-2xl border border-gray-200/20 dark:border-gray-700/20 max-w-lg pointer-events-auto",children:(0,r.jsxs)("div",{className:"text-center",children:[a.alt&&(0,r.jsx)("p",{className:"font-medium mb-2 max-w-md truncate",children:a.alt}),o.length>1&&(0,r.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[i+1," of ",o.length]}),o.length>1&&o.length<=10&&(0,r.jsxs)("button",{onClick:e=>{e.stopPropagation(),Z(!_)},className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-all duration-200 flex items-center gap-1 px-3 py-1.5 rounded-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/50 border border-transparent hover:border-gray-300 dark:hover:border-gray-600",children:[(0,r.jsxs)("span",{children:[_?"Hide":"Show"," thumbnails"]}),(0,r.jsx)(b.Z,{className:(0,m.cn)("w-3 h-3 transition-transform duration-200",_?"rotate-180":"")})]})]}),o.length>1&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center mt-2",children:["Use ← → keys to navigate",o.length<=10?", T to toggle thumbnails":""]})]})})}),o.length>1&&o.length<=10&&(0,r.jsx)("div",{className:(0,m.cn)("fixed bottom-0 left-0 right-0 z-30 transition-all duration-500 ease-out",_?"translate-y-0 opacity-100":"translate-y-full opacity-0 pointer-events-none"),onClick:e=>e.stopPropagation(),children:(0,r.jsx)("div",{className:"bg-gradient-to-t from-black/80 via-black/60 to-transparent backdrop-blur-xl",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-4 sm:py-6",children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"bg-white/10 dark:bg-gray-800/20 backdrop-blur-md rounded-2xl p-3 sm:p-4 shadow-2xl border border-white/20 dark:border-gray-700/30 max-w-full",children:(0,r.jsx)("div",{className:"flex gap-2 sm:gap-3 justify-center max-w-full overflow-x-auto pb-2 thumbnail-scroll",children:o.map((e,t)=>(0,r.jsxs)("button",{onClick:a=>{a.stopPropagation(),n(t),l(e)},className:(0,m.cn)("relative rounded-xl overflow-hidden border-2 transition-all duration-300 flex-shrink-0 group","w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16",t===i?"border-white/80 scale-110 shadow-2xl ring-2 ring-white/40":"border-white/20 hover:border-white/60 opacity-60 hover:opacity-100 hover:scale-105"),children:[(0,r.jsx)(g.default,{src:z(e),alt:e.alt||`Thumbnail ${t+1}`,width:64,height:64,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"}),t===i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),(0,r.jsx)("div",{className:"absolute top-1 right-1 w-2 h-2 bg-white rounded-full shadow-lg"})]})]},e.id))})})})})})})]})]})}var N=a(99376),k=a(33276),S=a(76858);function C({albums:e}){let t=(0,N.useRouter)(),a=e=>{t.push(`/gallery/album/${e.id}`)},l=e=>{let t;return e.cover_image&&(t="object"==typeof e.cover_image&&null!==e.cover_image?e.cover_image.thumbnail_url||e.cover_image.url:e.cover_image)?t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`):"/images/placeholder.jpg"},n=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short"})}catch{return e}},c=s.memo(({album:e,index:t})=>{let c=(0,v.Lv)(.15),x=(0,v.wb)(8),[u,p]=(0,s.useState)(!1),y=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".album-card"),a=e.currentTarget;t&&a&&(t.style.transform="translateY(-8px) scale(1.02)",t.style.boxShadow=h.AS.shadows.cardHover,a.style.zIndex="10")},[]),b=(0,s.useCallback)(e=>{let t=e.currentTarget.querySelector(".album-card"),a=e.currentTarget;t&&a&&(t.style.transform="translateY(0) scale(1)",t.style.boxShadow=h.WD.getAlbumCardStyle().boxShadow||"",a.style.zIndex="1")},[]);return(0,r.jsx)(j.ay,{delay:150*t,children:(0,r.jsx)(j.Uf,{children:(0,r.jsxs)("div",{ref:c,className:"group relative cursor-pointer",onClick:t=>{t.preventDefault(),t.stopPropagation(),a(e)},onMouseEnter:y,onMouseLeave:b,children:[(0,r.jsx)("div",{className:"absolute -inset-2 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl",style:{background:h.AS.gradients.glow}}),(0,r.jsxs)("div",{ref:x,className:"album-card relative rounded-2xl transition-all duration-500 transform-gpu h-[420px] group",style:{...h.WD.getAlbumCardStyle(),overflow:"visible"},children:[(0,r.jsxs)("div",{className:"relative h-64 overflow-hidden rounded-t-2xl",children:[(0,r.jsx)("div",{className:"absolute inset-0 z-10",style:{background:h.AS.gradients.overlay}}),(0,r.jsx)(g.default,{src:l(e),alt:e.title,fill:!0,className:(0,m.cn)("object-cover transition-all duration-700 group-hover:scale-110",u?"opacity-100":"opacity-0"),sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onLoad:()=>p(!0)}),!u&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse flex items-center justify-center",children:(0,r.jsx)(o.Z,{className:"w-12 h-12 text-gray-400"})}),(0,r.jsx)("div",{className:"absolute inset-0 z-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,r.jsx)(j.Vj,{intensity:"intense",children:(0,r.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white shadow-2xl transition-transform duration-300 group-hover:scale-110",style:{backgroundColor:h.AS.primary.main},children:(0,r.jsx)(k.Z,{className:"w-6 h-6 ml-1",fill:"currentColor"})})})}),(0,r.jsx)("div",{className:"absolute top-4 right-4 z-20",children:(0,r.jsxs)("div",{className:"px-3 py-1 rounded-full text-white text-sm font-medium backdrop-blur-sm",style:{backgroundColor:`${h.AS.primary.main}CC`},children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 inline mr-1"}),e.image_count]})})]}),(0,r.jsxs)("div",{className:"relative z-10 p-6 flex flex-col h-40 bg-white dark:bg-gray-800 rounded-b-2xl",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-1",children:e.title}),(0,r.jsx)("div",{className:"h-10 mb-3",children:(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-2",children:e.description||"暂无描述"})}),(0,r.jsx)("div",{className:"mb-3",children:e.category&&(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",style:{backgroundColor:`${h.AS.primary.main}20`,color:h.AS.primary.main,border:`1px solid ${h.AS.primary.main}30`},children:[(0,r.jsx)(i.Z,{className:"w-3 h-3 mr-1"}),e.category.name]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mt-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:n(e.created_at)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-primary font-medium group-hover:gap-2 transition-all duration-300",children:[(0,r.jsx)("span",{children:"View Album"}),(0,r.jsx)(S.Z,{className:"w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"})]})]})]}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 h-1 bg-gradient-to-r transition-all duration-500 transform origin-left scale-x-0 group-hover:scale-x-100",style:{background:h.AS.gradients.primary,width:"100%"}})]})]})})})});return 0===e.length?(0,r.jsxs)("div",{className:"py-12 text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:(0,r.jsx)(i.Z,{className:"w-12 h-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No albums yet"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Create your first album to get started"})]}):(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)(()=>{let t=e.reduce((e,t)=>e+t.image_count,0),a=e.length;return(0,r.jsx)(j.ay,{children:(0,r.jsx)("div",{className:"mb-8 p-6 rounded-2xl border",style:h.WD.getAlbumCardStyle(),children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:a}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Albums"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:t}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Photos"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:Math.round(t/a)||0}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avg per Album"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold mb-1",style:{color:h.AS.primary.main},children:[new Date().getFullYear()-Math.min(...e.map(e=>new Date(e.created_at).getFullYear())),"+"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Years"})]})]})})})},{}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4",children:e.map((e,t)=>(0,r.jsx)("div",{className:"relative",style:{paddingTop:"12px",paddingBottom:"12px"},children:(0,r.jsx)(c,{album:e,index:t})},e.id))})]})}var A=a(43391),_=a(88997),Z=a(68919);function T({images:e,loading:t=!1,searchQuery:a="",selectedCategory:l=null,selectedTags:i=[],layout:n="masonry"}){let[o,d]=(0,s.useState)(null),[c,x]=(0,s.useState)(0),[u,p]=(0,s.useState)(null),[w,N]=(0,s.useState)(new Set),[k,S]=(0,s.useState)(new Set),[C,T]=(0,s.useState)(!1),{preloadImage:E,isImageLoaded:z}=(0,v.o1)(),$=(0,s.useRef)(null),D=(0,s.useRef)(null),L=e=>{if(!e)return"";let t=e.thumbnail_url||e.url;return t?t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`):""},W=t=>{let a=e.findIndex(e=>e.id===t.id);d(t),x(a)},I=()=>{d(null),x(0)},M=()=>{if(0===e.length)return;let t=c>0?c-1:e.length-1;x(t),d(e[t])},P=()=>{if(0===e.length)return;let t=c<e.length-1?c+1:0;x(t),d(e[t])};(0,s.useEffect)(()=>{let t=t=>{if(o)switch(t.key){case"ArrowLeft":t.preventDefault(),M();break;case"ArrowRight":t.preventDefault(),P();break;case"Escape":t.preventDefault(),I();break;case"t":case"T":e.length>1&&e.length<=20&&(t.preventDefault(),T(!C))}};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)},[o,c,e,C]),(0,s.useEffect)(()=>(D.current=new IntersectionObserver(t=>{t.forEach(t=>{if(t.isIntersecting){let a=parseInt(t.target.getAttribute("data-index")||"0");S(e=>new Set(e).add(a));let r=e[a];r&&E(L(r))}})},{threshold:.1,rootMargin:"50px"}),()=>{D.current&&D.current.disconnect()}),[e,E]);let Y=({image:e,index:t})=>{let a=(0,s.useRef)(null),[l,i]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=a.current;return e&&D.current&&D.current.observe(e),()=>{e&&D.current&&D.current.unobserve(e)}},[]);let c=e.width&&e.height?e.width/e.height:1;return(0,r.jsx)(j.ay,{delay:50*t,children:(0,r.jsxs)("div",{ref:a,"data-index":t,className:"group relative cursor-pointer break-inside-avoid mb-4",style:{aspectRatio:"masonry"===n?c:"1"},onMouseEnter:()=>{d(!0),p(t)},onMouseLeave:()=>{d(!1),p(null)},onClick:()=>W(e),children:[(0,r.jsx)("div",{className:"absolute -inset-1 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 blur-sm",style:{background:h.AS.gradients.glow}}),(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-lg transition-all duration-500 transform-gpu group-hover:scale-[1.02] group-hover:shadow-2xl",style:h.WD.getGridCardStyle(),children:[k.has(t)&&(0,r.jsxs)(r.Fragment,{children:[!l&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse"}),(0,r.jsx)(g.default,{src:L(e),alt:e.alt||`Gallery image ${t+1}`,fill:!0,className:(0,m.cn)("object-cover transition-all duration-700 group-hover:scale-110",l?"opacity-100":"opacity-0"),sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",onLoad:()=>i(!0),quality:75})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300"}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(j.Vj,{intensity:"normal",children:(0,r.jsx)("button",{className:"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors duration-200 flex items-center justify-center",onClick:t=>{t.stopPropagation(),W(e)},children:(0,r.jsx)(A.Z,{className:"w-5 h-5"})})}),(0,r.jsx)(j.Vj,{intensity:"normal",children:(0,r.jsx)("button",{className:"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors duration-200 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,r.jsx)(_.Z,{className:"w-5 h-5"})})}),(0,r.jsx)(j.Vj,{intensity:"normal",children:(0,r.jsx)("button",{className:"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors duration-200 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,r.jsx)(Z.Z,{className:"w-5 h-5"})})})]})}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[e.alt&&(0,r.jsx)("p",{className:"text-white text-sm font-medium mb-1 line-clamp-2",children:e.alt}),e.tags&&e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,t)=>{let a="string"==typeof e?e:e.name||e;return(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full bg-white/20 backdrop-blur-sm text-white",children:a},t)}),e.tags.length>3&&(0,r.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full bg-white/20 backdrop-blur-sm text-white",children:["+",e.tags.length-3]})]})]}),k.has(t)&&!l&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-t-transparent rounded-full animate-spin",style:{borderColor:h.AS.primary.main}})})]})]})})};return t?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:12}).map((e,t)=>(0,r.jsx)("div",{className:"aspect-square bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"},t))}):0===e.length?(0,r.jsxs)("div",{className:"py-12 text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:(0,r.jsx)(y.Z,{className:"w-12 h-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No images found"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:a?`No results for "${a}"`:"No images to display"})]}):(0,r.jsxs)("div",{ref:$,className:"relative",children:[(()=>{switch(n){case"masonry":return(0,r.jsx)("div",{className:"columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4",children:e.map((e,t)=>(0,r.jsx)(Y,{image:e,index:t},e.id))});case"grid":return(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:e.map((e,t)=>(0,r.jsx)(Y,{image:e,index:t},e.id))});case"justified":return(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map((e,t)=>{let a=e.width&&e.height?e.width/e.height:1,s=Math.max(200,Math.min(400,200*a));return(0,r.jsx)("div",{style:{width:`${s}px`,flexGrow:a},children:(0,r.jsx)(Y,{image:e,index:t})},e.id)})});default:return null}})(),o&&(0,r.jsxs)("div",{className:"fixed inset-0 z-[60] flex items-center justify-center backdrop-blur-md bg-white/95 dark:bg-gray-900/95",onClick:I,children:[(0,r.jsxs)("div",{className:"relative max-w-7xl max-h-[90vh] m-4 flex items-center justify-center",onClick:e=>e.stopPropagation(),children:[e.length>1&&(0,r.jsx)("button",{onClick:M,className:"absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:(0,r.jsx)(b.Z,{className:"w-6 h-6 rotate-[-90deg]"})}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(g.default,{src:(e=>{if(!e||!e.url)return"";let t=e.url;return t.startsWith("http://")||t.startsWith("https://")?t:(t.startsWith("/")||(t="/"+t),`http://**************:8000${t}`)})(o),alt:o.alt||"Gallery image",width:1200,height:800,className:(0,m.cn)("object-contain rounded-lg max-w-[85vw] transition-all duration-300",C?"max-h-[70vh]":"max-h-[85vh]"),quality:90})}),e.length>1&&(0,r.jsx)("button",{onClick:P,className:"absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:(0,r.jsx)(b.Z,{className:"w-6 h-6 rotate-90"})}),(0,r.jsx)("button",{onClick:I,className:"absolute top-4 right-4 z-10 w-12 h-12 bg-white/80 dark:bg-gray-800/80 hover:bg-white/90 dark:hover:bg-gray-700/90 rounded-full flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-200 hover:scale-110 backdrop-blur-sm shadow-lg",children:(0,r.jsx)(f.Z,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"fixed bottom-6 left-1/2 -translate-x-1/2 z-20 pointer-events-none",children:(0,r.jsx)("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl px-6 py-4 text-gray-700 dark:text-gray-300 shadow-2xl border border-gray-200/20 dark:border-gray-700/20 max-w-lg pointer-events-auto",children:(0,r.jsxs)("div",{className:"text-center",children:[o.alt&&(0,r.jsx)("p",{className:"font-medium mb-2 max-w-md truncate",children:o.alt}),o.tags&&o.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 justify-center mb-3",children:o.tags.map((e,t)=>{let a="string"==typeof e?e:e.name||e;return(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full bg-gray-200/50 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300",children:a},t)})}),e.length>1&&(0,r.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[c+1," of ",e.length]}),e.length>1&&e.length<=20&&(0,r.jsxs)("button",{onClick:e=>{e.stopPropagation(),T(!C)},className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-all duration-200 flex items-center gap-1 px-3 py-1.5 rounded-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/50 border border-transparent hover:border-gray-300 dark:hover:border-gray-600",children:[(0,r.jsxs)("span",{children:[C?"Hide":"Show"," thumbnails"]}),(0,r.jsx)(b.Z,{className:(0,m.cn)("w-3 h-3 transition-transform duration-200",C?"rotate-180":"")})]})]}),e.length>1&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center mt-2",children:["Use ← → keys to navigate",e.length<=20?", T to toggle thumbnails":""]})]})})}),e.length>1&&e.length<=20&&(0,r.jsx)("div",{className:(0,m.cn)("fixed bottom-0 left-0 right-0 z-30 transition-all duration-500 ease-out",C?"translate-y-0 opacity-100":"translate-y-full opacity-0 pointer-events-none"),onClick:e=>e.stopPropagation(),children:(0,r.jsx)("div",{className:"bg-gradient-to-t from-black/80 via-black/60 to-transparent backdrop-blur-xl",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-4 sm:py-6",children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"bg-white/10 dark:bg-gray-800/20 backdrop-blur-md rounded-2xl p-3 sm:p-4 shadow-2xl border border-white/20 dark:border-gray-700/30 max-w-full",children:(0,r.jsx)("div",{className:"flex gap-2 sm:gap-3 justify-center max-w-full overflow-x-auto pb-2 thumbnail-scroll",children:e.slice(Math.max(0,c-6),c+7).map((e,t)=>{let a=Math.max(0,c-6)+t;return(0,r.jsxs)("button",{onClick:t=>{t.stopPropagation(),x(a),d(e)},className:(0,m.cn)("relative rounded-xl overflow-hidden border-2 transition-all duration-300 flex-shrink-0 group","w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16",a===c?"border-white/80 scale-110 shadow-2xl ring-2 ring-white/40":"border-white/20 hover:border-white/60 opacity-60 hover:opacity-100 hover:scale-105"),children:[(0,r.jsx)(g.default,{src:L(e),alt:e.alt||`Thumbnail ${a+1}`,width:64,height:64,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"}),a===c&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),(0,r.jsx)("div",{className:"absolute top-1 right-1 w-2 h-2 bg-white rounded-full shadow-lg"})]})]},e.id)})})})})})})})]})]})}var E=a(73247),z=a(35363),$=a(62720);function D({searchQuery:e,onSearchChange:t,categories:a,selectedCategory:l,onCategorySelect:n,selectedTags:o,onTagSelect:d,availableTags:c,onClearFilters:g,showFilters:x,onToggleFilters:u}){let[p,y]=(0,s.useState)(!1),[b,v]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1),k=(0,s.useRef)(null),S=e=>{d(o.includes(e)?o.filter(t=>t!==e):[...o,e])},C=()=>{let e=0;return l&&e++,o.length>0&&(e+=o.length),e};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(j.ay,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("div",{className:(0,m.cn)("relative flex items-center rounded-2xl border-2 transition-all duration-300 backdrop-blur-sm",p?"border-primary shadow-lg shadow-primary/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),style:{background:p?h.AS.gradients.card:"rgba(255, 255, 255, 0.8)"},children:[(0,r.jsx)("div",{className:"absolute left-4 flex items-center",children:(0,r.jsx)(E.Z,{className:(0,m.cn)("w-5 h-5 transition-colors duration-300",p?"text-primary":"text-gray-400")})}),(0,r.jsx)("input",{ref:k,type:"text",placeholder:"Search photos, albums, or tags...",value:e,onChange:e=>t(e.target.value),onFocus:()=>y(!0),onBlur:()=>y(!1),className:"w-full pl-12 pr-20 py-4 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none text-lg"}),e&&(0,r.jsx)("button",{onClick:()=>t(""),className:"absolute right-16 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:(0,r.jsx)(f.Z,{className:"w-4 h-4"})}),(0,r.jsxs)("button",{onClick:u,className:(0,m.cn)("absolute right-4 p-2 rounded-lg transition-all duration-300 flex items-center gap-2",x?"bg-primary text-white shadow-lg":"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"),children:[(0,r.jsx)(z.Z,{className:"w-5 h-5"}),C()>0&&(0,r.jsx)("span",{className:"w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:C()})]})]}),p&&e.length>0&&(0,r.jsx)(j.oX,{direction:"down",children:(0,r.jsx)("div",{className:"absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-xl z-10 max-h-60 overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-4 text-sm text-gray-500 dark:text-gray-400",children:['Press Enter to search for "',e,'"']})})})]})}),x&&(0,r.jsx)(j.oX,{direction:"down",children:(0,r.jsxs)("div",{className:"p-6 rounded-2xl border backdrop-blur-sm",style:{background:h.AS.gradients.card,borderColor:h.AS.primary.main+"30"},children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(i.Z,{className:"w-5 h-5",style:{color:h.AS.primary.main}}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Categories"})]}),(0,r.jsxs)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[(0,r.jsxs)("button",{onClick:()=>n(null),className:(0,m.cn)("w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-between",null===l?"bg-primary text-white shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"),children:[(0,r.jsx)("span",{children:"All Categories"}),(0,r.jsx)("span",{className:"text-sm opacity-70",children:a.reduce((e,t)=>e+t.image_count,0)})]}),a.map(e=>(0,r.jsxs)("button",{onClick:()=>n(l===e.id?null:e.id),className:(0,m.cn)("w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-between",l===e.id?"bg-primary text-white shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon&&(0,r.jsx)("span",{className:"text-lg",children:e.icon}),(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{children:e.name})]}),(0,r.jsx)("span",{className:"text-sm opacity-70",children:e.image_count})]},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)($.Z,{className:"w-5 h-5",style:{color:h.AS.primary.main}}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Tags"}),o.length>0&&(0,r.jsx)("button",{onClick:()=>d([]),className:"text-sm text-primary hover:text-primary/80 transition-colors",children:"Clear all"})]}),(0,r.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:c.slice(0,20).map(e=>(0,r.jsxs)("button",{onClick:()=>S(e.name),className:(0,m.cn)("w-full text-left px-4 py-2 rounded-lg transition-all duration-200 flex items-center justify-between text-sm",o.includes(e.name)?"bg-primary text-white shadow-md":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"),children:[(0,r.jsxs)("span",{children:["#",e.name]}),(0,r.jsx)("span",{className:"text-xs opacity-70",children:e.count})]},e.name))})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:C()>0&&(0,r.jsxs)("span",{children:[C()," filter(s) active"]})}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:g,className:"px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"Clear All"}),(0,r.jsx)("button",{onClick:u,className:"px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium",children:"Apply Filters"})]})]})]})}),(l||o.length>0)&&(0,r.jsx)(j.ay,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[l&&(0,r.jsxs)("span",{className:"inline-flex items-center gap-2 px-3 py-1 bg-primary text-white rounded-full text-sm",children:[(0,r.jsx)(i.Z,{className:"w-3 h-3"}),a.find(e=>e.id===l)?.name,(0,r.jsx)("button",{onClick:()=>n(null),className:"hover:bg-white/20 rounded-full p-0.5 transition-colors",children:(0,r.jsx)(f.Z,{className:"w-3 h-3"})})]}),o.map(e=>(0,r.jsxs)("span",{className:"inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary border border-primary/20 rounded-full text-sm",children:[(0,r.jsx)($.Z,{className:"w-3 h-3"}),e,(0,r.jsx)("button",{onClick:()=>S(e),className:"hover:bg-primary/20 rounded-full p-0.5 transition-colors",children:(0,r.jsx)(f.Z,{className:"w-3 h-3"})})]},e))]})})]})}function L({albums:e=[],timelineEntries:t=[],initialView:a="timeline",highlightedEntryId:g,highlightedAlbumId:x,galleryTitle:u="Photo Gallery",galleryDescription:p="Explore my photography collection through different perspectives - timeline memories, organized albums, or browse all photos in a grid."}){let y=e.map(e=>({id:e.id,title:e.title,description:e.description,cover_image:e.coverImage?{id:parseInt(e.coverImage.id)||0,url:e.coverImage.url,thumbnail_url:e.coverImage.thumbnail_url,display_name:e.coverImage.alt,original_filename:e.coverImage.alt||"image",width:e.coverImage.width,height:e.coverImage.height}:null,date:e.date,location:e.location,type:"theme",image_count:e.images.length,created_at:e.date,updated_at:e.date,slug:e.slug})),[b,f]=(0,s.useState)(a),[v,N]=(0,s.useState)([]),[k,S]=(0,s.useState)([]),[A,_]=(0,s.useState)([]),[Z,E]=(0,s.useState)(!1),[z,$]=(0,s.useState)(""),[L,W]=(0,s.useState)(null),[I,M]=(0,s.useState)([]),[P,Y]=(0,s.useState)(!1),q=s.useMemo(()=>{let e=new Map;return v.forEach(t=>{t.tags?.forEach(t=>{let a="string"==typeof t?t:t.name||t;e.set(a,(e.get(a)||0)+1)})}),Array.from(e.entries()).map(([e,t])=>({name:e,count:t})).sort((e,t)=>t.count-e.count)},[v]),F=(0,s.useCallback)(()=>{(async()=>{E(!0);try{let e=await fetch("http://**************:8000/api/gallery/public");if(e.ok){let t=await e.json(),a=t.featured_items.map(e=>{let t=e.image,a="http://**************:8000";return{id:t.id,url:t.url.startsWith("http")?t.url:`${a}${t.url}`,thumbnail_url:t.thumbnail_url?t.thumbnail_url.startsWith("http")?t.thumbnail_url:`${a}${t.thumbnail_url}`:null,alt:t.display_name||t.original_filename,width:t.width||800,height:t.height||600,category:e.category,tags:e.tags||[],created_at:t.created_at}});N(a),S(a);let r=new Map;t.featured_items.forEach(e=>{e.category&&r.set(e.category.id,{...e.category,image_count:(r.get(e.category.id)?.image_count||0)+1})}),_(Array.from(r.values()))}}catch(e){}finally{E(!1)}})()},[]);(0,s.useEffect)(()=>{F()},[F]),(0,s.useEffect)(()=>{let e=v;if(z.trim()){let t=z.toLowerCase();e=e.filter(e=>e.alt?.toLowerCase().includes(t)||e.tags?.some(e=>("string"==typeof e?e:e.name||e).toLowerCase().includes(t)))}L&&(e=e.filter(e=>e.category?.id===L)),I.length>0&&(e=e.filter(e=>I.every(t=>e.tags?.some(e=>("string"==typeof e?e:e.name||e)===t)))),S(e)},[v,z,L,I]);let R=()=>{$(""),W(null),M([])};return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)(j.ay,{children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)(j.Vj,{intensity:"normal",children:(0,r.jsx)(c.Z,{className:"w-8 h-8",style:{color:h.AS.primary.main}})}),(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white",children:u}),(0,r.jsx)(j.Vj,{intensity:"normal",children:(0,r.jsx)(c.Z,{className:"w-8 h-8",style:{color:h.AS.primary.main}})})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:p})]})}),(0,r.jsx)(D,{searchQuery:z,onSearchChange:$,categories:A,selectedCategory:L,onCategorySelect:W,selectedTags:I,onTagSelect:M,availableTags:q,onClearFilters:R,showFilters:P,onToggleFilters:()=>Y(!P)}),(0,r.jsx)(()=>{let e=[{key:"timeline",label:"Timeline",icon:l.Z,description:"Chronological view"},{key:"albums",label:"Albums",icon:i.Z,description:"Organized collections"},{key:"grid",label:"Grid",icon:n.Z,description:"All photos"}];return(0,r.jsx)(j.ay,{children:(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("div",{className:"inline-flex rounded-2xl p-1 backdrop-blur-sm border",style:{background:h.AS.gradients.card,borderColor:h.AS.primary.main+"30"},children:e.map(e=>{let t=e.icon,a=b===e.key;return(0,r.jsxs)("button",{onClick:()=>f(e.key),className:(0,m.cn)("relative flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 font-medium",a?"text-white shadow-lg transform scale-105":"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"),style:a?{background:h.AS.gradients.primary,boxShadow:h.AS.shadows.glow}:{},children:[a&&(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl opacity-20 animate-pulse",style:{background:h.AS.gradients.glow}}),(0,r.jsx)(t,{className:"w-5 h-5 relative z-10"}),(0,r.jsx)("span",{className:"relative z-10",children:e.label}),a&&(0,r.jsx)("div",{className:"absolute -inset-1 rounded-xl opacity-30 blur-sm",style:{background:h.AS.gradients.primary}})]},e.key)})})})})},{}),(0,r.jsx)(()=>{let a=v.length,s=k.length,l=e.length,n=t.length;return(0,r.jsx)(j.ay,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl backdrop-blur-sm border",style:{background:h.AS.gradients.subtle,borderColor:h.AS.primary.main+"20"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[s," of ",a," photos"]})]}),l>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[l," albums"]})]}),n>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.Z,{className:"w-4 h-4",style:{color:h.AS.primary.main}}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[n," memories"]})]})]}),(z||L||I.length>0)&&(0,r.jsx)("button",{onClick:R,className:"text-sm text-primary hover:text-primary/80 transition-colors font-medium",children:"Clear filters"})]})})},{}),(0,r.jsxs)("div",{className:"min-h-[400px]",children:["timeline"===b&&(0,r.jsx)(j.oX,{direction:"up",children:(0,r.jsx)(w,{entries:t,highlightedEntryId:g})}),"albums"===b&&(0,r.jsx)(j.oX,{direction:"up",children:(0,r.jsx)(C,{albums:y})}),"grid"===b&&(0,r.jsx)(j.oX,{direction:"up",children:(0,r.jsx)(T,{images:k,loading:Z,searchQuery:z,selectedCategory:L,selectedTags:I,layout:"masonry"})})]})]})}}},function(e){e.O(0,[105,216,592,744],function(){return e(e.s=33889)}),_N_E=e.O()}]);