(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{33112:function(e,t,r){Promise.resolve().then(r.t.bind(r,72972,23)),Promise.resolve().then(r.bind(r,68766)),Promise.resolve().then(r.bind(r,99175)),Promise.resolve().then(r.bind(r,11457)),Promise.resolve().then(r.bind(r,632)),Promise.resolve().then(r.bind(r,52043)),Promise.resolve().then(r.bind(r,41920)),Promise.resolve().then(r.bind(r,92331)),Promise.resolve().then(r.bind(r,18191)),Promise.resolve().then(r.bind(r,95512)),Promise.resolve().then(r.bind(r,21832))},68766:function(e,t,r){"use strict";r.d(t,{BlogPreloader:function(){return n}});var o=r(57437),a=r(2265);let s=new Set,i=new Map;function n({slug:e,children:t}){let n=(0,a.useRef)(null),l=(0,a.useRef)(),d=(0,a.useCallback)(async()=>{if(!s.has(e)){if(i.has(e))return i.get(e);s.add(e);try{let t=fetch(`/api/blogs/${e}`,{method:"GET",headers:{"Cache-Control":"public, max-age=300, stale-while-revalidate=600"}}).then(e=>{if(e.ok)return e.json();throw Error(`Failed to prefetch blog: ${e.status}`)});i.set(e,t);let o=await t;return o?.content&&r.e(58).then(r.bind(r,48058)).then(({processMarkdownFast:e})=>{try{e(o.content)}catch(e){}}),o}catch(t){throw s.delete(e),i.delete(e),t}}},[e]),u=(0,a.useCallback)(()=>{l.current&&clearTimeout(l.current),l.current=setTimeout(()=>{d().catch(()=>{})},100)},[d]),c=(0,a.useCallback)(()=>{l.current&&(clearTimeout(l.current),l.current=void 0)},[]),m=(0,a.useCallback)(()=>{d().catch(()=>{})},[d]);return(0,a.useEffect)(()=>{let e=n.current;if(e)return e.addEventListener("mouseenter",u,{passive:!0}),e.addEventListener("mouseleave",c,{passive:!0}),e.addEventListener("touchstart",m,{passive:!0}),()=>{e.removeEventListener("mouseenter",u),e.removeEventListener("mouseleave",c),e.removeEventListener("touchstart",m),l.current&&clearTimeout(l.current)}},[u,c,m]),(0,o.jsx)("div",{ref:n,"data-blog-slug":e,className:"w-full h-full",children:t})}},99175:function(e,t,r){"use strict";r.d(t,{default:function(){return d}});var o=r(57437),a=r(78927),s=r(18191),i=r(71837),n=r(2265);function l({careerItem:e}){return(0,o.jsxs)("li",{className:"group/item flex gap-4 p-3 rounded-lg transition-all duration-300 hover:bg-muted/20 hover:scale-[1.02]",children:[(0,o.jsxs)("div",{className:"relative mt-1 flex h-10 w-10 flex-none items-center justify-center rounded-full shadow-md border border-muted bg-background transition-all duration-300 group-hover/item:shadow-lg group-hover/item:border-primary/30 group-hover/item:bg-primary/5",children:[(0,o.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/20 blur opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"}),e.logo?(0,o.jsx)(s.CustomIcon,{name:e.logo}):(0,o.jsx)(a.a,{size:20,weight:"duotone",className:"relative text-primary transition-all duration-300 group-hover/item:scale-110"})]}),(0,o.jsxs)("dl",{className:"flex flex-auto flex-wrap gap-x-2",children:[(0,o.jsx)("dt",{className:"sr-only",children:"Company"}),(0,o.jsxs)("dd",{className:"w-full flex-none text-sm font-medium transition-colors duration-300 group-hover/item:text-primary",children:[e.company,e.is_current&&(0,o.jsx)("span",{className:"ml-2 text-green-500 text-xs",children:"(Current)"})]}),(0,o.jsx)("dt",{className:"sr-only",children:"Position"}),(0,o.jsx)("dd",{className:"text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/80",children:e.position}),e.location&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("dt",{className:"sr-only",children:"Location"}),(0,o.jsxs)("dd",{className:"text-xs text-muted-foreground",children:["• ",e.location]})]}),(0,o.jsx)("dt",{className:"sr-only",children:"Date"}),(0,o.jsxs)("dd",{className:"ml-auto text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/70","aria-label":`${e.start_date} until ${e.end_date||"Present"}`,children:[e.start_date," - ",e.end_date||"Present"]}),e.description&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("dt",{className:"sr-only",children:"Description"}),(0,o.jsx)("dd",{className:"w-full text-xs text-muted-foreground mt-1",children:e.description})]})]})]})}function d(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)(!0);return((0,n.useEffect)(()=>{(async()=>{try{let e=await (0,i.kX)();t(e)}catch(e){}finally{s(!1)}})()},[]),r||0!==e.length)?(0,o.jsxs)("div",{className:"rounded-2xl border border-muted shadow-sm p-6",children:[(0,o.jsxs)("h2",{className:"flex text-sm font-semibold",children:[(0,o.jsx)(a.a,{size:24,weight:"duotone"}),(0,o.jsx)("span",{className:"ml-3",children:"Work"})]}),r?(0,o.jsx)("div",{className:"mt-6 space-y-4",children:[1,2].map(e=>(0,o.jsxs)("div",{className:"animate-pulse flex gap-4 p-3",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-muted rounded-full"}),(0,o.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,o.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"}),(0,o.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]})]},e))}):(0,o.jsx)("ol",{className:"mt-6 space-y-4",children:e.map(e=>(0,o.jsx)(l,{careerItem:e},e.id))})]}):null}},11457:function(e,t,r){"use strict";r.d(t,{default:function(){return c}});var o=r(57437),a=r(60303),s=r(46831),i=r(18191),n=r(71837),l=r(2265);let d={bachelor:{label:"Bachelor",color:"text-gray-700 dark:text-gray-300",iconColor:"text-gray-900 dark:text-gray-200",hoverColor:"group-hover/item:border-secondary/30 group-hover/item:bg-secondary/10"},master:{label:"Master",color:"text-blue-500",iconColor:"text-blue-500",hoverColor:"group-hover/item:border-blue-500/30 group-hover/item:bg-blue-50 dark:group-hover/item:bg-blue-950/20"},phd:{label:"PhD",color:"text-purple-500",iconColor:"text-purple-500",hoverColor:"group-hover/item:border-purple-500/30 group-hover/item:bg-purple-50 dark:group-hover/item:bg-purple-950/20"},diploma:{label:"Diploma",color:"text-orange-500",iconColor:"text-orange-500",hoverColor:"group-hover/item:border-orange-500/30 group-hover/item:bg-orange-50 dark:group-hover/item:bg-orange-950/20"},certificate:{label:"Certificate",color:"text-green-500",iconColor:"text-green-500",hoverColor:"group-hover/item:border-green-500/30 group-hover/item:bg-green-50 dark:group-hover/item:bg-green-950/20"}};function u({educationItem:e}){let t=d[e.degree_type]||d.bachelor;return(0,o.jsxs)("li",{className:"group/item flex gap-4 p-3 rounded-lg transition-all duration-300 hover:bg-muted/20 hover:scale-[1.02]",children:[(0,o.jsxs)("div",{className:`relative mt-1 flex h-10 w-10 flex-none items-center justify-center rounded-full shadow-md border border-muted bg-background transition-all duration-300 group-hover/item:shadow-lg ${t.hoverColor}`,children:[(0,o.jsx)("div",{className:`absolute inset-0 rounded-full bg-${"master"===e.degree_type?"blue":"phd"===e.degree_type?"purple":"diploma"===e.degree_type?"orange":"certificate"===e.degree_type?"green":"secondary"}-500/20 blur opacity-0 group-hover/item:opacity-100 transition-opacity duration-300`}),e.logo?(0,o.jsx)(i.CustomIcon,{name:e.logo}):(0,o.jsx)(a.X,{size:20,weight:"duotone",className:`relative ${t.iconColor} transition-all duration-300 group-hover/item:scale-110`})]}),(0,o.jsxs)("dl",{className:"flex flex-auto flex-wrap gap-x-2",children:[(0,o.jsx)("dt",{className:"sr-only",children:"学位类型"}),(0,o.jsxs)("dd",{className:`w-full flex-none text-xs font-medium ${t.color} mb-1 transition-colors duration-300`,children:[t.label,e.is_current&&(0,o.jsx)("span",{className:"ml-2 text-green-500",children:"(Current)"})]}),(0,o.jsx)("dt",{className:"sr-only",children:"School"}),(0,o.jsx)("dd",{className:"w-full flex-none text-sm font-medium transition-colors duration-300 group-hover/item:text-primary",children:e.school}),(0,o.jsx)("dt",{className:"sr-only",children:"Major"}),(0,o.jsx)("dd",{className:"text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/80",children:e.major}),(0,o.jsx)("dt",{className:"sr-only",children:"Date"}),(0,o.jsxs)("dd",{className:"ml-auto text-xs text-muted-foreground transition-colors duration-300 group-hover/item:text-foreground/70","aria-label":`${e.start_year} until ${e.end_year}`,children:[e.start_year," - ",e.end_year]}),e.gpa&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("dt",{className:"sr-only",children:"GPA"}),(0,o.jsxs)("dd",{className:"w-full text-xs text-muted-foreground",children:["GPA: ",e.gpa]})]})]})]})}function c(){let[e,t]=(0,l.useState)([]),[r,a]=(0,l.useState)(!0);if((0,l.useEffect)(()=>{(async()=>{try{let e=await (0,n.hQ)();t(e)}catch(e){}finally{a(!1)}})()},[]),!r&&0===e.length)return null;let i=e.filter(e=>"master"===e.degree_type),d=e.filter(e=>"bachelor"===e.degree_type),c=e.filter(e=>"phd"===e.degree_type),m=e.filter(e=>!["master","bachelor","phd"].includes(e.degree_type));return(0,o.jsxs)("div",{className:"group relative rounded-2xl border border-muted-foreground/20 shadow-sm p-6 bg-background/80 backdrop-blur-sm transition-all duration-500 hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5",children:[(0,o.jsx)("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,o.jsx)("div",{className:"absolute top-4 right-4 w-1.5 h-1.5 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),(0,o.jsxs)("div",{className:"relative z-10",children:[(0,o.jsxs)("h2",{className:"flex items-center text-sm font-semibold mb-6 group-hover:text-primary transition-colors duration-300",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(s.Z,{size:24,weight:"duotone",className:"transition-all duration-300 group-hover:scale-110 group-hover:text-primary"}),(0,o.jsx)("div",{className:"absolute inset-0 bg-primary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,o.jsxs)("span",{className:"ml-3 relative",children:["Education",(0,o.jsx)("div",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"})]})]}),r?(0,o.jsx)("div",{className:"space-y-4",children:[1,2].map(e=>(0,o.jsxs)("div",{className:"animate-pulse flex gap-4 p-3",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-muted rounded-full"}),(0,o.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,o.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"}),(0,o.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]})]},e))}):(0,o.jsxs)("ol",{className:"space-y-6",children:[c.map((e,t)=>(0,o.jsx)(u,{educationItem:e},`phd-${e.id}`)),i.map((e,t)=>(0,o.jsx)(u,{educationItem:e},`master-${e.id}`)),d.map((e,t)=>(0,o.jsx)(u,{educationItem:e},`bachelor-${e.id}`)),m.map((e,t)=>(0,o.jsx)(u,{educationItem:e},`other-${e.id}`))]})]})]})}},632:function(e,t,r){"use strict";r.d(t,{default:function(){return a}});var o=r(57437);function a(){return(0,o.jsxs)("div",{className:"group relative w-full overflow-hidden max-h-36 flex items-center justify-center py-1",children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,o.jsx)("div",{className:"absolute inset-0 border-t border-b border-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,o.jsx)("div",{className:"absolute left-4 top-1/2 w-1 h-1 bg-primary/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),(0,o.jsx)("div",{className:"absolute right-4 top-1/2 w-1 h-1 bg-secondary/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft",style:{animationDelay:"1s"}}),(0,o.jsxs)("div",{className:"dark:hidden w-full relative group/light",children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 rounded-lg blur opacity-0 group-hover/light:opacity-100 transition-opacity duration-500"}),(0,o.jsx)("img",{src:"/github-contribution-snake/github-contribution-grid-snake.svg",alt:"github-contribution",className:"relative w-full h-auto transition-all duration-500 group-hover:scale-[1.02] group-hover:brightness-110 group-hover:contrast-110"})]}),(0,o.jsxs)("div",{className:"hidden dark:block w-full relative group/dark",children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 rounded-lg blur opacity-0 group-hover/dark:opacity-100 transition-opacity duration-500"}),(0,o.jsx)("img",{src:"/github-contribution-snake/github-contribution-grid-snake-dark.svg",alt:"github-contribution",className:"relative w-full h-auto transition-all duration-500 group-hover:scale-[1.02] group-hover:brightness-110 group-hover:contrast-110"})]}),(0,o.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-foreground/90 text-background text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none",children:["GitHub Contribution Activity",(0,o.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-foreground/90"})]})]})}},41920:function(e,t,r){"use strict";r.d(t,{ProjectCard:function(){return f}});var o=r(57437);r(2265),r(44794),r(73644);var a=r(33073),s=r(76858),i=r(73278),n=r(33241),l=r(3616),d=r(47914),u=r(24721),c=r(27648),m=r(8682),p=r(18191),h=r(92331);let g={github:e=>(0,o.jsx)(i.b,{...e,size:14,weight:"duotone"}),npm:e=>(0,o.jsx)(n.M,{...e,size:14,weight:"duotone"}),pypi:e=>(0,o.jsx)(n.M,{...e,size:14,weight:"duotone"}),python:e=>(0,o.jsx)(p.CustomIcon,{...e,name:"python",size:14}),huggingface:()=>(0,o.jsx)("div",{className:"text-xs font-bold",children:"\uD83E\uDD17"}),kaggle:()=>(0,o.jsx)("div",{className:"text-xs font-bold",children:"K"}),docker:()=>(0,o.jsx)("div",{className:"text-xs font-bold",children:"\uD83D\uDC33"}),llm:()=>(0,o.jsx)("div",{className:"text-xs font-bold",children:"\uD83E\uDD16"}),knowledge_graph:()=>(0,o.jsx)("div",{className:"text-xs font-bold",children:"\uD83D\uDD0D"})},x=e=>(0,o.jsx)(a.Z,{...e,size:20,className:"text-muted-foreground"});function f({project:e,titleAs:t,className:r,style:a}){var n;let f="#",v=e=>e.startsWith("http://")||e.startsWith("https://")?e.replace(/^https?:\/\//,""):e;if(e.has_detail_page)f=`/projects/${e.slug}`;else if(e.link){let t="string"==typeof e.link?e.link:e.link.href;f=t.startsWith("http://")||t.startsWith("https://")?u.fI?`${t}?utm_source=${u.fI}`:t:u.fI?`https://${t}?utm_source=${u.fI}`:`https://${t}`}let b=!e.has_detail_page&&e.link,y=()=>"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium text-white",j=e=>{if(!e)return null;if(e.includes(":"))return(0,o.jsx)(p.CustomIcon,{name:e,size:14});let t=e.toLowerCase();return g[t]?g[t]({}):(0,o.jsx)(h.AntdIcon,{iconName:e,size:14})};return(0,o.jsxs)("li",{className:`group relative flex flex-col items-start h-full ${r||""}`,style:a,children:[(0,o.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-primary/10 via-secondary/5 to-primary/10 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse"}),(0,o.jsxs)("div",{className:"relative flex flex-col justify-between h-full w-full p-6 rounded-2xl border border-muted-foreground/20 shadow-sm bg-background/80 backdrop-blur-sm transition-all duration-500 ease-smooth group-hover:shadow-2xl group-hover:shadow-primary/10 group-hover:-translate-y-3 group-hover:scale-[1.03] group-hover:border-primary/30 group-hover:bg-gradient-to-br group-hover:from-background group-hover:to-primary/5 group-hover:rotate-1",children:[(0,o.jsx)("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,o.jsx)("div",{className:"absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"}),(0,o.jsx)("div",{className:"absolute bottom-6 left-4 w-1 h-1 bg-secondary/40 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft",style:{animationDelay:"0.5s"}}),(0,o.jsxs)("div",{className:"relative z-10",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center sm:justify-start items-start sm:items-center gap-4",children:[(0,o.jsxs)("div",{className:"relative flex h-12 w-12 items-center justify-center",children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-glow-pulse"}),(0,o.jsx)("div",{className:"relative flex h-12 w-12 items-center justify-center rounded-full bg-muted/30 border border-muted-foreground/10 backdrop-blur-sm transition-all duration-300 group-hover:bg-primary/10 group-hover:border-primary/20 group-hover:scale-110 group-hover:rotate-12",children:(0,o.jsx)("div",{className:"transition-all duration-300 group-hover:scale-110 group-hover:text-primary",children:(()=>{if(e.icon)return e.icon.includes(":")?(0,o.jsx)(p.CustomIcon,{name:e.icon,size:24}):(0,o.jsx)(h.AntdIcon,{iconName:e.icon,size:24});if(e.has_detail_page)return(0,o.jsx)(l.d,{size:24,weight:"duotone"});if("opensource"===e.type)return(0,o.jsx)(i.b,{size:24,weight:"duotone"});if(e.link?.href)try{return(0,o.jsx)(m.Favicon,{url:v("string"==typeof e.link?e.link:e.link.href)})}catch(e){}return(0,o.jsx)(x,{})})()})}),(0,o.jsx)("div",{className:"absolute inset-0 rounded-full border border-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft"})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(t??"h2",{className:"text-base font-semibold transition-colors duration-300 group-hover:text-primary",children:e.name}),(0,o.jsx)("div",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary transition-all duration-300 group-hover:w-full"})]})]}),(0,o.jsxs)("div",{className:"relative mt-4 ml-2",children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-muted/20 to-transparent rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,o.jsx)("p",{className:"relative text-sm text-muted-foreground h-[4.5rem] line-clamp-3 overflow-hidden text-ellipsis leading-relaxed transition-colors duration-300 group-hover:text-foreground/80",children:(n=e.description)?n:""})]})]}),(0,o.jsx)("div",{className:"relative z-10 mt-auto pt-4 ml-1",children:(0,o.jsxs)("div",{className:"mt-2 flex flex-wrap items-center gap-2",children:[e.category&&!e.categories&&(0,o.jsxs)("span",{className:y(),style:{backgroundColor:"#6b7280"},children:[(()=>{if(e.category_icon)return j(e.category_icon);if(e.categories&&e.categories.length>0){let t=e.categories[0];if(t.icon)return j(t.icon)}return e.category?j(e.category):null})(),(0,o.jsx)("span",{children:e.category})]}),e.categories&&e.categories.length>0&&e.categories.map((e,t)=>(0,o.jsxs)("span",{className:y(),style:{backgroundColor:e.color||"#6b7280"},children:[j(e.icon),(0,o.jsx)("span",{children:e.name})]},`cat-${t}`)),e.module_categories&&e.module_categories.length>0&&e.module_categories.map((e,t)=>!1!==e.is_active?(0,o.jsxs)("span",{className:y()+" !bg-gradient-to-r from-blue-500 to-indigo-600",children:[j(e.icon),(0,o.jsx)("span",{children:e.name})]},`module-${t}`):null)]})}),b?(0,o.jsx)("a",{href:f,target:"_blank",rel:"noopener noreferrer",className:"absolute inset-0 z-20",children:(0,o.jsx)(d.G,{size:32,weight:"duotone",className:"absolute top-4 right-4 h-4 w-4 group-hover:text-primary group-hover:cursor-pointer"})}):(0,o.jsx)(c.default,{href:f,className:"absolute inset-0 z-20",children:(0,o.jsx)(s.Z,{className:"absolute top-4 right-4 h-4 w-4 group-hover:text-primary group-hover:cursor-pointer"})})]})]})}},95512:function(e,t,r){"use strict";r.d(t,{GlowOnHover:function(){return n},RippleEffect:function(){return i},ShimmerEffect:function(){return l}});var o=r(57437),a=r(2265),s=r(93448);function i({children:e,className:t,color:r="rgba(255, 255, 255, 0.6)",duration:i=600}){let[n,l]=(0,a.useState)([]),d=(0,a.useRef)(0);return(0,o.jsxs)("div",{className:(0,s.cn)("relative overflow-hidden",t),onMouseDown:e=>{let t=e.currentTarget.getBoundingClientRect(),r={x:e.clientX-t.left,y:e.clientY-t.top,id:d.current++};l(e=>[...e,r]),setTimeout(()=>{l(e=>e.filter(e=>e.id!==r.id))},i)},children:[e,n.map(e=>(0,o.jsx)("span",{className:"absolute pointer-events-none animate-ping",style:{left:e.x-10,top:e.y-10,width:20,height:20,borderRadius:"50%",backgroundColor:r,animationDuration:`${i}ms`}},e.id))]})}function n({children:e,className:t,glowColor:r="hsl(var(--primary))",intensity:a="medium"}){return(0,o.jsx)("div",{className:(0,s.cn)("transition-all duration-300 hover:scale-105",t),style:{"--glow-color":r,"--glow-intensity":{low:"0 0 10px",medium:"0 0 20px",high:"0 0 30px"}[a]},onMouseEnter:e=>{e.currentTarget.style.boxShadow="var(--glow-intensity) var(--glow-color)"},onMouseLeave:e=>{e.currentTarget.style.boxShadow="none"},children:e})}function l({children:e,className:t,direction:r="left-to-right",duration:a=1500}){return(0,o.jsxs)("div",{className:(0,s.cn)("relative overflow-hidden group",t),children:[e,(0,o.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:`linear-gradient(${"left-to-right"===r?"to right":"to bottom"}, transparent, rgba(255,255,255,0.4), transparent)`,animation:`shimmer ${a}ms ease-in-out infinite`}})]})}let d=`
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(var(--float-amplitude, -10px)); }
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  
  .animate-float {
    animation: float var(--float-duration, 3s) ease-in-out infinite;
    animation-delay: var(--float-delay, 0s);
  }
`;if("undefined"!=typeof document){let e=document.createElement("style");e.textContent=d,document.head.appendChild(e)}}},function(e){e.O(0,[105,216,592,744],function(){return e(e.s=33112)}),_N_E=e.O()}]);