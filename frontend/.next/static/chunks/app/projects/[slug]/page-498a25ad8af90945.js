(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[101],{8462:function(e,r,t){Promise.resolve().then(t.bind(t,98150)),Promise.resolve().then(t.bind(t,79231))},98150:function(e,r,t){"use strict";t.d(r,{BlogContent:function(){return l}});var a=t(57437),o=t(2265),i=t(6603);let n=e=>{e.querySelectorAll("[data-icon-placeholder]").forEach(e=>{let r=e.getAttribute("data-src"),t=e.getAttribute("data-alt");if(r){let a=document.createElement("img");a.src=r,a.alt=t||"Icon",a.className="markdown-icon",a.style.display="inline-block",a.style.verticalAlign="middle",a.loading="lazy",e.parentNode?.replaceChild(a,e)}})};function s({src:e,alt:r,onClose:t}){return(0,o.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t]),(0,a.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 animate-fade-in",style:{backdropFilter:"blur(10px)"},onClick:t,children:(0,a.jsx)("img",{src:e,alt:r,className:"max-w-full max-h-full object-contain rounded-lg shadow-2xl animate-scale-in",onClick:e=>e.stopPropagation()})})}function l({content:e,className:r=""}){let l=(0,o.useRef)(null),[d,c]=(0,o.useState)(null);return(0,o.useEffect)(()=>{if(!l.current)return;let e=e=>{e.style.columnCount="1",e.style.columns="1",e.style.columnFill="auto",e.style.columnGap="0",e.style.columnRule="none",e.style.columnSpan="none",e.style.columnWidth="auto",e.style.webkitColumnCount="1",e.style.mozColumnCount="1"};e(l.current),l.current.querySelectorAll("*").forEach(r=>{r instanceof HTMLElement&&e(r)}),l.current.querySelectorAll('[data-code-block="true"]').forEach(e=>{let r=e.getAttribute("data-language")||"text",a=e.textContent||"",n=document.createElement("div");e.parentNode?.replaceChild(n,e),Promise.resolve().then(t.t.bind(t,34040,19)).then(({createRoot:e})=>{e(n).render(o.createElement(i.d,{language:r,children:a}))})}),l.current.querySelectorAll("code.inline-code").forEach(e=>{e.className="px-1.5 py-0.5 bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 rounded text-sm font-mono"});let r=l.current,a=(e,r)=>e.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g,"").replace(/\s+/g,"-").trim()||`heading-${r}`;r.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach((e,r)=>{switch(e.tagName.toLowerCase()){case"h1":if(e.className="text-4xl font-bold mb-6 mt-12 text-foreground tracking-tight leading-tight scroll-mt-20 relative group",!e.querySelector(".heading-underline")){let r=document.createElement("div");r.className="absolute -bottom-2 left-0 w-16 h-1 bg-gradient-to-r from-primary to-primary/50 rounded-full heading-underline",e.appendChild(r)}break;case"h2":e.className="text-3xl font-bold mb-4 mt-10 text-foreground tracking-tight leading-tight scroll-mt-20 relative group border-l-4 border-primary/30 pl-4 hover:border-primary/60 transition-colors duration-300";break;case"h3":if(e.className="text-2xl font-semibold mb-3 mt-8 text-foreground/90 tracking-tight leading-tight scroll-mt-20 relative group",!e.querySelector(".heading-dot")){let r=document.createElement("div");r.className="absolute -left-4 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-primary/60 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 heading-dot",e.appendChild(r)}break;case"h4":e.className="text-xl font-semibold mb-3 mt-6 text-foreground/80 tracking-tight leading-tight scroll-mt-20 relative group";break;case"h5":e.className="text-lg font-medium mb-2 mt-5 text-foreground/70 tracking-tight leading-tight scroll-mt-20 relative group";break;case"h6":e.className="text-base font-medium mb-2 mt-4 text-foreground/60 tracking-tight leading-tight scroll-mt-20 relative group uppercase text-xs letter-spacing-wide"}e.id||(e.id=a(e.textContent||"",r))}),r.querySelectorAll("p").forEach(e=>{e.className="mb-8 leading-9 text-foreground/85 text-lg tracking-wide hover:text-foreground transition-colors duration-300 relative group",e.style.paddingLeft="1rem",e.style.paddingRight="1rem",e.style.paddingTop="0.5rem",e.style.paddingBottom="0.5rem",e.style.borderRadius="0.5rem",e.style.transition="all 0.3s ease";let r=document.createElement("div");r.className="absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none",e.style.position="relative",e.insertBefore(r,e.firstChild)}),r.querySelectorAll("ul").forEach(e=>{e.className="list-none mb-8 space-y-4 text-foreground/85 pl-6",e.querySelectorAll("li").forEach((e,r)=>{e.className="relative pl-8 py-2 hover:text-foreground transition-colors duration-300 group";let t=document.createElement("div");t.className="absolute left-0 top-3 w-2 h-2 rounded-full bg-primary/60 group-hover:bg-primary group-hover:scale-125 transition-all duration-300",e.insertBefore(t,e.firstChild);let a=document.createElement("div");a.className="absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none",e.style.position="relative",e.insertBefore(a,e.firstChild)})}),r.querySelectorAll("ol").forEach(e=>{e.className="list-none mb-8 space-y-4 text-foreground/85 pl-6 counter-reset-list",e.querySelectorAll("li").forEach((e,r)=>{e.className="relative pl-12 py-2 hover:text-foreground transition-colors duration-300 group",e.style.counterIncrement="list-item";let t=document.createElement("div");t.className="absolute left-0 top-2 w-8 h-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-sm font-semibold text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300",t.textContent=(r+1).toString(),e.insertBefore(t,e.firstChild);let a=document.createElement("div");a.className="absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none",e.style.position="relative",e.insertBefore(a,e.firstChild)})}),r.querySelectorAll("blockquote").forEach(e=>{e.className="relative pl-8 pr-6 py-6 my-8 bg-gradient-to-r from-primary/8 via-primary/5 to-transparent border-l-4 border-primary rounded-r-2xl italic text-foreground/80 text-lg leading-8 hover:from-primary/12 hover:via-primary/8 hover:to-primary/5 transition-all duration-500 group overflow-hidden";let r=document.createElement("div");r.className="absolute top-4 left-2 text-4xl text-primary/30 font-serif leading-none",r.textContent='"',e.insertBefore(r,e.firstChild);let t=document.createElement("div");t.className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-primary/50 via-primary/20 to-transparent rounded-l-full",e.appendChild(t);let a=document.createElement("div");a.className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500",e.insertBefore(a,e.firstChild)}),r.querySelectorAll("a").forEach(e=>{if(e.className="text-primary hover:text-primary/80 underline decoration-primary/30 underline-offset-4 hover:decoration-primary/60 transition-all duration-300 font-medium relative group hover:scale-105 hover:-translate-y-0.5",e.style.transform="perspective(100px) translateZ(0)",e.style.transformStyle="preserve-3d",e.getAttribute("href")?.startsWith("http")){e.setAttribute("target","_blank"),e.setAttribute("rel","noopener noreferrer");let r=document.createElement("span");r.className="inline-block ml-1 opacity-60 group-hover:opacity-100 group-hover:scale-110 transition-all duration-300",r.innerHTML="↗",e.appendChild(r)}let r=document.createElement("div");r.className="absolute inset-0 bg-primary/10 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none -z-10",e.style.position="relative",e.insertBefore(r,e.firstChild)}),r.querySelectorAll("strong").forEach(e=>e.className="font-bold text-foreground bg-gradient-to-r from-primary/10 to-primary/5 px-1 py-0.5 rounded"),r.querySelectorAll("em").forEach(e=>e.className="italic text-foreground/90 font-medium"),r.querySelectorAll("hr").forEach(e=>e.className="my-8 border-t border-zinc-200 dark:border-zinc-700"),r.querySelectorAll("img").forEach((e,r)=>{let t=e.src,a=document.createElement("div");a.className="relative my-8 group cursor-pointer overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500";let o=document.createElement("div");o.className="flex items-center justify-center bg-muted/30 animate-pulse min-h-[200px] w-full rounded-lg",o.innerHTML=`
        <div class="flex flex-col items-center gap-2 text-muted-foreground">
          <div class="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <span class="text-sm">Loading image...</span>
        </div>
      `,e.parentNode?.insertBefore(a,e),a.appendChild(o),a.appendChild(e),e.style.display="none",e.className="w-full h-auto transition-all duration-500 group-hover:scale-105";let i=document.createElement("div");i.className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",i.innerHTML=`
        <div class="bg-white/90 dark:bg-black/90 rounded-full p-3 backdrop-blur-sm">
          <svg class="w-6 h-6 text-gray-800 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
          </svg>
        </div>
      `,a.appendChild(i),a.addEventListener("click",()=>{c({src:t,alt:e.alt||`Image ${r+1}`})}),(()=>{if(r<2)e.onload=()=>{o.style.display="none",e.style.display="block",e.classList.add("animate-fade-in-up")},e.onerror=()=>{o.innerHTML=`
              <div class="flex flex-col items-center gap-2 text-muted-foreground">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <span class="text-sm">Failed to load image</span>
              </div>
            `};else{let r=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(e.onload=()=>{o.style.display="none",e.style.display="block",e.classList.add("animate-fade-in-up")},e.onerror=()=>{o.innerHTML=`
                    <div class="flex flex-col items-center gap-2 text-muted-foreground">
                      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                      </svg>
                      <span class="text-sm">Failed to load image</span>
                    </div>
                  `},r.unobserve(t.target))})},{rootMargin:"50px"});r.observe(a)}})()}),n(r),r.querySelectorAll("table").forEach(e=>{e.className="table-auto border-collapse border border-zinc-300 dark:border-zinc-600 w-full my-6 rounded-lg overflow-hidden",e.querySelectorAll("th").forEach(e=>e.className="border border-zinc-300 dark:border-zinc-600 px-4 py-2 bg-zinc-100 dark:bg-zinc-800 font-semibold text-zinc-900 dark:text-zinc-100"),e.querySelectorAll("td").forEach(e=>e.className="border border-zinc-300 dark:border-zinc-600 px-4 py-2 text-zinc-700 dark:text-zinc-300")})},[e]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{ref:l,className:`blog-content-container prose prose-zinc dark:prose-invert max-w-none ${r}`,style:{columnCount:"unset",columns:"unset",columnFill:"unset",columnGap:"unset"},dangerouslySetInnerHTML:{__html:e}}),d&&(0,a.jsx)(s,{src:d.src,alt:d.alt,onClose:()=>c(null)})]})}},79231:function(e,r,t){"use strict";t.d(r,{ProjectLayout:function(){return k}});var a=t(57437),o=t(2265),i=t(99376),n=t(46021),s=t(90648),l=t(78394),d=t(45539),c=t(29901),m=t(83275),u=t(94258),p=t(57741);t(58608);var h=t(86595),g=t(71769),x=t(31047),f=t(92369),b=t(96362),v=t(64935);function y(e){return(0,a.jsx)("svg",{viewBox:"0 0 16 16",fill:"none","aria-hidden":"true",...e,children:(0,a.jsx)("path",{d:"M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}function k({project:e,children:r}){let t=(0,i.useRouter)(),{previousPathname:k}=(0,o.useContext)(n.I),[N,w]=o.useState(""),j=o.useCallback(()=>{if(k){let e=k.includes("/projects")||"/"===k;t.back(),e&&setTimeout(()=>{t.refresh()},150)}else t.push("/projects")},[t,k]);return o.useEffect(()=>{let t="";o.isValidElement(r)&&r.props?.content?t=r.props.content:e.content?t=e.content:e.detail_content&&(t=e.detail_content),w(t)},[r,e]),o.useEffect(()=>{let e=setTimeout(()=>{let e=new CustomEvent("tocRefresh");window.dispatchEvent(e)},1e3);return()=>clearTimeout(e)},[N]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.CK,{}),(0,a.jsx)(s.W2,{className:"mt-16 lg:mt-32",children:(0,a.jsxs)("div",{className:"xl:relative",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("button",{onClick:j,className:"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20","aria-label":"返回项目列表",children:(0,a.jsx)(y,{className:"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400"})})}),(0,a.jsxs)("article",{className:"animate-fade-in-up",children:[(0,a.jsxs)("header",{className:"relative mb-16 p-10 rounded-3xl bg-gradient-to-r from-background/85 via-background/95 to-background/85 border border-border/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl hover:shadow-primary/10 transition-all duration-700 group/header overflow-hidden",style:{transform:"perspective(1000px) translateZ(0)",transformStyle:"preserve-3d",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700"}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("div",{className:"absolute top-0 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent opacity-60"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-primary/30 rounded-tl-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-primary/30 rounded-tr-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),(0,a.jsxs)("div",{className:"relative z-10 space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[e.is_featured&&(0,a.jsxs)("span",{className:"relative inline-flex items-center gap-1.5 px-4 py-2 text-xs font-bold bg-gradient-to-r from-amber-500/15 to-orange-500/15 border-2 border-amber-500/30 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 hover:shadow-lg hover:shadow-amber-500/25 transition-all duration-300 cursor-default overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-500/20 to-orange-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)(h.Z,{className:"w-3 h-3 fill-current animate-pulse-soft relative z-10"}),(0,a.jsx)("span",{className:"relative z-10 tracking-wide",children:"FEATURED"})]}),e.status&&(0,a.jsxs)("span",{className:"relative inline-flex items-center px-4 py-2 text-xs font-bold bg-gradient-to-r from-green-500/15 to-emerald-500/15 border-2 border-green-500/30 rounded-full text-green-700 dark:text-green-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 cursor-default overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("span",{className:"relative z-10 tracking-wide uppercase",children:e.status})]})]}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md",style:{transform:"translateZ(20px)",transformStyle:"preserve-3d",fontFamily:'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif'},children:e.name}),e.description&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,a.jsx)("div",{className:"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm",children:[(0,a.jsx)(g.Z,{className:"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300"}),(0,a.jsx)("span",{className:"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide",children:"ABSTRACT"})]}),(0,a.jsx)("div",{className:"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1"})]}),(0,a.jsxs)("div",{className:"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5",children:[(0,a.jsx)("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"absolute top-6 right-6 w-3 h-3 bg-primary/20 rounded-full blur-sm animate-pulse-soft"}),(0,a.jsx)("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-secondary/30 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"1s"}}),(0,a.jsx)("div",{className:"relative z-10",children:(0,a.jsx)("p",{className:"text-lg sm:text-xl leading-relaxed text-muted-foreground group-hover/header:text-foreground transition-colors duration-300 font-medium",style:{fontFamily:'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',fontStyle:"italic"},children:e.description})})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-8 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-lg bg-primary/10 text-primary group-hover/meta:bg-primary/20 group-hover/meta:scale-110 transition-all duration-300",children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})}),(0,a.jsx)("time",{dateTime:e.display_date||e.created_at||e.updated_at,className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:(0,p.p)(e.display_date||e.created_at||e.updated_at)})]}),e.author&&(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-lg bg-blue-500/10 text-blue-500 group-hover/meta:bg-blue-500/20 group-hover/meta:scale-110 transition-all duration-300",children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})}),(0,a.jsx)("span",{className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:e.author})]}),e.module_categories&&e.module_categories.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-lg bg-green-500/10 text-green-500 group-hover/meta:bg-green-500/20 group-hover/meta:scale-110 transition-all duration-300",children:(0,a.jsx)(g.Z,{className:"w-4 h-4"})}),(0,a.jsx)("span",{className:"group-hover/meta:text-primary transition-colors duration-300 font-medium",children:e.module_categories[0].name})]}),(0,a.jsx)(u.CommentStats,{path:`/projects/${e.slug}`,showIcons:!0})]}),(e.github_url||e.demo_url||e.link)&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[e.github_url&&(0,a.jsxs)("a",{href:e.github_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 group/link",children:[(0,a.jsx)("svg",{className:"w-4 h-4 group-hover/link:scale-110 transition-transform duration-300",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})}),"GitHub"]}),e.demo_url&&(0,a.jsxs)("a",{href:e.demo_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-primary to-primary/90 rounded-xl hover:shadow-lg hover:shadow-primary/25 hover:scale-105 transition-all duration-300 group/link",children:[(0,a.jsx)("svg",{className:"w-4 h-4 group-hover/link:scale-110 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})}),"Live Demo"]}),e.link&&(0,a.jsxs)("a",{href:"string"==typeof e.link?e.link:e.link.href,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 group/link",children:[(0,a.jsx)(b.Z,{className:"w-4 h-4 group-hover/link:scale-110 transition-transform duration-300"}),"Visit Project"]})]}),e.tech_stack&&e.tech_stack.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tech_stack.map((e,r)=>(0,a.jsxs)("span",{className:"group/tag relative inline-flex items-center px-3 py-1.5 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 transition-all duration-300 cursor-pointer overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm bg-primary"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500"}),(0,a.jsx)("span",{className:"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300",children:e.name||e}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200"})]},r))})]})]}),(0,a.jsx)(l.M,{className:"mt-8","data-mdx-content":!0,children:r}),(0,a.jsx)("section",{className:"mt-20 mb-12",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"}),(0,a.jsx)("div",{className:"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent"}),(0,a.jsxs)("div",{className:"pt-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"p-2 rounded-xl bg-primary/10 text-primary",children:(0,a.jsx)(v.Z,{className:"w-5 h-5"})}),(0,a.jsx)("span",{className:"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase",children:"Project Discussion"})]}),(0,a.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3",children:"Share Your Experience"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed",children:"Questions, suggestions, or just want to share how you're using this project? Let's connect."})]}),(0,a.jsx)(m.WalineComment,{path:`/projects/${e.slug}`,title:e.name,className:"max-w-5xl mx-auto"})]})]})})]})]}),(0,a.jsx)("div",{className:"hidden 2xl:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto",children:(0,a.jsx)(c.r,{content:N})}),(0,a.jsx)("div",{className:"hidden xl:block 2xl:hidden fixed right-2 top-1/2 transform -translate-y-1/2 w-72 z-40 pointer-events-auto",children:(0,a.jsx)(c.r,{content:N})}),(0,a.jsx)("div",{className:"xl:hidden",children:(0,a.jsx)(c.r,{content:N})})]})})]})}}},function(e){e.O(0,[105,216,592,744],function(){return e(e.s=8462)}),_N_E=e.O()}]);