(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[895],{37799:function(e,r,t){Promise.resolve().then(t.bind(t,81523)),Promise.resolve().then(t.bind(t,70049)),Promise.resolve().then(t.bind(t,54394)),Promise.resolve().then(t.bind(t,80089))},54394:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return V}});var a=t(57437),s=t(2265),o=t(90648),i=t(36629),l=t(93448),n=t(56475),d=t(41473),c=t(73247),m=t(99397),u=t(21047),p=t(40875),x=t(22135),h=t(32489),g=t(64935),b=t(86595),f=t(3414),v=t(42208),y=t(11239),j=t(31047),w=t(96362),N=t(75135),k=t(33145),_=t(92331);function C({project:e,isOpen:r,onClose:t,onNext:o,onPrev:i}){let[l,n]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{if(!r)return;let e=e=>{switch(e.key){case"Escape":t();break;case"ArrowRight":o?.();break;case"ArrowLeft":i?.()}};return document.addEventListener("keydown",e),document.body.style.overflow="hidden",()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[r,t,o,i]),!r||!e)return null;let d=()=>{if(e.has_detail_page)return`/projects/${e.slug}`;if(e.link){let r="string"==typeof e.link?e.link:e.link.href;return r.startsWith("http")?r:`https://${r}`}return"#"},c=!e.has_detail_page&&e.link;return(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:t}),(0,a.jsxs)("div",{className:"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-background/95 backdrop-blur-md rounded-3xl border border-border/50 shadow-2xl overflow-hidden animate-fade-in-up",children:[(0,a.jsx)("button",{onClick:t,className:"absolute top-4 right-4 z-10 w-10 h-10 bg-background/80 hover:bg-background rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm border border-border/50",children:(0,a.jsx)(h.Z,{size:20})}),i&&(0,a.jsx)("button",{onClick:i,className:"absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-background/80 hover:bg-background rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm border border-border/50",children:"←"}),o&&(0,a.jsx)("button",{onClick:o,className:"absolute right-16 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-background/80 hover:bg-background rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm border border-border/50",children:"→"}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row h-full max-h-[90vh]",children:[(0,a.jsx)("div",{className:"lg:w-1/2 relative bg-gradient-to-br from-primary/5 to-secondary/5 flex items-center justify-center min-h-[300px] lg:min-h-[500px]",children:e.preview_image?(0,a.jsxs)("div",{className:"relative w-full h-full",children:[(0,a.jsx)(k.default,{src:e.preview_image,alt:e.name,fill:!0,className:`object-cover transition-opacity duration-300 ${l?"opacity-100":"opacity-0"}`,onLoad:()=>n(!0)}),!l&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"})})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mb-4",children:e.icon?(0,a.jsx)(_.AntdIcon,{iconName:e.icon,size:48}):(0,a.jsx)(g.Z,{size:48})}),(0,a.jsx)("p",{className:"text-sm",children:"No preview available"})]})}),(0,a.jsx)("div",{className:"lg:w-1/2 p-8 overflow-y-auto",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center border border-primary/20",children:e.icon?(0,a.jsx)(_.AntdIcon,{iconName:e.icon,size:32}):(0,a.jsx)(g.Z,{size:32,className:"text-primary"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-foreground mb-2",children:e.name}),e.stats&&(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.stats.stars&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(b.Z,{size:14}),(0,a.jsx)("span",{children:e.stats.stars})]}),e.stats.forks&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(f.Z,{size:14}),(0,a.jsx)("span",{children:e.stats.forks})]}),e.stats.views&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(v.Z,{size:14}),(0,a.jsx)("span",{children:e.stats.views})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[(0,a.jsx)(y.Z,{size:18,className:"text-primary"}),"Description"]}),(0,a.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e.description})]}),e.tech_stack&&e.tech_stack.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[(0,a.jsx)(g.Z,{size:18,className:"text-primary"}),"Tech Stack"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tech_stack.map((e,r)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20",children:e},r))})]}),e.categories&&e.categories.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Categories"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.categories.map((e,r)=>(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium border",style:{backgroundColor:e.color?`${e.color}20`:"hsl(var(--muted))",borderColor:e.color?`${e.color}40`:"hsl(var(--border))",color:e.color||"hsl(var(--foreground))"},children:e.name},r))})]}),(e.created_date||e.last_updated)&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[(0,a.jsx)(j.Z,{size:18,className:"text-primary"}),"Timeline"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[e.created_date&&(0,a.jsxs)("div",{children:["Created: ",new Date(e.created_date).toLocaleDateString()]}),e.last_updated&&(0,a.jsxs)("div",{children:["Last updated: ",new Date(e.last_updated).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[c?(0,a.jsxs)("a",{href:d(),target:"_blank",rel:"noopener noreferrer",className:"flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-xl font-medium hover:bg-primary/90 transition-colors duration-200 text-center flex items-center justify-center gap-2",children:["View Project",(0,a.jsx)(w.Z,{size:16})]}):(0,a.jsx)("a",{href:d(),className:"flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-xl font-medium hover:bg-primary/90 transition-colors duration-200 text-center",children:"View Details"}),e.link&&"object"==typeof e.link&&e.link.href?.includes("github")&&(0,a.jsxs)("a",{href:e.link.href,target:"_blank",rel:"noopener noreferrer",className:"px-6 py-3 border border-border/50 rounded-xl font-medium hover:bg-background/80 hover:border-primary/30 transition-all duration-300 backdrop-blur-sm flex items-center justify-center gap-2",children:[(0,a.jsx)(N.Z,{size:16}),"GitHub"]})]})]})})]})]})]})}let z={ai:{name:"AI/Machine Learning",colors:{primary:"#667eea",secondary:"#764ba2",accent:"#f093fb",gradient:"from-purple-500/20 via-blue-500/10 to-indigo-500/20",glow:"shadow-purple-500/25",border:"border-purple-500/30"},keywords:["ai","machine learning","ml","neural","deep learning","tensorflow","pytorch","nlp","computer vision"]},web:{name:"Web Development",colors:{primary:"#4facfe",secondary:"#00f2fe",accent:"#43e97b",gradient:"from-blue-500/20 via-cyan-500/10 to-teal-500/20",glow:"shadow-blue-500/25",border:"border-blue-500/30"},keywords:["web","frontend","react","vue","angular","javascript","typescript","html","css","nextjs"]},backend:{name:"Backend Development",colors:{primary:"#43e97b",secondary:"#38f9d7",accent:"#4facfe",gradient:"from-green-500/20 via-teal-500/10 to-emerald-500/20",glow:"shadow-green-500/25",border:"border-green-500/30"},keywords:["backend","api","server","database","node","python","java","go","rust","php"]},mobile:{name:"Mobile Development",colors:{primary:"#fa709a",secondary:"#fee140",accent:"#667eea",gradient:"from-pink-500/20 via-yellow-500/10 to-orange-500/20",glow:"shadow-pink-500/25",border:"border-pink-500/30"},keywords:["mobile","ios","android","react native","flutter","swift","kotlin","xamarin"]},devops:{name:"DevOps & Infrastructure",colors:{primary:"#f093fb",secondary:"#f5576c",accent:"#4facfe",gradient:"from-purple-500/20 via-pink-500/10 to-red-500/20",glow:"shadow-purple-500/25",border:"border-purple-500/30"},keywords:["devops","docker","kubernetes","aws","azure","gcp","ci/cd","terraform","ansible"]},data:{name:"Data Science",colors:{primary:"#4facfe",secondary:"#43e97b",accent:"#f093fb",gradient:"from-blue-500/20 via-green-500/10 to-purple-500/20",glow:"shadow-blue-500/25",border:"border-blue-500/30"},keywords:["data","analytics","visualization","pandas","numpy","jupyter","r","statistics","big data"]},game:{name:"Game Development",colors:{primary:"#667eea",secondary:"#f093fb",accent:"#43e97b",gradient:"from-indigo-500/20 via-purple-500/10 to-pink-500/20",glow:"shadow-indigo-500/25",border:"border-indigo-500/30"},keywords:["game","unity","unreal","godot","c#","c++","3d","graphics","shader"]},blockchain:{name:"Blockchain & Web3",colors:{primary:"#f5576c",secondary:"#f093fb",accent:"#4facfe",gradient:"from-red-500/20 via-pink-500/10 to-purple-500/20",glow:"shadow-red-500/25",border:"border-red-500/30"},keywords:["blockchain","web3","ethereum","solidity","smart contract","defi","nft","crypto"]},default:{name:"General",colors:{primary:"hsl(var(--primary))",secondary:"hsl(var(--secondary))",accent:"hsl(var(--accent))",gradient:"from-primary/20 via-secondary/10 to-accent/20",glow:"shadow-primary/25",border:"border-primary/30"},keywords:[]}},S=(0,s.createContext)(void 0);function E({children:e}){let r=(0,s.useMemo)(()=>({getThemeForProject:e=>{let r=e.categories||e.module_categories||[],t=(e.description||"").toLowerCase(),a=(e.name||"").toLowerCase(),s=e.tech_stack||[],o=[...r.map(e=>e.name.toLowerCase()),t,a,...s.map(e=>e.toLowerCase())].join(" ");for(let[e,r]of Object.entries(z))if("default"!==e&&r.keywords.reduce((e,r)=>e+(o.includes(r)?1:0),0)>0)return r;return z.default},getThemeByType:e=>z[e]||z.default,getAllThemes:()=>z}),[]);return(0,a.jsx)(S.Provider,{value:r,children:e})}function M(){let e=(0,s.useContext)(S);if(void 0===e)throw Error("useProjectTheme must be used within a ProjectThemeProvider");return e}var $=t(90740),L=t(70525),Z=t(62720);function D({projects:e,categories:r,onFilterChange:t,className:o=""}){let[i,l]=(0,s.useState)(!1),[n,d]=(0,s.useState)({search:"",selectedCategories:[],selectedTechStack:[],sortBy:"name",sortOrder:"asc",showOnlyFeatured:!1}),{getAllThemes:m}=M();m();let u=(0,s.useMemo)(()=>{let r=new Set;return e.forEach(e=>{e.tech_stack?.forEach(e=>r.add(e)),e.categories?.forEach(e=>r.add(e.name))}),Array.from(r).sort()},[e]),p=(0,s.useMemo)(()=>{let r=e.filter(e=>{if(n.search){let r=n.search.toLowerCase();if(!(e.name.toLowerCase().includes(r)||e.description?.toLowerCase().includes(r)||e.tech_stack?.some(e=>e.toLowerCase().includes(r))||e.categories?.some(e=>e.name.toLowerCase().includes(r))))return!1}if(n.selectedCategories.length>0){let r=e.categories?.map(e=>e.name)||[];if(!n.selectedCategories.some(e=>r.includes(e)))return!1}if(n.selectedTechStack.length>0){let r=e.tech_stack||[];if(!n.selectedTechStack.some(e=>r.includes(e)))return!1}return!n.showOnlyFeatured||!!e.featured});return r.sort((e,r)=>{let t=0;switch(n.sortBy){case"name":t=e.name.localeCompare(r.name);break;case"date":let a=new Date(e.created_date||e.updated_at||0),s=new Date(r.created_date||r.updated_at||0);t=a.getTime()-s.getTime();break;case"popularity":t=(e.stats?.stars||0)+(e.stats?.views||0)-((r.stats?.stars||0)+(r.stats?.views||0));break;case"category":let o=e.categories?.[0]?.name||"",i=r.categories?.[0]?.name||"";t=o.localeCompare(i)}return"desc"===n.sortOrder?-t:t}),r},[e,n]);(0,s.useEffect)(()=>{t(p)},[p,t]);let x=(e,r)=>{d(t=>({...t,[e]:r}))},f=e=>{d(r=>({...r,selectedCategories:r.selectedCategories.includes(e)?r.selectedCategories.filter(r=>r!==e):[...r.selectedCategories,e]}))},v=e=>{d(r=>({...r,selectedTechStack:r.selectedTechStack.includes(e)?r.selectedTechStack.filter(r=>r!==e):[...r.selectedTechStack,e]}))},y=(n.search?1:0)+n.selectedCategories.length+n.selectedTechStack.length+(n.showOnlyFeatured?1:0);return(0,a.jsxs)("div",{className:`relative ${o}`,children:[(0,a.jsx)("div",{className:"relative mb-6 z-[1000]",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",size:20}),(0,a.jsx)("input",{type:"text",placeholder:"Search projects...",value:n.search,onChange:e=>x("search",e.target.value),className:"w-full pl-10 pr-12 py-3 bg-background/60 border border-border/50 rounded-xl backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-300"}),(0,a.jsxs)("button",{onClick:()=>l(!i),className:`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg transition-all duration-300 ${i?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,children:[(0,a.jsx)($.Z,{size:18}),y>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 w-4 h-4 bg-primary text-primary-foreground text-xs rounded-full flex items-center justify-center",children:y})]})]})}),i&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 z-[99999] mt-2 p-6 bg-background backdrop-blur-lg border border-border/50 rounded-2xl shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25)] animate-fade-in-up",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center gap-2",children:[(0,a.jsx)($.Z,{size:20,className:"text-primary"}),"Advanced Filters"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[y>0&&(0,a.jsx)("button",{onClick:()=>{d({search:"",selectedCategories:[],selectedTechStack:[],sortBy:"name",sortOrder:"asc",showOnlyFeatured:!1})},className:"text-sm text-muted-foreground hover:text-foreground transition-colors duration-200",children:"Clear All"}),(0,a.jsx)("button",{onClick:()=>l(!1),className:"p-1 text-muted-foreground hover:text-foreground transition-colors duration-200",children:(0,a.jsx)(h.Z,{size:18})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[(0,a.jsx)(L.Z,{size:16,className:"text-primary"}),"Sort By"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("select",{value:n.sortBy,onChange:e=>x("sortBy",e.target.value),className:"px-3 py-2 bg-background/60 border border-border/50 rounded-lg backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50",children:[(0,a.jsx)("option",{value:"name",children:"Name"}),(0,a.jsx)("option",{value:"date",children:"Date"}),(0,a.jsx)("option",{value:"popularity",children:"Popularity"}),(0,a.jsx)("option",{value:"category",children:"Category"})]}),(0,a.jsxs)("select",{value:n.sortOrder,onChange:e=>x("sortOrder",e.target.value),className:"px-3 py-2 bg-background/60 border border-border/50 rounded-lg backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50",children:[(0,a.jsx)("option",{value:"asc",children:"Ascending"}),(0,a.jsx)("option",{value:"desc",children:"Descending"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[(0,a.jsx)(Z.Z,{size:16,className:"text-primary"}),"Categories"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:r.map(e=>(0,a.jsxs)("button",{onClick:()=>f(e.categoryName),className:`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${n.selectedCategories.includes(e.categoryName)?"bg-primary text-primary-foreground shadow-lg":"bg-background/60 border border-border/50 hover:bg-primary/10 hover:border-primary/30"}`,children:[e.categoryName,(0,a.jsx)("span",{className:"ml-2 text-xs opacity-70",children:e.projects.length})]},e.categoryName))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[(0,a.jsx)(g.Z,{size:16,className:"text-primary"}),"Tech Stack"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:u.slice(0,20).map(e=>(0,a.jsx)("button",{onClick:()=>v(e),className:`px-2 py-1 rounded-md text-xs font-medium transition-all duration-300 ${n.selectedTechStack.includes(e)?"bg-secondary text-secondary-foreground":"bg-muted/50 hover:bg-muted"}`,children:e},e))})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:n.showOnlyFeatured,onChange:e=>x("showOnlyFeatured",e.target.checked),className:"w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary/50"}),(0,a.jsxs)("span",{className:"text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)(b.Z,{size:16,className:"text-primary"}),"Show only featured projects"]})]})}),(0,a.jsx)("div",{className:"pt-4 border-t border-border/50",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",p.length," of ",e.length," projects"]})})]})})]})}var T=t(13781);function I({projects:e,className:r=""}){let{getAllThemes:t}=M();t();let[o,i]=(0,s.useState)(null),[l,c]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async()=>{try{let e=await fetch("http://**************:8000/api/blogs/projects/stats");if(e.ok){let r=await e.json();i(r)}else i(m())}catch(e){i(m())}finally{c(!1)}})()},[e]);let m=()=>{let r=e.length,t=e.filter(e=>e.featured).length,a=e.filter(e=>e.is_open_source||e.github_link).length,s=new Set;return e.forEach(e=>{e.tag_objects?e.tag_objects.forEach(e=>s.add(e.name)):e.tags&&e.tags.forEach(e=>s.add(e))}),{total_projects:r,featured_projects:t,open_source_projects:a,category_count:s.size}},u=o||m(),p=[{title:"Total Projects",value:u.total_projects,icon:(0,a.jsx)(n.Z,{size:24}),color:"text-blue-500",description:"All projects in portfolio"},{title:"Featured Projects",value:u.featured_projects,icon:(0,a.jsx)(b.Z,{size:24}),color:"text-yellow-500",description:"Highlighted showcase projects"},{title:"Open Source",value:u.open_source_projects,icon:(0,a.jsx)(T.Z,{size:24}),color:"text-green-500",description:"Open source contributions"},{title:"Categories",value:u.category_count,icon:(0,a.jsx)(d.Z,{size:24}),color:"text-purple-500",description:"Project categories covered"}];return l?(0,a.jsx)("div",{className:`space-y-8 ${r}`,children:(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsx)("div",{className:"w-12 h-12 bg-muted rounded-xl"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-8 bg-muted rounded w-16"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-24"}),(0,a.jsx)("div",{className:"h-3 bg-muted rounded w-32"})]})]})},e))})}):(0,a.jsxs)("div",{className:`space-y-8 ${r}`,children:[(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,r)=>(0,a.jsxs)("div",{className:"group relative p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1 transition-all duration-500 animate-fade-in-up enhanced-hover",style:{animationDelay:`${100*r}ms`},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsx)("div",{className:`p-3 rounded-xl bg-background/80 ${e.color} group-hover:scale-110 transition-transform duration-300`,children:e.icon})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300",children:e.value}),(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground/80",children:e.description})]})]})]},e.title))}),u.last_updated&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(u.last_updated).toLocaleDateString()]})})]})}var P=t(18191),A=t(48736),O=t(21832),F=t(78298);function B({iconSlugs:e}){let[r,t]=(0,s.useState)([]),{theme:o}=(0,F.F)(),[i,l]=(0,s.useState)(!0),n=(0,s.useRef)(null),d=(0,s.useRef)(null),c=(0,s.useRef)(null),[m,u]=(0,s.useState)(180),[p,x]=(0,s.useState)(0),[h,g]=(0,s.useState)(0),[b,f]=(0,s.useState)(null),[v,y]=(0,s.useState)(0),[j,w]=(0,s.useState)(0),[N,_]=(0,s.useState)({x:0,y:0}),[C,z]=(0,s.useState)(!0),S=e=>{let r=[];for(let t=0;t<e;t++){let a=Math.acos(-1+2*t/e),s=Math.sqrt(e*Math.PI)*a,o=Math.sin(a)*Math.cos(s)*(.7+.25*Math.random()),i=Math.sin(a)*Math.sin(s)*(.7+.25*Math.random()),l=Math.cos(a)*(.7+.25*Math.random()),n=.9*(.6+.4*Math.random())/Math.sqrt(o*o+i*i+l*l);o*=n,i*=n,l*=n,r.push({x:o,y:i,z:l})}return r};if((0,s.useEffect)(()=>{!function(){l(!0);try{let r=S(e.length),a=e.map((e,t)=>{let a=r[t%r.length];return{title:e.charAt(0).toUpperCase()+e.slice(1),slug:e,path:`/images/icons/${e}.svg`,index:t,x:a.x,y:a.y,z:a.z}});t(a)}catch(e){}finally{l(!1)}}()},[e]),(0,s.useEffect)(()=>{if(!n.current||!d.current)return;let e=()=>{let e=n.current,r=d.current;if(!e||!r)return;let{width:t,height:a}=e.getBoundingClientRect();x(.8*t),g(.42*a),u(.48*Math.min(t,a)),r.style.width=`${t+140}px`,r.style.height=`${a+140}px`};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,s.useEffect)(()=>{if(!n.current)return;let e=n.current,r=r=>{if(!C){let t=e.getBoundingClientRect();_({x:(r.clientX-t.left)/t.width*2-1,y:(r.clientY-t.top)/t.height*2-1})}},t=()=>{z(!1)},a=()=>{z(!0)};return e.addEventListener("mousemove",r),e.addEventListener("mouseenter",t),e.addEventListener("mouseleave",a),()=>{e.removeEventListener("mousemove",r),e.removeEventListener("mouseenter",t),e.removeEventListener("mouseleave",a)}},[C]),(0,s.useEffect)(()=>{if(i||0===r.length||!n.current)return;let e=r=>{if(C)w(e=>(e+.5)%360),y(e=>Math.max(-15,Math.min(15,e+.08*Math.sin(5e-4*r))));else{let e=30*N.x,r=-(20*N.y);w(r=>r+(e-r)*.08),y(e=>e+(r-e)*.08)}c.current=requestAnimationFrame(e)};return c.current=requestAnimationFrame(e),()=>{c.current&&cancelAnimationFrame(c.current)}},[i,r.length,C,N]),i)return(0,a.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"})});let E=e=>{let r=v*Math.PI/180,t=j*Math.PI/180,a=e.x,s=e.z,o=Math.cos(t),i=Math.sin(t),l=a*o-s*i;s=a*i+s*o,a=l;let n=e.y,d=Math.cos(r),c=Math.sin(r),u=n*d-s*c,x=48*(.85+((s=n*c+s*d)+1)*.2),g=.6+(s+1)*.25;return{x:p+a*m,y:h+(n=u)*m,size:x,opacity:g,zIndex:Math.floor((s+1)*100),blur:s<0?`blur(${.25*Math.abs(s)}px)`:"blur(0px)",scale:.95+(s+1)*.15}};return(0,a.jsxs)("div",{ref:n,className:"w-full h-full relative overflow-visible m-0 flex items-center justify-center group",style:{perspective:"1200px"},children:[(0,a.jsx)("div",{className:"absolute bg-gradient-radial from-primary/5 via-transparent to-transparent rounded-full blur-3xl opacity-60 group-hover:opacity-80 transition-opacity duration-500",style:{top:"50%",left:"50%",width:"80%",height:"80%",maxWidth:"200px",maxHeight:"200px",transform:"translate(-50%, -50%)",animation:"glow-pulse 4s ease-in-out infinite"}}),(0,a.jsx)("div",{className:`absolute rounded-[50%] ${"dark"===o?"bg-gradient-radial from-primary/8 via-teal-500/5 to-transparent":"bg-gradient-radial from-primary/5 via-teal-500/3 to-transparent"} group-hover:opacity-100 transition-opacity duration-300`,style:{bottom:"25%",left:"50%",width:"60%",height:"30%",maxWidth:"120px",maxHeight:"60px",transform:"translateX(-50%) rotateX(70deg)",filter:"blur(12px)",opacity:.7}}),(0,a.jsx)("div",{className:"absolute w-2 h-2 bg-primary/40 rounded-full blur-sm animate-pulse-soft",style:{top:"30%",left:"25%",animationDelay:"0s"}}),(0,a.jsx)("div",{className:"absolute w-1.5 h-1.5 bg-secondary/40 rounded-full blur-sm animate-pulse-soft",style:{top:"65%",left:"75%",animationDelay:"1s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-primary/30 rounded-full blur-sm animate-pulse-soft",style:{top:"45%",left:"80%",animationDelay:"2s"}}),(0,a.jsx)("div",{ref:d,className:"absolute overflow-visible",style:{transformStyle:"preserve-3d",top:"50%",left:"50%",transform:"translate(-50%, -50%)",padding:"40px",width:"100%",height:"100%",maxWidth:"300px",maxHeight:"300px"},children:r.map(e=>{let r=E(e),t=b===e.slug;return(0,a.jsxs)("div",{className:"absolute transition-all duration-300 ease-out cursor-pointer group",style:{left:`${r.x}px`,top:`${r.y}px`,transform:`translate(-50%, -50%) scale(${r.scale})`,opacity:r.opacity,zIndex:t?999:r.zIndex,filter:r.blur,transition:"opacity 0.3s, filter 0.3s, transform 0.3s"},onMouseEnter:()=>f(e.slug),onMouseLeave:()=>f(null),children:[(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{transform:"scale(2)",zIndex:-1}}),(0,a.jsxs)("div",{className:"relative transition-all duration-300 ease-smooth",style:{transform:t?"scale(1.6) rotateY(15deg)":"scale(1)",filter:t?"brightness(1.2) saturate(1.3)":"brightness(1) saturate(1)"},children:[(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-white/10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{transform:"scale(1.2)",zIndex:-1}}),(0,a.jsx)(k.default,{src:e.path,alt:e.title,width:r.size,height:r.size,className:`transition-all duration-300 ${"dark"===o?"brightness-110 drop-shadow-glow group-hover:drop-shadow-[0_0_15px_rgba(var(--primary),0.5)]":"drop-shadow-sm group-hover:drop-shadow-lg"}`}),t&&(0,a.jsxs)("div",{className:"absolute left-1/2 top-full transform -translate-x-1/2 mt-2 px-3 py-1.5 rounded-lg bg-foreground/90 text-background text-xs font-medium whitespace-nowrap backdrop-blur-sm border border-white/20 animate-fade-in-up",style:{opacity:.95,zIndex:1e3,boxShadow:"0 4px 12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.1)"},children:[e.title,(0,a.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-foreground/90"})]})]})]},e.slug)})})]})}let H=["openai","claude","anthropic","gemini","meta","mistral","huggingface","qwen","doubao","hunyuan","zhipu","deepseek","langchain","modelscope","cursor","ollama","openrouter","github"];function R({className:e}){let[r,t]=(0,s.useState)(H),[o,i]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{(async()=>{try{let e=await fetch("http://**************:8000/api/site-settings/tech-icons/visible",{cache:"no-store"});if(e.ok){let r=await e.json();r.tech_icons&&Array.isArray(r.tech_icons)&&r.tech_icons.length>0?t(r.tech_icons):t(H)}else t(H)}catch(e){t(H)}finally{i(!1)}})()},[]),o)?(0,a.jsx)("div",{className:`flex items-center justify-center h-96 ${e}`,children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4 text-center",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-muted rounded-full mx-auto"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-32 mx-auto"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading tech stack..."})]})}):(0,a.jsx)("div",{className:e,children:(0,a.jsx)(B,{iconSlugs:r})})}let W=(0,s.forwardRef)(({variant:e="primary",animate:r=!1,className:t,children:s,...o},i)=>(0,a.jsx)("span",{ref:i,className:(0,l.cn)("bg-clip-text text-transparent font-semibold",{primary:"bg-gradient-to-r from-primary to-primary/70",secondary:"bg-gradient-to-r from-secondary to-muted",rainbow:"bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600",ocean:"bg-gradient-to-r from-blue-500 via-teal-500 to-green-500"}[e],r&&"animate-pulse-soft",t),...o,children:s}));function V({sortedCategories:e,pagesConfig:r}){let[t,h]=(0,s.useState)(null),[g,b]=(0,s.useState)(!1),[f,v]=(0,s.useState)(0),[y,j]=(0,s.useState)([]),[w,N]=(0,s.useState)(e),[k,z]=(0,s.useState)(!1),[S,M]=(0,s.useState)(new Set);(0,s.useEffect)(()=>{let r=[];e.forEach(e=>{r.push(...e.projects)}),j(r),N(e)},[e]),(0,s.useEffect)(()=>{e.length>0&&M(new Set([e[0].categoryName]))},[e]);let $=(0,s.useCallback)(e=>{M(r=>{let t=new Set(r);return t.has(e)?t.delete(e):t.add(e),t})},[]),L=(0,s.useCallback)(e=>{v(y.findIndex(r=>r.id===e.id)),h(e),b(!0)},[y]),Z=(0,s.useCallback)(()=>{let e=(f+1)%y.length;v(e),h(y[e])},[f,y]),T=(0,s.useCallback)(()=>{let e=0===f?y.length-1:f-1;v(e),h(y[e])},[f,y]),F=(0,s.useCallback)(r=>{let t=new Map;r.forEach((e,r)=>{t.set(e.id,r)}),N(e.map(e=>{let a=e.projects.filter(e=>r.some(r=>r.id===e.id));return a.sort((e,r)=>(t.get(e.id)??1/0)-(t.get(r.id)??1/0)),{...e,projects:a}}).filter(e=>e.projects.length>0))},[e]);return(0,a.jsx)(E,{children:(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(o.W2,{className:"mt-16 sm:mt-24",children:(0,a.jsxs)("div",{className:"relative mb-16 rounded-3xl overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/12 via-green-500/5 to-secondary/12"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-background/95 via-background/85 to-background/95"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent"}),(0,a.jsx)("div",{className:"absolute top-8 right-8 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-3xl animate-pulse-slow"}),(0,a.jsx)("div",{className:"absolute bottom-8 left-8 w-24 h-24 bg-gradient-to-tr from-green-500/10 to-transparent rounded-full blur-2xl animate-pulse-slow",style:{animationDelay:"1s"}}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/4 w-2 h-2 bg-primary/30 rounded-full animate-pulse"}),(0,a.jsx)("div",{className:"absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-green-500/40 rounded-full animate-pulse",style:{animationDelay:"2s"}}),(0,a.jsxs)("div",{className:"relative py-16 px-8 lg:py-20 lg:px-12 min-h-[500px]",children:[(0,a.jsx)("div",{className:"relative z-10 max-w-3xl",children:(0,a.jsxs)(O.AnimatedSection,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl",children:(0,a.jsx)(W,{variant:"primary",animate:!0,children:r.projects.title})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed max-w-2xl",children:r.projects.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 pt-8",children:[(0,a.jsx)("div",{className:"group",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-5 rounded-2xl bg-background/60 border border-border/50 backdrop-blur-sm transition-all duration-500 hover:bg-background/90 hover:border-primary/30 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1 enhanced-hover relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"p-3 rounded-xl bg-primary/10 text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300 relative z-10",children:(0,a.jsx)(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300",children:e.reduce((e,r)=>e+r.projects.length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Projects"})]})]})}),(0,a.jsx)("div",{className:"group",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-5 rounded-2xl bg-background/60 border border-border/50 backdrop-blur-sm transition-all duration-500 hover:bg-background/90 hover:border-green-500/30 hover:shadow-xl hover:shadow-green-500/10 hover:-translate-y-1 enhanced-hover relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"p-3 rounded-xl bg-green-500/10 text-green-500 group-hover:bg-green-500/20 group-hover:scale-110 transition-all duration-300 relative z-10",children:(0,a.jsx)(d.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground group-hover:text-green-500 transition-colors duration-300",children:e.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Categories"})]})]})})]})]})}),(0,a.jsx)(O.AnimatedSection,{delay:200,direction:"right",className:"absolute top-1/2 right-0 transform -translate-y-4 hidden xl:block",children:(0,a.jsxs)("div",{className:"relative",style:{width:"min(10vw, 160px)",height:"min(10vw, 160px)",minWidth:"120px",minHeight:"120px"},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-radial from-primary/10 via-transparent to-transparent rounded-full blur-3xl animate-glow-pulse"}),(0,a.jsx)("div",{className:"absolute top-4 right-4 w-2 h-2 bg-primary/40 rounded-full blur-sm animate-pulse-soft"}),(0,a.jsx)("div",{className:"absolute bottom-8 left-6 w-1.5 h-1.5 bg-secondary/40 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"1s"}}),(0,a.jsx)("div",{className:"absolute top-1/2 left-2 w-1 h-1 bg-primary/30 rounded-full blur-sm animate-pulse-soft",style:{animationDelay:"2s"}}),(0,a.jsx)(R,{})]})})]})]})}),(0,a.jsxs)(o.W2,{className:"mb-16",children:[(0,a.jsx)("div",{className:"space-y-8 relative z-[2000]",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 rounded-2xl"}),(0,a.jsxs)("div",{className:"relative p-6 rounded-2xl border border-border/50 backdrop-blur-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary",children:(0,a.jsx)(c.Z,{className:"w-5 h-5"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Search & Filter Projects"})]}),(0,a.jsx)(D,{projects:y,categories:e,onFilterChange:F,className:"w-full"})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 items-start mt-8",children:[(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsx)("div",{className:"flex gap-3",children:(0,a.jsx)("button",{onClick:()=>z(!k),className:"px-6 py-3 bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-xl font-medium hover:bg-primary/20 transition-all duration-300 backdrop-blur-sm",children:k?"Hide Stats":"Show Stats"})})]}),k&&(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)(I,{projects:y})})]}),(0,a.jsx)("div",{className:"sm:px-8",children:(0,a.jsx)("div",{className:"mx-auto w-full max-w-7xl lg:px-8",children:(0,a.jsx)("div",{className:"relative px-4 sm:px-8 lg:px-12",children:(0,a.jsx)("div",{className:"mx-auto max-w-6xl",children:(0,a.jsx)("div",{className:"space-y-20",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/50 hidden lg:block"}),w.map((e,r)=>{let t=S.has(e.categoryName);return(0,a.jsx)(O.AnimatedSection,{delay:200*r,children:(0,a.jsxs)("div",{className:"relative mb-16 last:mb-0",children:[(0,a.jsxs)("button",{onClick:()=>$(e.categoryName),className:"absolute left-2 top-8 w-12 h-12 bg-gradient-to-br from-primary to-primary/70 rounded-full border-4 border-background shadow-lg hidden lg:flex items-center justify-center z-20 hover:scale-110 hover:shadow-xl transition-all duration-300 group/node",children:[(0,a.jsxs)("div",{className:"relative w-full h-full rounded-full overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-primary via-primary/80 to-primary/60"}),(0,a.jsx)("div",{className:"absolute inset-1 rounded-full bg-gradient-to-br from-white/30 via-transparent to-transparent"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:t?(0,a.jsx)(u.Z,{className:"w-4 h-4 text-white drop-shadow-sm"}):(0,a.jsx)(m.Z,{className:"w-4 h-4 text-white drop-shadow-sm"})})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-primary/30 rounded-full animate-ping opacity-0 group-hover/node:opacity-100"})]}),(0,a.jsxs)("div",{className:"relative lg:ml-16",children:[(0,a.jsxs)("div",{className:"relative mb-8",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 rounded-2xl blur-xl"}),(0,a.jsx)("button",{onClick:()=>$(e.categoryName),className:"relative w-full p-6 rounded-2xl bg-background/60 border border-border/30 backdrop-blur-sm hover:bg-background/80 transition-all duration-300 text-left group/header",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl blur opacity-50"}),(0,a.jsx)("div",{className:"relative w-12 h-12 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center border border-primary/20 backdrop-blur-sm group-hover/header:scale-105 transition-transform duration-300",children:e.icon?e.icon.includes(":")?(0,a.jsx)(P.CustomIcon,{name:e.icon,size:24}):(0,a.jsx)(_.AntdIcon,{iconName:e.icon,size:24}):e.categoryName.toLowerCase().includes("open source")||e.categoryName.toLowerCase().includes("开源")?(0,a.jsx)(P.CustomIcon,{name:"github",size:24}):(0,a.jsx)(A.Z,{size:24})})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:(0,a.jsx)(W,{variant:"primary",children:e.categoryName})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,a.jsx)("span",{className:"text-xs font-medium lg:hidden",children:t?"Collapse":"Expand"}),(0,a.jsx)("div",{className:"p-1 rounded-md hover:bg-muted/50 transition-colors",children:t?(0,a.jsx)(x.Z,{className:"w-4 h-4 transition-transform duration-300"}):(0,a.jsx)(p.Z,{className:"w-4 h-4 transition-transform duration-300"})})]})]}),e.description&&(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:e.description})]})]}),(0,a.jsx)("div",{className:"px-4 py-2 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full border border-primary/20 backdrop-blur-sm group-hover/header:scale-105 transition-transform duration-300",children:(0,a.jsxs)("span",{className:"text-sm font-medium text-primary",children:[e.projects.length," projects"]})})]})})]}),(0,a.jsx)("div",{className:(0,l.cn)("transition-all duration-700 ease-in-out overflow-hidden",t?"max-h-[10000px] opacity-100 transform scale-100 translate-y-0":"max-h-0 opacity-0 transform scale-95 -translate-y-4"),style:{transformOrigin:"top center"},children:t&&(0,a.jsx)("div",{className:"project-grid-container px-6 sm:px-8 lg:px-12 py-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 sm:gap-10 lg:gap-12 auto-rows-fr",children:e.projects.map((e,t)=>(0,a.jsx)("div",{className:"min-w-0 w-full",style:{minWidth:"320px"},children:(0,a.jsx)(i.s,{project:e,index:t,categoryIndex:r,onPreview:L})},`${e.id||e.name}-${t}`))})})})]})]})},e.categoryName)})]})})})})})}),(0,a.jsx)(C,{project:t,isOpen:g,onClose:()=>b(!1),onNext:Z,onPrev:T})]})})}W.displayName="GradientText",(0,s.forwardRef)(({intensity:e="medium",tint:r="none",className:t,children:s,...o},i)=>(0,a.jsx)("div",{ref:i,className:(0,l.cn)("rounded-xl border backdrop-blur-md","dark:bg-black/10 dark:border-white/10",{light:"bg-white/5 backdrop-blur-sm border-white/10",medium:"bg-white/10 backdrop-blur-md border-white/20",strong:"bg-white/20 backdrop-blur-lg border-white/30"}[e],{none:"",primary:"bg-primary/5 border-primary/20",secondary:"bg-secondary/5 border-secondary/20"}[r],t),...o,children:s})).displayName="GlassCard",(0,s.forwardRef)(({variant:e="raised",size:r="md",className:t,children:s,...o},i)=>(0,a.jsx)("div",{ref:i,className:(0,l.cn)("bg-background border-0 transition-all duration-300",{raised:"shadow-[8px_8px_16px_rgba(0,0,0,0.1),-8px_-8px_16px_rgba(255,255,255,0.1)]",inset:"shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)]",flat:"shadow-[0_0_0_1px_rgba(0,0,0,0.05)]"}[e],{sm:"p-4 rounded-lg",md:"p-6 rounded-xl",lg:"p-8 rounded-2xl"}[r],t),...o,children:s})).displayName="NeumorphismCard",(0,s.forwardRef)(({color:e="primary",intensity:r="medium",animate:t=!1,className:s,children:o,...i},n)=>(0,a.jsx)("div",{ref:n,className:(0,l.cn)("transition-all duration-300",{primary:"shadow-primary/50",secondary:"shadow-secondary/50",success:"shadow-green-500/50",warning:"shadow-yellow-500/50",error:"shadow-red-500/50"}[e],{subtle:"shadow-lg",medium:"shadow-xl",strong:"shadow-2xl"}[r],t&&"animate-pulse-soft",s),...i,children:o})).displayName="GlowEffect",(0,s.forwardRef)(({pattern:e="dots",opacity:r=.1,className:t,children:s,...o},i)=>{let n={dots:{backgroundImage:`radial-gradient(circle, currentColor ${100*r}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},grid:{backgroundImage:`linear-gradient(currentColor ${100*r}% 1px, transparent 1px), linear-gradient(90deg, currentColor ${100*r}% 1px, transparent 1px)`,backgroundSize:"20px 20px"},diagonal:{backgroundImage:`repeating-linear-gradient(45deg, transparent, transparent 10px, currentColor ${100*r}% 10px, currentColor ${100*r}% 11px)`},waves:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000' fill-opacity='${r}'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}};return(0,a.jsx)("div",{ref:i,className:(0,l.cn)("relative",t),style:n[e],...o,children:s})}).displayName="PatternBackground",(0,s.forwardRef)(({gradient:e="rainbow",width:r=2,animate:t=!1,className:s,children:o,...i},n)=>(0,a.jsx)("div",{ref:n,className:(0,l.cn)("relative rounded-xl overflow-hidden",t&&"animate-pulse-soft",s),...i,children:(0,a.jsx)("div",{className:(0,l.cn)("absolute inset-0 bg-gradient-to-r",{rainbow:"from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500",sunset:"from-orange-500 via-pink-500 to-purple-600",ocean:"from-blue-500 via-teal-500 to-green-500",forest:"from-green-600 via-emerald-500 to-teal-500",custom:"from-primary via-secondary to-accent"}[e],t&&"animate-spin-slow"),style:{padding:`${r}px`},children:(0,a.jsx)("div",{className:"w-full h-full bg-background rounded-xl",children:o})})})).displayName="ColorfulBorder"},80089:function(e,r,t){"use strict";t.d(r,{LazyWrapper:function(){return l}});var a=t(57437),s=t(2265),o=t(51817);function i({size:e="default"}){return(0,a.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,a.jsx)(o.Z,{className:`${{sm:"w-4 h-4",default:"w-6 h-6",lg:"w-8 h-8"}[e]} animate-spin text-primary`})})}function l({children:e,fallback:r=(0,a.jsx)(i,{}),errorFallback:t=(0,a.jsx)("div",{className:"text-center text-muted-foreground",children:"加载失败"})}){return(0,a.jsx)(s.Suspense,{fallback:r,children:e})}}},function(e){e.O(0,[105,216,592,744],function(){return e(e.s=37799)}),_N_E=e.O()}]);