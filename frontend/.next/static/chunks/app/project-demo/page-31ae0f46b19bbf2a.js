(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[989],{1662:function(e,s,t){Promise.resolve().then(t.bind(t,12177))},12177:function(e,s,t){"use strict";var a=t(57437);t(2265);var i=t(36629);let r=[{id:1,name:"AI Chat Assistant",description:"A modern AI-powered chat assistant built with React and OpenAI API.",categories:[{id:1,name:"AI",slug:"ai",icon:null,color:"#84CC16"},{id:2,name:"Web",slug:"web",icon:null,color:"#10B981"}],tech_stack:["React","TypeScript","OpenAI","Node.js"],stats:{stars:1200,forks:89,views:5600},status:"active",priority:"high",last_updated:"2024-01-15",has_detail_page:!0,slug:"ai-chat-assistant",link:{href:"/projects/ai-chat-assistant",label:"View Project"},type:"work"},{id:2,name:"Mobile E-commerce App",description:"A comprehensive e-commerce mobile application with advanced features including real-time inventory management, secure payment processing, user authentication, shopping cart functionality, order tracking, push notifications, and seamless integration with multiple payment gateways. Built using modern mobile development frameworks.",categories:[{id:3,name:"Mobile",slug:"mobile",icon:null,color:"#14B8A6"},{id:4,name:"E-commerce",slug:"ecommerce",icon:null,color:"#059669"}],tech_stack:["React Native","Redux","Firebase","Stripe","Node.js"],stats:{stars:856,downloads:12e3},status:"completed",priority:"medium",last_updated:"2024-01-10",has_detail_page:!1,link:{href:"https://github.com/example/mobile-ecommerce",label:"View on GitHub"},type:"work"},{id:3,name:"Design System",description:"Modern design system.",categories:[{id:5,name:"Design",slug:"design",icon:null,color:"#06B6D4"}],tech_stack:["Figma","React","Storybook"],stats:{views:3400},status:"in-progress",priority:"low",last_updated:"2024-01-12",has_detail_page:!0,slug:"design-system",link:{href:"/projects/design-system",label:"View Project"},type:"work"},{id:4,name:"Open Source Library",description:"A powerful and flexible open source library for building modern web applications. Features include component composition, state management, routing, and extensive customization options. Designed with developer experience in mind.",categories:[{id:6,name:"Open Source",slug:"opensource",icon:null,color:"#16A34A"},{id:7,name:"Library",slug:"library",icon:null,color:"#059669"}],tech_stack:["TypeScript","React","Rollup","Jest","Storybook"],stats:{stars:2340,forks:234,contributors:45},status:"active",priority:"high",last_updated:"2024-01-14",has_detail_page:!1,link:{href:"https://github.com/example/open-source-library",label:"View on GitHub"},type:"opensource"}];s.default=function(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"红点奖级别项目卡片演示"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"展示统一高度、美观的绿色主题卡片设计"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 auto-rows-fr",children:r.map((e,s)=>(0,a.jsx)("div",{className:"min-w-0 w-full",style:{minWidth:"320px"},children:(0,a.jsx)(i.s,{project:e,index:s,categoryIndex:0,onPreview:e=>{}},e.id)}))}),(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsxs)("div",{className:"inline-flex items-center gap-4 px-6 py-3 bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"统一高度"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-emerald-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"绿色主题"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-teal-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"智能截断"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-cyan-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:"高级交互"})]})]})})]})})}}},function(e){e.O(0,[105,592,216,744],function(){return e(e.s=1662)}),_N_E=e.O()}]);