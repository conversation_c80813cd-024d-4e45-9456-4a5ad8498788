"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_markdown-processor_ts"],{

/***/ "(app-pages-browser)/./src/lib/markdown-processor.ts":
/*!***************************************!*\
  !*** ./src/lib/markdown-processor.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearMarkdownCache: function() { return /* binding */ clearMarkdownCache; },\n/* harmony export */   getCacheStats: function() { return /* binding */ getCacheStats; },\n/* harmony export */   processMarkdownFast: function() { return /* binding */ processMarkdownFast; }\n/* harmony export */ });\n// 高性能 Markdown 处理器\n// 使用更高效的算法和缓存机制\n// 内存缓存，避免重复处理相同内容\nconst markdownCache = {};\n// 生成缓存键\nfunction getCacheKey(content) {\n    // 使用简单的哈希算法生成缓存键\n    let hash = 0;\n    for(let i = 0; i < content.length; i++){\n        const char = content.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // 转换为32位整数\n        ;\n    }\n    return hash.toString();\n}\n// 优化的正则表达式，预编译以提高性能\nconst REGEX_PATTERNS = {\n    // HTML标签保护\n    htmlTags: /<[^>]+>/g,\n    // 代码块\n    codeBlocks: /```(\\w+)?\\n([\\s\\S]*?)```/g,\n    inlineCode: /`([^`]+)`/g,\n    // 标题\n    h4: /^#### (.*$)/gim,\n    h3: /^### (.*$)/gim,\n    h2: /^## (.*$)/gim,\n    h1: /^# (.*$)/gim,\n    // 文本样式\n    bold: /\\*\\*(.*?)\\*\\*/g,\n    italic: /\\*(.*?)\\*/g,\n    strikethrough: /~~(.*?)~~/g,\n    // 链接和图片\n    links: /\\[([^\\]]+)\\]\\(([^)]+)\\)/g,\n    icons: /!\\[([^\\]]*)\\]\\((\\/api\\/icons\\/render\\/[^)]+)\\)/g,\n    images: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g,\n    // 其他\n    blockquotes: /^> (.+)$/gm,\n    horizontalRule: /^---$/gm,\n    // 列表\n    unorderedList: /^[\\s]*[-*+]\\s+(.+)$/gm,\n    orderedList: /^[\\s]*\\d+\\.\\s+(.+)$/gm,\n    // 表格\n    tableRow: /^\\|(.+)\\|$/gm,\n    // 清理\n    extraBr: /<br><\\/p>/g,\n    emptyP: /<p><\\/p>/g\n};\n// 快速 Markdown 处理函数\nfunction processMarkdownFast(content) {\n    // 检查缓存\n    const cacheKey = getCacheKey(content);\n    if (markdownCache[cacheKey]) {\n        return markdownCache[cacheKey];\n    }\n    try {\n        // 如果内容为空或过短，直接返回\n        if (!content || content.length < 10) {\n            return content || \"\";\n        }\n        let html = content;\n        // 1. 保护HTML标签\n        const htmlTags = [];\n        let tagIndex = 0;\n        html = html.replace(REGEX_PATTERNS.htmlTags, (match)=>{\n            const placeholder = `__HTML_TAG_${tagIndex}__`;\n            htmlTags[tagIndex] = match;\n            tagIndex++;\n            return placeholder;\n        });\n        // 2. 处理代码块（优先级最高）\n        html = html.replace(REGEX_PATTERNS.codeBlocks, (match, lang, code)=>{\n            const language = lang || \"text\";\n            const cleanCode = code.trim();\n            return `<div data-code-block=\"true\" data-language=\"${language}\">${cleanCode}</div>`;\n        });\n        html = html.replace(REGEX_PATTERNS.inlineCode, '<code class=\"inline-code\">$1</code>');\n        // 3. 处理标题（从大到小）\n        html = html.replace(REGEX_PATTERNS.h4, \"<h4>$1</h4>\");\n        html = html.replace(REGEX_PATTERNS.h3, \"<h3>$1</h3>\");\n        html = html.replace(REGEX_PATTERNS.h2, \"<h2>$1</h2>\");\n        html = html.replace(REGEX_PATTERNS.h1, \"<h1>$1</h1>\");\n        // 4. 处理文本样式\n        html = html.replace(REGEX_PATTERNS.bold, \"<strong>$1</strong>\");\n        html = html.replace(REGEX_PATTERNS.italic, \"<em>$1</em>\");\n        html = html.replace(REGEX_PATTERNS.strikethrough, \"<del>$1</del>\");\n        // 5. 处理链接和图片\n        html = html.replace(REGEX_PATTERNS.links, '<a href=\"$2\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>');\n        html = html.replace(REGEX_PATTERNS.icons, '<span data-icon-placeholder data-src=\"$2\" data-alt=\"$1\"></span>');\n        html = html.replace(REGEX_PATTERNS.images, '<img src=\"$2\" alt=\"$1\" loading=\"lazy\" style=\"max-width: 100%; height: auto;\" />');\n        // 6. 处理其他元素\n        html = html.replace(REGEX_PATTERNS.blockquotes, \"<blockquote>$1</blockquote>\");\n        html = html.replace(REGEX_PATTERNS.horizontalRule, \"<hr>\");\n        // 7. 处理列表（简化版）\n        html = html.replace(REGEX_PATTERNS.unorderedList, \"<li>$1</li>\");\n        html = html.replace(REGEX_PATTERNS.orderedList, \"<li>$1</li>\");\n        // 包装连续的li标签\n        html = html.replace(/(<li>[\\s\\S]*?<\\/li>)/g, (match)=>{\n            if (match.includes(\"<ul>\") || match.includes(\"<ol>\")) return match;\n            return `<ul>${match}</ul>`;\n        });\n        // 8. 处理段落（优化版）\n        const lines = html.split(\"\\n\");\n        const processedLines = [];\n        let inParagraph = false;\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (!trimmedLine) {\n                if (inParagraph) {\n                    processedLines.push(\"</p>\");\n                    inParagraph = false;\n                }\n                continue;\n            }\n            // 检查是否是块级元素\n            if (trimmedLine.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)) {\n                if (inParagraph) {\n                    processedLines.push(\"</p>\");\n                    inParagraph = false;\n                }\n                processedLines.push(trimmedLine);\n            } else {\n                if (!inParagraph) {\n                    processedLines.push(\"<p>\");\n                    inParagraph = true;\n                }\n                processedLines.push(trimmedLine + \"<br>\");\n            }\n        }\n        if (inParagraph) {\n            processedLines.push(\"</p>\");\n        }\n        html = processedLines.join(\"\\n\");\n        // 9. 恢复HTML标签\n        for(let i = 0; i < htmlTags.length; i++){\n            html = html.replace(`__HTML_TAG_${i}__`, htmlTags[i]);\n        }\n        // 10. 清理\n        html = html.replace(REGEX_PATTERNS.extraBr, \"</p>\");\n        html = html.replace(REGEX_PATTERNS.emptyP, \"\");\n        // 缓存结果\n        markdownCache[cacheKey] = html;\n        return html;\n    } catch (error) {\n        console.error(\"Fast markdown processing error:\", error);\n        // 如果处理失败，返回原始内容并添加基本的换行处理\n        const fallback = content.replace(/\\n\\n/g, \"</p><p>\").replace(/\\n/g, \"<br>\");\n        markdownCache[cacheKey] = fallback;\n        return fallback;\n    }\n}\n// 清理缓存的函数（可选）\nfunction clearMarkdownCache() {\n    Object.keys(markdownCache).forEach((key)=>delete markdownCache[key]);\n}\n// 获取缓存统计信息（调试用）\nfunction getCacheStats() {\n    return {\n        size: Object.keys(markdownCache).length,\n        keys: Object.keys(markdownCache)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/markdown-processor.ts\n"));

/***/ })

}]);