"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58],{48058:function(e,r,t){t.r(r),t.d(r,{clearMarkdownCache:function(){return c},getCacheStats:function(){return o},processMarkdownFast:function(){return n}});let l={},a={htmlTags:/<[^>]+>/g,codeBlocks:/```(\w+)?\n([\s\S]*?)```/g,inlineCode:/`([^`]+)`/g,h4:/^#### (.*$)/gim,h3:/^### (.*$)/gim,h2:/^## (.*$)/gim,h1:/^# (.*$)/gim,bold:/\*\*(.*?)\*\*/g,italic:/\*(.*?)\*/g,strikethrough:/~~(.*?)~~/g,links:/\[([^\]]+)\]\(([^)]+)\)/g,icons:/!\[([^\]]*)\]\((\/api\/icons\/render\/[^)]+)\)/g,images:/!\[([^\]]*)\]\(([^)]+)\)/g,blockquotes:/^> (.+)$/gm,horizontalRule:/^---$/gm,unorderedList:/^[\s]*[-*+]\s+(.+)$/gm,orderedList:/^[\s]*\d+\.\s+(.+)$/gm,extraBr:/<br><\/p>/g,emptyP:/<p><\/p>/g};function n(e){let r=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r&=r;return r.toString()}(e);if(l[r])return l[r];try{if(!e||e.length<10)return e||"";let t=e,n=[],c=0,o=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(a.htmlTags,e=>{let r=`__HTML_TAG_${c}__`;return n[c]=e,c++,r})).replace(a.codeBlocks,(e,r,t)=>{let l=t.trim();return`<div data-code-block="true" data-language="${r||"text"}">${l}</div>`})).replace(a.inlineCode,'<code class="inline-code">$1</code>')).replace(a.h4,"<h4>$1</h4>")).replace(a.h3,"<h3>$1</h3>")).replace(a.h2,"<h2>$1</h2>")).replace(a.h1,"<h1>$1</h1>")).replace(a.bold,"<strong>$1</strong>")).replace(a.italic,"<em>$1</em>")).replace(a.strikethrough,"<del>$1</del>")).replace(a.links,'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')).replace(a.icons,'<span data-icon-placeholder data-src="$2" data-alt="$1"></span>')).replace(a.images,'<img src="$2" alt="$1" loading="lazy" style="max-width: 100%; height: auto;" />')).replace(a.blockquotes,"<blockquote>$1</blockquote>")).replace(a.horizontalRule,"<hr>")).replace(a.unorderedList,"<li>$1</li>")).replace(a.orderedList,"<li>$1</li>")).replace(/(<li>[\s\S]*?<\/li>)/g,e=>e.includes("<ul>")||e.includes("<ol>")?e:`<ul>${e}</ul>`)).split("\n"),i=[],s=!1;for(let e of o){let r=e.trim();if(!r){s&&(i.push("</p>"),s=!1);continue}r.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)?(s&&(i.push("</p>"),s=!1),i.push(r)):(s||(i.push("<p>"),s=!0),i.push(r+"<br>"))}s&&i.push("</p>"),t=i.join("\n");for(let e=0;e<n.length;e++)t=t.replace(`__HTML_TAG_${e}__`,n[e]);return t=(t=t.replace(a.extraBr,"</p>")).replace(a.emptyP,""),l[r]=t,t}catch(a){let t=e.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>");return l[r]=t,t}}function c(){Object.keys(l).forEach(e=>delete l[e])}function o(){return{size:Object.keys(l).length,keys:Object.keys(l)}}}}]);