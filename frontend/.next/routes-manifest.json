{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "public, s-maxage=60, stale-while-revalidate=300"}], "regex": "^/api(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/blogs/[slug]", "regex": "^/blogs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/blogs/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/gallery/album/[id]", "regex": "^/gallery/album/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/gallery/album/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/gallery/[slug]", "regex": "^/gallery/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/gallery/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/projects/[slug]", "regex": "^/projects/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/projects/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/version-history/[id]", "regex": "^/version\\-history/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/version\\-history/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/blogs", "regex": "^/blogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/blogs(?:/)?$"}, {"page": "/demo/waline-design", "regex": "^/demo/waline\\-design(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo/waline\\-design(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/feed", "regex": "^/feed(?:/)?$", "routeKeys": {}, "namedRegex": "^/feed(?:/)?$"}, {"page": "/gallery", "regex": "^/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/gallery(?:/)?$"}, {"page": "/project-demo", "regex": "^/project\\-demo(?:/)?$", "routeKeys": {}, "namedRegex": "^/project\\-demo(?:/)?$"}, {"page": "/projects", "regex": "^/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/projects(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/version-history", "regex": "^/version\\-history(?:/)?$", "routeKeys": {}, "namedRegex": "^/version\\-history(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}