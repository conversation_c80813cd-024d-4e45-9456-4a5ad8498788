{"kind": "FETCH", "data": {"headers": {"content-length": "3697", "content-type": "application/json", "date": "Fri, 01 Aug 2025 08:23:26 GMT", "server": "u<PERSON><PERSON>"}, "body": "eyJ0aXRsZSI6IkFib3V0IE1lIiwiY29udGVudCI6IiMgQWJvdXQgTWVcblxuIyMg8J+OkyBFZHVjYXRpb24gJiBSZXNlYXJjaFxuXG5JJ20gYSBNYXN0ZXIncyBjYW5kaWRhdGUgaW4gKipJbmZvcm1hdGlvbiBTY2llbmNlKiogYXQgdGhlICoqSW5zdGl0dXRlIG9mIEluZm9ybWF0aW9uIG9uIFRyYWRpdGlvbmFsIENoaW5lc2UgTWVkaWNpbmUqKiAoKipDSU5UQ00qKiksICoqQ2hpbmEgQWNhZGVteSBvZiBDaGluZXNlIE1lZGljYWwgU2NpZW5jZXMqKiAoKipDQUNNUyoqKS5cblxuQ3VycmVudGx5LCBJJ20gd29ya2luZyBhdCB0aGUgKipUQ00gSGVhbHRoIEludGVsbGlnZW5jZSBSJkQgQ2VudGVyKiogdW5kZXIgdGhlIHN1cGVydmlzaW9uIG9mIFNlbmlvciBSZXNlYXJjaGVyICoqVG9uZyBZdSoqIHNpbmNlIDIwMjMuXG5cbiMjIPCflKwgUmVzZWFyY2ggRm9jdXNcblxuTXkgcHJpbWFyeSByZXNlYXJjaCBjZW50ZXJzIG9uIGRldmVsb3BpbmcgYW5kIGFwcGx5aW5nICoqTGFyZ2UgTGFuZ3VhZ2UgTW9kZWxzKiogYW5kICoqS25vd2xlZGdlIEdyYXBocyoqIGluIHRoZSBmaWVsZCBvZiBUcmFkaXRpb25hbCBDaGluZXNlIE1lZGljaW5lLCB3aXRoIHBhcnRpY3VsYXIgZW1waGFzaXMgb246XG5cbiMjIyDwn6SWIEFkdmFuY2VkIExMTSBEZXZlbG9wbWVudFxuRGVzaWduaW5nIGFuZCBmaW5lLXR1bmluZyBzcGVjaWFsaXplZCBMTE0gYXJjaGl0ZWN0dXJlcyBmb3IgVENNIGtub3dsZWRnZSB1bmRlcnN0YW5kaW5nIGFuZCBjbGluaWNhbCByZWFzb25pbmdcblxuIyMjIPCflIQgTXVsdGktbW9kYWwgTGVhcm5pbmdcbkludGVncmF0aW5nIHRleHR1YWwsIHN0cnVjdHVyYWwsIGFuZCBkb21haW4ga25vd2xlZGdlIGZvciBjb21wcmVoZW5zaXZlIFRDTSBrbm93bGVkZ2UgcmVwcmVzZW50YXRpb25cblxuIyMjIOKaoSBDbGluaWNhbCBJbnRlbGxpZ2VuY2VcbkJ1aWxkaW5nIEFJLXBvd2VyZWQgc3lzdGVtcyBmb3IgVENNIGNsaW5pY2FsIGRlY2lzaW9uIHN1cHBvcnQgYW5kIGtub3dsZWRnZSBkaXNjb3ZlcnlcblxuIyMg8J+PhiBSZXNlYXJjaCBJbXBhY3RcblxuSSdtIGFjdGl2ZWx5IGNvbnRyaWJ1dGluZyB0byBzZXZlcmFsIGhpZ2gtaW1wYWN0IGluaXRpYXRpdmVzOlxuXG4+ICoqS2V5IFByb2plY3RzOioqXG4+IC0gKipOYXRpb25hbCBLZXkgUiZEIFByb2dyYW0qKjogXCJSZXNlYXJjaCBvbiBLZXkgVGVjaG5vbG9naWVzIGZvciBJbnRlbGxpZ2VudCBUcmFkaXRpb25hbCBDaGluZXNlIE1lZGljaW5lIEhlYWx0aCBTZXJ2aWNlc1wiICgyMDIyWUZDMzUwMTUwMClcbj4gLSAqKkNBQ01TIElubm92YXRpb24gRnVuZCoqOiBcIkNvbnN0cnVjdGlvbiBhbmQgQXBwbGljYXRpb24gb2YgVHJhZGl0aW9uYWwgQ2hpbmVzZSBNZWRpY2luZSBLbm93bGVkZ2UgR3JhcGggQmFzZWQgb24gTGFyZ2UgTGFuZ3VhZ2UgTW9kZWxzXCIgKENJMjAyM0EwMDUxMilcbj4gLSAqKkJlaWppbmcgTmF0dXJhbCBTY2llbmNlIEZvdW5kYXRpb24qKjogXCJSZXNlYXJjaCBvbiBNdWx0aS1tb2RhbCBLbm93bGVkZ2UgR3JhcGggQ29uc3RydWN0aW9uIGFuZCBSZWFzb25pbmcgTWV0aG9kcyBmb3IgVHJhZGl0aW9uYWwgQ2hpbmVzZSBNZWRpY2luZVwiICg0MjMyMDM3KVxuXG4jIyMg8J+TiiBSZXNlYXJjaCBNZXRyaWNzXG4tICoqUHVibGljYXRpb25zKio6IDgrIHBlZXItcmV2aWV3ZWQgcGFwZXJzIGluIHRvcC10aWVyIGpvdXJuYWxzXG4tICoqUGF0ZW50cyoqOiAzIGludmVudGlvbiBwYXRlbnRzIGZpbGVkXG4tICoqRGF0YXNldHMqKjogMiBsYXJnZS1zY2FsZSBUQ00gZGF0YXNldHMgcmVsZWFzZWRcbi0gKipDaXRhdGlvbnMqKjogMTUwKyBjaXRhdGlvbnMgKEdvb2dsZSBTY2hvbGFyKVxuXG4jIyDwn5K7IFRlY2huaWNhbCBFeHBlcnRpc2VcblxuIyMjIFByb2dyYW1taW5nICYgRGV2ZWxvcG1lbnRcbi0gKipMYW5ndWFnZXMqKjogUHl0aG9uLCBKYXZhU2NyaXB0LCBUeXBlU2NyaXB0LCBTUUwsIFJcbi0gKipGcmFtZXdvcmtzKio6IEZhc3RBUEksIFJlYWN0LCBOZXh0LmpzLCBQeVRvcmNoLCBUZW5zb3JGbG93XG4tICoqRGF0YWJhc2VzKio6IFBvc3RncmVTUUwsIE15U1FMLCBOZW80aiwgTW9uZ29EQlxuLSAqKkNsb3VkKio6IEFXUywgQXp1cmUsIERvY2tlciwgS3ViZXJuZXRlc1xuXG4jIyMgQUkgJiBNYWNoaW5lIExlYXJuaW5nXG4tICoqRGVlcCBMZWFybmluZyoqOiBUcmFuc2Zvcm1lciBhcmNoaXRlY3R1cmVzLCBCRVJULCBHUFQsIFQ1XG4tICoqS25vd2xlZGdlIEdyYXBocyoqOiBOZW80aiwgUkRGLCBTUEFSUUwsIEdyYXBoIE5ldXJhbCBOZXR3b3Jrc1xuLSAqKk5MUCoqOiBUZXh0IG1pbmluZywgTmFtZWQgRW50aXR5IFJlY29nbml0aW9uLCBSZWxhdGlvbiBFeHRyYWN0aW9uXG4tICoqTUxPcHMqKjogTW9kZWwgZGVwbG95bWVudCwgbW9uaXRvcmluZywgYW5kIHZlcnNpb25pbmdcblxuIyMg8J+MnyBQcm9mZXNzaW9uYWwgRXhwZXJpZW5jZVxuXG4jIyMgUmVzZWFyY2ggQXNzaXN0YW50IHwgQ0FDTVMgKDIwMjMtUHJlc2VudClcbi0gTGVhZGluZyBkZXZlbG9wbWVudCBvZiBUQ00tc3BlY2lmaWMgbGFyZ2UgbGFuZ3VhZ2UgbW9kZWxzXG4tIERlc2lnbmluZyBrbm93bGVkZ2UgZ3JhcGggY29uc3RydWN0aW9uIHBpcGVsaW5lc1xuLSBDb2xsYWJvcmF0aW5nIHdpdGggY2xpbmljYWwgZXhwZXJ0cyBvbiBBSSBzeXN0ZW0gdmFsaWRhdGlvblxuXG4jIyMgU29mdHdhcmUgRW5naW5lZXIgSW50ZXJuIHwgVGVjaCBDb21wYW55ICgyMDIyKVxuLSBEZXZlbG9wZWQgZnVsbC1zdGFjayB3ZWIgYXBwbGljYXRpb25zIHVzaW5nIFJlYWN0IGFuZCBOb2RlLmpzXG4tIEltcGxlbWVudGVkIG1hY2hpbmUgbGVhcm5pbmcgbW9kZWxzIGZvciBkYXRhIGFuYWx5c2lzXG4tIENvbnRyaWJ1dGVkIHRvIG9wZW4tc291cmNlIHByb2plY3RzXG5cbiMjIPCfjq8gQ3VycmVudCBHb2Fsc1xuXG4tICoqU2hvcnQtdGVybSoqOiBDb21wbGV0ZSBteSBNYXN0ZXIncyB0aGVzaXMgb24gXCJNdWx0aS1tb2RhbCBLbm93bGVkZ2UgR3JhcGggQ29uc3RydWN0aW9uIGZvciBUcmFkaXRpb25hbCBDaGluZXNlIE1lZGljaW5lXCJcbi0gKipNZWRpdW0tdGVybSoqOiBQdXJzdWUgUGhEIGluIEFJL01MIHdpdGggZm9jdXMgb24gaGVhbHRoY2FyZSBhcHBsaWNhdGlvbnNcbi0gKipMb25nLXRlcm0qKjogQnJpZGdlIHRoZSBnYXAgYmV0d2VlbiB0cmFkaXRpb25hbCBtZWRpY2luZSBhbmQgbW9kZXJuIEFJIHRlY2hub2xvZ3lcblxuIyMg8J+TqyBMZXQncyBDb25uZWN0XG5cbkknbSBhbHdheXMgaW50ZXJlc3RlZCBpbiBkaXNjdXNzaW5nIHJlc2VhcmNoIGNvbGxhYm9yYXRpb25zLCB0ZWNobmljYWwgY2hhbGxlbmdlcywgb3IgY2FyZWVyIG9wcG9ydHVuaXRpZXMgaW4gQUkgYW5kIGhlYWx0aGNhcmUuIEZlZWwgZnJlZSB0byByZWFjaCBvdXQhXG5cbi0tLVxuXG4qXCJUaGUgYmVzdCB3YXkgdG8gcHJlZGljdCB0aGUgZnV0dXJlIGlzIHRvIGludmVudCBpdC5cIiogLSBBbGFuIEtheSIsImlzX2FjdGl2ZSI6dHJ1ZSwicGhvdG9fdXJsIjoiL2ltYWdlcy9wcm9maWxlL21lLmpwZyIsInBob3RvX3Bvc2l0aW9uIjoicmlnaHQiLCJwaG90b19zdHlsZSI6InJvdGF0ZWQiLCJwaG90b19zaXplIjoibGFyZ2UiLCJsYXlvdXRfY29uZmlnIjp7fSwiaWQiOjEsImNyZWF0ZWRfYXQiOiIyMDI1LTA3LTEyVDIzOjUyOjUxIiwidXBkYXRlZF9hdCI6IjIwMjUtMDctMTNUMTE6Mzg6MTAifQ==", "status": 200, "url": "http://**************:8000/api/about/"}, "revalidate": 30, "tags": []}