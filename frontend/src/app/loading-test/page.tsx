'use client'
import { useState } from 'react'
import { useLoading, useApiLoading } from '@/contexts/LoadingContext'
import { usePageLoading, useDataLoading } from '@/hooks/usePageLoading'
import { InlineLoadingIndicator, SimpleLoadingSpinner } from '@/components/ui/PageLoadingOverlay'
import { quickLoadingTest, loadingPerformanceTracker } from '@/lib/loading-performance-test'

export default function LoadingTestPage() {
  const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = useLoading()
  const { withLoading } = useApiLoading()
  const { withLoading: withDataLoading } = useDataLoading()
  const { startLoading, stopLoading } = usePageLoading()
  const [testResult, setTestResult] = useState<string>('')
  const [performanceResult, setPerformanceResult] = useState<any>(null)

  // 模拟API调用
  const simulateApiCall = async (delay: number = 2000) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve('API调用成功！')
      }, delay)
    })
  }

  const testPageLoading = () => {
    showPageLoading('测试页面加载...')
    setTimeout(() => {
      hideLoading()
      setTestResult('页面加载测试完成')
    }, 3000)
  }

  const testContentLoading = () => {
    showContentLoading('测试内容加载...')
    setTimeout(() => {
      hideLoading()
      setTestResult('内容加载测试完成')
    }, 2000)
  }

  const testApiLoading = async () => {
    try {
      const result = await withLoading(
        () => simulateApiCall(2500),
        '正在调用API...'
      )
      setTestResult(`API测试完成: ${result}`)
    } catch (error) {
      setTestResult('API测试失败')
    }
  }

  const testDataLoading = async () => {
    try {
      const result = await withDataLoading(
        () => simulateApiCall(2000),
        {
          message: '正在加载数据...',
          type: 'content',
          minLoadingTime: 1000
        }
      )
      setTestResult(`数据加载测试完成: ${result}`)
    } catch (error) {
      setTestResult('数据加载测试失败')
    }
  }

  const testManualPageLoading = () => {
    const cleanup = startLoading('手动页面加载测试...')
    setTimeout(() => {
      stopLoading()
      setTestResult('手动页面加载测试完成')
      cleanup?.()
    }, 2500)
  }

  const runPerformanceTest = async () => {
    try {
      setTestResult('正在运行性能测试...')
      const result = await quickLoadingTest()
      setPerformanceResult(result)
      setTestResult(`性能测试完成 - 评分: ${result.evaluation.score}/100 (${result.evaluation.grade})`)
    } catch (error) {
      setTestResult('性能测试失败')
      console.error('Performance test error:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            全局加载体验测试
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            测试各种加载状态和指示器
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* 页面加载测试 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              页面加载测试
            </h2>
            <div className="space-y-3">
              <button
                onClick={testPageLoading}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                测试页面加载遮罩
              </button>
              <button
                onClick={testManualPageLoading}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                测试手动页面加载
              </button>
            </div>
          </div>

          {/* 内容加载测试 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              内容加载测试
            </h2>
            <div className="space-y-3">
              <button
                onClick={testContentLoading}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                测试内容加载
              </button>
              <button
                onClick={testDataLoading}
                className="w-full px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
              >
                测试数据加载Hook
              </button>
            </div>
          </div>

          {/* API加载测试 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              API加载测试
            </h2>
            <button
              onClick={testApiLoading}
              className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              测试API加载Hook
            </button>
          </div>

          {/* 组件示例 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              加载组件示例
            </h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">简单加载器:</p>
                <SimpleLoadingSpinner size="md" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">内联加载指示器:</p>
                <InlineLoadingIndicator text="正在处理..." />
              </div>
            </div>
          </div>

          {/* 性能测试 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 md:col-span-2">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              性能测试
            </h2>
            <button
              onClick={runPerformanceTest}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              运行完整性能测试
            </button>
          </div>
        </div>

        {/* 性能测试结果 */}
        {performanceResult && (
          <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              性能测试详细结果
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">评估结果</h4>
                <div className="space-y-1 text-sm">
                  <p>评分: <span className="font-mono text-blue-600">{performanceResult.evaluation.score}/100</span></p>
                  <p>等级: <span className="font-mono text-green-600">{performanceResult.evaluation.grade}</span></p>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">关键指标</h4>
                <div className="space-y-1 text-sm">
                  <p>首次反馈: <span className="font-mono">{performanceResult.routeMetrics.timeToFirstFeedback?.toFixed(2)}ms</span></p>
                  <p>总加载时间: <span className="font-mono">{performanceResult.routeMetrics.totalLoadingTime?.toFixed(2)}ms</span></p>
                  <p>用户感知延迟: <span className="font-mono">{performanceResult.routeMetrics.userPerceivedDelay?.toFixed(2)}ms</span></p>
                </div>
              </div>
            </div>
            {performanceResult.evaluation.recommendations.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">优化建议</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                  {performanceResult.evaluation.recommendations.map((rec: string, index: number) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* 测试结果 */}
        {testResult && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              测试结果
            </h3>
            <p className="text-green-600 dark:text-green-400">{testResult}</p>
          </div>
        )}

        {/* 路由测试链接 */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            路由加载测试
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            点击以下链接测试路由变化时的加载指示器：
          </p>
          <div className="flex flex-wrap gap-3">
            <a
              href="/"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              首页
            </a>
            <a
              href="/blogs"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              博客
            </a>
            <a
              href="/projects"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              项目
            </a>
            <a
              href="/about"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              关于
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
