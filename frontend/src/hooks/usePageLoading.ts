'use client'
import { useEffect, useState, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { useLoading } from '@/contexts/LoadingContext'

interface UsePageLoadingOptions {
  enableRouteLoading?: boolean
  loadingDelay?: number
  minLoadingTime?: number
}

export function usePageLoading(options: UsePageLoadingOptions = {}) {
  const {
    enableRouteLoading = true,
    loadingDelay = 200,
    minLoadingTime = 500
  } = options

  const pathname = usePathname()
  const { showPageLoading, hideLoading } = useLoading()
  const [isPageLoading, setIsPageLoading] = useState(false)
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null)

  // 手动控制加载状态
  const startLoading = useCallback((message?: string) => {
    setIsPageLoading(true)
    setLoadingStartTime(Date.now())
    
    // 延迟显示加载指示器，避免闪烁
    const timer = setTimeout(() => {
      showPageLoading(message)
    }, loadingDelay)

    return () => clearTimeout(timer)
  }, [showPageLoading, loadingDelay])

  const stopLoading = useCallback(() => {
    const now = Date.now()
    const elapsed = loadingStartTime ? now - loadingStartTime : 0
    const remainingTime = Math.max(0, minLoadingTime - elapsed)

    // 确保最小加载时间，避免加载指示器闪烁
    setTimeout(() => {
      setIsPageLoading(false)
      setLoadingStartTime(null)
      hideLoading()
    }, remainingTime)
  }, [hideLoading, loadingStartTime, minLoadingTime])

  // 路由变化时的自动加载处理
  useEffect(() => {
    if (!enableRouteLoading) return

    // 路由变化时停止加载
    stopLoading()
  }, [pathname, enableRouteLoading, stopLoading])

  return {
    isPageLoading,
    startLoading,
    stopLoading
  }
}

// 专门用于数据获取的加载Hook
export function useDataLoading() {
  const { showContentLoading, showApiLoading, hideLoading } = useLoading()
  const [isLoading, setIsLoading] = useState(false)

  const withLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    options: {
      message?: string
      type?: 'content' | 'api'
      minLoadingTime?: number
    } = {}
  ): Promise<T> => {
    const { message, type = 'content', minLoadingTime = 300 } = options
    
    setIsLoading(true)
    const startTime = Date.now()

    try {
      // 显示加载指示器
      if (type === 'api') {
        showApiLoading(message)
      } else {
        showContentLoading(message)
      }

      const result = await asyncFn()
      
      // 确保最小加载时间
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, minLoadingTime - elapsed)
      
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime))
      }

      return result
    } finally {
      setIsLoading(false)
      hideLoading()
    }
  }, [showContentLoading, showApiLoading, hideLoading])

  return {
    isLoading,
    withLoading
  }
}

// 用于组件级别的加载状态
export function useComponentLoading(initialLoading = false) {
  const [isLoading, setIsLoading] = useState(initialLoading)
  const [error, setError] = useState<Error | null>(null)

  const executeWithLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await asyncFn()
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      onError?.(error)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setIsLoading(false)
    setError(null)
  }, [])

  return {
    isLoading,
    error,
    executeWithLoading,
    reset
  }
}
