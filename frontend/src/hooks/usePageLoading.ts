'use client'
import { useEffect, useState, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { useLoading } from '@/contexts/LoadingContext'

interface UsePageLoadingOptions {
  enableRouteLoading?: boolean
  loadingDelay?: number
  minLoadingTime?: number
}

export function usePageLoading(options: UsePageLoadingOptions = {}) {
  const {
    enableRouteLoading = true,
    loadingDelay = 200,
    minLoadingTime = 500
  } = options

  const pathname = usePathname()
  const { showPageLoading, hideLoading } = useLoading()
  const [isPageLoading, setIsPageLoading] = useState(false)
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null)

  // Manual loading state control
  const startLoading = useCallback((message?: string) => {
    setIsPageLoading(true)
    setLoadingStartTime(Date.now())

    // Delay showing loading indicator to avoid flickering
    const timer = setTimeout(() => {
      showPageLoading(message)
    }, loadingDelay)

    return () => clearTimeout(timer)
  }, [showPageLoading, loadingDelay])

  const stopLoading = useCallback(() => {
    const now = Date.now()
    const elapsed = loadingStartTime ? now - loadingStartTime : 0
    const remainingTime = Math.max(0, minLoadingTime - elapsed)

    // Ensure minimum loading time to avoid loading indicator flickering
    setTimeout(() => {
      setIsPageLoading(false)
      setLoadingStartTime(null)
      hideLoading()
    }, remainingTime)
  }, [hideLoading, loadingStartTime, minLoadingTime])

  // Automatic loading handling on route changes
  useEffect(() => {
    if (!enableRouteLoading) return

    // Stop loading on route change
    stopLoading()
  }, [pathname, enableRouteLoading, stopLoading])

  return {
    isPageLoading,
    startLoading,
    stopLoading
  }
}

// Hook specifically for data fetching loading states
export function useDataLoading() {
  const { showContentLoading, showApiLoading, hideLoading } = useLoading()
  const [isLoading, setIsLoading] = useState(false)

  const withLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    options: {
      message?: string
      type?: 'content' | 'api'
      minLoadingTime?: number
    } = {}
  ): Promise<T> => {
    const { message, type = 'content', minLoadingTime = 300 } = options
    
    setIsLoading(true)
    const startTime = Date.now()

    try {
      // Show loading indicator
      if (type === 'api') {
        showApiLoading(message)
      } else {
        showContentLoading(message)
      }

      const result = await asyncFn()

      // Ensure minimum loading time
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, minLoadingTime - elapsed)
      
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime))
      }

      return result
    } finally {
      setIsLoading(false)
      hideLoading()
    }
  }, [showContentLoading, showApiLoading, hideLoading])

  return {
    isLoading,
    withLoading
  }
}

// Hook for component-level loading states
export function useComponentLoading(initialLoading = false) {
  const [isLoading, setIsLoading] = useState(initialLoading)
  const [error, setError] = useState<Error | null>(null)

  const executeWithLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await asyncFn()
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      onError?.(error)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setIsLoading(false)
    setError(null)
  }, [])

  return {
    isLoading,
    error,
    executeWithLoading,
    reset
  }
}
