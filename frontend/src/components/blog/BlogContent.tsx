'use client';

import React, { useEffect, useRef, useState } from 'react';
import { CodeBlock } from './CodeBlock';
import { replaceIconPlaceholders } from '../../utils/markdownIconProcessor';
import { UnifiedImageComponent, ImageModal } from '@/components/unified/UnifiedImageComponent';

interface BlogContentProps {
  content: string;
  className?: string;
}

export function BlogContent({ content, className = '' }: BlogContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [modalImage, setModalImage] = useState<{ src: string; alt: string } | null>(null);

  useEffect(() => {
    if (!contentRef.current) return;

    // 强制重置所有可能的多列布局
    const resetColumnStyles = (element: HTMLElement) => {
      element.style.columnCount = '1';
      element.style.columns = '1';
      element.style.columnFill = 'auto';
      element.style.columnGap = '0';
      element.style.columnRule = 'none';
      element.style.columnSpan = 'none';
      element.style.columnWidth = 'auto';
      // @ts-ignore
      element.style.webkitColumnCount = '1';
      // @ts-ignore
      element.style.mozColumnCount = '1';
    };

    // 重置容器和所有子元素
    resetColumnStyles(contentRef.current);
    const allElements = contentRef.current.querySelectorAll('*');
    allElements.forEach((el) => {
      if (el instanceof HTMLElement) {
        resetColumnStyles(el);
      }
    });

    // 查找所有代码块标记
    const codeBlocks = contentRef.current.querySelectorAll('[data-code-block="true"]');
    
    codeBlocks.forEach((block) => {
      const language = block.getAttribute('data-language') || 'text';
      const code = block.textContent || '';
      
      // 创建React组件的容器
      const container = document.createElement('div');
      block.parentNode?.replaceChild(container, block);
      
      // 使用React渲染CodeBlock组件
      import('react-dom/client').then(({ createRoot }) => {
        const root = createRoot(container);
        root.render(
          React.createElement(CodeBlock, {
            language,
            children: code
          })
        );
      });
    });

    // 为内联代码添加样式
    const inlineCodes = contentRef.current.querySelectorAll('code.inline-code');
    inlineCodes.forEach((code) => {
      code.className = 'px-1.5 py-0.5 bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 rounded text-sm font-mono';
    });

    // 为其他元素添加样式
    const elements = contentRef.current;

    // 生成标题ID的辅助函数
    const generateHeadingId = (text: string, index: number): string => {
      // 移除特殊字符，转换为小写，用连字符连接
      const cleanText = text
        .toLowerCase()
        .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文字符
        .replace(/\s+/g, '-')
        .trim();
      return cleanText || `heading-${index}`;
    };

    // 增强的标题样式和ID设置 - 使用全局索引确保ID唯一性
    const allHeadings = elements.querySelectorAll('h1, h2, h3, h4, h5, h6');
    allHeadings.forEach((h, globalIndex) => {
      const tagName = h.tagName.toLowerCase();

      // 设置基础样式
      switch (tagName) {
        case 'h1':
          h.className = 'text-4xl font-bold mb-6 mt-12 text-foreground tracking-tight leading-tight scroll-mt-20 relative group';
          // 添加装饰性下划线
          if (!h.querySelector('.heading-underline')) {
            const underline = document.createElement('div');
            underline.className = 'absolute -bottom-2 left-0 w-16 h-1 bg-gradient-to-r from-primary to-primary/50 rounded-full heading-underline';
            h.appendChild(underline);
          }
          break;
        case 'h2':
          h.className = 'text-3xl font-bold mb-4 mt-10 text-foreground tracking-tight leading-tight scroll-mt-20 relative group border-l-4 border-primary/30 pl-4 hover:border-primary/60 transition-colors duration-300';
          break;
        case 'h3':
          h.className = 'text-2xl font-semibold mb-3 mt-8 text-foreground/90 tracking-tight leading-tight scroll-mt-20 relative group';
          // 添加左侧装饰点
          if (!h.querySelector('.heading-dot')) {
            const dot = document.createElement('div');
            dot.className = 'absolute -left-4 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-primary/60 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 heading-dot';
            h.appendChild(dot);
          }
          break;
        case 'h4':
          h.className = 'text-xl font-semibold mb-3 mt-6 text-foreground/80 tracking-tight leading-tight scroll-mt-20 relative group';
          break;
        case 'h5':
          h.className = 'text-lg font-medium mb-2 mt-5 text-foreground/70 tracking-tight leading-tight scroll-mt-20 relative group';
          break;
        case 'h6':
          h.className = 'text-base font-medium mb-2 mt-4 text-foreground/60 tracking-tight leading-tight scroll-mt-20 relative group uppercase text-xs letter-spacing-wide';
          break;
      }

      // 设置唯一ID
      if (!h.id) {
        h.id = generateHeadingId(h.textContent || '', globalIndex);
      }
    });

    // 增强的段落样式 - 优化阅读节奏
    const paragraphs = elements.querySelectorAll('p');
    paragraphs.forEach(p => {
      p.className = 'mb-8 leading-9 text-foreground/85 text-lg tracking-wide hover:text-foreground transition-colors duration-300 relative group'

      // 添加段落悬停效果
      p.style.paddingLeft = '1rem'
      p.style.paddingRight = '1rem'
      p.style.paddingTop = '0.5rem'
      p.style.paddingBottom = '0.5rem'
      p.style.borderRadius = '0.5rem'
      p.style.transition = 'all 0.3s ease'

      // 创建悬停背景
      const hoverBg = document.createElement('div')
      hoverBg.className = 'absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none'
      p.style.position = 'relative'
      p.insertBefore(hoverBg, p.firstChild)
    });

    // 增强的列表样式
    const uls = elements.querySelectorAll('ul');
    uls.forEach(ul => {
      ul.className = 'list-none mb-8 space-y-4 text-foreground/85 pl-6'

      // 为每个列表项添加自定义样式
      const lis = ul.querySelectorAll('li')
      lis.forEach((li, index) => {
        li.className = 'relative pl-8 py-2 hover:text-foreground transition-colors duration-300 group'

        // 添加自定义项目符号
        const bullet = document.createElement('div')
        bullet.className = 'absolute left-0 top-3 w-2 h-2 rounded-full bg-primary/60 group-hover:bg-primary group-hover:scale-125 transition-all duration-300'
        li.insertBefore(bullet, li.firstChild)

        // 添加悬停背景
        const hoverBg = document.createElement('div')
        hoverBg.className = 'absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none'
        li.style.position = 'relative'
        li.insertBefore(hoverBg, li.firstChild)
      })
    });

    const ols = elements.querySelectorAll('ol');
    ols.forEach(ol => {
      ol.className = 'list-none mb-8 space-y-4 text-foreground/85 pl-6 counter-reset-list'

      // 为有序列表添加自定义编号
      const lis = ol.querySelectorAll('li')
      lis.forEach((li, index) => {
        li.className = 'relative pl-12 py-2 hover:text-foreground transition-colors duration-300 group'
        li.style.counterIncrement = 'list-item'

        // 添加自定义编号
        const number = document.createElement('div')
        number.className = 'absolute left-0 top-2 w-8 h-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-sm font-semibold text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300'
        number.textContent = (index + 1).toString()
        li.insertBefore(number, li.firstChild)

        // 添加悬停背景
        const hoverBg = document.createElement('div')
        hoverBg.className = 'absolute inset-0 bg-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none'
        li.style.position = 'relative'
        li.insertBefore(hoverBg, li.firstChild)
      })
    });

    // 重设计引用块 - 添加装饰线和背景渐变
    const blockquotes = elements.querySelectorAll('blockquote');
    blockquotes.forEach(blockquote => {
      blockquote.className = 'relative pl-8 pr-6 py-6 my-8 bg-gradient-to-r from-primary/8 via-primary/5 to-transparent border-l-4 border-primary rounded-r-2xl italic text-foreground/80 text-lg leading-8 hover:from-primary/12 hover:via-primary/8 hover:to-primary/5 transition-all duration-500 group overflow-hidden'

      // 添加装饰引号
      const quote = document.createElement('div')
      quote.className = 'absolute top-4 left-2 text-4xl text-primary/30 font-serif leading-none'
      quote.textContent = '"'
      blockquote.insertBefore(quote, blockquote.firstChild)

      // 添加右侧装饰线
      const rightBorder = document.createElement('div')
      rightBorder.className = 'absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-primary/50 via-primary/20 to-transparent rounded-l-full'
      blockquote.appendChild(rightBorder)

      // 添加悬停光效
      const glow = document.createElement('div')
      glow.className = 'absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500'
      blockquote.insertBefore(glow, blockquote.firstChild)
    });

    // 增强的链接样式 - 3D悬停效果
    const links = elements.querySelectorAll('a');
    links.forEach(a => {
      a.className = 'text-primary hover:text-primary/80 underline decoration-primary/30 underline-offset-4 hover:decoration-primary/60 transition-all duration-300 font-medium relative group hover:scale-105 hover:-translate-y-0.5'
      a.style.transform = 'perspective(100px) translateZ(0)'
      a.style.transformStyle = 'preserve-3d'

      if (a.getAttribute('href')?.startsWith('http')) {
        a.setAttribute('target', '_blank');
        a.setAttribute('rel', 'noopener noreferrer');
        // 添加外部链接图标
        const icon = document.createElement('span');
        icon.className = 'inline-block ml-1 opacity-60 group-hover:opacity-100 group-hover:scale-110 transition-all duration-300';
        icon.innerHTML = '↗';
        a.appendChild(icon);
      }

      // 添加悬停背景
      const hoverBg = document.createElement('div')
      hoverBg.className = 'absolute inset-0 bg-primary/10 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none -z-10'
      a.style.position = 'relative'
      a.insertBefore(hoverBg, a.firstChild)
    });

    // 增强的强调样式
    const strongs = elements.querySelectorAll('strong');
    strongs.forEach(strong => strong.className = 'font-bold text-foreground bg-gradient-to-r from-primary/10 to-primary/5 px-1 py-0.5 rounded');

    const ems = elements.querySelectorAll('em');
    ems.forEach(em => em.className = 'italic text-foreground/90 font-medium');

    // 水平线样式
    const hrs = elements.querySelectorAll('hr');
    hrs.forEach(hr => hr.className = 'my-8 border-t border-zinc-200 dark:border-zinc-700');

    // 优化的图片展示 - 懒加载和渐进式加载
    const imgs = elements.querySelectorAll('img');
    imgs.forEach((img, index) => {
      const originalSrc = img.src;

      // 创建图片容器
      const container = document.createElement('div');
      container.className = 'relative my-8 group cursor-pointer overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500';

      // 创建占位符
      const placeholder = document.createElement('div');
      placeholder.className = 'flex items-center justify-center bg-muted/30 animate-pulse min-h-[200px] w-full rounded-lg';
      placeholder.innerHTML = `
        <div class="flex flex-col items-center gap-2 text-muted-foreground">
          <div class="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <span class="text-sm">Loading image...</span>
        </div>
      `;

      // 包装图片
      img.parentNode?.insertBefore(container, img);
      container.appendChild(placeholder);
      container.appendChild(img);

      // 初始隐藏图片
      img.style.display = 'none';
      img.className = 'w-full h-auto transition-all duration-500 group-hover:scale-105';

      // 懒加载逻辑
      const loadImage = () => {
        // 前两张图片立即加载，其他图片懒加载
        if (index < 2) {
          img.onload = () => {
            placeholder.style.display = 'none';
            img.style.display = 'block';
            img.classList.add('animate-fade-in-up');
          };
          img.onerror = () => {
            placeholder.innerHTML = `
              <div class="flex flex-col items-center gap-2 text-muted-foreground">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <span class="text-sm">Failed to load image</span>
              </div>
            `;
          };
        } else {
          // 懒加载观察器
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                img.onload = () => {
                  placeholder.style.display = 'none';
                  img.style.display = 'block';
                  img.classList.add('animate-fade-in-up');
                };
                img.onerror = () => {
                  placeholder.innerHTML = `
                    <div class="flex flex-col items-center gap-2 text-muted-foreground">
                      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                      </svg>
                      <span class="text-sm">Failed to load image</span>
                    </div>
                  `;
                };
                observer.unobserve(entry.target);
              }
            });
          }, { rootMargin: '50px' });

          observer.observe(container);
        }
      };

      // 添加悬停效果和点击功能
      const overlay = document.createElement('div');
      overlay.className = 'absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center';
      overlay.innerHTML = `
        <div class="bg-white/90 dark:bg-black/90 rounded-full p-3 backdrop-blur-sm">
          <svg class="w-6 h-6 text-gray-800 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
          </svg>
        </div>
      `;

      container.appendChild(overlay);

      // 点击放大功能
      container.addEventListener('click', () => {
        setModalImage({ src: originalSrc, alt: img.alt || `Image ${index + 1}` });
      });

      // 开始加载图片
      loadImage();
    });

    // 处理图标占位符
    replaceIconPlaceholders(elements);

    // 表格样式
    const tables = elements.querySelectorAll('table');
    tables.forEach(table => {
      table.className = 'table-auto border-collapse border border-zinc-300 dark:border-zinc-600 w-full my-6 rounded-lg overflow-hidden';
      
      const ths = table.querySelectorAll('th');
      ths.forEach(th => th.className = 'border border-zinc-300 dark:border-zinc-600 px-4 py-2 bg-zinc-100 dark:bg-zinc-800 font-semibold text-zinc-900 dark:text-zinc-100');
      
      const tds = table.querySelectorAll('td');
      tds.forEach(td => td.className = 'border border-zinc-300 dark:border-zinc-600 px-4 py-2 text-zinc-700 dark:text-zinc-300');
    });

  }, [content]);

  return (
    <>
      <div
        ref={contentRef}
        className={`blog-content-container prose prose-zinc dark:prose-invert max-w-none ${className}`}
        style={{
          columnCount: 'unset',
          columns: 'unset',
          columnFill: 'unset',
          columnGap: 'unset'
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      />

      {/* 图片模态框 */}
      {modalImage && (
        <ImageModal
          src={modalImage.src}
          alt={modalImage.alt}
          onClose={() => setModalImage(null)}
        />
      )}
    </>
  );
}

// 添加自定义CSS动画样式
const blogContentStyles = `
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fade-out {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-fade-out {
    animation: fade-out 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  /* 自定义滚动条 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.5);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.7);
  }
`

// 移除全局样式注入，改为组件内部管理
// 这些样式现在通过 Tailwind CSS 类名实现，不需要全局注入
