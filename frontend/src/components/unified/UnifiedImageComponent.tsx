'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import Image, { ImageProps } from 'next/image'
import { cn } from '@/lib/utils'
import { Loader2, ImageIcon, AlertCircle, ZoomIn } from 'lucide-react'

// 统一图片组件接口
interface UnifiedImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  // 基础属性
  src: string
  alt: string
  className?: string
  containerClassName?: string
  
  // 功能开关
  progressive?: boolean
  lazy?: boolean
  responsive?: boolean
  showZoomIcon?: boolean
  showLoadingSpinner?: boolean
  
  // 自定义配置
  fallbackSrc?: string
  placeholder?: string
  quality?: number
  sizes?: string
  
  // 样式配置
  loadingClassName?: string
  errorClassName?: string
  
  // 事件回调
  onLoadComplete?: () => void
  onError?: (error: Error) => void
  onClick?: () => void
}

// 统一图片组件
export function UnifiedImageComponent({
  src,
  alt,
  className = '',
  containerClassName = '',
  progressive = true,
  lazy = true,
  responsive = true,
  showZoomIcon = false,
  showLoadingSpinner = true,
  fallbackSrc = '/images/placeholder.jpg',
  placeholder,
  quality = 75,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  loadingClassName = 'animate-pulse bg-muted',
  errorClassName = 'bg-muted flex items-center justify-center text-muted-foreground',
  onLoadComplete,
  onError,
  onClick,
  ...props
}: UnifiedImageProps) {
  // 状态管理
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(!lazy)
  const [currentSrc, setCurrentSrc] = useState(src)
  
  // 引用
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 懒加载观察器
  useEffect(() => {
    if (!lazy) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '50px' // 提前50px开始加载
      }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [lazy])

  // 图片加载处理
  const handleImageLoad = useCallback(() => {
    setIsLoading(false)
    setHasError(false)
    onLoadComplete?.()
  }, [onLoadComplete])

  const handleImageError = useCallback(() => {
    setIsLoading(false)
    
    // 如果有fallback图片且当前不是fallback，则尝试加载fallback
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc)
      setIsLoading(true)
      setHasError(false)
    } else {
      setHasError(true)
      onError?.(new Error(`Failed to load image: ${src}`))
    }
  }, [fallbackSrc, currentSrc, src, onError])

  // 当src改变时重置状态
  useEffect(() => {
    if (src !== currentSrc) {
      setCurrentSrc(src)
      setIsLoading(true)
      setHasError(false)
    }
  }, [src, currentSrc])

  // 生成优化的图片URL
  const getOptimizedImageUrl = useCallback((originalSrc: string, targetWidth?: number) => {
    // 如果是外部URL，直接返回
    if (originalSrc.startsWith('http://') || originalSrc.startsWith('https://')) {
      return originalSrc
    }

    // 如果是本地API图片，添加优化参数
    if (originalSrc.includes('/api/')) {
      const url = new URL(originalSrc, window.location.origin)
      if (targetWidth) {
        url.searchParams.set('w', targetWidth.toString())
        url.searchParams.set('q', quality.toString())
      }
      return url.toString()
    }

    return originalSrc
  }, [quality])

  // 生成占位符
  const generatePlaceholder = useCallback(() => {
    if (placeholder) return placeholder
    
    // 生成简单的SVG占位符
    const width = props.width || 400
    const height = props.height || 300
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="sans-serif" font-size="14">
          Loading...
        </text>
      </svg>
    `
    return `data:image/svg+xml;base64,${btoa(svg)}`
  }, [placeholder, props.width, props.height])

  // 占位符组件
  const Placeholder = () => (
    <div className="absolute inset-0 flex items-center justify-center">
      {showLoadingSpinner && isLoading && (
        <>
          {progressive && (
            <img
              src={generatePlaceholder()}
              alt=""
              className="w-full h-full object-cover opacity-50"
            />
          )}
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
          </div>
        </>
      )}
      
      {hasError && (
        <div className="flex flex-col items-center justify-center text-muted-foreground">
          <AlertCircle className="w-12 h-12 mb-2" />
          <span className="text-sm">Image failed to load</span>
        </div>
      )}
      
      {!isInView && !hasError && (
        <div className="flex items-center justify-center">
          <ImageIcon className="w-12 h-12 text-gray-400" />
        </div>
      )}
    </div>
  )

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-hidden group",
        containerClassName,
        onClick && "cursor-pointer"
      )}
      onClick={onClick}
    >
      {/* 占位符 - 在图片加载时显示 */}
      {(isLoading || hasError || !isInView) && <Placeholder />}

      {/* 实际图片 - 只在进入视口时加载 */}
      {isInView && !hasError && (
        <Image
          ref={imgRef}
          src={getOptimizedImageUrl(currentSrc, props.width as number)}
          alt={alt}
          quality={progressive ? quality : 90}
          loading={lazy ? 'lazy' : 'eager'}
          sizes={responsive ? sizes : undefined}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={cn(
            "transition-all duration-500",
            "group-hover:scale-105",
            isLoading ? "opacity-0" : "opacity-100",
            className
          )}
          {...props}
        />
      )}

      {/* 缩放图标 */}
      {showZoomIcon && onClick && !isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black/20">
          <div className="bg-white/90 rounded-full p-2">
            <ZoomIn className="w-6 h-6 text-gray-800" />
          </div>
        </div>
      )}

      {/* 加载完成后的渐入动画 */}
      {!isLoading && !hasError && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
      )}
    </div>
  )
}

// 响应式图片组件
interface ResponsiveImageProps extends UnifiedImageProps {
  breakpoints?: {
    sm?: string
    md?: string
    lg?: string
    xl?: string
  }
}

export function ResponsiveImage({
  breakpoints = {},
  ...props
}: ResponsiveImageProps) {
  const sizes = [
    breakpoints.sm && `(max-width: 640px) ${breakpoints.sm}`,
    breakpoints.md && `(max-width: 768px) ${breakpoints.md}`,
    breakpoints.lg && `(max-width: 1024px) ${breakpoints.lg}`,
    breakpoints.xl && `(max-width: 1280px) ${breakpoints.xl}`,
    '100vw'
  ].filter(Boolean).join(', ')

  return (
    <UnifiedImageComponent
      sizes={sizes}
      responsive={true}
      {...props}
    />
  )
}

// 头像组件
interface UnifiedAvatarProps extends UnifiedImageProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  rounded?: boolean
}

export function UnifiedAvatar({
  size = 'md',
  rounded = true,
  className,
  ...props
}: UnifiedAvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }

  const sizeValues = {
    sm: { width: 32, height: 32 },
    md: { width: 48, height: 48 },
    lg: { width: 64, height: 64 },
    xl: { width: 96, height: 96 }
  }

  return (
    <UnifiedImageComponent
      width={sizeValues[size].width}
      height={sizeValues[size].height}
      className={cn(
        sizeClasses[size],
        rounded && 'rounded-full',
        'object-cover',
        className
      )}
      quality={90} // 头像使用更高质量
      progressive={true}
      lazy={true}
      responsive={false}
      {...props}
    />
  )
}

// 背景图片组件
interface BackgroundImageProps {
  src: string
  alt?: string
  children?: React.ReactNode
  className?: string
  overlay?: boolean
  overlayOpacity?: number
}

export function BackgroundImage({
  src,
  alt = '',
  children,
  className,
  overlay = false,
  overlayOpacity = 0.5
}: BackgroundImageProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <UnifiedImageComponent
        src={src}
        alt={alt}
        fill
        className="object-cover"
        quality={60} // 背景图片可以使用较低质量
        priority={false}
        progressive={true}
        lazy={true}
        responsive={true}
      />

      {overlay && (
        <div
          className="absolute inset-0 bg-black z-10"
          style={{ opacity: overlayOpacity }}
        />
      )}

      {children && (
        <div className="relative z-20">
          {children}
        </div>
      )}
    </div>
  )
}

// 图片模态框组件
interface ImageModalProps {
  src: string
  alt: string
  onClose: () => void
}

export function ImageModal({ src, alt, onClose }: ImageModalProps) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    document.body.style.overflow = 'hidden'

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [onClose])

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
      onClick={onClose}
    >
      <div className="relative max-w-[90vw] max-h-[90vh] p-4">
        <button
          onClick={onClose}
          className="absolute -top-2 -right-2 z-10 w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <UnifiedImageComponent
          src={src}
          alt={alt}
          priority
          progressive={false}
          lazy={false}
          responsive={true}
          className="max-w-full max-h-full rounded-lg shadow-2xl"
        />
      </div>
    </div>
  )
}

// 图片预加载Hook
export function useImagePreload(urls: string[]) {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set())

  useEffect(() => {
    const preloadImage = (url: string) => {
      return new Promise<void>((resolve, reject) => {
        const img = new window.Image()
        img.onload = () => {
          setLoadedImages(prev => new Set(prev).add(url))
          resolve()
        }
        img.onerror = reject
        img.src = url
      })
    }

    const preloadAll = async () => {
      try {
        await Promise.all(urls.map(preloadImage))
      } catch (error) {
        // Some images failed to preload (silently handled in production)
      }
    }

    if (urls.length > 0) {
      preloadAll()
    }
  }, [urls])

  return {
    isLoaded: (url: string) => loadedImages.has(url),
    loadedCount: loadedImages.size,
    totalCount: urls.length,
    isAllLoaded: loadedImages.size === urls.length
  }
}

// 简化的加载指示器组件
export function SimpleLoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
  )
}

// 内联加载指示器
export function InlineLoadingIndicator({ text = 'Loading...' }: { text?: string }) {
  return (
    <div className="flex items-center space-x-2">
      <SimpleLoadingSpinner size="sm" />
      <span className="text-sm text-muted-foreground">{text}</span>
    </div>
  )
}
