'use client'
import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

interface PageLoadingOverlayProps {
  isLoading?: boolean
  message?: string
  showOverlay?: boolean
}

export function PageLoadingOverlay({
  isLoading = false,
  message = 'Loading...',
  showOverlay = true
}: PageLoadingOverlayProps) {
  const [show, setShow] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState(message)
  const pathname = usePathname()

  useEffect(() => {
    if (isLoading) {
      setShow(true)
      // Set different loading messages based on path
      if (pathname.includes('/blogs/')) {
        setLoadingMessage('Loading article content...')
      } else if (pathname.includes('/projects/')) {
        setLoadingMessage('Loading project details...')
      } else if (pathname.includes('/gallery/')) {
        setLoadingMessage('Loading images...')
      } else {
        setLoadingMessage(message)
      }
    } else {
      // Delay hiding to let user see loading completion feedback
      const timer = setTimeout(() => setShow(false), 300)
      return () => clearTimeout(timer)
    }
  }, [isLoading, message, pathname])

  if (!show || !showOverlay) return null

  return (
    <div className="fixed inset-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm z-50 flex items-center justify-center transition-all duration-300">
      <div className="text-center max-w-sm mx-auto px-6">
        {/* 主加载动画 */}
        <div className="relative mb-6">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 dark:border-gray-700 border-t-blue-600 dark:border-t-blue-400 mx-auto"></div>
          
          {/* 内部脉冲效果 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full animate-pulse"></div>
          </div>
          
          {/* 外部光环效果 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-20 h-20 border-2 border-blue-200 dark:border-blue-800 rounded-full animate-ping opacity-20"></div>
          </div>
        </div>
        
        {/* 加载文本 */}
        <div className="space-y-2">
          <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {loadingMessage}
          </p>
          
          {/* 进度指示点 */}
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"></div>
            <div 
              className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" 
              style={{ animationDelay: '0.1s' }}
            ></div>
            <div 
              className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" 
              style={{ animationDelay: '0.2s' }}
            ></div>
          </div>
        </div>
        
        {/* Hint text */}
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
          Please wait, preparing amazing content for you
        </p>
      </div>
    </div>
  )
}

// 简化版本的加载指示器
export function SimpleLoadingSpinner({ 
  size = 'md', 
  className = '' 
}: { 
  size?: 'sm' | 'md' | 'lg'
  className?: string 
}) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}></div>
  )
}

// Inline loading indicator
export function InlineLoadingIndicator({
  text = 'Loading...',
  className = ''
}: {
  text?: string
  className?: string
}) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <SimpleLoadingSpinner size="sm" />
      <span className="text-sm text-gray-600 dark:text-gray-400">{text}</span>
    </div>
  )
}
