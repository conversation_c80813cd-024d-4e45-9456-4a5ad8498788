'use client'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import NProgress from 'nprogress'

// 配置NProgress
NProgress.configure({
  minimum: 0.3,
  easing: 'ease',
  speed: 800,
  showSpinner: false,
})

export function GlobalLoadingIndicator() {
  const [loading, setLoading] = useState(false)
  const [routeChanging, setRouteChanging] = useState(false)

  useEffect(() => {
    // 路由变化监听
    const handleRouteStart = () => {
      setLoading(true)
      setRouteChanging(true)
      NProgress.start()
    }

    const handleRouteComplete = () => {
      setLoading(false)
      setRouteChanging(false)
      NProgress.done()
    }

    // 页面可见性变化
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        NProgress.done()
        setLoading(false)
        setRouteChanging(false)
      }
    }

    // 监听页面加载状态
    const handleLoad = () => {
      NProgress.done()
      setLoading(false)
      setRouteChanging(false)
    }

    // 监听beforeunload事件
    const handleBeforeUnload = () => {
      setLoading(true)
      setRouteChanging(true)
      NProgress.start()
    }

    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('load', handleLoad)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 监听路由变化（通过popstate事件）
    window.addEventListener('popstate', handleRouteStart)

    // 监听点击链接事件
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement
      const link = target.closest('a')
      
      if (link && link.href && !link.href.startsWith('#') && !link.target) {
        const url = new URL(link.href)
        const currentUrl = new URL(window.location.href)
        
        // 只在跳转到不同页面时显示加载
        if (url.pathname !== currentUrl.pathname) {
          handleRouteStart()
        }
      }
    }

    document.addEventListener('click', handleLinkClick)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('load', handleLoad)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('popstate', handleRouteStart)
      document.removeEventListener('click', handleLinkClick)
    }
  }, [])

  return (
    <>
      {/* 全局进度条样式 */}
      <style jsx global>{`
        #nprogress {
          pointer-events: none;
        }
        
        #nprogress .bar {
          background: linear-gradient(90deg, #3b82f6, #8b5cf6) !important;
          position: fixed;
          z-index: 1031;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
        }
        
        #nprogress .peg {
          display: block;
          position: absolute;
          right: 0px;
          width: 100px;
          height: 100%;
          box-shadow: 0 0 10px #3b82f6, 0 0 5px #3b82f6;
          opacity: 1.0;
          transform: rotate(3deg) translate(0px, -4px);
        }
        
        /* 暗色主题适配 */
        .dark #nprogress .bar {
          background: linear-gradient(90deg, #60a5fa, #a78bfa) !important;
        }
        
        .dark #nprogress .peg {
          box-shadow: 0 0 10px #60a5fa, 0 0 5px #60a5fa;
        }
      `}</style>

      {/* 页面加载遮罩 */}
      {loading && (
        <div className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center transition-opacity duration-300">
          <div className="text-center">
            <div className="relative">
              {/* 主加载动画 */}
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700 border-t-blue-600 dark:border-t-blue-400 mx-auto"></div>
              
              {/* 内部脉冲动画 */}
              <div className="absolute inset-0 animate-pulse">
                <div className="rounded-full h-8 w-8 bg-blue-100 dark:bg-blue-900/30 mx-auto mt-2"></div>
              </div>
            </div>
            
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400 font-medium">
              {routeChanging ? '页面跳转中...' : '加载中...'}
            </p>
            
            {/* 加载进度指示点 */}
            <div className="flex justify-center mt-3 space-x-1">
              <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
