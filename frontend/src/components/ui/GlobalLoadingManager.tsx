'use client'
import { useLoading } from '@/contexts/LoadingContext'
import { PageLoadingOverlay } from './PageLoadingOverlay'

export function GlobalLoadingManager() {
  const { loadingState } = useLoading()

  // 只在页面级别加载时显示全屏遮罩
  if (loadingState.type === 'page' && loadingState.isLoading) {
    return (
      <PageLoadingOverlay
        isLoading={loadingState.isLoading}
        message={loadingState.message}
        showOverlay={true}
      />
    )
  }

  return null
}
