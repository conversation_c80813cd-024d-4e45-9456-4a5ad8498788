'use client'
import { useEffect, useState, Suspense } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import NProgress from 'nprogress'

// 配置NProgress
NProgress.configure({
  minimum: 0.3,
  easing: 'ease',
  speed: 500,
  showSpinner: false,
})

function RouteProgressBarInner() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // 路由变化时的处理
    const handleStart = () => {
      setIsLoading(true)
      NProgress.start()
    }

    const handleComplete = () => {
      setIsLoading(false)
      NProgress.done()
    }

    // 监听链接点击
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement
      const link = target.closest('a')
      
      if (link && link.href && !link.href.startsWith('#') && !link.target && !link.download) {
        try {
          const url = new URL(link.href)
          const currentUrl = new URL(window.location.href)
          
          // 检查是否是外部链接
          if (url.origin !== currentUrl.origin) {
            return
          }
          
          // 检查是否是相同页面
          if (url.pathname !== currentUrl.pathname || url.search !== currentUrl.search) {
            handleStart()
            
            // 设置超时，防止加载指示器一直显示
            setTimeout(() => {
              if (isLoading) {
                handleComplete()
              }
            }, 10000) // 10秒超时
          }
        } catch (error) {
          // URL解析失败，忽略
        }
      }
    }

    // 监听浏览器前进后退
    const handlePopState = () => {
      handleStart()
    }

    // 页面可见性变化
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isLoading) {
        handleComplete()
      }
    }

    // 添加事件监听器
    document.addEventListener('click', handleLinkClick, true)
    window.addEventListener('popstate', handlePopState)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 页面加载完成时清除加载状态
    const timer = setTimeout(handleComplete, 100)

    return () => {
      document.removeEventListener('click', handleLinkClick, true)
      window.removeEventListener('popstate', handlePopState)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      clearTimeout(timer)
      NProgress.done()
    }
  }, [pathname, searchParams, isLoading])

  // 当路由变化时，清除加载状态
  useEffect(() => {
    setIsLoading(false)
    NProgress.done()
  }, [pathname, searchParams])

  return (
    <>
      {/* NProgress样式 */}
      <style jsx global>{`
        #nprogress {
          pointer-events: none;
        }
        
        #nprogress .bar {
          background: linear-gradient(90deg, #3b82f6, #8b5cf6) !important;
          position: fixed;
          z-index: 1031;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
          border-radius: 0 0 2px 2px;
        }
        
        #nprogress .peg {
          display: block;
          position: absolute;
          right: 0px;
          width: 100px;
          height: 100%;
          box-shadow: 0 0 10px #3b82f6, 0 0 5px #3b82f6;
          opacity: 1.0;
          transform: rotate(3deg) translate(0px, -4px);
        }
        
        /* 暗色主题适配 */
        .dark #nprogress .bar {
          background: linear-gradient(90deg, #60a5fa, #a78bfa) !important;
        }
        
        .dark #nprogress .peg {
          box-shadow: 0 0 10px #60a5fa, 0 0 5px #60a5fa;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
          #nprogress .bar {
            height: 2px;
          }
        }
      `}</style>
    </>
  )
}

export function RouteProgressBar() {
  return (
    <Suspense fallback={null}>
      <RouteProgressBarInner />
    </Suspense>
  )
}
