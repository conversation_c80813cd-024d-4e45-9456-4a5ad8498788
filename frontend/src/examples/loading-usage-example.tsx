/**
 * Example usage of the global loading system with English prompts
 * This file demonstrates how to use the loading hooks and components
 */

'use client'
import { useState } from 'react'
import { useLoading, useApiLoading } from '@/contexts/LoadingContext'
import { usePageLoading, useDataLoading } from '@/hooks/usePageLoading'
import { InlineLoadingIndicator, SimpleLoadingSpinner } from '@/components/ui/PageLoadingOverlay'

export function LoadingUsageExample() {
  const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = useLoading()
  const { withLoading } = useApiLoading()
  const { withLoading: withDataLoading } = useDataLoading()
  const { startLoading, stopLoading } = usePageLoading()
  const [result, setResult] = useState<string>('')

  // Simulate API call
  const simulateApiCall = async (delay: number = 2000) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve('API call successful!')
      }, delay)
    })
  }

  // Example 1: Page loading
  const handlePageLoading = () => {
    showPageLoading('Loading page content...')
    setTimeout(() => {
      hideLoading()
      setResult('Page loading completed')
    }, 3000)
  }

  // Example 2: Content loading
  const handleContentLoading = () => {
    showContentLoading('Loading article content...')
    setTimeout(() => {
      hideLoading()
      setResult('Content loading completed')
    }, 2000)
  }

  // Example 3: API loading with hook
  const handleApiLoading = async () => {
    try {
      const result = await withLoading(
        () => simulateApiCall(2500),
        'Fetching data from server...'
      )
      setResult(`API loading completed: ${result}`)
    } catch (error) {
      setResult('API loading failed')
    }
  }

  // Example 4: Data loading with options
  const handleDataLoading = async () => {
    try {
      const result = await withDataLoading(
        () => simulateApiCall(2000),
        {
          message: 'Processing your request...',
          type: 'content',
          minLoadingTime: 1000
        }
      )
      setResult(`Data loading completed: ${result}`)
    } catch (error) {
      setResult('Data loading failed')
    }
  }

  // Example 5: Manual page loading control
  const handleManualPageLoading = () => {
    const cleanup = startLoading('Preparing content...')
    setTimeout(() => {
      stopLoading()
      setResult('Manual page loading completed')
      cleanup?.()
    }, 2500)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Global Loading System Examples
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Demonstrating various loading states with English prompts
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Page Loading Examples */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Page Loading
          </h2>
          <div className="space-y-3">
            <button
              onClick={handlePageLoading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Show Page Loading
            </button>
            <button
              onClick={handleManualPageLoading}
              className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Manual Page Loading
            </button>
          </div>
        </div>

        {/* Content Loading Examples */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Content Loading
          </h2>
          <div className="space-y-3">
            <button
              onClick={handleContentLoading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Show Content Loading
            </button>
            <button
              onClick={handleDataLoading}
              className="w-full px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              Data Loading Hook
            </button>
          </div>
        </div>

        {/* API Loading Examples */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            API Loading
          </h2>
          <button
            onClick={handleApiLoading}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            API Loading Hook
          </button>
        </div>

        {/* Component Examples */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Loading Components
          </h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Simple Spinner:</p>
              <SimpleLoadingSpinner size="md" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Inline Indicator:</p>
              <InlineLoadingIndicator text="Processing..." />
            </div>
          </div>
        </div>
      </div>

      {/* Result Display */}
      {result && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Result
          </h3>
          <p className="text-green-600 dark:text-green-400">{result}</p>
        </div>
      )}

      {/* Usage Code Examples */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Code Examples
        </h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Basic Usage:</h4>
            <pre className="bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-x-auto">
              <code>{`const { showPageLoading, hideLoading } = useLoading()

// Show loading
showPageLoading('Loading page content...')

// Hide loading
hideLoading()`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">API Loading Hook:</h4>
            <pre className="bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-x-auto">
              <code>{`const { withLoading } = useApiLoading()

const result = await withLoading(
  () => fetch('/api/data').then(res => res.json()),
  'Fetching data from server...'
)`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}
