'use client'
import { createContext, useContext, useState, useCallback, ReactNode } from 'react'

interface LoadingState {
  isLoading: boolean
  message: string
  type: 'page' | 'content' | 'api' | 'route'
}

interface LoadingContextType {
  loadingState: LoadingState
  setLoading: (loading: boolean, message?: string, type?: LoadingState['type']) => void
  showPageLoading: (message?: string) => void
  showContentLoading: (message?: string) => void
  showApiLoading: (message?: string) => void
  hideLoading: () => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function LoadingProvider({ children }: { children: ReactNode }) {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    message: 'Loading...',
    type: 'page'
  })

  const setLoading = useCallback((
    loading: boolean,
    message: string = 'Loading...',
    type: LoadingState['type'] = 'page'
  ) => {
    setLoadingState({
      isLoading: loading,
      message,
      type
    })
  }, [])

  const showPageLoading = useCallback((message: string = 'Loading page...') => {
    setLoading(true, message, 'page')
  }, [setLoading])

  const showContentLoading = useCallback((message: string = 'Loading content...') => {
    setLoading(true, message, 'content')
  }, [setLoading])

  const showApiLoading = useCallback((message: string = 'Loading data...') => {
    setLoading(true, message, 'api')
  }, [setLoading])

  const hideLoading = useCallback(() => {
    setLoading(false)
  }, [setLoading])

  const value: LoadingContextType = {
    loadingState,
    setLoading,
    showPageLoading,
    showContentLoading,
    showApiLoading,
    hideLoading
  }

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  )
}

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

// Convenient Hook for loading state during API calls
export function useApiLoading() {
  const { showApiLoading, hideLoading } = useLoading()
  
  const withLoading = useCallback(async <T,>(
    asyncFn: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    try {
      showApiLoading(message)
      const result = await asyncFn()
      return result
    } finally {
      hideLoading()
    }
  }, [showApiLoading, hideLoading])

  return { withLoading }
}

// Convenient Hook for loading state during page navigation
export function usePageLoading() {
  const { showPageLoading, hideLoading } = useLoading()
  
  const withPageLoading = useCallback(async <T,>(
    asyncFn: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    try {
      showPageLoading(message)
      const result = await asyncFn()
      return result
    } finally {
      hideLoading()
    }
  }, [showPageLoading, hideLoading])

  return { withPageLoading }
}
