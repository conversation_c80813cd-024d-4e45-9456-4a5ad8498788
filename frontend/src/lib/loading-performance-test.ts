/**
 * 全局加载体验性能测试工具
 * 用于测试和验证加载指示器的性能表现
 */

interface LoadingPerformanceMetrics {
  // 用户感知指标
  timeToFirstFeedback: number // 首次反馈时间
  totalLoadingTime: number // 总加载时间
  userPerceivedDelay: number // 用户感知延迟
  
  // 技术指标
  componentRenderTime: number // 组件渲染时间
  contextUpdateTime: number // 上下文更新时间
  hookExecutionTime: number // Hook执行时间
  
  // 体验指标
  loadingConsistency: number // 加载一致性评分
  visualFeedbackQuality: number // 视觉反馈质量评分
}

class LoadingPerformanceTracker {
  private metrics: Partial<LoadingPerformanceMetrics> = {}
  private startTimes: Map<string, number> = new Map()
  private testResults: Array<{
    testName: string
    metrics: Partial<LoadingPerformanceMetrics>
    timestamp: number
  }> = []

  // 开始性能测试
  startTest(testName: string) {
    this.startTimes.set(testName, performance.now())
    console.log(`🚀 开始测试: ${testName}`)
  }

  // 结束性能测试
  endTest(testName: string): number {
    const startTime = this.startTimes.get(testName)
    if (!startTime) {
      console.warn(`⚠️ 未找到测试 ${testName} 的开始时间`)
      return 0
    }

    const duration = performance.now() - startTime
    this.startTimes.delete(testName)
    console.log(`✅ 测试完成: ${testName} - ${duration.toFixed(2)}ms`)
    return duration
  }

  // 测试路由加载性能
  async testRouteLoadingPerformance(): Promise<LoadingPerformanceMetrics> {
    const metrics: Partial<LoadingPerformanceMetrics> = {}

    // 测试首次反馈时间
    this.startTest('firstFeedback')
    // 模拟路由变化触发
    const feedbackTime = this.endTest('firstFeedback')
    metrics.timeToFirstFeedback = feedbackTime

    // 测试组件渲染时间
    this.startTest('componentRender')
    await new Promise(resolve => setTimeout(resolve, 50)) // 模拟渲染时间
    metrics.componentRenderTime = this.endTest('componentRender')

    // 测试总加载时间
    this.startTest('totalLoading')
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟完整加载
    metrics.totalLoadingTime = this.endTest('totalLoading')

    // 计算用户感知延迟
    metrics.userPerceivedDelay = Math.max(0, metrics.totalLoadingTime - metrics.timeToFirstFeedback)

    return metrics as LoadingPerformanceMetrics
  }

  // 测试加载状态管理性能
  async testLoadingStatePerformance(): Promise<Partial<LoadingPerformanceMetrics>> {
    const metrics: Partial<LoadingPerformanceMetrics> = {}

    // 测试上下文更新时间
    this.startTest('contextUpdate')
    // 模拟状态更新
    await new Promise(resolve => setTimeout(resolve, 10))
    metrics.contextUpdateTime = this.endTest('contextUpdate')

    // 测试Hook执行时间
    this.startTest('hookExecution')
    // 模拟Hook执行
    await new Promise(resolve => setTimeout(resolve, 5))
    metrics.hookExecutionTime = this.endTest('hookExecution')

    return metrics
  }

  // 评估加载体验质量
  evaluateLoadingExperience(metrics: LoadingPerformanceMetrics): {
    score: number
    grade: string
    recommendations: string[]
  } {
    const recommendations: string[] = []
    let score = 100

    // 评估首次反馈时间 (目标: <200ms)
    if (metrics.timeToFirstFeedback > 200) {
      score -= 20
      recommendations.push('优化首次反馈时间，建议使用预加载或骨架屏')
    }

    // 评估用户感知延迟 (目标: <1000ms)
    if (metrics.userPerceivedDelay > 1000) {
      score -= 15
      recommendations.push('减少用户感知延迟，考虑分步加载或进度指示')
    }

    // 评估组件渲染性能 (目标: <100ms)
    if (metrics.componentRenderTime > 100) {
      score -= 10
      recommendations.push('优化组件渲染性能，考虑使用React.memo或懒加载')
    }

    // 评估总加载时间 (目标: <2000ms)
    if (metrics.totalLoadingTime > 2000) {
      score -= 25
      recommendations.push('优化总加载时间，检查网络请求和数据处理')
    }

    // 确定等级
    let grade: string
    if (score >= 90) grade = 'A+'
    else if (score >= 80) grade = 'A'
    else if (score >= 70) grade = 'B'
    else if (score >= 60) grade = 'C'
    else grade = 'D'

    return { score, grade, recommendations }
  }

  // 运行完整的性能测试套件
  async runFullPerformanceTest(): Promise<{
    routeMetrics: LoadingPerformanceMetrics
    stateMetrics: Partial<LoadingPerformanceMetrics>
    evaluation: ReturnType<LoadingPerformanceTracker['evaluateLoadingExperience']>
  }> {
    console.log('🔍 开始全局加载体验性能测试...')

    const routeMetrics = await this.testRouteLoadingPerformance()
    const stateMetrics = await this.testLoadingStatePerformance()
    
    const combinedMetrics: LoadingPerformanceMetrics = {
      ...routeMetrics,
      ...stateMetrics
    } as LoadingPerformanceMetrics

    const evaluation = this.evaluateLoadingExperience(combinedMetrics)

    // 保存测试结果
    this.testResults.push({
      testName: 'fullPerformanceTest',
      metrics: combinedMetrics,
      timestamp: Date.now()
    })

    console.log('📊 性能测试结果:')
    console.log(`评分: ${evaluation.score}/100 (${evaluation.grade})`)
    console.log('指标详情:', combinedMetrics)
    if (evaluation.recommendations.length > 0) {
      console.log('优化建议:', evaluation.recommendations)
    }

    return {
      routeMetrics,
      stateMetrics,
      evaluation
    }
  }

  // 获取测试历史
  getTestHistory() {
    return this.testResults
  }

  // 清除测试历史
  clearTestHistory() {
    this.testResults = []
  }

  // 导出测试报告
  exportTestReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      testCount: this.testResults.length,
      results: this.testResults,
      summary: this.generateSummary()
    }

    return JSON.stringify(report, null, 2)
  }

  private generateSummary() {
    if (this.testResults.length === 0) {
      return { message: '暂无测试数据' }
    }

    const latestResult = this.testResults[this.testResults.length - 1]
    return {
      latestTest: latestResult.testName,
      latestTimestamp: new Date(latestResult.timestamp).toLocaleString(),
      keyMetrics: latestResult.metrics
    }
  }
}

// 导出单例实例
export const loadingPerformanceTracker = new LoadingPerformanceTracker()

// 便捷的测试函数
export async function quickLoadingTest() {
  return await loadingPerformanceTracker.runFullPerformanceTest()
}

// 在浏览器控制台中使用的全局函数
if (typeof window !== 'undefined') {
  (window as any).testLoadingPerformance = quickLoadingTest
  (window as any).loadingTracker = loadingPerformanceTracker
}
