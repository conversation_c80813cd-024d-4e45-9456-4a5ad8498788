/* Waline Unified Design System */
/* 统一的Waline评论系统样式 - 整合所有变体 */

/* ===== CSS变量系统 ===== */
:root {
  /* 主色调系统 */
  --waline-primary: hsl(var(--primary));
  --waline-primary-soft: hsl(var(--primary) / 0.08);
  --waline-primary-medium: hsl(var(--primary) / 0.15);
  --waline-primary-strong: hsl(var(--primary) / 0.9);
  --waline-primary-foreground: hsl(var(--primary-foreground));
  
  /* 表面色彩系统 */
  --waline-background: hsl(var(--background));
  --waline-card: hsl(var(--card));
  --waline-surface-elevated: hsl(var(--background) / 0.98);
  --waline-surface-glass: hsl(var(--background) / 0.85);
  
  /* 边框系统 */
  --waline-border: hsl(var(--border));
  --waline-border-subtle: hsl(var(--border) / 0.2);
  --waline-border-medium: hsl(var(--border) / 0.4);
  --waline-border-strong: hsl(var(--border) / 0.8);
  
  /* 文本层次系统 */
  --waline-foreground: hsl(var(--foreground));
  --waline-muted-foreground: hsl(var(--muted-foreground));
  --waline-text-secondary: hsl(var(--muted-foreground));
  --waline-text-tertiary: hsl(var(--muted-foreground) / 0.7);
  --waline-text-inverse: hsl(var(--background));
  
  /* 间距系统 */
  --waline-space-xs: 0.25rem;
  --waline-space-sm: 0.5rem;
  --waline-space-md: 0.75rem;
  --waline-space-lg: 1rem;
  --waline-space-xl: 1.5rem;
  --waline-space-2xl: 2rem;
  --waline-space-3xl: 3rem;
  
  /* 圆角系统 */
  --waline-radius-sm: 0.375rem;
  --waline-radius-md: 0.5rem;
  --waline-radius-lg: 0.75rem;
  --waline-radius-xl: 1rem;
  --waline-radius-2xl: 1.5rem;
  
  /* 阴影系统 */
  --waline-shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.04);
  --waline-shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.08);
  --waline-shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.12);
  --waline-shadow-glow: 0 0 32px var(--waline-primary-soft);
  
  /* 动画系统 */
  --waline-duration-fast: 0.15s;
  --waline-duration-normal: 0.2s;
  --waline-duration-slow: 0.3s;
  --waline-ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --waline-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 基础容器样式 ===== */
.waline-container {
  margin: var(--waline-space-2xl) 0;
  font-family: inherit;
}

.waline-wrapper {
  background-color: var(--waline-card);
  border: 1px solid var(--waline-border);
  border-radius: var(--waline-radius-lg);
  padding: var(--waline-space-xl);
  box-shadow: var(--waline-shadow-subtle);
  transition: box-shadow var(--waline-duration-normal) var(--waline-ease-out);
}

.waline-wrapper:hover {
  box-shadow: var(--waline-shadow-medium);
}

/* ===== 面板样式 ===== */
.waline-wrapper .wl-panel {
  background-color: var(--waline-card) !important;
  border: 1px solid var(--waline-border) !important;
  border-radius: var(--waline-radius-md) !important;
  color: var(--waline-foreground) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
}

/* ===== 编辑器样式 ===== */
.waline-wrapper .wl-editor {
  background-color: var(--waline-background) !important;
  border: 1px solid var(--waline-border) !important;
  border-radius: var(--waline-radius-sm) !important;
  color: var(--waline-foreground) !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  transition: all var(--waline-duration-normal) var(--waline-ease-out) !important;
  min-height: 120px !important;
  padding: var(--waline-space-md) !important;
}

.waline-wrapper .wl-editor:focus {
  border-color: var(--waline-primary) !important;
  box-shadow: 0 0 0 2px var(--waline-primary-soft) !important;
  outline: none !important;
}

.waline-wrapper .wl-editor:hover {
  border-color: var(--waline-border-strong) !important;
}

/* ===== 输入框样式 ===== */
.waline-wrapper .wl-input {
  background-color: var(--waline-background) !important;
  border: 1px solid var(--waline-border) !important;
  border-radius: var(--waline-radius-sm) !important;
  color: var(--waline-foreground) !important;
  padding: var(--waline-space-sm) var(--waline-space-md) !important;
  font-size: 0.875rem !important;
  transition: all var(--waline-duration-normal) var(--waline-ease-out) !important;
}

.waline-wrapper .wl-input:focus {
  border-color: var(--waline-primary) !important;
  box-shadow: 0 0 0 2px var(--waline-primary-soft) !important;
  outline: none !important;
}

.waline-wrapper .wl-input:hover {
  border-color: var(--waline-border-strong) !important;
}

.waline-wrapper .wl-input::placeholder {
  color: var(--waline-text-tertiary) !important;
}

/* ===== 按钮样式 ===== */
.waline-wrapper .wl-btn {
  background-color: var(--waline-primary) !important;
  color: var(--waline-primary-foreground) !important;
  border: none !important;
  border-radius: var(--waline-radius-sm) !important;
  padding: var(--waline-space-sm) var(--waline-space-lg) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all var(--waline-duration-normal) var(--waline-ease-out) !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: var(--waline-space-xs) !important;
}

.waline-wrapper .wl-btn:hover {
  background-color: var(--waline-primary-strong) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--waline-shadow-medium) !important;
}

.waline-wrapper .wl-btn:active {
  transform: translateY(0) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
}

.waline-wrapper .wl-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* ===== 次要按钮样式 ===== */
.waline-wrapper .wl-btn.wl-btn-secondary {
  background-color: transparent !important;
  color: var(--waline-text-secondary) !important;
  border: 1px solid var(--waline-border) !important;
}

.waline-wrapper .wl-btn.wl-btn-secondary:hover {
  background-color: var(--waline-primary-soft) !important;
  color: var(--waline-primary) !important;
  border-color: var(--waline-primary) !important;
}

/* ===== 评论列表样式 ===== */
.waline-wrapper .wl-cards {
  margin-top: var(--waline-space-xl) !important;
}

.waline-wrapper .wl-card {
  background-color: var(--waline-surface-elevated) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-md) !important;
  padding: var(--waline-space-lg) !important;
  margin-bottom: var(--waline-space-lg) !important;
  transition: all var(--waline-duration-normal) var(--waline-ease-out) !important;
}

.waline-wrapper .wl-card:hover {
  border-color: var(--waline-border-medium) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
}

/* ===== 用户信息样式 ===== */
.waline-wrapper .wl-user {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-sm) !important;
  margin-bottom: var(--waline-space-md) !important;
}

.waline-wrapper .wl-user .wl-user-avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  border: 2px solid var(--waline-border) !important;
  transition: border-color var(--waline-duration-normal) var(--waline-ease-out) !important;
}

.waline-wrapper .wl-user .wl-user-avatar:hover {
  border-color: var(--waline-primary) !important;
}

.waline-wrapper .wl-user .wl-user-name {
  font-weight: 600 !important;
  color: var(--waline-foreground) !important;
  font-size: 0.875rem !important;
}

.waline-wrapper .wl-user .wl-user-badge {
  background-color: var(--waline-primary-soft) !important;
  color: var(--waline-primary) !important;
  padding: 2px 6px !important;
  border-radius: var(--waline-radius-sm) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
}

/* ===== 评论内容样式 ===== */
.waline-wrapper .wl-content {
  color: var(--waline-foreground) !important;
  line-height: 1.6 !important;
  font-size: 0.875rem !important;
  margin-bottom: var(--waline-space-md) !important;
}

.waline-wrapper .wl-content p {
  margin-bottom: var(--waline-space-sm) !important;
}

.waline-wrapper .wl-content a {
  color: var(--waline-primary) !important;
  text-decoration: none !important;
  transition: color var(--waline-duration-fast) var(--waline-ease-out) !important;
}

.waline-wrapper .wl-content a:hover {
  color: var(--waline-primary-strong) !important;
  text-decoration: underline !important;
}

/* ===== 元信息样式 ===== */
.waline-wrapper .wl-meta {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-md) !important;
  color: var(--waline-text-secondary) !important;
  font-size: 0.75rem !important;
}

.waline-wrapper .wl-time {
  color: var(--waline-text-tertiary) !important;
}

/* ===== 操作按钮样式 ===== */
.waline-wrapper .wl-action {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-sm) !important;
  margin-top: var(--waline-space-md) !important;
}

.waline-wrapper .wl-action button {
  background: none !important;
  border: none !important;
  color: var(--waline-text-secondary) !important;
  font-size: 0.75rem !important;
  cursor: pointer !important;
  padding: var(--waline-space-xs) var(--waline-space-sm) !important;
  border-radius: var(--waline-radius-sm) !important;
  transition: all var(--waline-duration-fast) var(--waline-ease-out) !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 4px !important;
}

.waline-wrapper .wl-action button:hover {
  background-color: var(--waline-primary-soft) !important;
  color: var(--waline-primary) !important;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .waline-wrapper {
    padding: var(--waline-space-lg);
    margin: var(--waline-space-lg) 0;
  }
  
  .waline-wrapper .wl-editor {
    min-height: 100px !important;
  }
  
  .waline-wrapper .wl-user .wl-user-avatar {
    width: 32px !important;
    height: 32px !important;
  }
  
  .waline-wrapper .wl-action {
    flex-wrap: wrap !important;
  }
}

/* ===== 暗色主题适配 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --waline-shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.2);
    --waline-shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.3);
    --waline-shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.4);
  }
}

/* ===== 加载状态样式 ===== */
.waline-wrapper .wl-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: var(--waline-space-xl) !important;
  color: var(--waline-text-secondary) !important;
}

.waline-wrapper .wl-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--waline-border);
  border-top-color: var(--waline-primary);
  border-radius: 50%;
  animation: waline-spin 1s linear infinite;
  margin-left: var(--waline-space-sm);
}

@keyframes waline-spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== 错误状态样式 ===== */
.waline-wrapper .wl-error {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: rgb(239, 68, 68) !important;
  padding: var(--waline-space-md) !important;
  border-radius: var(--waline-radius-sm) !important;
  font-size: 0.875rem !important;
  margin-bottom: var(--waline-space-md) !important;
}
