# Global Loading Experience Optimization Documentation

## Overview

The global loading experience optimization system provides a unified and smooth loading feedback mechanism for the website, including route progress bars, page loading overlays, intelligent state management, and other features.

## 核心组件

### 1. RouteProgressBar - 路由进度条
- **位置**: `src/components/ui/RouteProgressBar.tsx`
- **功能**: 在页面路由变化时显示顶部进度条
- **特性**: 
  - 基于NProgress库
  - 支持暗色主题
  - 响应式设计
  - 智能路由检测

### 2. PageLoadingOverlay - 页面加载遮罩
- **位置**: `src/components/ui/PageLoadingOverlay.tsx`
- **功能**: 全屏加载遮罩和各种加载指示器
- **特性**:
  - 多种动画效果
  - 智能加载消息
  - 暗色主题支持
  - 简化版和内联版组件

### 3. LoadingContext - 加载状态管理
- **位置**: `src/contexts/LoadingContext.tsx`
- **功能**: 全局加载状态管理
- **特性**:
  - 支持页面、内容、API三种加载类型
  - 便捷的Hook接口
  - 自动状态清理

## Hook系统

### useLoading
基础加载状态管理Hook
```typescript
const { showPageLoading, showContentLoading, showApiLoading, hideLoading } = useLoading()

// 显示页面加载
showPageLoading('页面加载中...')

// 隐藏加载
hideLoading()
```

### useApiLoading
API调用加载状态Hook
```typescript
const { withLoading } = useApiLoading()

const fetchData = async () => {
  const result = await withLoading(
    () => fetch('/api/data').then(res => res.json()),
    '正在获取数据...'
  )
  return result
}
```

### usePageLoading
页面级加载控制Hook
```typescript
const { startLoading, stopLoading } = usePageLoading()

// 手动控制页面加载
const handleNavigation = () => {
  const cleanup = startLoading('跳转中...')
  // 执行导航逻辑
  setTimeout(() => {
    stopLoading()
    cleanup?.()
  }, 2000)
}
```

### useDataLoading
数据获取加载Hook
```typescript
const { withLoading } = useDataLoading()

const loadContent = async () => {
  const data = await withLoading(
    () => fetchContentData(),
    {
      message: '正在加载内容...',
      type: 'content',
      minLoadingTime: 500
    }
  )
  return data
}
```

## 使用示例

### 1. 在组件中使用加载状态
```typescript
'use client'
import { useApiLoading } from '@/contexts/LoadingContext'

export function MyComponent() {
  const { withLoading } = useApiLoading()
  
  const handleSubmit = async () => {
    try {
      const result = await withLoading(
        () => submitForm(formData),
        '正在提交表单...'
      )
      console.log('提交成功:', result)
    } catch (error) {
      console.error('提交失败:', error)
    }
  }
  
  return (
    <button onClick={handleSubmit}>
      提交
    </button>
  )
}
```

### 2. 在页面中使用页面加载
```typescript
'use client'
import { usePageLoading } from '@/hooks/usePageLoading'

export default function MyPage() {
  const { startLoading, stopLoading } = usePageLoading()
  
  useEffect(() => {
    // 页面初始化时显示加载
    const cleanup = startLoading('页面初始化中...')
    
    // 模拟数据加载
    setTimeout(() => {
      stopLoading()
      cleanup?.()
    }, 2000)
  }, [])
  
  return <div>页面内容</div>
}
```

### 3. 使用内联加载指示器
```typescript
import { InlineLoadingIndicator, SimpleLoadingSpinner } from '@/components/ui/PageLoadingOverlay'

export function LoadingExample() {
  return (
    <div>
      {/* 简单加载器 */}
      <SimpleLoadingSpinner size="md" />
      
      {/* 内联加载指示器 */}
      <InlineLoadingIndicator text="正在处理..." />
    </div>
  )
}
```

## 性能测试

### 测试页面
访问 `/loading-test` 页面可以测试所有加载功能：
- 页面加载测试
- 内容加载测试
- API加载测试
- 路由跳转测试
- 性能评估

### 性能测试工具
```typescript
import { quickLoadingTest, loadingPerformanceTracker } from '@/lib/loading-performance-test'

// 快速性能测试
const result = await quickLoadingTest()
console.log('性能评分:', result.evaluation.score)

// 详细性能跟踪
loadingPerformanceTracker.startTest('myTest')
// ... 执行测试逻辑
const duration = loadingPerformanceTracker.endTest('myTest')
```

## 配置选项

### NProgress配置
在 `RouteProgressBar.tsx` 中可以调整进度条配置：
```typescript
NProgress.configure({
  minimum: 0.3,      // 最小进度
  easing: 'ease',    // 动画缓动
  speed: 500,        // 动画速度
  showSpinner: false // 是否显示旋转器
})
```

### 加载时间配置
在Hook中可以配置最小加载时间：
```typescript
const { withLoading } = useDataLoading()

await withLoading(
  asyncFunction,
  {
    minLoadingTime: 500, // 最小显示500ms
    message: '加载中...'
  }
)
```

## 最佳实践

1. **避免加载闪烁**: 使用最小加载时间确保用户能看到加载反馈
2. **提供有意义的消息**: 根据操作类型显示相应的加载消息
3. **合理使用加载类型**: 页面级用于路由跳转，内容级用于数据加载
4. **性能监控**: 定期运行性能测试确保加载体验优化
5. **错误处理**: 在加载失败时提供适当的错误反馈

## 技术架构

```
LoadingProvider (Context)
├── RouteProgressBar (全局进度条)
├── GlobalLoadingManager (全局状态管理)
└── PageLoadingOverlay (页面遮罩)

Hook系统:
├── useLoading (基础状态管理)
├── useApiLoading (API调用优化)
├── usePageLoading (页面级控制)
└── useDataLoading (数据获取优化)
```

## 浏览器兼容性

- 现代浏览器 (Chrome 60+, Firefox 60+, Safari 12+)
- 支持 CSS Grid 和 Flexbox
- 支持 ES2018+ 特性
- 响应式设计支持移动端

## 故障排除

### 常见问题

1. **进度条不显示**: 检查是否正确导入和配置RouteProgressBar
2. **加载状态不更新**: 确保组件在LoadingProvider内部
3. **SSR错误**: 使用Suspense边界包裹使用useSearchParams的组件
4. **性能问题**: 检查最小加载时间设置，避免过长的加载动画

### 调试工具

在浏览器控制台中可以使用：
```javascript
// 快速性能测试
window.testLoadingPerformance()

// 查看加载跟踪器
window.loadingTracker
```
